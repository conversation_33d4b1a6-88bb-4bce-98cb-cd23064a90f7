//+------------------------------------------------------------------+
//|                                                      changer.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_plots 0
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+

void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch autoscroll
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("E", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, false);
			}
			if (lparam == StringGetCharacter("Q", 0))
			{
				ChartSetInteger(0, CHART_AUTOSCROLL, true);
			}
		}
	}
	/*
	{
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetCharacter("O", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "simple28.tpl");
			}
			if (lparam == StringGetCharacter("P", 0))
			{
				ObjectsDeleteAll(0, 0, -1);
				Comment("");
				ChartApplyTemplate(0, "cust.tpl");
			}
		}
	}
	*/
	{ //Switch TF by hand
		if (id == CHARTEVENT_KEYDOWN)
		{
			switch (int(lparam))
			{
			case 97:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M1);
				WindowRedraw();
				break;
			case 98:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M5);
				WindowRedraw();
				break;
			case 99:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M15);
				WindowRedraw();
				break;
			case 100:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M30);
				WindowRedraw();
				break;
			case 101:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H1);
				WindowRedraw();
				break;
			case 102:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H4);
				WindowRedraw();
				break;
			case 103:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_D1);
				WindowRedraw();
				break;
			case 104:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_W1);
				WindowRedraw();
				break;
			case 105:
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_MN1);
				WindowRedraw();
				break;
			}
		}
	}
}
//+------------------------------------------------------------------+
