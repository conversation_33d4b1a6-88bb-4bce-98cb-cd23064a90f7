//+------------------------------------------------------------------+
//|                                                         xmas.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
//--- input parameters
input int      DayStart = 1;

#define Name WindowExpertName()

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   trap(DayStart);
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void trap(int pico){
   int startt = pico;
   if (pico < 1) startt = 1;
   if (pico > 10) startt = 10;
   
   string obname;
   
   double begin = iMA(_Symbol, PERIOD_D1, 200, 0, MODE_EMA, PRICE_CLOSE, startt); //iOpen(_Symbol, PERIOD_D1, startt);
   
   double startadr = adr(5, startt + 5);
   
   double P1 = 0, P2 = 0, P3 = 0, P4 = 0, P5 = 0, P6 = 0, P7 = 0, P8 = 0, P9 = 0, P10 = 0;
   //double N1 = 0, N2 = 0, N3 = 0, N4 = 0, N5 = 0, N6 = 0, N7 = 0, N8 = 0, N9 = 0, N10 = 0;
   double H1 = 0, H2 = 0, H3 = 0, H4 = 0, H5 = 0, H6 = 0, H7 = 0, H8 = 0, H9 = 0, H10 = 0, H11 = 0, H12 = 0, H13 = 0, H14 = 0, H15 = 0, H16 = 0, H17 = 0, H18 = 0, H19 = 0, H20 = 0, H21 = 0, H22 = 0, H23 = 0, H24 = 0, H25 = 0, H26 = 0, H27 = 0, H28 = 0, H29 = 0, H30 = 0, H31 = 0, H32 = 0, H33 = 0, H34 = 0, H35 = 0, H36 = 0, H37 = 0, H38 = 0, H39 = 0, H40 = 0, H41 = 0, H42 = 0, H43 = 0, H44 = 0, H45 = 0, H46 = 0, H47 = 0, H48 = 0, H49 = 0, H50 = 0, H51 = 0, H52 = 0, H53 = 0, H54 = 0, H55 = 0;
   double L1 = 0, L2 = 0, L3 = 0, L4 = 0, L5 = 0, L6 = 0, L7 = 0, L8 = 0, L9 = 0, L10 = 0, L11 = 0, L12 = 0, L13 = 0, L14 = 0, L15 = 0, L16 = 0, L17 = 0, L18 = 0, L19 = 0, L20 = 0, L21 = 0, L22 = 0, L23 = 0, L24 = 0, L25 = 0, L26 = 0, L27 = 0, L28 = 0, L29 = 0, L30 = 0, L31 = 0, L32 = 0, L33 = 0, L34 = 0, L35 = 0, L36 = 0, L37 = 0, L38 = 0, L39 = 0, L40 = 0, L41 = 0, L42 = 0, L43 = 0, L44 = 0, L45 = 0, L46 = 0, L47 = 0, L48 = 0, L49 = 0, L50 = 0, L51 = 0, L52 = 0, L53 = 0, L54 = 0, L55 = 0;
   
   if (startt == 1) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      obname = Name + "fh";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrBlue, "");
      obname = Name + "fl";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrBlue, "");
      
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      
      }
   if (startt == 2) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 3) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 4) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 5) {
      double S3 = 1.58113883008419 * startadr;
      double S4 = 2 * 1.58113883008419 * startadr;
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      P6 = startadr / 2;
      H16 = begin + P6 + startadr + startadr + startadr;
      L16 = begin + P6 + startadr + startadr - startadr;
      H17 = begin + P6 + startadr + startadr;
      L17 = begin + P6 + startadr - startadr;
      H18 = begin + P6 + startadr;
      L18 = begin + P6 - startadr;
      H19 = begin - P6 + startadr;
      L19 = begin - P6 - startadr;
      H20 = begin - P6 - startadr + startadr;
      L20 = begin - P6 - startadr - startadr;
      H21 = begin - P6 - startadr - startadr + startadr;
      L21 = begin - P6 - startadr - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fhs1";
      objtrend3(obname, P1 + S3, P1 + S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fls1";
      objtrend3(obname, P1 - S3, P1 - S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fhs2";
      objtrend3(obname, P1 + S4, P1 + S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fls2";
      objtrend3(obname, P1 - S4, P1 - S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh6a";
      objtrend3(obname, begin + P6 + startadr + startadr, H16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6a";
      objtrend3(obname, begin + P6 + startadr + startadr, L16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh6b";
      objtrend3(obname, begin + P6 + startadr, H17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6b";
      objtrend3(obname, begin + P6 + startadr, L17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh6c";
      objtrend3(obname, begin + P6, H18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6c";
      objtrend3(obname, begin + P6, L18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh6d";
      objtrend3(obname, begin - P6, H19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6d";
      objtrend3(obname, begin - P6, L19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh6e";
      objtrend3(obname, begin - P6 - startadr, H20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6e";
      objtrend3(obname, begin - P6 - startadr, L20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh6f";
      objtrend3(obname, begin - P6 - startadr - startadr, H21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl6f";
      objtrend3(obname, begin - P6 - startadr - startadr, L21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 6) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      P6 = startadr / 2;
      H16 = begin + P6 + startadr + startadr + startadr;
      L16 = begin + P6 + startadr + startadr - startadr;
      H17 = begin + P6 + startadr + startadr;
      L17 = begin + P6 + startadr - startadr;
      H18 = begin + P6 + startadr;
      L18 = begin + P6 - startadr;
      H19 = begin - P6 + startadr;
      L19 = begin - P6 - startadr;
      H20 = begin - P6 - startadr + startadr;
      L20 = begin - P6 - startadr - startadr;
      H21 = begin - P6 - startadr - startadr + startadr;
      L21 = begin - P6 - startadr - startadr - startadr;
      P7 = startadr;
      H22 = begin + P7 + startadr + startadr + startadr;
      L22 = begin + P7 + startadr + startadr - startadr;
      H23 = begin + P7 + startadr + startadr;
      L23 = begin + P7 + startadr - startadr;
      H24 = begin + P7 + startadr;
      L24 = begin + P7 - startadr;
      H25 = begin + startadr;
      L25 = begin - startadr;
      H26 = begin - P7 + startadr;
      L26 = begin - P7 - startadr;
      H27 = begin - P7 - startadr + startadr;
      L27 = begin - P7 - startadr - startadr;
      H28 = begin - P7 - startadr - startadr + startadr;
      L28 = begin - P7 - startadr - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh6a";
      objtrend3(obname, begin + P6 + startadr + startadr, H16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6a";
      objtrend3(obname, begin + P6 + startadr + startadr, L16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6b";
      objtrend3(obname, begin + P6 + startadr, H17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6b";
      objtrend3(obname, begin + P6 + startadr, L17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6c";
      objtrend3(obname, begin + P6, H18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6c";
      objtrend3(obname, begin + P6, L18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6d";
      objtrend3(obname, begin - P6, H19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6d";
      objtrend3(obname, begin - P6, L19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6e";
      objtrend3(obname, begin - P6 - startadr, H20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6e";
      objtrend3(obname, begin - P6 - startadr, L20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6f";
      objtrend3(obname, begin - P6 - startadr - startadr, H21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6f";
      objtrend3(obname, begin - P6 - startadr - startadr, L21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh7a";
      objtrend3(obname, begin + P7 + startadr + startadr, H22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7a";
      objtrend3(obname, begin + P7 + startadr + startadr, L22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7b";
      objtrend3(obname, begin + P7 + startadr, H23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7b";
      objtrend3(obname, begin + P7 + startadr, L23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7c";
      objtrend3(obname, begin + P7, H24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7c";
      objtrend3(obname, begin + P7, L24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7d";
      objtrend3(obname, begin, H25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7d";
      objtrend3(obname, begin, L25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7e";
      objtrend3(obname, begin - P7, H26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7e";
      objtrend3(obname, begin - P7, L26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7f";
      objtrend3(obname, begin - P7 - startadr, H27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7f";
      objtrend3(obname, begin - P7 - startadr, L27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh7g";
      objtrend3(obname, begin - P7 - startadr - startadr, H28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl7g";
      objtrend3(obname, begin - P7 - startadr - startadr, L28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 7) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      P6 = startadr / 2;
      H16 = begin + P6 + startadr + startadr + startadr;
      L16 = begin + P6 + startadr + startadr - startadr;
      H17 = begin + P6 + startadr + startadr;
      L17 = begin + P6 + startadr - startadr;
      H18 = begin + P6 + startadr;
      L18 = begin + P6 - startadr;
      H19 = begin - P6 + startadr;
      L19 = begin - P6 - startadr;
      H20 = begin - P6 - startadr + startadr;
      L20 = begin - P6 - startadr - startadr;
      H21 = begin - P6 - startadr - startadr + startadr;
      L21 = begin - P6 - startadr - startadr - startadr;
      P7 = startadr;
      H22 = begin + P7 + startadr + startadr + startadr;
      L22 = begin + P7 + startadr + startadr - startadr;
      H23 = begin + P7 + startadr + startadr;
      L23 = begin + P7 + startadr - startadr;
      H24 = begin + P7 + startadr;
      L24 = begin + P7 - startadr;
      H25 = begin + startadr;
      L25 = begin - startadr;
      H26 = begin - P7 + startadr;
      L26 = begin - P7 - startadr;
      H27 = begin - P7 - startadr + startadr;
      L27 = begin - P7 - startadr - startadr;
      H28 = begin - P7 - startadr - startadr + startadr;
      L28 = begin - P7 - startadr - startadr - startadr;
      P8 = startadr / 2;
      H29 = begin + P8 + startadr + startadr + startadr + startadr;
      L29 = begin + P8 + startadr + startadr + startadr - startadr;
      H30 = begin + P8 + startadr + startadr + startadr;
      L30 = begin + P8 + startadr + startadr - startadr;
      H31 = begin + P8 + startadr + startadr;
      L31 = begin + P8 + startadr - startadr;
      H32 = begin + P8 + startadr;
      L32 = begin + P8 - startadr;
      H33 = begin - P8 + startadr;
      L33 = begin - P8 - startadr;
      H34 = begin - P8 - startadr + startadr;
      L34 = begin - P8 - startadr - startadr;
      H35 = begin - P8 - startadr - startadr + startadr;
      L35 = begin - P8 - startadr - startadr - startadr;
      H36 = begin - P8 - startadr - startadr - startadr + startadr;
      L36 = begin - P8 - startadr - startadr - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh6a";
      objtrend3(obname, begin + P6 + startadr + startadr, H16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6a";
      objtrend3(obname, begin + P6 + startadr + startadr, L16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6b";
      objtrend3(obname, begin + P6 + startadr, H17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6b";
      objtrend3(obname, begin + P6 + startadr, L17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6c";
      objtrend3(obname, begin + P6, H18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6c";
      objtrend3(obname, begin + P6, L18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6d";
      objtrend3(obname, begin - P6, H19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6d";
      objtrend3(obname, begin - P6, L19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6e";
      objtrend3(obname, begin - P6 - startadr, H20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6e";
      objtrend3(obname, begin - P6 - startadr, L20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6f";
      objtrend3(obname, begin - P6 - startadr - startadr, H21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6f";
      objtrend3(obname, begin - P6 - startadr - startadr, L21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh7a";
      objtrend3(obname, begin + P7 + startadr + startadr, H22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7a";
      objtrend3(obname, begin + P7 + startadr + startadr, L22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7b";
      objtrend3(obname, begin + P7 + startadr, H23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7b";
      objtrend3(obname, begin + P7 + startadr, L23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7c";
      objtrend3(obname, begin + P7, H24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7c";
      objtrend3(obname, begin + P7, L24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7d";
      objtrend3(obname, begin, H25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7d";
      objtrend3(obname, begin, L25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7e";
      objtrend3(obname, begin - P7, H26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7e";
      objtrend3(obname, begin - P7, L26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7f";
      objtrend3(obname, begin - P7 - startadr, H27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7f";
      objtrend3(obname, begin - P7 - startadr, L27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7g";
      objtrend3(obname, begin - P7 - startadr - startadr, H28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7g";
      objtrend3(obname, begin - P7 - startadr - startadr, L28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, H29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, L29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8b";
      objtrend3(obname, begin + P8 + startadr + startadr, H30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8b";
      objtrend3(obname, begin + P8 + startadr + startadr, L30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8c";
      objtrend3(obname, begin + P8 + startadr, H31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8c";
      objtrend3(obname, begin + P8 + startadr, L31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8d";
      objtrend3(obname, begin + P8, H32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8d";
      objtrend3(obname, begin + P8, L32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8e";
      objtrend3(obname, begin - P8, H33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8e";
      objtrend3(obname, begin - P8, L33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8f";
      objtrend3(obname, begin - P8 - startadr, H34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8f";
      objtrend3(obname, begin - P8 - startadr, L34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8g";
      objtrend3(obname, begin - P8 - startadr - startadr, H35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8g";
      objtrend3(obname, begin - P8 - startadr - startadr, L35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, H36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, L36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 8) {
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      P6 = startadr / 2;
      H16 = begin + P6 + startadr + startadr + startadr;
      L16 = begin + P6 + startadr + startadr - startadr;
      H17 = begin + P6 + startadr + startadr;
      L17 = begin + P6 + startadr - startadr;
      H18 = begin + P6 + startadr;
      L18 = begin + P6 - startadr;
      H19 = begin - P6 + startadr;
      L19 = begin - P6 - startadr;
      H20 = begin - P6 - startadr + startadr;
      L20 = begin - P6 - startadr - startadr;
      H21 = begin - P6 - startadr - startadr + startadr;
      L21 = begin - P6 - startadr - startadr - startadr;
      P7 = startadr;
      H22 = begin + P7 + startadr + startadr + startadr;
      L22 = begin + P7 + startadr + startadr - startadr;
      H23 = begin + P7 + startadr + startadr;
      L23 = begin + P7 + startadr - startadr;
      H24 = begin + P7 + startadr;
      L24 = begin + P7 - startadr;
      H25 = begin + startadr;
      L25 = begin - startadr;
      H26 = begin - P7 + startadr;
      L26 = begin - P7 - startadr;
      H27 = begin - P7 - startadr + startadr;
      L27 = begin - P7 - startadr - startadr;
      H28 = begin - P7 - startadr - startadr + startadr;
      L28 = begin - P7 - startadr - startadr - startadr;
      P8 = startadr / 2;
      H29 = begin + P8 + startadr + startadr + startadr + startadr;
      L29 = begin + P8 + startadr + startadr + startadr - startadr;
      H30 = begin + P8 + startadr + startadr + startadr;
      L30 = begin + P8 + startadr + startadr - startadr;
      H31 = begin + P8 + startadr + startadr;
      L31 = begin + P8 + startadr - startadr;
      H32 = begin + P8 + startadr;
      L32 = begin + P8 - startadr;
      H33 = begin - P8 + startadr;
      L33 = begin - P8 - startadr;
      H34 = begin - P8 - startadr + startadr;
      L34 = begin - P8 - startadr - startadr;
      H35 = begin - P8 - startadr - startadr + startadr;
      L35 = begin - P8 - startadr - startadr - startadr;
      H36 = begin - P8 - startadr - startadr - startadr + startadr;
      L36 = begin - P8 - startadr - startadr - startadr - startadr;
      P9 = startadr;
      H37 = begin + P9 + startadr + startadr + startadr + startadr;
      L37 = begin + P9 + startadr + startadr + startadr - startadr;
      H38 = begin + P9 + startadr + startadr + startadr;
      L38 = begin + P9 + startadr + startadr - startadr;
      H39 = begin + P9 + startadr + startadr;
      L39 = begin + P9 + startadr - startadr;
      H40 = begin + P9 + startadr;
      L40 = begin + P9 - startadr;
      H41 = begin + startadr;
      L41 = begin - startadr;
      H42 = begin - P9 + startadr;
      L42 = begin - P9 - startadr;
      H43 = begin - P9 - startadr + startadr;
      L43 = begin - P9 - startadr - startadr;
      H44 = begin - P9 - startadr - startadr + startadr;
      L44 = begin - P9 - startadr - startadr - startadr;
      H45 = begin - P9 - startadr - startadr - startadr + startadr;
      L45 = begin - P9 - startadr - startadr - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh6a";
      objtrend3(obname, begin + P6 + startadr + startadr, H16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6a";
      objtrend3(obname, begin + P6 + startadr + startadr, L16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6b";
      objtrend3(obname, begin + P6 + startadr, H17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6b";
      objtrend3(obname, begin + P6 + startadr, L17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6c";
      objtrend3(obname, begin + P6, H18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6c";
      objtrend3(obname, begin + P6, L18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6d";
      objtrend3(obname, begin - P6, H19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6d";
      objtrend3(obname, begin - P6, L19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6e";
      objtrend3(obname, begin - P6 - startadr, H20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6e";
      objtrend3(obname, begin - P6 - startadr, L20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6f";
      objtrend3(obname, begin - P6 - startadr - startadr, H21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6f";
      objtrend3(obname, begin - P6 - startadr - startadr, L21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh7a";
      objtrend3(obname, begin + P7 + startadr + startadr, H22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7a";
      objtrend3(obname, begin + P7 + startadr + startadr, L22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7b";
      objtrend3(obname, begin + P7 + startadr, H23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7b";
      objtrend3(obname, begin + P7 + startadr, L23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7c";
      objtrend3(obname, begin + P7, H24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7c";
      objtrend3(obname, begin + P7, L24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7d";
      objtrend3(obname, begin, H25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7d";
      objtrend3(obname, begin, L25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7e";
      objtrend3(obname, begin - P7, H26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7e";
      objtrend3(obname, begin - P7, L26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7f";
      objtrend3(obname, begin - P7 - startadr, H27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7f";
      objtrend3(obname, begin - P7 - startadr, L27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7g";
      objtrend3(obname, begin - P7 - startadr - startadr, H28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7g";
      objtrend3(obname, begin - P7 - startadr - startadr, L28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, H29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, L29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8b";
      objtrend3(obname, begin + P8 + startadr + startadr, H30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8b";
      objtrend3(obname, begin + P8 + startadr + startadr, L30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8c";
      objtrend3(obname, begin + P8 + startadr, H31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8c";
      objtrend3(obname, begin + P8 + startadr, L31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8d";
      objtrend3(obname, begin + P8, H32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8d";
      objtrend3(obname, begin + P8, L32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8e";
      objtrend3(obname, begin - P8, H33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8e";
      objtrend3(obname, begin - P8, L33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8f";
      objtrend3(obname, begin - P8 - startadr, H34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8f";
      objtrend3(obname, begin - P8 - startadr, L34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8g";
      objtrend3(obname, begin - P8 - startadr - startadr, H35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8g";
      objtrend3(obname, begin - P8 - startadr - startadr, L35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, H36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, L36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh9a";
      objtrend3(obname, begin + P9 + startadr + startadr + startadr, H37, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9a";
      objtrend3(obname, begin + P9 + startadr + startadr + startadr, L37, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9b";
      objtrend3(obname, begin + P9 + startadr + startadr, H38, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9b";
      objtrend3(obname, begin + P9 + startadr + startadr, L38, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9c";
      objtrend3(obname, begin + P9 + startadr, H39, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9c";
      objtrend3(obname, begin + P9 + startadr, L39, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9d";
      objtrend3(obname, begin + P9, H40, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9d";
      objtrend3(obname, begin + P9, L40, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9e";
      objtrend3(obname, begin, H41, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9e";
      objtrend3(obname, begin, L41, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9f";
      objtrend3(obname, begin - P9, H42, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9f";
      objtrend3(obname, begin - P9, L42, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9g";
      objtrend3(obname, begin - P9 - startadr, H43, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9g";
      objtrend3(obname, begin - P9 - startadr, L43, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9h";
      objtrend3(obname, begin - P9 - startadr - startadr, H44, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9h";
      objtrend3(obname, begin - P9 - startadr - startadr, L44, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh9i";
      objtrend3(obname, begin - P9 - startadr - startadr - startadr, H45, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl9i";
      objtrend3(obname, begin - P9 - startadr - startadr - startadr, L45, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   if (startt == 9) {
      double S3 = MathSqrt(startt * 0.5 * 0.5) * startadr;
      double S4 = 2 * MathSqrt(startt * 0.5 * 0.5) * startadr;
      P1 = begin;
      H1 = begin + startadr;
      L1 = begin - startadr;
      P2 = startadr / 2;
      H2 = begin + P2 + startadr;
      L2 = begin + P2 - startadr;
      H3 = begin - P2 + startadr;
      L3 = begin - P2 - startadr;
      P3 = startadr;
      H4 = begin + P3 + startadr;
      L4 = begin + P3 - startadr;
      H5 = begin + startadr;
      L5 = begin - startadr;
      H6 = begin - P3 + startadr;
      L6 = begin - P3 - startadr;
      P4 = startadr / 2;
      H7 = begin + P4 + startadr + startadr;
      L7 = begin + P4 + startadr - startadr;
      H8 = begin + P4 + startadr;
      L8 = begin + P4 - startadr;
      H9 = begin - P4 + startadr;
      L9 = begin - P4 - startadr;
      H10 = begin - P4 - startadr + startadr;
      L10 = begin - P4 - startadr - startadr;
      P5 = startadr;
      H11 = begin + P5 + startadr + startadr;
      L11 = begin + P5 + startadr - startadr;
      H12 = begin + P5 + startadr;
      L12 = begin + P5 - startadr;
      H13 = begin + startadr;
      L13 = begin - startadr;
      H14 = begin - P5 + startadr;
      L14 = begin - P5 - startadr;
      H15 = begin - P5 - startadr + startadr;
      L15 = begin - P5 - startadr - startadr;
      P6 = startadr / 2;
      H16 = begin + P6 + startadr + startadr + startadr;
      L16 = begin + P6 + startadr + startadr - startadr;
      H17 = begin + P6 + startadr + startadr;
      L17 = begin + P6 + startadr - startadr;
      H18 = begin + P6 + startadr;
      L18 = begin + P6 - startadr;
      H19 = begin - P6 + startadr;
      L19 = begin - P6 - startadr;
      H20 = begin - P6 - startadr + startadr;
      L20 = begin - P6 - startadr - startadr;
      H21 = begin - P6 - startadr - startadr + startadr;
      L21 = begin - P6 - startadr - startadr - startadr;
      P7 = startadr;
      H22 = begin + P7 + startadr + startadr + startadr;
      L22 = begin + P7 + startadr + startadr - startadr;
      H23 = begin + P7 + startadr + startadr;
      L23 = begin + P7 + startadr - startadr;
      H24 = begin + P7 + startadr;
      L24 = begin + P7 - startadr;
      H25 = begin + startadr;
      L25 = begin - startadr;
      H26 = begin - P7 + startadr;
      L26 = begin - P7 - startadr;
      H27 = begin - P7 - startadr + startadr;
      L27 = begin - P7 - startadr - startadr;
      H28 = begin - P7 - startadr - startadr + startadr;
      L28 = begin - P7 - startadr - startadr - startadr;
      P8 = startadr / 2;
      H29 = begin + P8 + startadr + startadr + startadr + startadr;
      L29 = begin + P8 + startadr + startadr + startadr - startadr;
      H30 = begin + P8 + startadr + startadr + startadr;
      L30 = begin + P8 + startadr + startadr - startadr;
      H31 = begin + P8 + startadr + startadr;
      L31 = begin + P8 + startadr - startadr;
      H32 = begin + P8 + startadr;
      L32 = begin + P8 - startadr;
      H33 = begin - P8 + startadr;
      L33 = begin - P8 - startadr;
      H34 = begin - P8 - startadr + startadr;
      L34 = begin - P8 - startadr - startadr;
      H35 = begin - P8 - startadr - startadr + startadr;
      L35 = begin - P8 - startadr - startadr - startadr;
      H36 = begin - P8 - startadr - startadr - startadr + startadr;
      L36 = begin - P8 - startadr - startadr - startadr - startadr;
      P9 = startadr;
      H37 = begin + P9 + startadr + startadr + startadr + startadr;
      L37 = begin + P9 + startadr + startadr + startadr - startadr;
      H38 = begin + P9 + startadr + startadr + startadr;
      L38 = begin + P9 + startadr + startadr - startadr;
      H39 = begin + P9 + startadr + startadr;
      L39 = begin + P9 + startadr - startadr;
      H40 = begin + P9 + startadr;
      L40 = begin + P9 - startadr;
      H41 = begin + startadr;
      L41 = begin - startadr;
      H42 = begin - P9 + startadr;
      L42 = begin - P9 - startadr;
      H43 = begin - P9 - startadr + startadr;
      L43 = begin - P9 - startadr - startadr;
      H44 = begin - P9 - startadr - startadr + startadr;
      L44 = begin - P9 - startadr - startadr - startadr;
      H45 = begin - P9 - startadr - startadr - startadr + startadr;
      L45 = begin - P9 - startadr - startadr - startadr - startadr;
      P10 = startadr / 2;
      H46 = begin + P10 + startadr + startadr + startadr + startadr + startadr;
      L46 = begin + P10 + startadr + startadr + startadr + startadr - startadr;
      H47 = begin + P10 + startadr + startadr + startadr + startadr;
      L47 = begin + P10 + startadr + startadr + startadr - startadr;
      H48 = begin + P10 + startadr + startadr + startadr;
      L48 = begin + P10 + startadr + startadr - startadr;
      H49 = begin + P10 + startadr + startadr;
      L49 = begin + P10 + startadr - startadr;
      H50 = begin + P10 + startadr;
      L50 = begin + P10 - startadr;
      H51 = begin - P10 + startadr;
      L51 = begin - P10 - startadr;
      H52 = begin - P10 - startadr + startadr;
      L52 = begin - P10 - startadr - startadr;
      H53 = begin - P10 - startadr - startadr + startadr;
      L53 = begin - P10 - startadr - startadr - startadr;
      H54 = begin - P10 - startadr - startadr - startadr + startadr;
      L54 = begin - P10 - startadr - startadr - startadr - startadr;
      H55 = begin - P10 - startadr - startadr - startadr - startadr + startadr;
      L55 = begin - P10 - startadr - startadr - startadr - startadr - startadr;
      
      obname = Name + "fh1";
      objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fl1";
      objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
      obname = Name + "fhs1";
      objtrend3(obname, P1 + S3, P1 + S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fls1";
      objtrend3(obname, P1 - S3, P1 - S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fhs2";
      objtrend3(obname, P1 + S4, P1 + S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      obname = Name + "fls2";
      objtrend3(obname, P1 - S4, P1 - S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrWhite, "");
      //obname = Name + "fh2";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl2";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh2a";
      objtrend3(obname, begin + P2, H2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P2, L2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fh2b";
      objtrend3(obname, begin - P2, H3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P2, L3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrYellow, "");
      //obname = Name + "fh31";
      //objtrend3(obname, begin + P3, begin + P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fh32";
      //objtrend3(obname, begin + P2, begin + P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl31";
      //objtrend3(obname, begin - P2, begin - P2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      //obname = Name + "fl32";
      //objtrend3(obname, begin - P3, begin - P3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), 1, 0, clrCyan, "");
      obname = Name + "fh3a";
      objtrend3(obname, begin + P3, H4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P3, L4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3b";
      objtrend3(obname, begin, H5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, L5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh3c";
      objtrend3(obname, begin - P3, H6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P3, L6, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 2), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), 1, 0, clrYellow, "");
      obname = Name + "fh4a";
      objtrend3(obname, begin + P4 + startadr, H7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P4 + startadr, L7, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4b";
      objtrend3(obname, begin + P4, H8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P4, L8, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4c";
      objtrend3(obname, begin - P4, H9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P4, L9, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh4d";
      objtrend3(obname, begin - P4 - startadr, H10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P4 - startadr, L10, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 3), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), 1, 0, clrYellow, "");
      obname = Name + "fh5a";
      objtrend3(obname, begin + P5 + startadr, H11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P5 + startadr, L11, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5b";
      objtrend3(obname, begin + P5, H12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P5, L12, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5c";
      objtrend3(obname, begin, H13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, L13, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5d";
      objtrend3(obname, begin - P5, H14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P5, L14, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fh5e";
      objtrend3(obname, begin - P5 - startadr, H15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P5 - startadr, L15, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 4), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh6a";
      objtrend3(obname, begin + P6 + startadr + startadr, H16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6a";
      objtrend3(obname, begin + P6 + startadr + startadr, L16, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6b";
      objtrend3(obname, begin + P6 + startadr, H17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6b";
      objtrend3(obname, begin + P6 + startadr, L17, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6c";
      objtrend3(obname, begin + P6, H18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6c";
      objtrend3(obname, begin + P6, L18, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6d";
      objtrend3(obname, begin - P6, H19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6d";
      objtrend3(obname, begin - P6, L19, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6e";
      objtrend3(obname, begin - P6 - startadr, H20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6e";
      objtrend3(obname, begin - P6 - startadr, L20, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fh6f";
      objtrend3(obname, begin - P6 - startadr - startadr, H21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      obname = Name + "fl6f";
      objtrend3(obname, begin - P6 - startadr - startadr, L21, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 5), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh7a";
      objtrend3(obname, begin + P7 + startadr + startadr, H22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7a";
      objtrend3(obname, begin + P7 + startadr + startadr, L22, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7b";
      objtrend3(obname, begin + P7 + startadr, H23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7b";
      objtrend3(obname, begin + P7 + startadr, L23, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7c";
      objtrend3(obname, begin + P7, H24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7c";
      objtrend3(obname, begin + P7, L24, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7d";
      objtrend3(obname, begin, H25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7d";
      objtrend3(obname, begin, L25, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7e";
      objtrend3(obname, begin - P7, H26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7e";
      objtrend3(obname, begin - P7, L26, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7f";
      objtrend3(obname, begin - P7 - startadr, H27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7f";
      objtrend3(obname, begin - P7 - startadr, L27, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fh7g";
      objtrend3(obname, begin - P7 - startadr - startadr, H28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      obname = Name + "fl7g";
      objtrend3(obname, begin - P7 - startadr - startadr, L28, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 6), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, H29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8a";
      objtrend3(obname, begin + P8 + startadr + startadr + startadr, L29, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8b";
      objtrend3(obname, begin + P8 + startadr + startadr, H30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8b";
      objtrend3(obname, begin + P8 + startadr + startadr, L30, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8c";
      objtrend3(obname, begin + P8 + startadr, H31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8c";
      objtrend3(obname, begin + P8 + startadr, L31, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8d";
      objtrend3(obname, begin + P8, H32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8d";
      objtrend3(obname, begin + P8, L32, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8e";
      objtrend3(obname, begin - P8, H33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8e";
      objtrend3(obname, begin - P8, L33, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8f";
      objtrend3(obname, begin - P8 - startadr, H34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8f";
      objtrend3(obname, begin - P8 - startadr, L34, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8g";
      objtrend3(obname, begin - P8 - startadr - startadr, H35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8g";
      objtrend3(obname, begin - P8 - startadr - startadr, L35, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fh8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, H36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      obname = Name + "fl8h";
      objtrend3(obname, begin - P8 - startadr - startadr - startadr, L36, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 7), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh9a";
      objtrend3(obname, begin + P9 + startadr + startadr + startadr, H37, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9a";
      objtrend3(obname, begin + P9 + startadr + startadr + startadr, L37, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9b";
      objtrend3(obname, begin + P9 + startadr + startadr, H38, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9b";
      objtrend3(obname, begin + P9 + startadr + startadr, L38, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9c";
      objtrend3(obname, begin + P9 + startadr, H39, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9c";
      objtrend3(obname, begin + P9 + startadr, L39, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9d";
      objtrend3(obname, begin + P9, H40, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9d";
      objtrend3(obname, begin + P9, L40, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9e";
      objtrend3(obname, begin, H41, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9e";
      objtrend3(obname, begin, L41, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9f";
      objtrend3(obname, begin - P9, H42, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9f";
      objtrend3(obname, begin - P9, L42, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9g";
      objtrend3(obname, begin - P9 - startadr, H43, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9g";
      objtrend3(obname, begin - P9 - startadr, L43, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9h";
      objtrend3(obname, begin - P9 - startadr - startadr, H44, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9h";
      objtrend3(obname, begin - P9 - startadr - startadr, L44, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fh9i";
      objtrend3(obname, begin - P9 - startadr - startadr - startadr, H45, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      obname = Name + "fl9i";
      objtrend3(obname, begin - P9 - startadr - startadr - startadr, L45, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 8), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), 1, 0, clrYellow, "");
      
      obname = Name + "fh10a";
      objtrend3(obname, begin + P10 + startadr + startadr + startadr + startadr, H46, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10a";
      objtrend3(obname, begin + P10 + startadr + startadr + startadr + startadr, L46, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10b";
      objtrend3(obname, begin + P10 + startadr + startadr + startadr, H47, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10b";
      objtrend3(obname, begin + P10 + startadr + startadr + startadr, L47, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10c";
      objtrend3(obname, begin + P10 + startadr + startadr, H48, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10c";
      objtrend3(obname, begin + P10 + startadr + startadr, L48, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10d";
      objtrend3(obname, begin + P10 + startadr, H49, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10d";
      objtrend3(obname, begin + P10 + startadr, L49, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10e";
      objtrend3(obname, begin + P10, H50, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10e";
      objtrend3(obname, begin + P10, L50, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10f";
      objtrend3(obname, begin - P10, H51, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10f";
      objtrend3(obname, begin - P10, L51, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10g";
      objtrend3(obname, begin - P10 - startadr, H52, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10g";
      objtrend3(obname, begin - P10 - startadr, L52, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10h";
      objtrend3(obname, begin - P10 - startadr - startadr, H53, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10h";
      objtrend3(obname, begin - P10 - startadr - startadr, L53, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10i";
      objtrend3(obname, begin - P10 - startadr - startadr - startadr, H54, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10i";
      objtrend3(obname, begin - P10 - startadr - startadr - startadr, L54, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fh10j";
      objtrend3(obname, begin - P10 - startadr - startadr - startadr - startadr, H55, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      obname = Name + "fl10j";
      objtrend3(obname, begin - P10 - startadr - startadr - startadr - startadr, L55, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 9), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrYellow, "");
      }
   //Print(begin + " " + H1 + " " + L1 + " " + adr(5, startt + 5) + " " + iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt + 1), false) + " " + iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false));
}

double adr(const int X, const int Y){
   
   double HD[], LD[];
   ArrayResize(HD, Y + 1);
   ArrayResize(LD, Y + 1);
   CopyHigh(_Symbol, PERIOD_D1, 1, Y + 1, HD);
   CopyLow(_Symbol, PERIOD_D1, 1, Y + 1, LD);
   double adra = 0;
   
   for (int x = Y; x > Y - X; x--){
      adra += (HD[x] - LD[x]);
   }
   
   double adri = 0;
   adri = adra / X;
   
   ArrayFree(HD);
   ArrayFree(LD);
   return(adri);
}

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+
//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend3(string name, double pr1, double pr2, int t1, int t2, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2]);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett); // + " Price: " + DoubleToStr(pr1, _Digits));
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+