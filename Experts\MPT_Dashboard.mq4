//+------------------------------------------------------------------+
//|                                              MPT_Dashboard.mq4 |
//|                                        Copyright © 2025, sakisf. |
//|                               http://www.forexfactory.com/sakisf |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, sakisf."
#property link      "http://www.forexfactory/sakisf"
#property version   "1.00"
#property strict
#property description "MPT Dashboard - Modern Portfolio Theory Dashboard"

//--- input parameters
input string Pair_Prefix = "";      // Prefix (if added by broker)
input string Pair_Suffix = "";      // Suffix (if added by broker)
input string Font_Type = "Calibri Bold";  // Font type
input int Font_Size = 13;           // Font size (buttons)
input color Font_Color = White;     // Font color
input color Border_Color = clrGreen; // Border color
input double DefaultSL = 50;      // Default SL value
input double DefaultTP = 100;     // Default TP value

#define Name WindowExpertName()

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
   ChartSetInteger(0, CHART_FOREGROUND, 0);
   ChartSetInteger(0, CHART_SCALEFIX, 0, true);
   ChartSetDouble(0, CHART_FIXED_MIN, 0.1);
   ChartSetDouble(0, CHART_FIXED_MAX, 0.2);
   ChartSetDouble(0, CHART_PRICE_MIN, 0.1);
   ChartSetDouble(0, CHART_PRICE_MAX, 0.2);
   
   // Initialize global variables if they don't exist
   if (GlobalVariableCheck("MPT_TOP9_SL") == false)
      GlobalVariableSet("MPT_TOP9_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_TOP9_TP") == false)
      GlobalVariableSet("MPT_TOP9_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_CSSD_SL") == false)
      GlobalVariableSet("MPT_CSSD_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_CSSD_TP") == false)
      GlobalVariableSet("MPT_CSSD_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_STARS_SL") == false)
      GlobalVariableSet("MPT_STARS_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_STARS_TP") == false)
      GlobalVariableSet("MPT_STARS_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_RISK_SL") == false)
      GlobalVariableSet("MPT_RISK_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_RISK_TP") == false)
      GlobalVariableSet("MPT_RISK_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_CVAR_SL") == false)
      GlobalVariableSet("MPT_CVAR_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_CVAR_TP") == false)
      GlobalVariableSet("MPT_CVAR_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_BASKET1_SL") == false)
      GlobalVariableSet("MPT_BASKET1_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_BASKET1_TP") == false)
      GlobalVariableSet("MPT_BASKET1_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_BASKET2_SL") == false)
      GlobalVariableSet("MPT_BASKET2_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_BASKET2_TP") == false)
      GlobalVariableSet("MPT_BASKET2_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_BASKET3_SL") == false)
      GlobalVariableSet("MPT_BASKET3_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_BASKET3_TP") == false)
      GlobalVariableSet("MPT_BASKET3_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_DOT1_SL") == false)
      GlobalVariableSet("MPT_DOT1_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_DOT1_TP") == false)
      GlobalVariableSet("MPT_DOT1_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_DOT2_SL") == false)
      GlobalVariableSet("MPT_DOT2_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_DOT2_TP") == false)
      GlobalVariableSet("MPT_DOT2_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_DOT3_SL") == false)
      GlobalVariableSet("MPT_DOT3_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_DOT3_TP") == false)
      GlobalVariableSet("MPT_DOT3_TP", DefaultTP);
   
   if (GlobalVariableCheck("MPT_TOTAL_SL") == false)
      GlobalVariableSet("MPT_TOTAL_SL", DefaultSL);
   if (GlobalVariableCheck("MPT_TOTAL_TP") == false)
      GlobalVariableSet("MPT_TOTAL_TP", DefaultTP);
   
   // Create all UI elements
   CreateDashboard();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Delete all objects created by this EA
   ObjectsDeleteAll(0, Name);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update profit/loss values for each display box
   UpdateProfitDisplay();
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Handle edit box inputs and button clicks
   if(id == CHARTEVENT_OBJECT_ENDEDIT)
   {
      // Handle edit box changes
      HandleEditBoxInput(sparam);
   }
   else if(id == CHARTEVENT_OBJECT_CLICK)
   {
      // Handle button clicks
      HandleButtonClick(sparam);
   }
}

//+------------------------------------------------------------------+
//| Create all dashboard UI elements                                 |
//+------------------------------------------------------------------+
void CreateDashboard()
{
   int startX = 20;
   int startY = 20;
   int editBoxWidth = 800;
   int editBoxHeight = 25;
   int spacing = 20;  // Vertical spacing between rows
   int labelWidth = 150;
   int plBoxWidth = 150;
   int slTpWidth = 60;
   int slTpHeight = 20;
   int buttonWidth = 100;
   int buttonHeight = 25;
   int rowHeight = editBoxHeight;
   int panelY;  // Will track Y position as we add elements
   
   // Create the edit boxes with labels and P/L displays - Moving Rolling Stars above Rolling CSSD
   panelY = CreateEditBoxGroup("MPT_TOP9", "MPT - Top 9", startX, startY, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_STARS", "MPT - Rolling Stars", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_CSSD", "MPT - Rolling CSSD", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_RISK", "MPT - Risk", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_CVAR", "MPT - CVaR", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_BASKET1", "MPT - Basket1", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_BASKET2", "MPT - Basket2", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_BASKET3", "MPT - Basket3", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_DOT1", "MPT - Dot1", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_DOT2", "MPT - Dot2", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   panelY = CreateEditBoxGroup("MPT_DOT3", "MPT - Dot3", startX, panelY + spacing, editBoxWidth, rowHeight, plBoxWidth);
   
   // Remove the main T1, T2, and C buttons that have X marks over them
   
   // Create Total P/L display with properly positioned elements
   int totalY = panelY + spacing + 30;
   
   // Create green box for Total P/L
   RecMake(Name + "Total_PL_Box", startX + labelWidth + 130, totalY, plBoxWidth, editBoxHeight, clrWhite, clrDarkGreen);
   
   // Label for Total P/L - positioned to align with the box
   LabelMake(Name + "Total_PL_Label", 0, startX + labelWidth + 50, totalY + 3, "Total P/L:", Font_Size, Font_Color);
   
   // Value display inside Total P/L box - improved centering
   LabelMake(Name + "Total_PL_Value", 0, startX + labelWidth + 130 + plBoxWidth/2, totalY, "0.00", Font_Size, Font_Color);
   
   // C button for Total P/L
   ButtonCreate(0, Name + "Total_C_Button", 0, startX + labelWidth + 130 + plBoxWidth + 10, totalY, editBoxHeight, editBoxHeight, 0, "C", Font_Type, Font_Size, Font_Color, clrDarkRed, Border_Color);
   
   // SL/TP for Total P/L - now below Total P/L
   int slTpY = totalY + editBoxHeight + 10;
   LabelMake(Name + "Total_SL_Label", 0, startX + labelWidth + 130, slTpY, "SL", Font_Size-3, Font_Color);
   EditBox(Name + "Total_SL_Edit", 0, startX + labelWidth + 130, slTpY + 20, slTpWidth, slTpHeight, 10, Font_Color, DoubleToString(GlobalVariableGet("MPT_TOTAL_SL"), 0));
   
   LabelMake(Name + "Total_TP_Label", 0, startX + labelWidth + 130 + slTpWidth + 20, slTpY, "TP", Font_Size-3, Font_Color);
   EditBox(Name + "Total_TP_Edit", 0, startX + labelWidth + 130 + slTpWidth + 20, slTpY + 20, slTpWidth, slTpHeight, 10, Font_Color, DoubleToString(GlobalVariableGet("MPT_TOTAL_TP"), 0));
   
   // Create Pos Open display
   int posX = startX + labelWidth + 130 + plBoxWidth + editBoxHeight + 100; // Position to the right of Total P/L
   
   // Create green box for Pos Open
   RecMake(Name + "Pos_Open_Box", posX, totalY, plBoxWidth, editBoxHeight, clrWhite, clrDarkGreen);
   
   // Label for Pos Open - positioned to align with the box
   LabelMake(Name + "Pos_Open_Label", 0, posX - 80, totalY, "Pos Open:", Font_Size, Font_Color);
   
   // Value display inside Pos Open box - improved centering
   LabelMake(Name + "Pos_Open_Value", 0, posX + plBoxWidth/2, totalY + 3, "0", Font_Size, Font_Color);
   
   // C button for Pos Open
   ButtonCreate(0, Name + "Pos_C_Button", 0, posX + plBoxWidth + 10, totalY, editBoxHeight, editBoxHeight, 0, "C", Font_Type, Font_Size, Font_Color, clrDarkRed, Border_Color);
   
   // Remove the SL field below Pos Open that has an X over it
}

//+------------------------------------------------------------------+
//| Create edit box group with label, P/L display, and SL/TP boxes   |
//+------------------------------------------------------------------+
int CreateEditBoxGroup(string baseName, string labelText, int x, int y, int width, int height, int plWidth)
{
   int labelY = y + height/2;
   int labelWidth = 150;
   int buttonWidth = 30;
   int buttonHeight = 25;
   int miniButtonSize = 15;
   int spacing = 10;
   
   // Create label - aligned with edit box vertically
   LabelMake(Name + baseName + "_Label", 0, x, labelY, labelText, Font_Size, Font_Color);
   
   // Create main edit box
   EditBox(Name + baseName + "_Edit", 0, x + labelWidth, y, width, height, Font_Size, Font_Color, "");
   
   // Create P/L display box
   RecMake(Name + baseName + "_PL_Box", x + labelWidth + width + 20, y, plWidth/2, height, clrWhite, clrDarkGreen);
   
   // Improved centering of P/L value in the green box
   LabelMake(Name + baseName + "_PL_Value", 0, x + labelWidth + width + 25, y + 3, "0.00", Font_Size - 2, Font_Color);
   
   // Create SL/TP edit boxes - aligned to the right
   int slTpX = x + labelWidth + width + plWidth - 20;
   
   // SL label and edit box
   LabelMake(Name + baseName + "_SL_Label", 0, slTpX, y - 10, "SL", 10, Font_Color);
   EditBox(Name + baseName + "_SL_Edit", 0, slTpX, y + 5, 60, 20, 10, Font_Color, DoubleToString(GlobalVariableGet(baseName + "_SL"), 0));
   
   // TP label and edit box
   LabelMake(Name + baseName + "_TP_Label", 0, slTpX + 80, y - 10, "TP", 10, Font_Color);
   EditBox(Name + baseName + "_TP_Edit", 0, slTpX + 80, y + 5, 60, 20, 10, Font_Color, DoubleToString(GlobalVariableGet(baseName + "_TP"), 0));
   
   // Add T1, T2 and C buttons below each edit box
   int buttonsY = y + height + 5;
   int t1X = x + labelWidth;
   int t2X = t1X + buttonWidth + spacing;
   int cX = t2X + buttonWidth + spacing * 2;  // Extra spacing before C button
   
   // Small T1 and T2 buttons
   ButtonCreate(0, Name + baseName + "_T1_Button", 0, t1X, buttonsY, buttonWidth, miniButtonSize, 0, "T1", Font_Type, 8, Font_Color, clrDarkBlue, Border_Color);
   ButtonCreate(0, Name + baseName + "_T2_Button", 0, t2X, buttonsY, buttonWidth, miniButtonSize, 0, "T2", Font_Type, 8, Font_Color, clrDarkBlue, Border_Color);
   ButtonCreate(0, Name + baseName + "_C_Button", 0, cX, buttonsY, miniButtonSize, miniButtonSize, 0, "C", Font_Type, 8, Font_Color, clrDarkRed, Border_Color);
   
   // Return the bottom position for the next element
   return y + height + 25; // Added more space to account for the buttons below each edit box
}

//+------------------------------------------------------------------+
//| Handle edit box input                                           |
//+------------------------------------------------------------------+
void HandleEditBoxInput(string objectName)
{
   // Check if the object exists
   if(!ObjectGetInteger(0, objectName, OBJPROP_TYPE) == OBJ_EDIT) return;
   
   string text = ObjectGetString(0, objectName, OBJPROP_TEXT);
   
   // Handle Total SL/TP edit boxes
   if(objectName == Name + "Total_SL_Edit")
   {
      double value = StringToDouble(text);
      if(value > 0) GlobalVariableSet("MPT_TOTAL_SL", value);
   }
   else if(objectName == Name + "Total_TP_Edit")
   {
      double value = StringToDouble(text);
      if(value > 0) GlobalVariableSet("MPT_TOTAL_TP", value);
   }
   // Handle Pos Open SL edit box
   else if(objectName == Name + "Pos_SL_Edit")
   {
      double value = StringToDouble(text);
      if(value > 0) GlobalVariableSet("MPT_POS_SL", value);
   }
   // Handle regular SL/TP edit boxes
   else if(StringFind(objectName, "_SL_Edit") >= 0)
   {
      string baseName = StringSubstr(objectName, StringLen(Name), StringFind(objectName, "_SL_Edit") - StringLen(Name));
      double value = StringToDouble(text);
      if(value > 0) GlobalVariableSet(baseName + "_SL", value);
   }
   else if(StringFind(objectName, "_TP_Edit") >= 0)
   {
      string baseName = StringSubstr(objectName, StringLen(Name), StringFind(objectName, "_TP_Edit") - StringLen(Name));
      double value = StringToDouble(text);
      if(value > 0) GlobalVariableSet(baseName + "_TP", value);
   }
}

//+------------------------------------------------------------------+
//| Handle button click                                             |
//+------------------------------------------------------------------+
void HandleButtonClick(string objectName)
{
   // Check if the object exists and is a button
   if(!ObjectGetInteger(0, objectName, OBJPROP_TYPE) == OBJ_BUTTON) return;
   
   // T1 button
   if(objectName == Name + "T1_Button")
   {
      Print("T1 button clicked");
      // Implement T1 button functionality
   }
   
   // T2 button
   else if(objectName == Name + "T2_Button")
   {
      Print("T2 button clicked");
      // Implement T2 button functionality
   }
   
   // C button
   else if(objectName == Name + "C_Button")
   {
      Print("C button clicked");
      // Implement C button functionality
   }
   
   // Total P/L C button
   else if(objectName == Name + "Total_C_Button")
   {
      Print("Total P/L C button clicked");
      // Implement Total P/L C button functionality
   }
   
   // Pos Open C button
   else if(objectName == Name + "Pos_C_Button")
   {
      Print("Pos Open C button clicked");
      // Implement Pos Open C button functionality
   }
}

//+------------------------------------------------------------------+
//| Update profit/loss displays                                     |
//+------------------------------------------------------------------+
void UpdateProfitDisplay()
{
   // Just placeholders for now - actual functionality would need to track real P/L
   double totalPL = 0;
   int totalPositions = 0;
   
   string basePanels[] = {"MPT_TOP9", "MPT_CSSD", "MPT_STARS", "MPT_RISK", "MPT_CVAR", "MPT_BASKET1", 
                         "MPT_BASKET2", "MPT_BASKET3", "MPT_DOT1", "MPT_DOT2", "MPT_DOT3"};
   
   for(int i=0; i<ArraySize(basePanels); i++)
   {
      // Generate a sample P/L value (would be replaced with actual calculation)
      double plValue = MathRand()/32767.0 * 1000 - 500;
      totalPL += plValue;
      
      if(plValue != 0) totalPositions++;
      
      // Update the P/L display
      ObjectSetString(0, Name + basePanels[i] + "_PL_Value", OBJPROP_TEXT, DoubleToString(plValue, 2));
      
      // Set color based on profit or loss
      color textColor = (plValue >= 0) ? clrLime : clrRed;
      ObjectSetInteger(0, Name + basePanels[i] + "_PL_Value", OBJPROP_COLOR, textColor);
   }
   
   // Update total P/L
   ObjectSetString(0, Name + "Total_PL_Value", OBJPROP_TEXT, DoubleToString(totalPL, 2));
   color totalColor = (totalPL >= 0) ? clrLime : clrRed;
   ObjectSetInteger(0, Name + "Total_PL_Value", OBJPROP_COLOR, totalColor);
   
   // Update positions open
   ObjectSetString(0, Name + "Pos_Open_Value", OBJPROP_TEXT, IntegerToString(totalPositions));
}

//+------------------------------------------------------------------+
//| Create a rectangular label                                       |
//+------------------------------------------------------------------+
bool RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
   if(ObjectFind(0, name) >= 0) ObjectDelete(0, name);
   
   if(!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      Print(__FUNCTION__, ": failed to create rectangle label! Error code = ", GetLastError());
      return(false);
   }
   
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
   ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, BCol);
   ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);
   
   return(true);
}

//+------------------------------------------------------------------+
//| Create a text label                                              |
//+------------------------------------------------------------------+
bool LabelMake(const string name, const int Corner, const int x, const int y, const string label, const int FSize, const color FCol)
{
   if(ObjectFind(0, name) >= 0) ObjectDelete(0, name);
   
   if(!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
   {
      Print(__FUNCTION__, ": failed to create text label! Error code = ", GetLastError());
      return(false);
   }
   
   ObjectSetInteger(0, name, OBJPROP_CORNER, Corner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, label);
   ObjectSetString(0, name, OBJPROP_FONT, Font_Type);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
   ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);
   
   return(true);
}

//+------------------------------------------------------------------+
//| Create an edit box                                               |
//+------------------------------------------------------------------+
bool EditBox(const string name, const int Corner, const int x, const int y, const int xdist, const int ydist, const int FSize, const color FCol, string text)
{
   if(ObjectFind(0, name) >= 0) ObjectDelete(0, name);
   
   if(!ObjectCreate(0, name, OBJ_EDIT, 0, 0, 0))
   {
      Print(__FUNCTION__, ": failed to create edit box! Error code = ", GetLastError());
      return(false);
   }
   
   ObjectSetInteger(0, name, OBJPROP_CORNER, Corner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, xdist);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, ydist);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, Font_Type);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
   ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, Border_Color);
   ObjectSetInteger(0, name, OBJPROP_ALIGN, ALIGN_CENTER);
   ObjectSetInteger(0, name, OBJPROP_READONLY, false);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);
   
   return(true);
}

//+------------------------------------------------------------------+
//| Create a button                                                  |
//+------------------------------------------------------------------+
bool ButtonCreate(const long chart_ID = 0, const string name = "Button", const int sub_window = 0, const int x = 0, const int y = 0, 
                 const int width = 50, const int height = 18, int Corner = 0, const string text = "Button", const string font = "Arial",
                 const int font_size = 10, const color clr = clrBlack, const color back_clr = clrLightGray, const color border_clr = clrNONE,
                 const bool state = false, const bool back = false, const bool selection = false, const bool hidden = true, const long z_order = 0)
{
   if(ObjectFind(chart_ID, name) >= 0) ObjectDelete(chart_ID, name);
   
   if(!ObjectCreate(chart_ID, name, OBJ_BUTTON, sub_window, 0, 0))
   {
      Print(__FUNCTION__, ": failed to create button! Error code = ", GetLastError());
      return(false);
   }
   
   ObjectSetInteger(chart_ID, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(chart_ID, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(chart_ID, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(chart_ID, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(chart_ID, name, OBJPROP_CORNER, Corner);
   ObjectSetInteger(chart_ID, name, OBJPROP_FONTSIZE, font_size);
   ObjectSetInteger(chart_ID, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(chart_ID, name, OBJPROP_BGCOLOR, back_clr);
   ObjectSetInteger(chart_ID, name, OBJPROP_BORDER_COLOR, border_clr);
   ObjectSetInteger(chart_ID, name, OBJPROP_BACK, back);
   ObjectSetInteger(chart_ID, name, OBJPROP_STATE, state);
   ObjectSetInteger(chart_ID, name, OBJPROP_SELECTABLE, selection);
   ObjectSetInteger(chart_ID, name, OBJPROP_SELECTED, selection);
   ObjectSetInteger(chart_ID, name, OBJPROP_HIDDEN, hidden);
   ObjectSetInteger(chart_ID, name, OBJPROP_ZORDER, z_order);
   ObjectSetString(chart_ID, name, OBJPROP_TEXT, text);
   ObjectSetString(chart_ID, name, OBJPROP_FONT, font);
   
   return(true);
}

//+------------------------------------------------------------------+
//| EditBoxHandler class for managing edit box inputs                |
//+------------------------------------------------------------------+
class EditBoxHandler {
private:
   string dashboardName;
   string baseNames[11];  // Array of base names for panels

public:
   EditBoxHandler(string name) {
      dashboardName = name;
      // Initialize base names for all panels
      baseNames[0] = "MPT_TOP9";
      baseNames[1] = "MPT_STARS";
      baseNames[2] = "MPT_CSSD";
      baseNames[3] = "MPT_RISK";
      baseNames[4] = "MPT_CVAR";
      baseNames[5] = "MPT_BASKET1";
      baseNames[6] = "MPT_BASKET2";
      baseNames[7] = "MPT_BASKET3";
      baseNames[8] = "MPT_DOT1";
      baseNames[9] = "MPT_DOT2";
      baseNames[10] = "MPT_DOT3";
   }
   
   // Handle edit box input events
   void HandleEditBoxEvent(int id, string objectName) {
      if (id != CHARTEVENT_OBJECT_ENDEDIT) return;
      if (StringFind(objectName, dashboardName) != 0) return;
      
      // Remove dashboard name from object name for easier handling
      string shortName = StringSubstr(objectName, StringLen(dashboardName));
      string text = ObjectGetString(0, objectName, OBJPROP_TEXT);
      double value = StringToDouble(text);
      
      // Handle Total SL/TP edit boxes
      if (shortName == "Total_SL_Edit") {
         if (value > 0) GlobalVariableSet("MPT_TOTAL_SL", value);
      }
      else if (shortName == "Total_TP_Edit") {
         if (value > 0) GlobalVariableSet("MPT_TOTAL_TP", value);
      }
      // Handle Pos Open SL edit box
      else if (shortName == "Pos_SL_Edit") {
         if (value > 0) GlobalVariableSet("MPT_POS_SL", value);
      }
      // Handle panel SL/TP edit boxes
      else {
         for (int i = 0; i < ArraySize(baseNames); i++) {
            // Check for SL edit boxes
            if (shortName == baseNames[i] + "_SL_Edit") {
               if (value > 0) GlobalVariableSet(baseNames[i] + "_SL", value);
               break;
            }
            // Check for TP edit boxes
            else if (shortName == baseNames[i] + "_TP_Edit") {
               if (value > 0) GlobalVariableSet(baseNames[i] + "_TP", value);
               break;
            }
         }
      }
   }
   
   // Handle button click events
   void HandleButtonClickEvent(int id, string objectName) {
      if (id != CHARTEVENT_OBJECT_CLICK) return;
      if (StringFind(objectName, dashboardName) != 0) return;
      
      // Remove dashboard name from object name for easier handling
      string shortName = StringSubstr(objectName, StringLen(dashboardName));
      
      // Handle main buttons
      if (shortName == "T1_Button") {
         Print("T1 button clicked");
         // Implement T1 button functionality
      }
      else if (shortName == "T2_Button") {
         Print("T2 button clicked");
         // Implement T2 button functionality
      }
      else if (shortName == "C_Button") {
         Print("C button clicked");
         // Implement C button functionality
      }
      // Handle Total P/L and Pos Open buttons
      else if (shortName == "Total_C_Button") {
         Print("Total P/L C button clicked");
         // Implement Total P/L C button functionality
      }
      else if (shortName == "Pos_C_Button") {
         Print("Pos Open C button clicked");
         // Implement Pos Open C button functionality
      }
      // Handle panel buttons
      else {
         for (int i = 0; i < ArraySize(baseNames); i++) {
            // Check for T1 buttons
            if (shortName == baseNames[i] + "_T1_Button") {
               Print(baseNames[i] + " T1 button clicked");
               // Implement T1 button functionality for this panel
               break;
            }
            // Check for T2 buttons
            else if (shortName == baseNames[i] + "_T2_Button") {
               Print(baseNames[i] + " T2 button clicked");
               // Implement T2 button functionality for this panel
               break;
            }
            // Check for C buttons
            else if (shortName == baseNames[i] + "_C_Button") {
               Print(baseNames[i] + " C button clicked");
               // Implement C button functionality for this panel
               break;
            }
         }
      }
   }
};

//+------------------------------------------------------------------+
//| ProfitLossManager class to calculate and display profits         |
//+------------------------------------------------------------------+
class ProfitLossManager {
private:
   string dashboardName;
   string rowNames[11];
   
public:
   ProfitLossManager(string name) {
      dashboardName = name;
      // Initialize with row names
      rowNames[0] = "MPT_TOP9";
      rowNames[1] = "MPT_STARS";
      rowNames[2] = "MPT_CSSD";
      rowNames[3] = "MPT_RISK";
      rowNames[4] = "MPT_CVAR";
      rowNames[5] = "MPT_BASKET1";
      rowNames[6] = "MPT_BASKET2";
      rowNames[7] = "MPT_BASKET3";
      rowNames[8] = "MPT_DOT1";
      rowNames[9] = "MPT_DOT2";
      rowNames[10] = "MPT_DOT3";
   }
   
   void UpdateProfitDisplay() {
      double totalPL = 0;
      int totalPositions = 0;
      
      for (int i = 0; i < ArraySize(rowNames); i++) {
         // Calculate profit for each row
         double profit = CalculateRowProfit(rowNames[i]);
         totalPL += profit;
         
         if (profit != 0) totalPositions++;
         
         // Update display
         UpdateRowDisplay(rowNames[i], profit);
      }
      
      // Update total P/L
      UpdateTotalDisplay(totalPL, totalPositions);
   }
   
   double CalculateRowProfit(string rowName) {
      // Calculate profit for a specific row
      // This is a placeholder - actual implementation would depend on your strategy
      return MathRand()/32767.0 * 1000 - 500;
   }
   
   void UpdateRowDisplay(string rowName, double profit) {
      ObjectSetString(0, dashboardName + rowName + "_PL_Value", OBJPROP_TEXT, 
                     DoubleToString(profit, 2));
      color textColor = (profit >= 0) ? clrLime : clrRed;
      ObjectSetInteger(0, dashboardName + rowName + "_PL_Value", OBJPROP_COLOR, textColor);
   }
   
   void UpdateTotalDisplay(double totalPL, int positions) {
      ObjectSetString(0, dashboardName + "Total_PL_Value", OBJPROP_TEXT, 
                     DoubleToString(totalPL, 2));
      color totalColor = (totalPL >= 0) ? clrLime : clrRed;
      ObjectSetInteger(0, dashboardName + "Total_PL_Value", OBJPROP_COLOR, totalColor);
      
      ObjectSetString(0, dashboardName + "Pos_Open_Value", OBJPROP_TEXT, 
                     IntegerToString(positions));
   }
};

//+------------------------------------------------------------------+
//| OrderManager class to handle order operations                    |
//+------------------------------------------------------------------+
class OrderManager {
private:
   int magicNumbers[11];
   double stopLossValues[11];
   double takeProfitValues[11];
   
public:
   OrderManager() {
      // Initialize arrays for each panel
      
      // Set default magic numbers (can be customized)
      magicNumbers[0] = 1001;  // MPT_TOP9
      magicNumbers[1] = 1002;  // MPT_STARS
      magicNumbers[2] = 1003;  // MPT_CSSD
      magicNumbers[3] = 1004;  // MPT_RISK
      magicNumbers[4] = 1005;  // MPT_CVAR
      magicNumbers[5] = 1006;  // MPT_BASKET1
      magicNumbers[6] = 1007;  // MPT_BASKET2
      magicNumbers[7] = 1008;  // MPT_BASKET3
      magicNumbers[8] = 1009;  // MPT_DOT1
      magicNumbers[9] = 1010;  // MPT_DOT2
      magicNumbers[10] = 1011; // MPT_DOT3
   }
   
   void UpdateStopLossValues() {
      // Update stop loss values from global variables
      stopLossValues[0] = GlobalVariableGet("MPT_TOP9_SL");
      stopLossValues[1] = GlobalVariableGet("MPT_STARS_SL");
      stopLossValues[2] = GlobalVariableGet("MPT_CSSD_SL");
      stopLossValues[3] = GlobalVariableGet("MPT_RISK_SL");
      stopLossValues[4] = GlobalVariableGet("MPT_CVAR_SL");
      stopLossValues[5] = GlobalVariableGet("MPT_BASKET1_SL");
      stopLossValues[6] = GlobalVariableGet("MPT_BASKET2_SL");
      stopLossValues[7] = GlobalVariableGet("MPT_BASKET3_SL");
      stopLossValues[8] = GlobalVariableGet("MPT_DOT1_SL");
      stopLossValues[9] = GlobalVariableGet("MPT_DOT2_SL");
      stopLossValues[10] = GlobalVariableGet("MPT_DOT3_SL");
   }
   
   void UpdateTakeProfitValues() {
      // Update take profit values from global variables
      takeProfitValues[0] = GlobalVariableGet("MPT_TOP9_TP");
      takeProfitValues[1] = GlobalVariableGet("MPT_STARS_TP");
      takeProfitValues[2] = GlobalVariableGet("MPT_CSSD_TP");
      takeProfitValues[3] = GlobalVariableGet("MPT_RISK_TP");
      takeProfitValues[4] = GlobalVariableGet("MPT_CVAR_TP");
      takeProfitValues[5] = GlobalVariableGet("MPT_BASKET1_TP");
      takeProfitValues[6] = GlobalVariableGet("MPT_BASKET2_TP");
      takeProfitValues[7] = GlobalVariableGet("MPT_BASKET3_TP");
      takeProfitValues[8] = GlobalVariableGet("MPT_DOT1_TP");
      takeProfitValues[9] = GlobalVariableGet("MPT_DOT2_TP");
      takeProfitValues[10] = GlobalVariableGet("MPT_DOT3_TP");
   }
   
   int GetMagicNumber(int index) {
      if (index >= 0 && index < ArraySize(magicNumbers))
         return magicNumbers[index];
      return 0;
   }
   
   double GetStopLoss(int index) {
      if (index >= 0 && index < ArraySize(stopLossValues))
         return stopLossValues[index];
      return DefaultSL;
   }
   
   double GetTakeProfit(int index) {
      if (index >= 0 && index < ArraySize(takeProfitValues))
         return takeProfitValues[index];
      return DefaultTP;
   }
   
   // Methods for opening and managing trades would go here
};

//+------------------------------------------------------------------+
//| DashboardManager class to create and manage the dashboard UI     |
//+------------------------------------------------------------------+
class DashboardManager {
private:
   string dashboardName;
   
public:
   DashboardManager(string name) {
      dashboardName = name;
   }
   
   ~DashboardManager() {
      // Cleanup resources if needed
   }
   
   void CreateDashboard(int startX, int startY, int spacing) {
      int editBoxWidth = 800;
      int editBoxHeight = 25;
      int rowHeight = editBoxHeight;
      int panelY = startY;
      
      // Create panels for each row using the CreateEditBoxGroup function
      panelY = CreateEditBoxGroup("MPT_TOP9", "MPT - Top 9", startX, panelY, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_STARS", "MPT - Rolling Stars", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_CSSD", "MPT - Rolling CSSD", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_RISK", "MPT - Risk", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_CVAR", "MPT - CVaR", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_BASKET1", "MPT - Basket1", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_BASKET2", "MPT - Basket2", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_BASKET3", "MPT - Basket3", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_DOT1", "MPT - Dot1", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_DOT2", "MPT - Dot2", startX, panelY + spacing, editBoxWidth, rowHeight);
      panelY = CreateEditBoxGroup("MPT_DOT3", "MPT - Dot3", startX, panelY + spacing, editBoxWidth, rowHeight);
      
      // Create Total P/L and Pos Open sections
      CreateTotalPLSection(startX, panelY + spacing, editBoxHeight);
   }
   
   int CreateEditBoxGroup(string baseName, string labelText, int x, int y, int width, int height) {
      int labelY = y + height/2;
      int labelWidth = 150;
      int buttonWidth = 30;
      int buttonHeight = 25;
      int miniButtonSize = 15;
      int spacing = 10;
      int plBoxWidth = 80;
      
      // Create label - aligned with edit box vertically
      LabelMake(dashboardName + baseName + "_Label", 0, x, labelY, labelText, Font_Size, Font_Color);
      
      // Create main edit box
      EditBox(dashboardName + baseName + "_Edit", 0, x + labelWidth, y, width, height, Font_Size, Font_Color, "");
      
      // Create P/L display box - smaller and adjusted to match screenshot
      RecMake(dashboardName + baseName + "_PL_Box", x + labelWidth + width + 20, y, plBoxWidth, height, clrWhite, clrDarkGreen);
      
      // Center P/L value in the green box
      LabelMake(dashboardName + baseName + "_PL_Value", 0, x + labelWidth + width + 20 + plBoxWidth/2, y + height/2, "0.00", Font_Size, Font_Color);
      
      // Create SL/TP edit boxes - aligned to the right
      int slTpX = x + labelWidth + width + 150;
      
      // SL label and edit box
      LabelMake(dashboardName + baseName + "_SL_Label", 0, slTpX - 25, y + 12, "SL", Font_Size-3, Font_Color);
      EditBox(dashboardName + baseName + "_SL_Edit", 0, slTpX, y, 50, 20, 10, Font_Color, DoubleToString(GlobalVariableGet(baseName + "_SL"), 0));
      
      // TP label and edit box
      LabelMake(dashboardName + baseName + "_TP_Label", 0, slTpX + 80, y + 12, "TP", Font_Size-3, Font_Color);
      EditBox(dashboardName + baseName + "_TP_Edit", 0, slTpX + 100, y, 50, 20, 10, Font_Color, DoubleToString(GlobalVariableGet(baseName + "_TP"), 0));
      
      // Add T1, T2 and C buttons below each edit box
      int buttonsY = y + height + 5;
      int t1X = x + labelWidth;
      int t2X = t1X + buttonWidth + spacing;
      int cX = t2X + buttonWidth + spacing * 2;  // Extra spacing before C button
      
      // Small T1 and T2 buttons
      ButtonCreate(0, dashboardName + baseName + "_T1_Button", 0, t1X, buttonsY, buttonWidth, miniButtonSize, 0, "T1", Font_Type, 8, Font_Color, clrDarkBlue, Border_Color);
      ButtonCreate(0, dashboardName + baseName + "_T2_Button", 0, t2X, buttonsY, buttonWidth, miniButtonSize, 0, "T2", Font_Type, 8, Font_Color, clrDarkBlue, Border_Color);
      ButtonCreate(0, dashboardName + baseName + "_C_Button", 0, cX, buttonsY, miniButtonSize, miniButtonSize, 0, "C", Font_Type, 8, Font_Color, clrDarkRed, Border_Color);
      
      // Return the bottom position for the next element
      return y + height + 25; // Added space for the buttons below each edit box
   }
   
   void CreateTotalPLSection(int x, int y, int height) {
      int totalY = y;
      int plBoxWidth = 150;
      int slTpWidth = 80;
      int slTpHeight = 25;
      
      // Total P/L label and box
      LabelMake(dashboardName + "Total_PL_Label", 0, x + 140, totalY + height/2, "Total P/L:", Font_Size, Font_Color);
      RecMake(dashboardName + "Total_PL_Box", x + 220, totalY, plBoxWidth, height, clrWhite, clrDarkGreen);
      LabelMake(dashboardName + "Total_PL_Value", 0, x + 220 + plBoxWidth/2, totalY + height/2, "0.00", Font_Size, Font_Color);
      
      // C button for Total P/L
      ButtonCreate(0, dashboardName + "Total_C_Button", 0, x + 220 + plBoxWidth + 10, totalY, height, height, 0, "C", Font_Type, Font_Size, Font_Color, clrDarkRed, Border_Color);
      
      // SL/TP for Total P/L
      int slTpY = totalY + height + 10;
      LabelMake(dashboardName + "Total_SL_Label", 0, x + 185, slTpY + 10, "SL", Font_Size-3, Font_Color);
      EditBox(dashboardName + "Total_SL_Edit", 0, x + 220, slTpY, slTpWidth, slTpHeight, 10, Font_Color, DoubleToString(GlobalVariableGet("MPT_TOTAL_SL"), 0));
      
      LabelMake(dashboardName + "Total_TP_Label", 0, x + 310, slTpY + 10, "TP", Font_Size-3, Font_Color);
      EditBox(dashboardName + "Total_TP_Edit", 0, x + 335, slTpY, slTpWidth, slTpHeight, 10, Font_Color, DoubleToString(GlobalVariableGet("MPT_TOTAL_TP"), 0));
      
      // Create Pos Open display
      int posX = x + 500; // Position to the right of Total P/L
      LabelMake(dashboardName + "Pos_Open_Label", 0, posX, totalY + height/2, "Pos Open:", Font_Size, Font_Color);
      RecMake(dashboardName + "Pos_Open_Box", posX + 80, totalY, plBoxWidth, height, clrWhite, clrDarkGreen);
      LabelMake(dashboardName + "Pos_Open_Value", 0, posX + 80 + plBoxWidth/2, totalY + height/2, "11", Font_Size, Font_Color);
      
      // C button for Pos Open
      ButtonCreate(0, dashboardName + "Pos_C_Button", 0, posX + 80 + plBoxWidth + 10, totalY, height, height, 0, "C", Font_Type, Font_Size, Font_Color, clrDarkRed, Border_Color);
   }
};