#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 14
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
//extern int daytocountback = 5000;		  // Lookback period in days
extern int periods = 8000;				  // Candles back to check
int drawlines = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
double dist = 0.25;						  // Distance of wick vs previous body
enum brtf
{
	M1 = 1,	  // 1
	M5 = 5,	  // 5
	M15 = 15, // 15
};

double finp[];
double finn[];
double linp[];
double linn[];

double signalrsiup[];
double signalrsidn[];
double signalbbup[];
double signalbbdn[];
double signalcomboup[];
double signalcombodn[];
double signalbuysl[];
double signalselsl[];
double signalbuytp[];
double signalseltp[];
static double signalbuy;
static double signalsel;
static double signalbuystop;
static double signalselstop;
//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(14);
	IndicatorShortName("FVG /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexStyle(0, DRAW_NONE, 0, 0);
	SetIndexArrow(0, 233);
	SetIndexBuffer(0, finp);
	SetIndexEmptyValue(0, EMPTY_VALUE);
	SetIndexLabel(0, "");
	SetIndexStyle(1, DRAW_NONE, 0, 0);
	SetIndexArrow(1, 234);
	SetIndexBuffer(1, finn);
	SetIndexEmptyValue(1, EMPTY_VALUE);
	SetIndexLabel(1, "");
	SetIndexStyle(2, DRAW_NONE, 0, 0);
	SetIndexArrow(2, 233);
	SetIndexBuffer(2, linp);
	SetIndexEmptyValue(2, EMPTY_VALUE);
	SetIndexLabel(2, "");
	SetIndexStyle(3, DRAW_NONE, 0, 0);
	SetIndexArrow(3, 234);
	SetIndexBuffer(3, linn);
	SetIndexEmptyValue(3, EMPTY_VALUE);
	SetIndexLabel(3, "");
	SetIndexBuffer(4, signalrsiup);
	SetIndexBuffer(5, signalrsidn);
	SetIndexBuffer(6, signalbbup);
	SetIndexBuffer(7, signalbbdn);
	SetIndexBuffer(8, signalcomboup);
	SetIndexBuffer(9, signalcombodn);
	SetIndexBuffer(10, signalbuysl);
	SetIndexBuffer(11, signalselsl);
	SetIndexBuffer(12, signalbuytp);
	SetIndexBuffer(13, signalseltp);

	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			ChartRedraw();
			RefreshRates();
			checkpre();
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{
	periods = 288;
	string obname;
	double nexp[], prep[];
	double nexn[], pren[];
	ArrayResize(prep, periods + 2);
	ArrayResize(nexp, periods + 1);
	ArrayResize(pren, periods + 2);
	ArrayResize(nexn, periods + 1);
	ArrayInitialize(finp, EMPTY_VALUE);
	ArrayInitialize(finn, EMPTY_VALUE);
	ArrayInitialize(linp, EMPTY_VALUE);
	ArrayInitialize(linn, EMPTY_VALUE);
	
	double copybuy[], copybuystop[];
	double copysel[], copyselstop[];

	double CD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 3, CD1);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 3, HD1);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 3, LD1);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, periods + 3, TD1);
	
	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] < CD1[x + 2]) && LD1[x + 1] - HD1[x + 3] > 0)
		{
			finp[x] = HD1[x + 3];
			linp[x] = LD1[x + 1];			
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] > CD1[x + 2]) && (LD1[x + 3] - HD1[x + 1] > 0))
		{
			finn[x] = LD1[x + 3];
			linn[x] = HD1[x + 1];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	int count11 = 0, count22 = 0;
	int adda = -1, addb = -1;
   
   for (int x = periods; x >= 1; x--)
   {
      double LL = CD1[ArrayMinimum(CD1, x, 1)];
      if (linp[x] != EMPTY_VALUE && LL > finp[x])
      { 
         adda++;
         ArrayResize(copybuy, adda + 1);
         ArrayResize(copybuystop, adda + 1);
         copybuy[adda] = linp[x];
         copybuystop[adda] = finp[x];
      }
   }
   for (int x = periods; x >= 1; x--)
   {
      double HH = CD1[ArrayMaximum(CD1, x, 1)];
      if (linn[x] != EMPTY_VALUE && HH < finn[x])
      {
         addb++;
         ArrayResize(copysel, addb + 1);
         ArrayResize(copyselstop, addb + 1);
         copysel[addb] = linn[x];
         copyselstop[addb] = finn[x];
      }
   }
   
   if (ArraySize(copysel) > 0)
   {
      ArraySort(copysel, 0, 0, MODE_ASCEND);
      ArraySort(copyselstop, 0, 0, MODE_ASCEND);
   }
   if (ArraySize(copybuy) > 0)
   {
      ArraySort(copybuy, 0, 0, MODE_DESCEND);
      ArraySort(copybuystop, 0, 0, MODE_DESCEND);
   }
   
   if (ArraySize(copybuy) > 0)
   {   
      signalbuy = copybuy[0];
      signalbuystop = copybuystop[0];
   }
   if (ArraySize(copysel) > 0)
   {
      signalsel = copysel[0];
      signalselstop = copyselstop[0];
   }
   
	if (signalsel != EMPTY_VALUE && signalbuy != EMPTY_VALUE && ArraySize(copybuy) >= 1 && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) < 45) signalrsiup[1] = 1;
	if (signalsel != EMPTY_VALUE && signalbuy != EMPTY_VALUE && ArraySize(copybuy) >= 1 && LD1[1] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, 1)) signalbbup[1] = 1;
	if (signalsel != EMPTY_VALUE && signalbuy != EMPTY_VALUE && ArraySize(copybuy) >= 1 && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) < 45 && LD1[1] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, 1)) signalcomboup[1] = 1;
	if (signalsel != EMPTY_VALUE && signalbuy != EMPTY_VALUE) signalbuysl[1] = signalbuystop - 20 * _Point;
	if (signalsel != EMPTY_VALUE && signalbuy != EMPTY_VALUE) signalbuytp[1] = signalsel;
	
	if (signalbuy != EMPTY_VALUE && signalsel != EMPTY_VALUE && ArraySize(copysel) >= 1 && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) > 55) signalrsidn[1] = 1;
	if (signalbuy != EMPTY_VALUE && signalsel != EMPTY_VALUE && ArraySize(copysel) >= 1 && HD1[1] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, 1)) signalbbdn[1] = 1;
	if (signalbuy != EMPTY_VALUE && signalsel != EMPTY_VALUE && ArraySize(copysel) >= 1 && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, 1) > 55 && HD1[1] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, 1)) signalcombodn[1] = 1;
	if (signalbuy != EMPTY_VALUE && signalsel != EMPTY_VALUE) signalselsl[1] = signalselstop + 20 * _Point;
	if (signalbuy != EMPTY_VALUE && signalsel != EMPTY_VALUE) signalseltp[1] = signalbuy;
	
	/*
	for (int x = periods - 3; x >= 1; x--)
	{
		string intrepl = "b" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];

		if (finp[x] != EMPTY_VALUE && x < drawlines && LL > finp[x])
		{
		   int count = 0, count1 = 0, counta = 0, countb = 0;
		   for (int y = x; y >= 1; y--)
		   {
		      //if (LD1[y] <= linp[x] && LD1[y] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) count++;
		      if (LD1[y] <= linp[x]) counta++;
		      //if (CD1[y] <= linp[x]) count1++;
		      //if (LD1[y] <= linp[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) < 45) count1++;
		      
		      if (LD1[y] <= linp[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) < 45) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y], 233, y, clrWhite);
		         //ObjectSet(obname, OBJPROP_WIDTH, 3);
		         count1++;
		      }
		      if (LD1[y] <= linp[x] && LD1[y] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y] - EMPTY_VALUE * _Point, 233, y, clrAqua);
		         //ObjectSet(obname, OBJPROP_WIDTH, 2);
		         count++;
		      }
		      if (LD1[y] <= linp[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) < 45 && LD1[y] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y] - 20 * _Point, 233, y, clrYellow);
		         countb++;
		      }
		   }
		   
		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, HD1[x + 3], LD1[x + 1], x + 3, 0, 3 * Period() * 60, clrAqua, DoubleToString(finp[x], _Digits) + " to " + DoubleToString(linp[x], _Digits) + " @ " + intrepl);
			if (LL1 <= linp[x] && LL > finp[x])
			{
				ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, clrDodgerBlue);
		      obname = Name + "FVGtouch" + intrepl;
		      Texter(obname, HD1[x + 3], Time[0], IntegerToString(counta) + " - " + IntegerToString(count) + " - " + IntegerToString(count1) + " - " + IntegerToString(countb), clrWhite);
			}
		}
		if (finp[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "FVGrec" + intrepl);
			ObjectDelete(Name + "FVGtouch" + intrepl);
		}
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		string intrepl = "s" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, x, 1)];
		double HH1 = HD1[ArrayMaximum(HD1, x, 1)];

		if (finn[x] != EMPTY_VALUE && x < drawlines && HH < finn[x])
		{
		   int count = 0, count1 = 0, counta = 0, countb = 0;
		   for (int y = x; y >= 1; y--)
		   {
		      //if (HD1[y] >= linn[x] && HD1[y] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) count++;
		      if (HD1[y] >= linn[x]) counta++;
		      //if (CD1[y] >= linn[x]) count1++;
		      //if (HD1[y] >= linn[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) > 55) count1++;
		      
		      if (HD1[y] >= linn[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) > 55) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y] + 20 * _Point, 234, y, clrWhite);
		         //ObjectSet(obname, OBJPROP_WIDTH, 3);
		         count1++;
		      }
		      if (HD1[y] >= linn[x] && HD1[y] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y] + EMPTY_VALUE * _Point, 234, y, clrAqua);
		         //ObjectSet(obname, OBJPROP_WIDTH, 2);
		         count++;
		      }
		      if (HD1[y] >= linn[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) > 55 && HD1[y] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y], 234, y, clrYellow);
		         countb++;
		      }
		   }
		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, HD1[x + 1], LD1[x + 3], x + 3, 0, 3 * Period() * 60, clrMagenta, DoubleToString(finn[x], _Digits) + " to " + DoubleToString(linn[x], _Digits) + " @ " + intrepl);
			if (HH1 >= linn[x] && HH < finn[x])
			{
				ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, clrRed);
   		   obname = Name + "FVGtouch" + intrepl;
   		   Texter(obname, LD1[x + 3], Time[0], IntegerToString(counta) + " - " + IntegerToString(count) + " - " + IntegerToString(count1) + " - " + IntegerToString(countb), clrWhite);		
   	      ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
			}
		}
		if (finn[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "FVGrec" + intrepl);
			ObjectDelete(Name + "FVGtouch" + intrepl);
		}
	}
	*/
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	//ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	//ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_RAISED);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+