//+------------------------------------------------------------------+
//|                                                   newbaskets.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 8
#property indicator_plots 8
input ENUM_TIMEFRAMES first = PERIOD_H1;
input ENUM_TIMEFRAMES second = PERIOD_MN1;
input int days = 1; //For how many periods back (0 = current)
input int lrlength = 120; //LR lines length
input string prefix = ""; //Broker symbol prefix (before)
input string suffix = ""; //Broker symbol suffix (after)
const string Name = MQLInfoString(MQL_PROGRAM_NAME);

#include <Math\Alglib\alglib.mqh>
double eur[], gbp[], aud[], nzd[], cad[], chf[], jpy[], usd[];


//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   IndicatorDigits(1);
   IndicatorBuffers(8);
   SetIndexBuffer(0, eur);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 3, clrDodgerBlue);
   SetIndexBuffer(1, gbp);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexBuffer(2, aud);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexBuffer(3, nzd);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 3, clrAqua);
   SetIndexBuffer(4, cad);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 3, clrPink);
   SetIndexBuffer(5, chf);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 3, clrGray);
   SetIndexBuffer(6, jpy);
   SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, 3, clrYellow);
   SetIndexBuffer(7, usd);
   SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   /*
   string obname;
   obname = Name + " EUR";
   LabelMake(obname, 0, 80, 50, "EUR", 20, clrDodgerBlue);
   obname = Name + " GBP";
   LabelMake(obname, 0, 80, 80, "GBP", 20, clrRed);
   obname = Name + " AUD";
   LabelMake(obname, 0, 80, 110, "AUD", 20, clrOrange);
   obname = Name + " NZD";
   LabelMake(obname, 0, 80, 140, "NZD", 20, clrAqua);
   obname = Name + " CAD";
   LabelMake(obname, 0, 80, 170, "CAD", 20, clrPink);
   obname = Name + " USD";
   LabelMake(obname, 0, 80, 200, "USD", 20, clrGreen);
   obname = Name + " CHF";
   LabelMake(obname, 0, 80, 230, "CHF", 20, clrGray);
   obname = Name + " JPY";
   LabelMake(obname, 0, 80, 260, "JPY", 20, clrYellow);
   */
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   build();
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void build()
{
   int limit = iBarShift(_Symbol, first, iTime(_Symbol, second, days), false);
      
   ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
   ChartSetInteger(0, CHART_FOREGROUND, 0);
   ChartSetInteger(0, CHART_SCALEFIX, 0, true);
   
   const string eu = prefix + "EURUSD" + suffix;
   const string gu = prefix + "GBPUSD" + suffix;   
   const string au = prefix + "AUDUSD" + suffix;
   const string nu = prefix + "NZDUSD" + suffix;
   const string uc = prefix + "USDCAD" + suffix;
   const string uf = prefix + "USDCHF" + suffix;
   const string uj = prefix + "USDJPY" + suffix;
   
   //PIP values
   double eupl = dblPipValue(eu);
   double gupl = dblPipValue(gu);
   double aupl = dblPipValue(au);
   double nupl = dblPipValue(nu);
   double ucpl = dblPipValue(uc);
   double ufpl = dblPipValue(uf);
   double ujpl = dblPipValue(uj);
   
   double eufopen = 0;
   double gufopen = 0;
   double aufopen = 0;
   double nufopen = 0;
   double ucfopen = 0;
   double uffopen = 0;
   double ujfopen = 0;
   double usfopen = 0;
   
   static datetime checktime = 0;
   bool check = false;
   if (checktime < iTime(_Symbol, PERIOD_M5, 0))
   {
      checktime = iTime(_Symbol, PERIOD_M5, 0);
      check = true;
   }
   if (check)
   {
      for(int i = limit; i >= 1; i--)
      {
         eufopen = (iClose(eu, first, i) - iClose(eu, second, days + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
         gufopen = (iClose(gu, first, i) - iClose(gu, second, days + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
         aufopen = (iClose(au, first, i) - iClose(au, second, days + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
         nufopen = (iClose(nu, first, i) - iClose(nu, second, days + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
         ucfopen = (iClose(uc, first, i) - iClose(uc, second, days + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
         uffopen = (iClose(uf, first, i) - iClose(uf, second, days + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
         ujfopen = (iClose(uj, first, i) - iClose(uj, second, days + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
         usfopen = ((50.14348112 * MathPow(iClose(eu, first, i), -0.576) * MathPow(iClose(uj, first, i), 0.136) * MathPow(iClose(gu, first, i), -0.119) * MathPow(iClose(uc, first, i), 0.091) * MathPow(iClose("USDSEK", first, i), 0.042) * MathPow(iClose(uf, first, i), 0.036)) - (50.14348112 * MathPow(iClose(eu, second, days + 1), -0.576) * MathPow(iClose(uj, second, days + 1), 0.136) * MathPow(iClose(gu, second, days + 1), -0.119) * MathPow(iClose(uc, second, days + 1), 0.091) * MathPow(iClose("USDSEK", second, days + 1), 0.042) * MathPow(iClose(uf, second, days + 1), 0.036))) / 0.001 / 10 * 10;
         
         eur[i] = (eufopen);
         gbp[i] = (gufopen);
         aud[i] = (aufopen);
         nzd[i] = (nufopen);
         cad[i] = (- ucfopen);
         chf[i] = (- uffopen);
         jpy[i] = (- ujfopen);
         usd[i] = usfopen;
      }
      check = false;
   }
   {
      eufopen = (iClose(eu, first, 0) - iClose(eu, second, days + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
      gufopen = (iClose(gu, first, 0) - iClose(gu, second, days + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
      aufopen = (iClose(au, first, 0) - iClose(au, second, days + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
      nufopen = (iClose(nu, first, 0) - iClose(nu, second, days + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
      ucfopen = (iClose(uc, first, 0) - iClose(uc, second, days + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
      uffopen = (iClose(uf, first, 0) - iClose(uf, second, days + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
      ujfopen = (iClose(uj, first, 0) - iClose(uj, second, days + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
      usfopen = ((50.14348112 * MathPow(iClose(eu, first, 0), -0.576) * MathPow(iClose(uj, first, 0), 0.136) * MathPow(iClose(gu, first, 0), -0.119) * MathPow(iClose(uc, first, 0), 0.091) * MathPow(iClose("USDSEK", first, 0), 0.042) * MathPow(iClose(uf, first, 0), 0.036)) - (50.14348112 * MathPow(iClose(eu, second, days + 1), -0.576) * MathPow(iClose(uj, second, days + 1), 0.136) * MathPow(iClose(gu, second, days + 1), -0.119) * MathPow(iClose(uc, second, days + 1), 0.091) * MathPow(iClose("USDSEK", second, days + 1), 0.042) * MathPow(iClose(uf, second, days + 1), 0.036))) / 0.001 / 10 * 10;

      eur[0] = (eufopen);
      gbp[0] = (gufopen);
      aud[0] = (aufopen);
      nzd[0] = (nufopen);
      cad[0] = (- ucfopen);
      chf[0] = (- uffopen);
      jpy[0] = (- ujfopen);
      usd[0] = usfopen;
   
   }
   
   double Min1 = MathMin(eur[ArrayMinimum(eur, limit - 1, 0)], gbp[ArrayMinimum(gbp, limit - 1, 0)]);
   double Min2 = MathMin(aud[ArrayMinimum(aud, limit - 1, 0)], nzd[ArrayMinimum(nzd, limit - 1, 0)]);
   double Min3 = MathMin(cad[ArrayMinimum(cad, limit - 1, 0)], usd[ArrayMinimum(usd, limit - 1, 0)]);
   double Min4 = MathMin(chf[ArrayMinimum(chf, limit - 1, 0)], jpy[ArrayMinimum(jpy, limit - 1, 0)]);
   
   double Min5 = MathMin(Min1, Min2);
   double Min6 = MathMin(Min3, Min4);
   
   double Min7 = MathMin(Min5, Min6);
   
   double Max1 = MathMax(eur[ArrayMaximum(eur, limit - 1, 0)], gbp[ArrayMaximum(gbp, limit - 1, 0)]);
   double Max2 = MathMax(aud[ArrayMaximum(aud, limit - 1, 0)], nzd[ArrayMaximum(nzd, limit - 1, 0)]);
   double Max3 = MathMax(cad[ArrayMaximum(cad, limit - 1, 0)], usd[ArrayMaximum(usd, limit - 1, 0)]);
   double Max4 = MathMax(chf[ArrayMaximum(chf, limit - 1, 0)], jpy[ArrayMaximum(jpy, limit - 1, 0)]);
   
   double Max5 = MathMax(Max1, Max2);
   double Max6 = MathMax(Max3, Max4);
   
   double Max7 = MathMax(Max5, Max6);   
   
   
   ChartSetDouble(0, CHART_FIXED_MIN, Min7 - 100);
   ChartSetDouble(0, CHART_FIXED_MAX, Max7 + 100);
   ChartSetDouble(0, CHART_PRICE_MIN, Min7 - 100);
   ChartSetDouble(0, CHART_PRICE_MAX, Max7 + 100);
}

//+------------------------------------------------------------------+

double dblTickValue( string strSymbol )
{
   return( MarketInfo( strSymbol, MODE_TICKVALUE ) );
}

double dblPipValue( string strSymbol )
{
   double dblCalcPipValue = dblTickValue( strSymbol );
   if (MarketInfo(strSymbol, MODE_DIGITS) == 3) dblCalcPipValue *= 10;
   if (MarketInfo(strSymbol, MODE_DIGITS) == 5) dblCalcPipValue *= 10;
   return( dblCalcPipValue );
}

//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//DE-INIT
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//CUSTOM
//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+