#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 14
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
//extern int daytocountback = 5000;		  // Lookback period in days
extern int periods = 8000;				  // Candles back to check
int drawlines = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
double dist = 0.25;						  // Distance of wick vs previous body
enum brtf
{
	M1 = 1,	  // 1
	M5 = 5,	  // 5
	M15 = 15, // 15
};

double finp[];
double finn[];
double linp[];
double linn[];

double signalrsiup[];
double signalrsidn[];
double signalbbup[];
double signalbbdn[];
double signalcomboup[];
double signalcombodn[];
double signalbuysl[];
double signalselsl[];
double signalbuytp[];
double signalseltp[];
static double signalbuy;
static double signalsel;
static double signalbuystop;
static double signalselstop;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(14);
	IndicatorShortName("FVG /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexStyle(0, DRAW_NONE, 0, 0);
	SetIndexArrow(0, 233);
	SetIndexBuffer(0, finp);
	SetIndexEmptyValue(0, EMPTY_VALUE);
	SetIndexLabel(0, "");
	SetIndexStyle(1, DRAW_NONE, 0, 0);
	SetIndexArrow(1, 234);
	SetIndexBuffer(1, finn);
	SetIndexEmptyValue(1, EMPTY_VALUE);
	SetIndexLabel(1, "");
	SetIndexStyle(2, DRAW_NONE, 0, 0);
	SetIndexArrow(2, 233);
	SetIndexBuffer(2, linp);
	SetIndexEmptyValue(2, EMPTY_VALUE);
	SetIndexLabel(2, "");
	SetIndexStyle(3, DRAW_NONE, 0, 0);
	SetIndexArrow(3, 234);
	SetIndexBuffer(3, linn);
	SetIndexEmptyValue(3, EMPTY_VALUE);
	SetIndexLabel(3, "");
	SetIndexBuffer(4, signalrsiup);
	SetIndexBuffer(5, signalrsidn);
	SetIndexBuffer(6, signalbbup);
	SetIndexBuffer(7, signalbbdn);
	SetIndexBuffer(8, signalcomboup);
	SetIndexBuffer(9, signalcombodn);
	SetIndexBuffer(10, signalbuysl);
	SetIndexBuffer(11, signalselsl);
	SetIndexBuffer(12, signalbuytp);
	SetIndexBuffer(13, signalseltp);

	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
