//+------------------------------------------------------------------+
//|                                                     MinCount.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#define Name WindowExpertName()

input int hours = 8; //Allowed trading hours
input int maximtrades = 20; //Allowed number of trades

int p = 580;
int o = 10;
double RT = 0;
double RTa = 0;
int FontSize = 10;
string Font = "Arial";
//--- input parameters
//input int      Hours;
//input int      Input1;
//input int      Input2;
//input int      Input3;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
	if (GlobalVariableCheck("TCP" + _Symbol) == false)
		GlobalVariableSet("TCP" + _Symbol, p);
	else
		p = (int)GlobalVariableGet("TCP" + _Symbol);
	if (GlobalVariableCheck("TCO" + _Symbol) == false)
		GlobalVariableSet("TCO" + _Symbol, o);
	else
		o = (int)GlobalVariableGet("TCO" + _Symbol);
		/*
	if (GlobalVariableCheck("RT") == false)
		GlobalVariableSet("RT", RT);
   else
      RT = GlobalVariableGet("RT");
	if (GlobalVariableCheck("RTa") == false)
		GlobalVariableSet("RTa", RTa);
   else
      RTa = GlobalVariableGet("RTa");
      
   if (totalpl() == 0) { GlobalVariableSet("RTa", 0); RTa = 0; }
*/
	reclabloc();
	statbuild();
	buildtab();
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   //uint start1 = GetTickCount();
   bool recheck = false;
   static datetime recheck_time = 0;
   if (recheck_time < iTime(_Symbol, PERIOD_M5, 0))
   {
      recheck = true;
      recheck_time = iTime(_Symbol, PERIOD_M5, 0);
   }
   if (openorders() > 0) buildtab();
   else if (recheck)
   {
      buildtab();
      recheck = false;
   }
   
   /*
   bool recheck_a_ = false;
   static datetime recheck_a__time = 0;
   if (recheck_a__time < iTime(_Symbol, PERIOD_D1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 300) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 600)))
   {
      recheck_a_ = true;
      recheck_a__time = iTime(_Symbol, PERIOD_D1, 0);
   }
   else if (recheck_a_)
   {
      GlobalVariableSet("RTa", 0); RTa = 0;
      recheck_a_ = false;
   }
   
   bool recheck_a = false;
   static datetime recheck_time_a = 0;
   if (TimeCurrent() >= recheck_time_a + 1)
   {
      recheck_a = true;
      recheck_time_a = TimeCurrent();
   }
   if (recheck_a && openorders() == 0)
   {
      RTa = GlobalVariableGet("RT");
      GlobalVariableSet("RTa", RTa);
      GlobalVariablesFlush();
      recheck_a = false;
   }
   */
   //uint end1 = GetTickCount();
   //Print(end1 - start1);
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
//---
   
  }
//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
//---
   { //Move rectangle
   	if (id == CHARTEVENT_OBJECT_DRAG)
   	{
   		if (sparam == StringConcatenate(Name + " MovRec"))
   		{
   			GlobalVariableSet("TCP" + _Symbol, (int)ObjectGet(Name + " MovRec", OBJPROP_XDISTANCE));
   			GlobalVariableSet("TCO" + _Symbol, (int)ObjectGet(Name + " MovRec", OBJPROP_YDISTANCE));
   			GlobalVariablesFlush();
   			reclabloc();
   			statbuild();
   			buildtab();
   		}
   	}
   }/*
   {
      if (id == CHARTEVENT_OBJECT_CLICK)
      {
         if (sparam == StringConcatenate(Name + "labelRMN"))
         {  
            //GlobalVariableSet("RT", 0);
            //RT = GlobalVariableGet("RT");
            ObjectSetInteger(0, Name + "labelRMN", OBJPROP_COLOR, clrRed);
   			GlobalVariablesFlush();
         }
      }
   }*/
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Comment("");
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+LIVE UPDATES------------------------------------------------------+
void buildtab()
{  	
	double vertd[4];
	ArrayInitialize(vertd, 0);
	double verte[4];
	ArrayInitialize(verte, 0);
	
	for (int x = 0; x <= 3; x++)
	{
	   vertd[x] = minutes(x);
	}
	for (int x = 0; x <= 3; x++)
	{
	   verte[x] = minutes(x + 4);
	}

	string obname;
	
	for (int i = 0; i <= 3; i++)
	{
		obname = Name + "labela" + IntegerToString(i);
		if (i == 1 || i == 3) LabelMake(obname, 0, p + 90, o + 20 + (i * 15), DoubleToString(vertd[i], 2), FontSize, clrBlack);
		if (i == 0 || i == 2) LabelMake(obname, 0, p + 90, o + 20 + (i * 15), DoubleToString(vertd[i], 0), FontSize, clrBlack);
		obname = Name + "labelb" + IntegerToString(i);
		if (i == 1 || i == 3) LabelMake(obname, 0, p + 240, o + 20 + (i * 15), DoubleToString(verte[i], 2), FontSize, clrBlack);
		if (i == 0 || i == 2) LabelMake(obname, 0, p + 240, o + 20 + (i * 15), DoubleToString(verte[i], 0), FontSize, clrBlack);
	}
	
	obname = Name + "labelPL";
	LabelMake(obname, 0, p, o + 90, "Active P/L Today: " + DoubleToString(totalpl(), 2), FontSize, clrBlack);
	if (totalpl() > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (totalpl() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	
	obname = Name + "labelREM";
	LabelMake(obname, 0, p, o + 110, "Remaining Minutes: " + DoubleToString(hours * 60 - (minutes(0) + minutes(4)), 0), FontSize - 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
	if ((minutes(0) + minutes(4)) < (hours * 60) * 0.75) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if ((minutes(0) + minutes(4)) >= (hours * 60) * 0.75) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	
	obname = Name + "labelTRA";
	LabelMake(obname, 0, p, o + 130, "Remaining Trades: " + DoubleToString(maximtrades - minutes(2) - minutes(6), 0), FontSize - 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
	if ((minutes(2) + minutes(6)) < 15) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if ((minutes(2) + minutes(6)) >= 15) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	/*
	if(openorders() > 0)
	{
	   //Print("running?");
	   RT = RTa + realtime();	   
	   GlobalVariableSet("RT", RT);
	   GlobalVariablesFlush();
	}
	obname = Name + "labelRMN";
	LabelMake(obname, 0, p, o + 150, "Real Minutes: " + DoubleToString(GlobalVariableGet("RT"), 0), FontSize - 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
	if (RT == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	*/
}
//+------------------------------------------------------------------+

//+STATIC OBJECTS----------------------------------------------------+
void statbuild()
{
	string verta[4] = {"Hist Minutes:", "Hist Lots:", "Hist Trades:", "Hist P/L:"};
	string vertb[4] = {"Actv Minutes:", "Actv Lots:", "Actv Trades:", "Actv P/L:"};

	string obname;

   obname = Name + "horlabel";
   LabelMake(obname, 0, p + 60, o, "BALANCE GUARDIAN", FontSize, clrBlack); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
   
	for (int i = 0; i <= 3; i++)
	{
		obname = Name + "verlabela" + IntegerToString(i);
		LabelMake(obname, 0, p, o + 20 + (i * 15), verta[i], FontSize, clrBlack);
		obname = Name + "verlabelb" + IntegerToString(i);
		LabelMake(obname, 0, p + 150, o + 20 + (i * 15), vertb[i], FontSize, clrBlack);
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void reclabloc()
{
	p = (int)GlobalVariableGet("TCP" + _Symbol);
	o = (int)GlobalVariableGet("TCO" + _Symbol);
	string obname;
	obname = Name + " LabRec";
	RecMake(obname, p - 5, o - 5, 300, 160, clrWhite, clrBlack);

	obname = Name + " MovRec";
	RecMake(obname, p - 4, o - 4, 0, 0, clrWhite, clrBlack);
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, true);
	ObjectSetInteger(0, obname, OBJPROP_SELECTED, true);
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, Name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//GUARDIAN STUFF

//+CHECKORDERS-------------------------------------------------------+
int openorders()
{
   int total = 0;
   for (int x = OrdersTotal() - 1; x >= 0; x--){
      if(OrderSelect(x, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
         { total++; }
   }
   return (total);
}
//+------------------------------------------------------------------+

//+REALTIME CHECK----------------------------------------------------+
double realtime()
{
   datetime count = 0;
   datetime start1[];
   ArrayResize(start1, 5);
   datetime start2[];
   ArrayResize(start2, 10000);
   datetime start3[];
   ArrayResize(start3, 10000);
   datetime starttime = 0;
   datetime startday = iTime(_Symbol, PERIOD_D1, 0);
   datetime endday = TimeCurrent();
   
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderOpenTime() >= startday && OrderOpenTime() <= endday && OrderType() < 2) {
         start1[i] = OrderOpenTime();
		}
	for (int i = OrdersHistoryTotal() - 1; i >= OrdersHistoryTotal() - 20; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderOpenTime() >= startday && OrderCloseTime() <= endday && OrderType() < 2) {
         start2[i] = OrderOpenTime();
         start3[i] = OrderCloseTime();
		}
	
	if (openorders() == 1)
	{
	   for (int x = OrdersHistoryTotal() - 21; x <= OrdersHistoryTotal() - 1; x++)
	   {
         if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] < start3[x + 1])
	      {
	         starttime = start2[x + 1];
	      }
	      else if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] > start3[x + 1])
	      {
	         starttime = start2[x];
	         break;
	      }
	      else if (start1[0] > start3[x])
	      {
	         starttime = start1[0];
	         break;
	      }
	   }
	}
	else if (openorders() == 2)
	{
	   for (int x = OrdersHistoryTotal() - 21; x <= OrdersHistoryTotal() - 1; x++)
	   {
         if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] < start3[x + 1])
	      {
	         starttime = start2[x + 1];
	      }
	      else if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] > start3[x + 1])
	      {
	         starttime = start2[x];
	         break;
	      }
	      else if (start1[0] > start3[x])
	      {
	         starttime = start1[0];
	         break;
	      }
	   }
	}
	else if (openorders() == 3)
	{
	   for (int x = OrdersHistoryTotal() - 21; x <= OrdersHistoryTotal() - 1; x++)
	   {
         if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] < start3[x + 1])
	      {
	         starttime = start2[x + 1];
	      }
	      else if (start1[0] > start2[x] && start1[0] < start3[x] && start2[x] > start3[x + 1])
	      {
	         starttime = start2[x];
	         break;
	      }
	      else if (start1[0] > start3[x])
	      {
	         starttime = start1[0];
	         break;
	      }
	   }
	}
   if (count < iTime(_Symbol, PERIOD_D1, 0))
      count = 0;
   if (openorders() > 0)
      count = TimeCurrent() - starttime;
      
   MqlDateTime str1;
   
   TimeToStruct(count, str1);
   
   return (60*str1.hour + str1.min);
}
//+------------------------------------------------------------------+

//+GUARDIAN NUMBERS--------------------------------------------------+
double minutes(const int x)
{
   datetime startday = iTime(_Symbol, PERIOD_D1, 0);
   datetime endday = TimeCurrent();
   
	datetime today_orders = 0, today_open = 0;
	
   double total = 0, open = 0;
   double totlots1 = 0, totlots2 = 0;
   double totprof1 = 0, totprof2 = 0;
   double totpl1 = 0, totpl2 = 0;
   
   double tottime1 = 0, tottime2 = 0;
   
   MqlDateTime str1, str2;
	
	for (int i = OrdersHistoryTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderCloseTime() >= startday && OrderCloseTime() <= endday && OrderType() < 2) {
         today_orders += OrderCloseTime() - OrderOpenTime();
         totprof1++;
         totpl1 += OrderProfit() + OrderSwap() + OrderCommission();
		}
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderOpenTime() >= startday && OrderOpenTime() <= endday && OrderType() < 2) {
         today_open += TimeCurrent() - OrderOpenTime();
         totprof2++;
         totpl2 += OrderProfit() + OrderSwap() + OrderCommission();
		}
	for (int i = OrdersHistoryTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderCloseTime() >= startday && OrderCloseTime() <= endday && OrderType() < 2 && MarketInfo(OrderSymbol(), MODE_MARGINCALCMODE) < 1) {
         totlots1 += OrderLots();
		}
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderOpenTime() >= startday && OrderOpenTime() <= endday && OrderType() < 2 && MarketInfo(OrderSymbol(), MODE_MARGINCALCMODE) < 1) {
         totlots2 += OrderLots();
		}
   TimeToStruct(today_orders, str1);
   TimeToStruct(today_open, str2);
   
   total = (60 * str1.hour) + str1.min;
   open = (60 * str2.hour) + str2.min;
   
   //time
   switch (x)
   {
      case 0:
      return(NormalizeDouble(total, 0)); break;
      case 1:
      return (NormalizeDouble(totlots1, 2)); break;
      case 2:
      return (NormalizeDouble(totprof1, 0)); break;
      case 3:
      return (NormalizeDouble(totpl1, 2)); break;
      case 4:
      return (NormalizeDouble(open, 0)); break;
      case 5:
      return (NormalizeDouble(totlots2, 2)); break;
      case 6:
      return (NormalizeDouble(totprof2, 0)); break;
      case 7:
      return (NormalizeDouble(totpl2, 2)); break;
      default:
      return(0); break;
   }
   return(0);
}
//+------------------------------------------------------------------+

//+P/L CALC----------------------------------------------------------+
double totalpl()
{
	double total = 0;
   datetime today_midnight=TimeCurrent()-(TimeCurrent()%(PERIOD_D1*60));
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	for (int i = OrdersHistoryTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY) && OrderCloseTime()>=today_midnight && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (NormalizeDouble(total, 2));
}
//+------------------------------------------------------------------+