//+------------------------------------------------------------------+
//|                                                        DCmid.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 3
double dcmid[], dcup[], dcdn[];
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   SetIndexBuffer(0, dcmid);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, C'55,31,0');
   SetIndexBuffer(1, dcup);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 1, C'55,31,0');
   SetIndexBuffer(2, dcdn);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 1, C'55,31,0');
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   static datetime kill = 0;
   bool killb = false;
   if (iTime(_Symbol, PERIOD_CURRENT, 0) > kill)
   {
      kill = iTime(_Symbol, PERIOD_CURRENT, 0);
      killb = true;
   }
   if (killb)
   {
      for (int x = 1000; x >= 0; x--)
      {
         dcmid[x] = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, x + 6)) + (iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, x + 6)) - iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, x + 6))) / 2;
         dcup[x] = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, x + 6)) - 0.25 * (iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, x + 6)) - iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, x + 6)));
         dcdn[x] = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, x + 6)) + 0.25 * (iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, x + 6)) - iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, x + 6)));
      }
      killb = false;
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
