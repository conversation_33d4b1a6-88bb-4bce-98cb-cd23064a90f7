//+------------------------------------------------------------------+
//|                                           TopSecret.mq4       |
//|                                           Copyright 2017, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property indicator_chart_window
#property strict

#define name WindowExpertName()

input bool baseredraw = false; // Enable moving of base with every C letter hit
input bool showpr = true; // Show mouse price at comment
input bool delonexit = false; // Delete lines / fibs on exit

input color col1 = clrBlue; // Top Colors
input color col2 = clrRed; // Bottom Colors
input color col3 = clrBlack; // Trade (fib) Colors
input color col4 = clrSienna; // Striken trade (fib) Colors

double mousecl;
datetime dt;
string obname;
bool continuous = false;
static double peradr;
bool debug = false;
static int defunct = 5;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, 1);
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-initialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())

		if (!IsTesting())
		{
			DeleteObjects();
		}
}
//+------------------------------------------------------------------+

//+DELETE FUNCTION---------------------------------------------------+
void DeleteObjects() {
	if (delonexit) {
		ObjectsDeleteAll(0, name);
		ObjectsDeleteAll(0, "TrenD");
		Comment("");
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, PERIOD_CURRENT, 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, PERIOD_CURRENT, 0);
	}
	if (new_1m_check)
	{
		if (defunct == 1) HLines1();
		new_1m_check = false;
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam) {
		{
			if (id == CHARTEVENT_KEYDOWN) {
				if (lparam == StringGetChar("C", 0)) { if (baseredraw) DelBaseline(); Baseline(); }
				else if (lparam == StringGetChar("V", 0))  DelBaseline();
			}
		}
		{
			if (id == CHARTEVENT_KEYDOWN) {
				if (lparam == StringGetChar("1", 0)) { DelHLines(); HLines1(); defunct = 1; }
				else if (lparam == StringGetChar("0", 0)) { DelHLines(); DelBaseline(); defunct = 5; }
			}
		}
		{
			if (id == CHARTEVENT_MOUSE_MOVE) {
				int sub_win;
				ChartXYToTimePrice(0, (int)lparam, (int)dparam, sub_win, dt, mousecl);
				if (defunct != 5) Comment("Used R: " + DoubleToStr(peradr, 2) + "\n" + "MouseDate: " + TimeToStr(dt, TIME_DATE | TIME_MINUTES) + "\n" + "MousePrice: " + DoubleToStr(mousecl, Digits)); //buildtabinfo();
				else if (defunct == 5) Comment("");
			}
		}
}
//+------------------------------------------------------------------+

//+DELETE H LINES----------------------------------------------------+
void DelHLines() {
	if (ObjectFind(name + "0") > -1) { Comment(""); ObjectsDeleteAll(0, name); }
}
//+------------------------------------------------------------------+

//+ATR/MODE/LOC+PRICE INFO-------------------------------------------+
void buildtabinfo() {
	Comment("Used R: " + DoubleToStr(peradr, 2) + "\n" + "MouseDate: " + TimeToStr(dt, TIME_DATE | TIME_MINUTES) + "\n" + "MousePrice: " + DoubleToStr(mousecl, Digits));
}
//+------------------------------------------------------------------+

//Yearly for 1H & above
//+YEARLY ATR CALCS && H LINE DRAW-----------------------------------+
double ATRPipCalcs1() {
	double AtrYrRange;

	double YearlyAtr = 0.00666 * iOpen(_Symbol, PERIOD_MN1, Month() - 1);
	if (YearlyAtr < 0.1) AtrYrRange = 10000.0 * YearlyAtr;
	else AtrYrRange = 100.0 * YearlyAtr;
	if (MarketInfo(_Symbol, MODE_PROFITCALCMODE) == 1 && (Digits == 1 || Digits == 2)) AtrYrRange = 1.0 * YearlyAtr;
	if (MarketInfo(_Symbol, MODE_PROFITCALCMODE) == 1 && Digits == 3) AtrYrRange = 10.0 * YearlyAtr;
	if (debug) Print(AtrYrRange);
	double YO = iOpen(_Symbol, PERIOD_MN1, Month() - 1);

	peradr = AtrYrRange;

	return (YO);
}

void HLines1() {
	double startline;
	int n = 50;

	startline = ATRPipCalcs1();

	for (int i = n; i >= -n; i--) {
		obname = name + IntegerToString(i);
		objtrend(obname, startline + i * 0.00666 * startline, col1);
		ObjectSetString(0, obname, OBJPROP_TEXT, DoubleToStr(i * 0.666, 2) + " " + DoubleToStr(startline + i * 0.00666 * startline, _Digits));
	}
}
//+------------------------------------------------------------------+

//+CREATE FIB-LIKE TRENDLINES ON H-LINES & MID-LINES-----------------+
void Baseline() {
	double delta = 0;
	static double firstloc;

	if (defunct == 1) delta = ATRPipCalcs1();
	else if (defunct == 5) return;

	datetime t32 = iTime(_Symbol, PERIOD_D1, 0) + 60 * 60 * 24 * 3;
	if (ObjectFind("TrenD") == -1) firstloc = mousecl; else firstloc = firstloc;

	if (ObjectFind("TrenD") == -1) {
		objbase("TrenD", dt, t32, firstloc, col3);
		ObjectCreate("TrenDt", OBJ_TEXT, 0, dt, firstloc);
		ObjectSetText("TrenDt", "BASE" + " " + "@ " + DoubleToStr(firstloc, Digits) + " ", 8, "Arial", col3);
		ObjectSetInteger(0, "TrenDt", OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	}

	double maxh = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, iBarShift(_Symbol, PERIOD_CURRENT, dt, true) + 1, 0));
	double maxl = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, iBarShift(_Symbol, PERIOD_CURRENT, dt, true) + 1, 0));

	for (int i = 1; i <= 30; i++) {
		if (ObjectFind("TrenD" + IntegerToString(i)) == -1 && maxh >= ((i - 1)*0.00333*delta) + firstloc) { objbase("TrenD" + IntegerToString(i), dt, t32, (i*0.00333*delta) + firstloc, col4); ObjectSetInteger(0, "TrenD" + IntegerToString(i), OBJPROP_STYLE, STYLE_DASHDOT); }
		else if (ObjectFind("TrenD" + IntegerToString(i)) == -1 && maxh < ((i - 1)*0.00333*delta) + firstloc) objbase("TrenD" + IntegerToString(i), dt, t32, (i*0.00333*delta) + firstloc, col3);
		ObjectCreate("TrenDt" + IntegerToString(i), OBJ_TEXT, 0, dt, (i*0.00333*delta) + firstloc);
		ObjectSetText("TrenDt" + IntegerToString(i), DoubleToStr(i * 0.33, 2) + " " + "@ " + DoubleToStr((i*0.00333*delta) + firstloc, Digits), 9, "Arial", col3);
		ObjectSetInteger(0, "TrenDt" + IntegerToString(i), OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
		if (ObjectGetInteger(0, "TrenD" + IntegerToString(i), OBJPROP_COLOR) == col4) { ObjectSetInteger(0, "TrenDt" + IntegerToString(i), OBJPROP_COLOR, col4);  ObjectSetInteger(0, "TrenDt" + IntegerToString(i), OBJPROP_FONTSIZE, 8); }
	}

	for (int i = 1; i <= 30; i++) {
		if (ObjectFind("TrenD-" + IntegerToString(i)) == -1 && maxl <= (-(i - 1)*0.00333*delta) + firstloc) { objbase("TrenD-" + IntegerToString(i), dt, t32, (-i*0.00333*delta) + firstloc, col4); ObjectSetInteger(0, "TrenD-" + IntegerToString(i), OBJPROP_STYLE, STYLE_DASHDOT); }
		else if (ObjectFind("TrenD-" + IntegerToString(i)) == -1 && maxl > (-(i - 1)*0.00333*delta) + firstloc) objbase("TrenD-" + IntegerToString(i), dt, t32, (-i*0.00333*delta) + firstloc, col3);
		ObjectCreate("TrenDt-" + IntegerToString(i), OBJ_TEXT, 0, dt, (-i*0.00333*delta) + firstloc);
		ObjectSetText("TrenDt-" + IntegerToString(i), DoubleToStr(-i * 0.33, 2) + " " + "@ " + DoubleToStr((-i*0.00333*delta) + firstloc, Digits), 9, "Arial", col3);
		ObjectSetInteger(0, "TrenDt-" + IntegerToString(i), OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
		if (ObjectGetInteger(0, "TrenD-" + IntegerToString(i), OBJPROP_COLOR) == col4) { ObjectSetInteger(0, "TrenDt-" + IntegerToString(i), OBJPROP_COLOR, col4); ObjectSetInteger(0, "TrenDt-" + IntegerToString(i), OBJPROP_FONTSIZE, 8); }
	}
}
//+------------------------------------------------------------------+

//+DELETE FIB-LIKE TRENDLINES----------------------------------------+
void DelBaseline() {
	if (ObjectFind("TrenD") > -1) ObjectsDeleteAll(0, "TrenD");
}
//+------------------------------------------------------------------+

//+CREATE H-LINES----------------------------------------------------+
void objtrend(string oname, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_HLINE, 0, 0, pr1))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(oname, OBJPROP_WIDTH, 0);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
}
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objbase(string oname, datetime t1, datetime t2, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_DASH);
	ObjectSet(oname, OBJPROP_WIDTH, 0);
	ObjectSet(oname, OBJPROP_BACK, false);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_TIME1, t1);
	ObjectSet(oname, OBJPROP_TIME2, t2);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_PRICE2, pr1);
	ObjectSet(oname, OBJPROP_RAY, false);
}
//+------------------------------------------------------------------+