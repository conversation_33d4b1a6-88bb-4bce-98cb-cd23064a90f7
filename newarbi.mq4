//+------------------------------------------------------------------+
//|                                                   newbaskets.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 42
#property indicator_plots 42
input ENUM_TIMEFRAMES first = PERIOD_M5;
input ENUM_TIMEFRAMES second = PERIOD_D1;
input int days = 0; //For how many periods back (0 = current)
input int lrlength = 72; //LR lines length
input string prefix = ""; //Broker symbol prefix (before)
input string suffix = ""; //Broker symbol suffix (after)
const string Name = MQLInfoString(MQL_PROGRAM_NAME);

//#include <Math\Alglib\alglib.mqh>
double eur[], gbp[], aud[], nzd[], cad[], usd[], chf[], jpy[];
double euru[], eurd[], gbpu[], gbpd[], audu[], audd[], nzdu[], nzdd[], cadu[], cadd[], usdu[], usdd[], chfu[], chfd[], jpyu[], jpyd[];
double buysell2[], buysell2u[], buysell2d[];
double trophya[], trophyb[], trophyc[], trophyd[], trophye[], trophyf[], trophyg[], trophyh[], trophyi[];
double buyside2[], sellside2[];
double rec[], irec[];
/*
double eus[], gus[], aus[], nus[], cus[], fus[], jus[], usu[];
double buyside[], sellside[];
double buysell[];
double buyside2[], sellside2[];
double buysell2[];
double buyside3[], sellside3[];
double buysell3[];
*/

static bool nojpy, nochf, noaud, nonzd, nousd, nocad, noeur, nogbp;
static bool recheck;

double spotup[], spotdn[];

bool alertup = false, alertdn = false;

bool newrun = false;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   IndicatorDigits(1);
   IndicatorBuffers(42);
   SetIndexBuffer(0, eur);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 3, clrDodgerBlue);
   SetIndexLabel(0, "EUR");
   SetIndexBuffer(1, gbp);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexLabel(1, "GBP");
   SetIndexBuffer(2, aud);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexLabel(2, "AUD");
   SetIndexBuffer(3, nzd);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 3, clrAqua);
   SetIndexLabel(3, "NZD");
   SetIndexBuffer(4, cad);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 3, clrPink);
   SetIndexLabel(4, "CAD");
   SetIndexBuffer(5, usd);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   SetIndexLabel(5, "USD");
   SetIndexBuffer(6, chf);
   SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, 3, clrGray);
   SetIndexLabel(6, "CHF");
   SetIndexBuffer(7, jpy);
   SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, 3, clrYellow);
   SetIndexLabel(7, "JPY");
   SetIndexBuffer(8, euru);
   SetIndexStyle(8, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(8, "eur sdu");
   SetIndexBuffer(9, eurd);
   SetIndexStyle(9, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(9, "eur sdd");
   SetIndexBuffer(10, gbpu);
   SetIndexStyle(10, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(10, "gbp sdu");
   SetIndexBuffer(11, gbpd);
   SetIndexStyle(11, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(11, "gbp sdd");
   SetIndexBuffer(12, audu);
   SetIndexStyle(12, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(12, "aud sdu");
   SetIndexBuffer(13, audd);
   SetIndexStyle(13, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(13, "aud sdd");
   SetIndexBuffer(14, nzdu);
   SetIndexStyle(14, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(14, "nzd sdu");
   SetIndexBuffer(15, nzdd);
   SetIndexStyle(15, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(15, "nzd sdd");
   SetIndexBuffer(16, usdu);
   SetIndexStyle(16, DRAW_LINE, STYLE_SOLID, 1, clrGreen);
   SetIndexLabel(16, "usd sdu");
   SetIndexBuffer(17, usdd);
   SetIndexStyle(17, DRAW_LINE, STYLE_SOLID, 1, clrGreen);
   SetIndexLabel(17, "usd sdd");
   SetIndexBuffer(18, cadu);
   SetIndexStyle(18, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexLabel(18, "cad sdu");
   SetIndexBuffer(19, cadd);
   SetIndexStyle(19, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexLabel(19, "cad sdd");
   SetIndexBuffer(20, chfu);
   SetIndexStyle(20, DRAW_LINE, STYLE_SOLID, 1, clrGray);
   SetIndexLabel(20, "chf sdu");
   SetIndexBuffer(21, chfd);
   SetIndexStyle(21, DRAW_LINE, STYLE_SOLID, 1, clrGray);
   SetIndexLabel(21, "chf sdd");
   SetIndexBuffer(22, jpyu);
   SetIndexStyle(22, DRAW_LINE, STYLE_SOLID, 1, clrYellow);
   SetIndexLabel(22, "jpy sdu");
   SetIndexBuffer(23, jpyd);
   SetIndexStyle(23, DRAW_LINE, STYLE_SOLID, 1, clrYellow);
   SetIndexLabel(23, "jpy sdd");
   SetIndexBuffer(24, buysell2);
   SetIndexStyle(24, DRAW_LINE, STYLE_SOLID, 3, clrMagenta);
   SetIndexLabel(24, "bs");
   SetIndexBuffer(25, buysell2u);
   SetIndexStyle(25, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(25, "bs sdu");
   SetIndexBuffer(26, buysell2d);
   SetIndexStyle(26, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(26, "bs sdd");
   SetIndexBuffer(27, trophya);
   SetIndexStyle(27, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(27, "eur sd");
   SetIndexBuffer(28, trophyb);
   SetIndexStyle(28, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(28, "gbp sd");
   SetIndexBuffer(29, trophyc);
   SetIndexStyle(29, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(29, "aud sd");
   SetIndexBuffer(30, trophyd);
   SetIndexStyle(30, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(30, "nzd sd");
   SetIndexBuffer(31, trophye);
   SetIndexStyle(31, DRAW_LINE, STYLE_SOLID, 1, clrGreen);
   SetIndexLabel(31, "usd sd");
   SetIndexBuffer(32, trophyf);
   SetIndexStyle(32, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexLabel(32, "cad sd");
   SetIndexBuffer(33, trophyg);
   SetIndexStyle(33, DRAW_LINE, STYLE_SOLID, 1, clrGray);
   SetIndexLabel(33, "chf sd");
   SetIndexBuffer(34, trophyh);
   SetIndexStyle(34, DRAW_LINE, STYLE_SOLID, 1, clrYellow);
   SetIndexLabel(34, "jpy sd");
   SetIndexBuffer(35, trophyi);
   SetIndexStyle(35, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(35, "bs sd");
   SetIndexBuffer(36, buyside2);
   SetIndexStyle(36, DRAW_NONE);
   SetIndexBuffer(37, sellside2);
   SetIndexStyle(37, DRAW_NONE);
   SetIndexBuffer(38, spotup);
   SetIndexStyle(38, DRAW_NONE);
   SetIndexBuffer(39, spotdn);
   SetIndexStyle(39, DRAW_NONE);
   SetIndexBuffer(40, rec);
   SetIndexStyle(40, DRAW_LINE, STYLE_SOLID, 3, clrWhite);
   SetIndexBuffer(41, irec);
   SetIndexStyle(41, DRAW_LINE, STYLE_SOLID, 3, clrWhite);
   /*
   SetIndexBuffer(8, eus);
   SetIndexStyle(8, DRAW_NONE, STYLE_SOLID, 0, clrDodgerBlue);
   SetIndexBuffer(9, gus);
   SetIndexStyle(9, DRAW_NONE, STYLE_SOLID, 0, clrRed);
   SetIndexBuffer(10, aus);
   SetIndexStyle(10, DRAW_NONE, STYLE_SOLID, 0, clrOrange);
   SetIndexBuffer(11, nus);
   SetIndexStyle(11, DRAW_NONE, STYLE_SOLID, 0, clrAqua);
   SetIndexBuffer(12, cus);
   SetIndexStyle(12, DRAW_NONE, STYLE_SOLID, 0, clrPink);
   SetIndexBuffer(13, fus);
   SetIndexStyle(13, DRAW_NONE, STYLE_SOLID, 0, clrGray);
   SetIndexBuffer(14, jus);
   SetIndexStyle(14, DRAW_NONE, STYLE_SOLID, 0, clrYellow);
   SetIndexBuffer(15, buyside);
   SetIndexStyle(15, DRAW_NONE, STYLE_SOLID, 1, clrLime);
   SetIndexBuffer(16, sellside);
   SetIndexStyle(16, DRAW_NONE, STYLE_SOLID, 1, clrMagenta);
   SetIndexBuffer(17, buysell);
   SetIndexStyle(17, DRAW_NONE, STYLE_SOLID, 2, clrLime);
   SetIndexBuffer(18, buyside2);
   SetIndexStyle(18, DRAW_NONE, STYLE_SOLID, 1, clrLime);
   SetIndexBuffer(19, sellside2);
   SetIndexStyle(19, DRAW_NONE, STYLE_SOLID, 1, clrMagenta);
   SetIndexBuffer(20, buysell2);
   SetIndexStyle(20, DRAW_NONE, STYLE_SOLID, 2, clrMagenta);
   SetIndexBuffer(21, buyside3);
   SetIndexStyle(21, DRAW_NONE, STYLE_SOLID, 1, clrLime);
   SetIndexBuffer(22, sellside3);
   SetIndexStyle(22, DRAW_NONE, STYLE_SOLID, 1, clrMagenta);
   SetIndexBuffer(23, buysell3);
   SetIndexStyle(23, DRAW_NONE, STYLE_SOLID, 2, clrWhite);
   SetIndexBuffer(24, usu);
   SetIndexStyle(24, DRAW_NONE, STYLE_SOLID, 0, clrGreen);
   */
   
   newrun = true;
   
   string obname;
   obname = Name + " EUR";
   LabelMake(obname, 1, 100, 50, "EUR", 12, clrDodgerBlue);
   obname = Name + " GBP";
   LabelMake(obname, 1, 100, 65, "GBP", 12, clrRed);
   obname = Name + " AUD";
   LabelMake(obname, 1, 100, 80, "AUD", 12, clrOrange);
   obname = Name + " NZD";
   LabelMake(obname, 1, 100, 95, "NZD", 12, clrAqua);
   obname = Name + " CAD";
   LabelMake(obname, 1, 100, 110, "CAD", 12, clrPink);
   obname = Name + " USD";
   LabelMake(obname, 1, 100, 125, "USD", 12, clrGreen);
   obname = Name + " CHF";
   LabelMake(obname, 1, 100, 140, "CHF", 12, clrGray);
   obname = Name + " JPY";
   LabelMake(obname, 1, 100, 155, "JPY", 12, clrYellow);
   obname = Name + " BS";
   LabelMake(obname, 1, 100, 170, "BS", 12, clrMagenta);
   obname = Name + " REC";
   LabelMake(obname, 1, 100, 185, "REC", 12, clrWhite);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   build();
   if (newrun) { files(); newrun = false; }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void build()
{
   int limit = iBarShift(_Symbol, first, iTime(_Symbol, second, days), false);
      
   ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
   ChartSetInteger(0, CHART_FOREGROUND, 0);
   ChartSetInteger(0, CHART_SCALEFIX, 0, true);
   
   const string eu = prefix + "EURUSD" + suffix;
   const string eg = prefix + "EURGBP" + suffix;
   const string ea = prefix + "EURAUD" + suffix;
   const string en = prefix + "EURNZD" + suffix;
   const string ec = prefix + "EURCAD" + suffix;
   const string ef = prefix + "EURCHF" + suffix;
   const string ej = prefix + "EURJPY" + suffix;
   
   const string gu = prefix + "GBPUSD" + suffix;
   const string ga = prefix + "GBPAUD" + suffix;
   const string gn = prefix + "GBPNZD" + suffix;
   const string gc = prefix + "GBPCAD" + suffix;
   const string gf = prefix + "GBPCHF" + suffix;
   const string gj = prefix + "GBPJPY" + suffix;
   
   const string au = prefix + "AUDUSD" + suffix;
   const string an = prefix + "AUDNZD" + suffix;
   const string ac = prefix + "AUDCAD" + suffix;
   const string af = prefix + "AUDCHF" + suffix;
   const string aj = prefix + "AUDJPY" + suffix;
   
   const string nu = prefix + "NZDUSD" + suffix;
   const string nc = prefix + "NZDCAD" + suffix;
   const string nf = prefix + "NZDCHF" + suffix;
   const string nj = prefix + "NZDJPY" + suffix;
   
   const string uc = prefix + "USDCAD" + suffix;
   const string cf = prefix + "CADCHF" + suffix;
   const string cj = prefix + "CADJPY" + suffix;
   
   const string uf = prefix + "USDCHF" + suffix;
   const string fj = prefix + "CHFJPY" + suffix;
   
   const string uj = prefix + "USDJPY" + suffix;
   
   //PIP values
   double eupl = dblPipValue(eu);
   double egpl = dblPipValue(eg);
   double eapl = dblPipValue(ea);
   double enpl = dblPipValue(en);
   double ecpl = dblPipValue(ec);
   double efpl = dblPipValue(ef);
   double ejpl = dblPipValue(ej);
   
   double gupl = dblPipValue(gu);
   double gapl = dblPipValue(ga);
   double gnpl = dblPipValue(gn);
   double gcpl = dblPipValue(gc);
   double gfpl = dblPipValue(gf);
   double gjpl = dblPipValue(gj);
   
   double aupl = dblPipValue(au);
   double anpl = dblPipValue(an);
   double acpl = dblPipValue(ac);
   double afpl = dblPipValue(af);
   double ajpl = dblPipValue(aj);
   
   double nupl = dblPipValue(nu);
   double ncpl = dblPipValue(nc);
   double nfpl = dblPipValue(nf);
   double njpl = dblPipValue(nj);
   
   double ucpl = dblPipValue(uc);
   double cfpl = dblPipValue(cf);
   double cjpl = dblPipValue(cj);
   
   double ufpl = dblPipValue(uf);
   double fjpl = dblPipValue(fj);
   
   double ujpl = dblPipValue(uj);
   
   double eufopen = 0, egfopen = 0, eafopen = 0, enfopen = 0, ecfopen = 0, effopen = 0, ejfopen = 0;
   double gufopen = 0, gafopen = 0, gnfopen = 0, gcfopen = 0, gffopen = 0, gjfopen = 0;
   double aufopen = 0, anfopen = 0, acfopen = 0, affopen = 0, ajfopen = 0;
   double nufopen = 0, ncfopen = 0, nffopen = 0, njfopen = 0;
   double ucfopen = 0, cffopen = 0, cjfopen = 0;
   double uffopen = 0, fjfopen = 0;
   double ujfopen = 0;
   double usfopen = 0;
   
   double eura = 0, gbpa = 0, auda = 0, nzda = 0, cada = 0, usda = 0, chfa = 0, jpya = 0, bsa = 0, reca = 0;
   
   double eufopen1 = 0, egfopen1 = 0, eafopen1 = 0, enfopen1 = 0, ecfopen1 = 0, effopen1 = 0, ejfopen1 = 0;
   double gufopen1 = 0, gafopen1 = 0, gnfopen1 = 0, gcfopen1 = 0, gffopen1 = 0, gjfopen1 = 0;
   double aufopen1 = 0, anfopen1 = 0, acfopen1 = 0, affopen1 = 0, ajfopen1 = 0;
   double nufopen1 = 0, ncfopen1 = 0, nffopen1 = 0, njfopen1 = 0;
   double ucfopen1 = 0, cffopen1 = 0, cjfopen1 = 0;
   double uffopen1 = 0, fjfopen1 = 0;
   double ujfopen1 = 0;
      
   static datetime checktime = 0;
   bool check = false;
   if (checktime < iTime(_Symbol, PERIOD_M5, 0))
   {
      checktime = iTime(_Symbol, PERIOD_M5, 0);
      check = true;
   }
   if (check || recheck)
   {
      ArrayInitialize(buyside2, 0);
      ArrayInitialize(sellside2, 0);
            
      double mordor[][28];
      ArrayInitialize(mordor, 0);
         
      ArrayInitialize(trophya, EMPTY_VALUE);
      ArrayInitialize(trophyb, EMPTY_VALUE);
      ArrayInitialize(trophyc, EMPTY_VALUE);
      ArrayInitialize(trophyd, EMPTY_VALUE);
      ArrayInitialize(trophye, EMPTY_VALUE);
      ArrayInitialize(trophyf, EMPTY_VALUE);
      ArrayInitialize(trophyg, EMPTY_VALUE);
      ArrayInitialize(trophyh, EMPTY_VALUE);
      ArrayInitialize(trophyi, EMPTY_VALUE);
      ArrayInitialize(euru, EMPTY_VALUE);
      ArrayInitialize(eurd, EMPTY_VALUE);
      ArrayInitialize(gbpu, EMPTY_VALUE);
      ArrayInitialize(gbpd, EMPTY_VALUE);
      ArrayInitialize(audu, EMPTY_VALUE);
      ArrayInitialize(audd, EMPTY_VALUE);
      ArrayInitialize(nzdu, EMPTY_VALUE);
      ArrayInitialize(nzdd, EMPTY_VALUE);
      ArrayInitialize(usdu, EMPTY_VALUE);
      ArrayInitialize(usdd, EMPTY_VALUE);
      ArrayInitialize(cadu, EMPTY_VALUE);
      ArrayInitialize(cadd, EMPTY_VALUE);
      ArrayInitialize(chfu, EMPTY_VALUE);
      ArrayInitialize(chfd, EMPTY_VALUE);
      ArrayInitialize(jpyu, EMPTY_VALUE);
      ArrayInitialize(jpyd, EMPTY_VALUE);
      ArrayInitialize(buysell2u, EMPTY_VALUE);
      ArrayInitialize(buysell2d, EMPTY_VALUE);
      ArrayInitialize(rec, EMPTY);
      ArrayInitialize(irec, EMPTY);
      
      int astart = 0, bstart = 0;
      
      for (int x = days; x >= 0; x--)
      {
         if (x == 0) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 0), false); bstart = 1; }
         if (x >= 1) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, x), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, x - 1), false); }
         /*if (x == 2) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 2), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 1), false); }
         if (x == 3) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 3), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 2), false); }
         if (x == 4) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 4), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 3), false); }
         if (x == 5) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 5), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 4), false); }
         if (x == 6) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 6), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 5), false); }
         if (x == 7) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 7), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 6), false); }
         if (x == 8) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 8), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 7), false); }
         if (x == 9) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 9), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 8), false); }
         if (x == 10) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 10), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 9), false); }
         if (x == 11) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 11), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 10), false); }
         if (x == 12) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 12), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 11), false); }
         if (x == 13) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 13), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 12), false); }
         if (x == 14) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 14), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 13), false); }
         if (x == 15) { astart = iBarShift(_Symbol, first, iTime(_Symbol, second, 15), false); bstart = iBarShift(_Symbol, first, iTime(_Symbol, second, 14), false); }*/
         
         ArrayResize(mordor, astart + 1);
      
         for(int i = astart; i >= bstart; i--)
         {
            eufopen = (iClose(eu, first, i) - iClose(eu, second, x + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
            egfopen = (iClose(eg, first, i) - iClose(eg, second, x + 1)) / MarketInfo(eg, MODE_POINT) / 10 * egpl;
            eafopen = (iClose(ea, first, i) - iClose(ea, second, x + 1)) / MarketInfo(ea, MODE_POINT) / 10 * eapl;
            enfopen = (iClose(en, first, i) - iClose(en, second, x + 1)) / MarketInfo(en, MODE_POINT) / 10 * enpl;
            ecfopen = (iClose(ec, first, i) - iClose(ec, second, x + 1)) / MarketInfo(ec, MODE_POINT) / 10 * ecpl;
            effopen = (iClose(ef, first, i) - iClose(ef, second, x + 1)) / MarketInfo(ef, MODE_POINT) / 10 * efpl;
            ejfopen = (iClose(ej, first, i) - iClose(ej, second, x + 1)) / MarketInfo(ej, MODE_POINT) / 10 * ejpl;
            gufopen = (iClose(gu, first, i) - iClose(gu, second, x + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
            gafopen = (iClose(ga, first, i) - iClose(ga, second, x + 1)) / MarketInfo(ga, MODE_POINT) / 10 * gapl;
            gnfopen = (iClose(gn, first, i) - iClose(gn, second, x + 1)) / MarketInfo(gn, MODE_POINT) / 10 * gnpl;
            gcfopen = (iClose(gc, first, i) - iClose(gc, second, x + 1)) / MarketInfo(gc, MODE_POINT) / 10 * gcpl;
            gffopen = (iClose(gf, first, i) - iClose(gf, second, x + 1)) / MarketInfo(gf, MODE_POINT) / 10 * gfpl;
            gjfopen = (iClose(gj, first, i) - iClose(gj, second, x + 1)) / MarketInfo(gj, MODE_POINT) / 10 * gjpl;
            aufopen = (iClose(au, first, i) - iClose(au, second, x + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
            anfopen = (iClose(an, first, i) - iClose(an, second, x + 1)) / MarketInfo(an, MODE_POINT) / 10 * anpl;
            acfopen = (iClose(ac, first, i) - iClose(ac, second, x + 1)) / MarketInfo(ac, MODE_POINT) / 10 * acpl;
            affopen = (iClose(af, first, i) - iClose(af, second, x + 1)) / MarketInfo(af, MODE_POINT) / 10 * afpl;
            ajfopen = (iClose(aj, first, i) - iClose(aj, second, x + 1)) / MarketInfo(aj, MODE_POINT) / 10 * ajpl;
            nufopen = (iClose(nu, first, i) - iClose(nu, second, x + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
            ncfopen = (iClose(nc, first, i) - iClose(nc, second, x + 1)) / MarketInfo(nc, MODE_POINT) / 10 * ncpl;
            nffopen = (iClose(nf, first, i) - iClose(nf, second, x + 1)) / MarketInfo(nf, MODE_POINT) / 10 * nfpl;
            njfopen = (iClose(nj, first, i) - iClose(nj, second, x + 1)) / MarketInfo(nj, MODE_POINT) / 10 * njpl;
            ucfopen = (iClose(uc, first, i) - iClose(uc, second, x + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
            cffopen = (iClose(cf, first, i) - iClose(cf, second, x + 1)) / MarketInfo(cf, MODE_POINT) / 10 * cfpl;
            cjfopen = (iClose(cj, first, i) - iClose(cj, second, x + 1)) / MarketInfo(cj, MODE_POINT) / 10 * cjpl;
            uffopen = (iClose(uf, first, i) - iClose(uf, second, x + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
            fjfopen = (iClose(fj, first, i) - iClose(fj, second, x + 1)) / MarketInfo(fj, MODE_POINT) / 10 * fjpl;
            ujfopen = (iClose(uj, first, i) - iClose(uj, second, x + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
            usfopen = ((50.14348112 * MathPow(iClose(eu, first, i), -0.576) * MathPow(iClose(uj, first, i), 0.136) * MathPow(iClose(gu, first, i), -0.119) * MathPow(iClose(uc, first, i), 0.091) * MathPow(iClose("USDSEK", first, i), 0.042) * MathPow(iClose(uf, first, i), 0.036)) - (50.14348112 * MathPow(iClose(eu, second, x + 1), -0.576) * MathPow(iClose(uj, second, x + 1), 0.136) * MathPow(iClose(gu, second, x + 1), -0.119) * MathPow(iClose(uc, second, x + 1), 0.091) * MathPow(iClose("USDSEK", second, x + 1), 0.042) * MathPow(iClose(uf, second, x + 1), 0.036))) / 0.001 / 10 * 10;
            
            if (nojpy) { ajfopen = 0; ujfopen = 0; cjfopen = 0; njfopen = 0; fjfopen = 0; ejfopen = 0; gjfopen = 0; }
            if (nochf) { affopen = 0; uffopen = 0; cffopen = 0; nffopen = 0; fjfopen = 0; effopen = 0; gffopen = 0; }
            if (noaud) { aufopen = 0; affopen = 0; acfopen = 0; anfopen = 0; ajfopen = 0; eafopen = 0; gafopen = 0; }
            if (nonzd) { nufopen = 0; nffopen = 0; ncfopen = 0; anfopen = 0; njfopen = 0; enfopen = 0; gnfopen = 0; }
            if (noeur) { eufopen = 0; effopen = 0; ecfopen = 0; enfopen = 0; ejfopen = 0; eafopen = 0; egfopen = 0; }
            if (nogbp) { gufopen = 0; gffopen = 0; gcfopen = 0; gnfopen = 0; gjfopen = 0; gafopen = 0; egfopen = 0; }
            if (nocad) { gcfopen = 0; cffopen = 0; ucfopen = 0; ncfopen = 0; acfopen = 0; ecfopen = 0; cjfopen = 0; }
            if (nousd) { gufopen = 0; uffopen = 0; ucfopen = 0; nufopen = 0; aufopen = 0; eufopen = 0; ujfopen = 0; }
            
            eur[astart] = 0;
            gbp[astart] = 0;
            aud[astart] = 0;
            nzd[astart] = 0;
            cad[astart] = 0;
            usd[astart] = 0;
            chf[astart] = 0;
            jpy[astart] = 0;
            rec[astart] = 0;
            eur[bstart] = EMPTY;
            gbp[bstart] = EMPTY;
            aud[bstart] = EMPTY;
            nzd[bstart] = EMPTY;
            cad[bstart] = EMPTY;
            usd[bstart] = EMPTY;
            chf[bstart] = EMPTY;
            jpy[bstart] = EMPTY;
            rec[bstart] = EMPTY;
            
            eur[i] = (eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen);
            gbp[i] = (gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen);
            aud[i] = (aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen);
            nzd[i] = (nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen);
            cad[i] = (- ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen);
            usd[i] = (- eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen);
            chf[i] = (- effopen - gffopen - affopen - nffopen - cffopen - uffopen + fjfopen);
            jpy[i] = (- ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen - ujfopen);
            
            if (noeur) ArrayInitialize(eur, 0);
            if (nogbp) ArrayInitialize(gbp, 0);
            if (noaud) ArrayInitialize(aud, 0);
            if (nonzd) ArrayInitialize(nzd, 0);
            if (nousd) ArrayInitialize(usd, 0);
            if (nocad) ArrayInitialize(cad, 0);
            if (nochf) ArrayInitialize(chf, 0);
            if (nojpy) ArrayInitialize(jpy, 0);
            
            if (MathAbs(eufopen) >= 250) mordor[i][0] = eufopen;
            if (MathAbs(egfopen) >= 250) mordor[i][1] = egfopen;
            if (MathAbs(eafopen) >= 250) mordor[i][2] = eafopen;
            if (MathAbs(enfopen) >= 250) mordor[i][3] = enfopen;
            if (MathAbs(ecfopen) >= 250) mordor[i][4] = ecfopen;
            if (MathAbs(effopen) >= 250) mordor[i][5] = effopen;
            if (MathAbs(ejfopen) >= 250) mordor[i][6] = ejfopen;
            if (MathAbs(gufopen) >= 250) mordor[i][7] = gufopen;
            if (MathAbs(gafopen) >= 250) mordor[i][8] = gafopen;
            if (MathAbs(gnfopen) >= 250) mordor[i][9] = gnfopen;
            if (MathAbs(gcfopen) >= 250) mordor[i][10] = gcfopen;
            if (MathAbs(gffopen) >= 250) mordor[i][11] = gffopen;
            if (MathAbs(gjfopen) >= 250) mordor[i][12] = gjfopen;
            if (MathAbs(aufopen) >= 250) mordor[i][13] = aufopen;
            if (MathAbs(anfopen) >= 250) mordor[i][14] = anfopen;
            if (MathAbs(acfopen) >= 250) mordor[i][15] = acfopen;
            if (MathAbs(affopen) >= 250) mordor[i][16] = affopen;
            if (MathAbs(ajfopen) >= 250) mordor[i][17] = ajfopen;
            if (MathAbs(nufopen) >= 250) mordor[i][18] = nufopen;
            if (MathAbs(ncfopen) >= 250) mordor[i][19] = ncfopen;
            if (MathAbs(nffopen) >= 250) mordor[i][20] = nffopen;
            if (MathAbs(njfopen) >= 250) mordor[i][21] = njfopen;
            if (MathAbs(ucfopen) >= 250) mordor[i][22] = ucfopen;
            if (MathAbs(cffopen) >= 250) mordor[i][23] = cffopen;
            if (MathAbs(cjfopen) >= 250) mordor[i][24] = cjfopen;
            if (MathAbs(uffopen) >= 250) mordor[i][25] = uffopen;
            if (MathAbs(fjfopen) >= 250) mordor[i][26] = fjfopen;
            if (MathAbs(ujfopen) >= 250) mordor[i][27] = ujfopen;
            
            for (int y = 27; y >= 0; y--)
            {
               rec[i] += MathAbs(mordor[i][y]);
               irec[i] = -rec[i];
            }
            
            /*
            eus[i] = eufopen;
            gus[i] = gufopen;
            aus[i] = aufopen;
            nus[i] = nufopen;
            cus[i] = -ucfopen;
            fus[i] = -uffopen;
            jus[i] = -ujfopen;
            usu[i] = usfopen;
            */
            
            /*
            buyside[i] = 0;
            if (eufopen >= 0) buyside[i] += eufopen;
            if (egfopen >= 0) buyside[i] += egfopen;
            if (eafopen >= 0) buyside[i] += eafopen;
            if (enfopen >= 0) buyside[i] += enfopen;
            if (ecfopen >= 0) buyside[i] += ecfopen;
            if (effopen >= 0) buyside[i] += effopen;
            if (ejfopen >= 0) buyside[i] += ejfopen;
            if (gufopen >= 0) buyside[i] += gufopen;
            if (gafopen >= 0) buyside[i] += gafopen;
            if (gnfopen >= 0) buyside[i] += gnfopen;
            if (gcfopen >= 0) buyside[i] += gcfopen;
            if (gffopen >= 0) buyside[i] += gffopen;
            if (gjfopen >= 0) buyside[i] += gjfopen;
            if (aufopen >= 0) buyside[i] += aufopen;
            if (anfopen >= 0) buyside[i] += anfopen;
            if (acfopen >= 0) buyside[i] += acfopen;
            if (affopen >= 0) buyside[i] += affopen;
            if (ajfopen >= 0) buyside[i] += ajfopen;
            if (nufopen >= 0) buyside[i] += nufopen;
            if (ncfopen >= 0) buyside[i] += ncfopen;
            if (nffopen >= 0) buyside[i] += nffopen;
            if (njfopen >= 0) buyside[i] += njfopen;
            if (ucfopen >= 0) buyside[i] += ucfopen;
            if (cffopen >= 0) buyside[i] += cffopen;
            if (cjfopen >= 0) buyside[i] += cjfopen;
            if (uffopen >= 0) buyside[i] += uffopen;
            if (fjfopen >= 0) buyside[i] += fjfopen;
            if (ujfopen >= 0) buyside[i] += ujfopen;
            
            sellside[i] = 0;
            if (eufopen < 0) sellside[i] += eufopen;
            if (egfopen < 0) sellside[i] += egfopen;
            if (eafopen < 0) sellside[i] += eafopen;
            if (enfopen < 0) sellside[i] += enfopen;
            if (ecfopen < 0) sellside[i] += ecfopen;
            if (effopen < 0) sellside[i] += effopen;
            if (ejfopen < 0) sellside[i] += ejfopen;
            if (gufopen < 0) sellside[i] += gufopen;
            if (gafopen < 0) sellside[i] += gafopen;
            if (gnfopen < 0) sellside[i] += gnfopen;
            if (gcfopen < 0) sellside[i] += gcfopen;
            if (gffopen < 0) sellside[i] += gffopen;
            if (gjfopen < 0) sellside[i] += gjfopen;
            if (aufopen < 0) sellside[i] += aufopen;
            if (anfopen < 0) sellside[i] += anfopen;
            if (acfopen < 0) sellside[i] += acfopen;
            if (affopen < 0) sellside[i] += affopen;
            if (ajfopen < 0) sellside[i] += ajfopen;
            if (nufopen < 0) sellside[i] += nufopen;
            if (ncfopen < 0) sellside[i] += ncfopen;
            if (nffopen < 0) sellside[i] += nffopen;
            if (njfopen < 0) sellside[i] += njfopen;
            if (ucfopen < 0) sellside[i] += ucfopen;
            if (cffopen < 0) sellside[i] += cffopen;
            if (cjfopen < 0) sellside[i] += cjfopen;
            if (uffopen < 0) sellside[i] += uffopen;
            if (fjfopen < 0) sellside[i] += fjfopen;
            if (ujfopen < 0) sellside[i] += ujfopen;
               
            buyside2[i] = 0;
            if (eufopen >= 0) buyside2[i] += eufopen;
            if (ecfopen >= 0) buyside2[i] += ecfopen;
            if (effopen >= 0) buyside2[i] += effopen;
            if (ejfopen >= 0) buyside2[i] += ejfopen;
            if (gufopen >= 0) buyside2[i] += gufopen;
            if (gcfopen >= 0) buyside2[i] += gcfopen;
            if (gffopen >= 0) buyside2[i] += gffopen;
            if (gjfopen >= 0) buyside2[i] += gjfopen;
            if (aufopen >= 0) buyside2[i] += aufopen;
            if (acfopen >= 0) buyside2[i] += acfopen;
            if (affopen >= 0) buyside2[i] += affopen;
            if (ajfopen >= 0) buyside2[i] += ajfopen;
            if (nufopen >= 0) buyside2[i] += nufopen;
            if (ncfopen >= 0) buyside2[i] += ncfopen;
            if (nffopen >= 0) buyside2[i] += nffopen;
            if (njfopen >= 0) buyside2[i] += njfopen;
            
            sellside2[i] = 0;
            if (eufopen < 0) sellside2[i] += eufopen;
            if (ecfopen < 0) sellside2[i] += ecfopen;
            if (effopen < 0) sellside2[i] += effopen;
            if (ejfopen < 0) sellside2[i] += ejfopen;
            if (gufopen < 0) sellside2[i] += gufopen;
            if (gcfopen < 0) sellside2[i] += gcfopen;
            if (gffopen < 0) sellside2[i] += gffopen;
            if (gjfopen < 0) sellside2[i] += gjfopen;
            if (aufopen < 0) sellside2[i] += aufopen;
            if (acfopen < 0) sellside2[i] += acfopen;
            if (affopen < 0) sellside2[i] += affopen;
            if (ajfopen < 0) sellside2[i] += ajfopen;
            if (nufopen < 0) sellside2[i] += nufopen;
            if (ncfopen < 0) sellside2[i] += ncfopen;
            if (nffopen < 0) sellside2[i] += nffopen;
            if (njfopen < 0) sellside2[i] += njfopen;
               
            buyside3[i] = 0;
            if (ecfopen >= 0) buyside3[i] += ecfopen;
            if (effopen >= 0) buyside3[i] += effopen;
            if (ejfopen >= 0) buyside3[i] += ejfopen;
            if (gcfopen >= 0) buyside3[i] += gcfopen;
            if (gffopen >= 0) buyside3[i] += gffopen;
            if (gjfopen >= 0) buyside3[i] += gjfopen;
            if (acfopen >= 0) buyside3[i] += acfopen;
            if (affopen >= 0) buyside3[i] += affopen;
            if (ajfopen >= 0) buyside3[i] += ajfopen;
            
            sellside3[i] = 0;
            if (ecfopen < 0) sellside3[i] += ecfopen;
            if (effopen < 0) sellside3[i] += effopen;
            if (ejfopen < 0) sellside3[i] += ejfopen;
            if (gcfopen < 0) sellside3[i] += gcfopen;
            if (gffopen < 0) sellside3[i] += gffopen;
            if (gjfopen < 0) sellside3[i] += gjfopen;
            if (acfopen < 0) sellside3[i] += acfopen;
            if (affopen < 0) sellside3[i] += affopen;
            if (ajfopen < 0) sellside3[i] += ajfopen;
            
            buysell[i] = 0;
            buysell[i] = (buyside[i] + sellside[i]) / 3;
            buysell2[i] = 0;
            buysell2[i] = (buyside2[i] + sellside2[i]) / 3;
            buysell3[i] = 0;
            buysell3[i] = (buyside3[i] + sellside3[i]) / 3;
            */
            
            buyside2[i] = 0;
            if (eufopen >= 0) buyside2[i] += eufopen;
            if (ecfopen >= 0) buyside2[i] += ecfopen;
            if (effopen >= 0) buyside2[i] += effopen;
            if (ejfopen >= 0) buyside2[i] += ejfopen;
            if (gufopen >= 0) buyside2[i] += gufopen;
            if (gcfopen >= 0) buyside2[i] += gcfopen;
            if (gffopen >= 0) buyside2[i] += gffopen;
            if (gjfopen >= 0) buyside2[i] += gjfopen;
            if (aufopen >= 0) buyside2[i] += aufopen;
            if (acfopen >= 0) buyside2[i] += acfopen;
            if (affopen >= 0) buyside2[i] += affopen;
            if (ajfopen >= 0) buyside2[i] += ajfopen;
            if (nufopen >= 0) buyside2[i] += nufopen;
            if (ncfopen >= 0) buyside2[i] += ncfopen;
            if (nffopen >= 0) buyside2[i] += nffopen;
            if (njfopen >= 0) buyside2[i] += njfopen;
            
            sellside2[i] = 0;
            if (eufopen < 0) sellside2[i] += eufopen;
            if (ecfopen < 0) sellside2[i] += ecfopen;
            if (effopen < 0) sellside2[i] += effopen;
            if (ejfopen < 0) sellside2[i] += ejfopen;
            if (gufopen < 0) sellside2[i] += gufopen;
            if (gcfopen < 0) sellside2[i] += gcfopen;
            if (gffopen < 0) sellside2[i] += gffopen;
            if (gjfopen < 0) sellside2[i] += gjfopen;
            if (aufopen < 0) sellside2[i] += aufopen;
            if (acfopen < 0) sellside2[i] += acfopen;
            if (affopen < 0) sellside2[i] += affopen;
            if (ajfopen < 0) sellside2[i] += ajfopen;
            if (nufopen < 0) sellside2[i] += nufopen;
            if (ncfopen < 0) sellside2[i] += ncfopen;
            if (nffopen < 0) sellside2[i] += nffopen;
            if (njfopen < 0) sellside2[i] += njfopen;
            
            buysell2[i] = 0;
            buysell2[i] = (buyside2[i] + sellside2[i]) / 3;
         }
      }
      check = false;
   }
   {
      double mordor[1][28];
      ArrayInitialize(mordor, 0);
      rec[0] = 0;
      
      eufopen = (iClose(eu, first, 0) - iClose(eu, second, 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
      egfopen = (iClose(eg, first, 0) - iClose(eg, second, 1)) / MarketInfo(eg, MODE_POINT) / 10 * egpl;
      eafopen = (iClose(ea, first, 0) - iClose(ea, second, 1)) / MarketInfo(ea, MODE_POINT) / 10 * eapl;
      enfopen = (iClose(en, first, 0) - iClose(en, second, 1)) / MarketInfo(en, MODE_POINT) / 10 * enpl;
      ecfopen = (iClose(ec, first, 0) - iClose(ec, second, 1)) / MarketInfo(ec, MODE_POINT) / 10 * ecpl;
      effopen = (iClose(ef, first, 0) - iClose(ef, second, 1)) / MarketInfo(ef, MODE_POINT) / 10 * efpl;
      ejfopen = (iClose(ej, first, 0) - iClose(ej, second, 1)) / MarketInfo(ej, MODE_POINT) / 10 * ejpl;
      gufopen = (iClose(gu, first, 0) - iClose(gu, second, 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
      gafopen = (iClose(ga, first, 0) - iClose(ga, second, 1)) / MarketInfo(ga, MODE_POINT) / 10 * gapl;
      gnfopen = (iClose(gn, first, 0) - iClose(gn, second, 1)) / MarketInfo(gn, MODE_POINT) / 10 * gnpl;
      gcfopen = (iClose(gc, first, 0) - iClose(gc, second, 1)) / MarketInfo(gc, MODE_POINT) / 10 * gcpl;
      gffopen = (iClose(gf, first, 0) - iClose(gf, second, 1)) / MarketInfo(gf, MODE_POINT) / 10 * gfpl;
      gjfopen = (iClose(gj, first, 0) - iClose(gj, second, 1)) / MarketInfo(gj, MODE_POINT) / 10 * gjpl;
      aufopen = (iClose(au, first, 0) - iClose(au, second, 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
      anfopen = (iClose(an, first, 0) - iClose(an, second, 1)) / MarketInfo(an, MODE_POINT) / 10 * anpl;
      acfopen = (iClose(ac, first, 0) - iClose(ac, second, 1)) / MarketInfo(ac, MODE_POINT) / 10 * acpl;
      affopen = (iClose(af, first, 0) - iClose(af, second, 1)) / MarketInfo(af, MODE_POINT) / 10 * afpl;
      ajfopen = (iClose(aj, first, 0) - iClose(aj, second, 1)) / MarketInfo(aj, MODE_POINT) / 10 * ajpl;
      nufopen = (iClose(nu, first, 0) - iClose(nu, second, 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
      ncfopen = (iClose(nc, first, 0) - iClose(nc, second, 1)) / MarketInfo(nc, MODE_POINT) / 10 * ncpl;
      nffopen = (iClose(nf, first, 0) - iClose(nf, second, 1)) / MarketInfo(nf, MODE_POINT) / 10 * nfpl;
      njfopen = (iClose(nj, first, 0) - iClose(nj, second, 1)) / MarketInfo(nj, MODE_POINT) / 10 * njpl;
      ucfopen = (iClose(uc, first, 0) - iClose(uc, second, 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
      cffopen = (iClose(cf, first, 0) - iClose(cf, second, 1)) / MarketInfo(cf, MODE_POINT) / 10 * cfpl;
      cjfopen = (iClose(cj, first, 0) - iClose(cj, second, 1)) / MarketInfo(cj, MODE_POINT) / 10 * cjpl;
      uffopen = (iClose(uf, first, 0) - iClose(uf, second, 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
      fjfopen = (iClose(fj, first, 0) - iClose(fj, second, 1)) / MarketInfo(fj, MODE_POINT) / 10 * fjpl;
      ujfopen = (iClose(uj, first, 0) - iClose(uj, second, 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
      usfopen = usfopen = ((50.14348112 * MathPow(iClose(eu, first, 0), -0.576) * MathPow(iClose(uj, first, 0), 0.136) * MathPow(iClose(gu, first, 0), -0.119) * MathPow(iClose(uc, first, 0), 0.091) * MathPow(iClose("USDSEK", first, 0), 0.042) * MathPow(iClose(uf, first, 0), 0.036)) - (50.14348112 * MathPow(iClose(eu, second, 1), -0.576) * MathPow(iClose(uj, second, 1), 0.136) * MathPow(iClose(gu, second, 1), -0.119) * MathPow(iClose(uc, second, 1), 0.091) * MathPow(iClose("USDSEK", second, 1), 0.042) * MathPow(iClose(uf, second, 1), 0.036))) / 0.001 / 10 * 10;
      
      eufopen1 = (iClose(eu, first, 0) - iClose(eu, second, days + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
      egfopen1 = (iClose(eg, first, 0) - iClose(eg, second, days + 1)) / MarketInfo(eg, MODE_POINT) / 10 * egpl;
      eafopen1 = (iClose(ea, first, 0) - iClose(ea, second, days + 1)) / MarketInfo(ea, MODE_POINT) / 10 * eapl;
      enfopen1 = (iClose(en, first, 0) - iClose(en, second, days + 1)) / MarketInfo(en, MODE_POINT) / 10 * enpl;
      ecfopen1 = (iClose(ec, first, 0) - iClose(ec, second, days + 1)) / MarketInfo(ec, MODE_POINT) / 10 * ecpl;
      effopen1 = (iClose(ef, first, 0) - iClose(ef, second, days + 1)) / MarketInfo(ef, MODE_POINT) / 10 * efpl;
      ejfopen1 = (iClose(ej, first, 0) - iClose(ej, second, days + 1)) / MarketInfo(ej, MODE_POINT) / 10 * ejpl;
      gufopen1 = (iClose(gu, first, 0) - iClose(gu, second, days + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
      gafopen1 = (iClose(ga, first, 0) - iClose(ga, second, days + 1)) / MarketInfo(ga, MODE_POINT) / 10 * gapl;
      gnfopen1 = (iClose(gn, first, 0) - iClose(gn, second, days + 1)) / MarketInfo(gn, MODE_POINT) / 10 * gnpl;
      gcfopen1 = (iClose(gc, first, 0) - iClose(gc, second, days + 1)) / MarketInfo(gc, MODE_POINT) / 10 * gcpl;
      gffopen1 = (iClose(gf, first, 0) - iClose(gf, second, days + 1)) / MarketInfo(gf, MODE_POINT) / 10 * gfpl;
      gjfopen1 = (iClose(gj, first, 0) - iClose(gj, second, days + 1)) / MarketInfo(gj, MODE_POINT) / 10 * gjpl;
      aufopen1 = (iClose(au, first, 0) - iClose(au, second, days + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
      anfopen1 = (iClose(an, first, 0) - iClose(an, second, days + 1)) / MarketInfo(an, MODE_POINT) / 10 * anpl;
      acfopen1 = (iClose(ac, first, 0) - iClose(ac, second, days + 1)) / MarketInfo(ac, MODE_POINT) / 10 * acpl;
      affopen1 = (iClose(af, first, 0) - iClose(af, second, days + 1)) / MarketInfo(af, MODE_POINT) / 10 * afpl;
      ajfopen1 = (iClose(aj, first, 0) - iClose(aj, second, days + 1)) / MarketInfo(aj, MODE_POINT) / 10 * ajpl;
      nufopen1 = (iClose(nu, first, 0) - iClose(nu, second, days + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
      ncfopen1 = (iClose(nc, first, 0) - iClose(nc, second, days + 1)) / MarketInfo(nc, MODE_POINT) / 10 * ncpl;
      nffopen1 = (iClose(nf, first, 0) - iClose(nf, second, days + 1)) / MarketInfo(nf, MODE_POINT) / 10 * nfpl;
      njfopen1 = (iClose(nj, first, 0) - iClose(nj, second, days + 1)) / MarketInfo(nj, MODE_POINT) / 10 * njpl;
      ucfopen1 = (iClose(uc, first, 0) - iClose(uc, second, days + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
      cffopen1 = (iClose(cf, first, 0) - iClose(cf, second, days + 1)) / MarketInfo(cf, MODE_POINT) / 10 * cfpl;
      cjfopen1 = (iClose(cj, first, 0) - iClose(cj, second, days + 1)) / MarketInfo(cj, MODE_POINT) / 10 * cjpl;
      uffopen1 = (iClose(uf, first, 0) - iClose(uf, second, days + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
      fjfopen1 = (iClose(fj, first, 0) - iClose(fj, second, days + 1)) / MarketInfo(fj, MODE_POINT) / 10 * fjpl;
      ujfopen1 = (iClose(uj, first, 0) - iClose(uj, second, days + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
      
      if (nojpy) { ajfopen = 0; ujfopen = 0; cjfopen = 0; njfopen = 0; fjfopen = 0; ejfopen = 0; gjfopen = 0; }
      if (nochf) { affopen = 0; uffopen = 0; cffopen = 0; nffopen = 0; fjfopen = 0; effopen = 0; gffopen = 0; }
      if (noaud) { aufopen = 0; affopen = 0; acfopen = 0; anfopen = 0; ajfopen = 0; eafopen = 0; gafopen = 0; }
      if (nonzd) { nufopen = 0; nffopen = 0; ncfopen = 0; anfopen = 0; njfopen = 0; enfopen = 0; gnfopen = 0; }
      if (noeur) { eufopen = 0; effopen = 0; ecfopen = 0; enfopen = 0; ejfopen = 0; eafopen = 0; egfopen = 0; }
      if (nogbp) { gufopen = 0; gffopen = 0; gcfopen = 0; gnfopen = 0; gjfopen = 0; gafopen = 0; egfopen = 0; }
      if (nocad) { gcfopen = 0; cffopen = 0; ucfopen = 0; ncfopen = 0; acfopen = 0; ecfopen = 0; cjfopen = 0; }
      if (nousd) { gufopen = 0; uffopen = 0; ucfopen = 0; nufopen = 0; aufopen = 0; eufopen = 0; ujfopen = 0; }
      
      if (nojpy) { ajfopen1 = 0; ujfopen1 = 0; cjfopen1 = 0; njfopen1 = 0; fjfopen1 = 0; ejfopen1 = 0; gjfopen1 = 0; }
      if (nochf) { affopen1 = 0; uffopen1 = 0; cffopen1 = 0; nffopen1 = 0; fjfopen1 = 0; effopen1 = 0; gffopen1 = 0; }
      if (noaud) { aufopen1 = 0; affopen1 = 0; acfopen1 = 0; anfopen1 = 0; ajfopen1 = 0; eafopen1 = 0; gafopen1 = 0; }
      if (nonzd) { nufopen1 = 0; nffopen1 = 0; ncfopen1 = 0; anfopen1 = 0; njfopen1 = 0; enfopen1 = 0; gnfopen1 = 0; }
      if (noeur) { eufopen1 = 0; effopen1 = 0; ecfopen1 = 0; enfopen1 = 0; ejfopen1 = 0; eafopen1 = 0; egfopen1 = 0; }
      if (nogbp) { gufopen1 = 0; gffopen1 = 0; gcfopen1 = 0; gnfopen1 = 0; gjfopen1 = 0; gafopen1 = 0; egfopen1 = 0; }
      if (nocad) { gcfopen1 = 0; cffopen1 = 0; ucfopen1 = 0; ncfopen1 = 0; acfopen1 = 0; ecfopen1 = 0; cjfopen1 = 0; }
      if (nousd) { gufopen1 = 0; uffopen1 = 0; ucfopen1 = 0; nufopen1 = 0; aufopen1 = 0; eufopen1 = 0; ujfopen1 = 0; }
            
      eur[0] = (eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen);
      gbp[0] = (gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen);
      aud[0] = (aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen);
      nzd[0] = (nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen);
      cad[0] = (- ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen);
      usd[0] = (- eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen);
      chf[0] = (- effopen - gffopen - affopen - nffopen - cffopen - uffopen + fjfopen);
      jpy[0] = (- ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen - ujfopen);
      
      if (noeur) ArrayInitialize(eur, 0);
      if (nogbp) ArrayInitialize(gbp, 0);
      if (noaud) ArrayInitialize(aud, 0);
      if (nonzd) ArrayInitialize(nzd, 0);
      if (nousd) ArrayInitialize(usd, 0);
      if (nocad) ArrayInitialize(cad, 0);
      if (nochf) ArrayInitialize(chf, 0);
      if (nojpy) ArrayInitialize(jpy, 0);
      
      eura = (eufopen1 + egfopen1 + eafopen1 + enfopen1 + ecfopen1 + effopen1 + ejfopen1);
      gbpa = (gufopen1 - egfopen1 + gafopen1 + gnfopen1 + gcfopen1 + gffopen1 + gjfopen1);
      auda = (aufopen1 - eafopen1 - gafopen1 + anfopen1 + acfopen1 + affopen1 + ajfopen1);
      nzda = (nufopen1 - enfopen1 - gnfopen1 - anfopen1 + ncfopen1 + nffopen1 + njfopen1);
      cada = (- ucfopen1 - ecfopen1 - gcfopen1 - acfopen1 - ncfopen1 + cffopen1 + cjfopen1);
      usda = (- eufopen1 - gufopen1 - aufopen1 - nufopen1 + ucfopen1 + uffopen1 + ujfopen1);
      chfa = (- effopen1 - gffopen1 - affopen1 - nffopen1 - cffopen1 - uffopen1 + fjfopen1);
      jpya = (- ejfopen1 - gjfopen1 - ajfopen1 - njfopen1 - cjfopen1 - fjfopen1 - ujfopen1);
      //reca = -ajfopen - uffopen - ucfopen + aufopen - cjfopen - fjfopen - ujfopen - gjfopen - ejfopen + gufopen + eufopen;
      
      if (MathAbs(eufopen) >= 250) mordor[0][0] = eufopen;
      if (MathAbs(egfopen) >= 250) mordor[0][1] = egfopen;
      if (MathAbs(eafopen) >= 250) mordor[0][2] = eafopen;
      if (MathAbs(enfopen) >= 250) mordor[0][3] = enfopen;
      if (MathAbs(ecfopen) >= 250) mordor[0][4] = ecfopen;
      if (MathAbs(effopen) >= 250) mordor[0][5] = effopen;
      if (MathAbs(ejfopen) >= 250) mordor[0][6] = ejfopen;
      if (MathAbs(gufopen) >= 250) mordor[0][7] = gufopen;
      if (MathAbs(gafopen) >= 250) mordor[0][8] = gafopen;
      if (MathAbs(gnfopen) >= 250) mordor[0][9] = gnfopen;
      if (MathAbs(gcfopen) >= 250) mordor[0][10] = gcfopen;
      if (MathAbs(gffopen) >= 250) mordor[0][11] = gffopen;
      if (MathAbs(gjfopen) >= 250) mordor[0][12] = gjfopen;
      if (MathAbs(aufopen) >= 250) mordor[0][13] = aufopen;
      if (MathAbs(anfopen) >= 250) mordor[0][14] = anfopen;
      if (MathAbs(acfopen) >= 250) mordor[0][15] = acfopen;
      if (MathAbs(affopen) >= 250) mordor[0][16] = affopen;
      if (MathAbs(ajfopen) >= 250) mordor[0][17] = ajfopen;
      if (MathAbs(nufopen) >= 250) mordor[0][18] = nufopen;
      if (MathAbs(ncfopen) >= 250) mordor[0][19] = ncfopen;
      if (MathAbs(nffopen) >= 250) mordor[0][20] = nffopen;
      if (MathAbs(njfopen) >= 250) mordor[0][21] = njfopen;
      if (MathAbs(ucfopen) >= 250) mordor[0][22] = ucfopen;
      if (MathAbs(cffopen) >= 250) mordor[0][23] = cffopen;
      if (MathAbs(cjfopen) >= 250) mordor[0][24] = cjfopen;
      if (MathAbs(uffopen) >= 250) mordor[0][25] = uffopen;
      if (MathAbs(fjfopen) >= 250) mordor[0][26] = fjfopen;
      if (MathAbs(ujfopen) >= 250) mordor[0][27] = ujfopen;
      
      for (int y = 27; y >= 0; y--)
      {
         rec[0] += MathAbs(mordor[0][y]);
         irec[0] = -rec[0];
         //Print(mordor[astart][y]);
      }
      
      double max1 = MathMax(MathAbs(eura), MathAbs(gbpa));
      double max2 = MathMax(MathAbs(auda), MathAbs(nzda));
      double max3 = MathMax(MathAbs(cada), MathAbs(usda));
      double max4 = MathMax(MathAbs(chfa), MathAbs(jpya));
      
      double max5 = MathMax(max1, max2);
      double max6 = MathMax(max3, max4);
      
      double max7 = MathMax(max5, max6);
      
      reca = rec[0] / max7;
      
      double bsbuy = 0;
      if (eufopen1 >= 0) bsbuy += eufopen1;
      if (ecfopen1 >= 0) bsbuy += ecfopen1;
      if (effopen1 >= 0) bsbuy += effopen1;
      if (ejfopen1 >= 0) bsbuy += ejfopen1;
      if (gufopen1 >= 0) bsbuy += gufopen1;
      if (gcfopen1 >= 0) bsbuy += gcfopen1;
      if (gffopen1 >= 0) bsbuy += gffopen1;
      if (gjfopen1 >= 0) bsbuy += gjfopen1;
      if (aufopen1 >= 0) bsbuy += aufopen1;
      if (acfopen1 >= 0) bsbuy += acfopen1;
      if (affopen1 >= 0) bsbuy += affopen1;
      if (ajfopen1 >= 0) bsbuy += ajfopen1;
      if (nufopen1 >= 0) bsbuy += nufopen1;
      if (ncfopen1 >= 0) bsbuy += ncfopen1;
      if (nffopen1 >= 0) bsbuy += nffopen1;
      if (njfopen1 >= 0) bsbuy += njfopen1;
      
      double bssell = 0;
      if (eufopen1 < 0) bssell += eufopen1;
      if (ecfopen1 < 0) bssell += ecfopen1;
      if (effopen1 < 0) bssell += effopen1;
      if (ejfopen1 < 0) bssell += ejfopen1;
      if (gufopen1 < 0) bssell += gufopen1;
      if (gcfopen1 < 0) bssell += gcfopen1;
      if (gffopen1 < 0) bssell += gffopen1;
      if (gjfopen1 < 0) bssell += gjfopen1;
      if (aufopen1 < 0) bssell += aufopen1;
      if (acfopen1 < 0) bssell += acfopen1;
      if (affopen1 < 0) bssell += affopen1;
      if (ajfopen1 < 0) bssell += ajfopen1;
      if (nufopen1 < 0) bssell += nufopen1;
      if (ncfopen1 < 0) bssell += ncfopen1;
      if (nffopen1 < 0) bssell += nffopen1;
      if (njfopen1 < 0) bssell += njfopen1;
      
      bsa = (bsbuy + bssell) / 3;
      
      ObjectSetString(0, Name + " EUR", OBJPROP_TEXT, "EUR "+DoubleToString(eura, 0));
      ObjectSetString(0, Name + " GBP", OBJPROP_TEXT, "GBP "+DoubleToString(gbpa, 0));
      ObjectSetString(0, Name + " AUD", OBJPROP_TEXT, "AUD "+DoubleToString(auda, 0));
      ObjectSetString(0, Name + " NZD", OBJPROP_TEXT, "NZD "+DoubleToString(nzda, 0));
      ObjectSetString(0, Name + " CAD", OBJPROP_TEXT, "CAD "+DoubleToString(cada, 0));
      ObjectSetString(0, Name + " USD", OBJPROP_TEXT, "USD "+DoubleToString(usda, 0));
      ObjectSetString(0, Name + " CHF", OBJPROP_TEXT, "CHF "+DoubleToString(chfa, 0));
      ObjectSetString(0, Name + " JPY", OBJPROP_TEXT, "JPY "+DoubleToString(jpya, 0));
      ObjectSetString(0, Name + " BS", OBJPROP_TEXT, "BS "+DoubleToString(bsa, 0));
      ObjectSetString(0, Name + " REC", OBJPROP_TEXT, "REC "+DoubleToString(reca, 2));
      
      /*
      eus[0] = eufopen;
      gus[0] = gufopen;
      aus[0] = aufopen;
      nus[0] = nufopen;
      cus[0] = -ucfopen;
      fus[0] = -uffopen;
      jus[0] = -ujfopen;
      usu[0] = usfopen;
      */
      
      /*
      buyside[0] = 0;
      if (eufopen >= 0) buyside[0] += eufopen;
      if (egfopen >= 0) buyside[0] += egfopen;
      if (eafopen >= 0) buyside[0] += eafopen;
      if (enfopen >= 0) buyside[0] += enfopen;
      if (ecfopen >= 0) buyside[0] += ecfopen;
      if (effopen >= 0) buyside[0] += effopen;
      if (ejfopen >= 0) buyside[0] += ejfopen;
      if (gufopen >= 0) buyside[0] += gufopen;
      if (gafopen >= 0) buyside[0] += gafopen;
      if (gnfopen >= 0) buyside[0] += gnfopen;
      if (gcfopen >= 0) buyside[0] += gcfopen;
      if (gffopen >= 0) buyside[0] += gffopen;
      if (gjfopen >= 0) buyside[0] += gjfopen;
      if (aufopen >= 0) buyside[0] += aufopen;
      if (anfopen >= 0) buyside[0] += anfopen;
      if (acfopen >= 0) buyside[0] += acfopen;
      if (affopen >= 0) buyside[0] += affopen;
      if (ajfopen >= 0) buyside[0] += ajfopen;
      if (nufopen >= 0) buyside[0] += nufopen;
      if (ncfopen >= 0) buyside[0] += ncfopen;
      if (nffopen >= 0) buyside[0] += nffopen;
      if (njfopen >= 0) buyside[0] += njfopen;
      if (ucfopen >= 0) buyside[0] += ucfopen;
      if (cffopen >= 0) buyside[0] += cffopen;
      if (cjfopen >= 0) buyside[0] += cjfopen;
      if (uffopen >= 0) buyside[0] += uffopen;
      if (fjfopen >= 0) buyside[0] += fjfopen;
      if (ujfopen >= 0) buyside[0] += ujfopen;
      
      sellside[0] = 0;
      if (eufopen < 0) sellside[0] += eufopen;
      if (egfopen < 0) sellside[0] += egfopen;
      if (eafopen < 0) sellside[0] += eafopen;
      if (enfopen < 0) sellside[0] += enfopen;
      if (ecfopen < 0) sellside[0] += ecfopen;
      if (effopen < 0) sellside[0] += effopen;
      if (ejfopen < 0) sellside[0] += ejfopen;
      if (gufopen < 0) sellside[0] += gufopen;
      if (gafopen < 0) sellside[0] += gafopen;
      if (gnfopen < 0) sellside[0] += gnfopen;
      if (gcfopen < 0) sellside[0] += gcfopen;
      if (gffopen < 0) sellside[0] += gffopen;
      if (gjfopen < 0) sellside[0] += gjfopen;
      if (aufopen < 0) sellside[0] += aufopen;
      if (anfopen < 0) sellside[0] += anfopen;
      if (acfopen < 0) sellside[0] += acfopen;
      if (affopen < 0) sellside[0] += affopen;
      if (ajfopen < 0) sellside[0] += ajfopen;
      if (nufopen < 0) sellside[0] += nufopen;
      if (ncfopen < 0) sellside[0] += ncfopen;
      if (nffopen < 0) sellside[0] += nffopen;
      if (njfopen < 0) sellside[0] += njfopen;
      if (ucfopen < 0) sellside[0] += ucfopen;
      if (cffopen < 0) sellside[0] += cffopen;
      if (cjfopen < 0) sellside[0] += cjfopen;
      if (uffopen < 0) sellside[0] += uffopen;
      if (fjfopen < 0) sellside[0] += fjfopen;
      if (ujfopen < 0) sellside[0] += ujfopen;
      
      buyside2[0] = 0;
      if (eufopen >= 0) buyside2[0] += eufopen;
      if (ecfopen >= 0) buyside2[0] += ecfopen;
      if (effopen >= 0) buyside2[0] += effopen;
      if (ejfopen >= 0) buyside2[0] += ejfopen;
      if (gufopen >= 0) buyside2[0] += gufopen;
      if (gcfopen >= 0) buyside2[0] += gcfopen;
      if (gffopen >= 0) buyside2[0] += gffopen;
      if (gjfopen >= 0) buyside2[0] += gjfopen;
      if (aufopen >= 0) buyside2[0] += aufopen;
      if (acfopen >= 0) buyside2[0] += acfopen;
      if (affopen >= 0) buyside2[0] += affopen;
      if (ajfopen >= 0) buyside2[0] += ajfopen;
      if (nufopen >= 0) buyside2[0] += nufopen;
      if (ncfopen >= 0) buyside2[0] += ncfopen;
      if (nffopen >= 0) buyside2[0] += nffopen;
      if (njfopen >= 0) buyside2[0] += njfopen;
      
      sellside2[0] = 0;
      if (eufopen < 0) sellside2[0] += eufopen;
      if (ecfopen < 0) sellside2[0] += ecfopen;
      if (effopen < 0) sellside2[0] += effopen;
      if (ejfopen < 0) sellside2[0] += ejfopen;
      if (gufopen < 0) sellside2[0] += gufopen;
      if (gcfopen < 0) sellside2[0] += gcfopen;
      if (gffopen < 0) sellside2[0] += gffopen;
      if (gjfopen < 0) sellside2[0] += gjfopen;
      if (aufopen < 0) sellside2[0] += aufopen;
      if (acfopen < 0) sellside2[0] += acfopen;
      if (affopen < 0) sellside2[0] += affopen;
      if (ajfopen < 0) sellside2[0] += ajfopen;
      if (nufopen < 0) sellside2[0] += nufopen;
      if (ncfopen < 0) sellside2[0] += ncfopen;
      if (nffopen < 0) sellside2[0] += nffopen;
      if (njfopen < 0) sellside2[0] += njfopen;
      
      buyside3[0] = 0;
      if (ecfopen >= 0) buyside3[0] += ecfopen;
      if (effopen >= 0) buyside3[0] += effopen;
      if (ejfopen >= 0) buyside3[0] += ejfopen;
      if (gcfopen >= 0) buyside3[0] += gcfopen;
      if (gffopen >= 0) buyside3[0] += gffopen;
      if (gjfopen >= 0) buyside3[0] += gjfopen;
      if (acfopen >= 0) buyside3[0] += acfopen;
      if (affopen >= 0) buyside3[0] += affopen;
      if (ajfopen >= 0) buyside3[0] += ajfopen;
      
      sellside3[0] = 0;
      if (ecfopen < 0) sellside3[0] += ecfopen;
      if (effopen < 0) sellside3[0] += effopen;
      if (ejfopen < 0) sellside3[0] += ejfopen;
      if (gcfopen < 0) sellside3[0] += gcfopen;
      if (gffopen < 0) sellside3[0] += gffopen;
      if (gjfopen < 0) sellside3[0] += gjfopen;
      if (acfopen < 0) sellside3[0] += acfopen;
      if (affopen < 0) sellside3[0] += affopen;
      if (ajfopen < 0) sellside3[0] += ajfopen;
            
      buysell[0] = 0;
      buysell[0] = (buyside[0] + sellside[0]) / 3;
      buysell2[0] = 0;
      buysell2[0] = (buyside2[0] + sellside2[0]) / 3;
      buysell3[0] = 0;
      buysell3[0] = (buyside3[0] + sellside3[0]) / 3;
      */
      
      buyside2[0] = 0;
      if (eufopen >= 0) buyside2[0] += eufopen;
      if (ecfopen >= 0) buyside2[0] += ecfopen;
      if (effopen >= 0) buyside2[0] += effopen;
      if (ejfopen >= 0) buyside2[0] += ejfopen;
      if (gufopen >= 0) buyside2[0] += gufopen;
      if (gcfopen >= 0) buyside2[0] += gcfopen;
      if (gffopen >= 0) buyside2[0] += gffopen;
      if (gjfopen >= 0) buyside2[0] += gjfopen;
      if (aufopen >= 0) buyside2[0] += aufopen;
      if (acfopen >= 0) buyside2[0] += acfopen;
      if (affopen >= 0) buyside2[0] += affopen;
      if (ajfopen >= 0) buyside2[0] += ajfopen;
      if (nufopen >= 0) buyside2[0] += nufopen;
      if (ncfopen >= 0) buyside2[0] += ncfopen;
      if (nffopen >= 0) buyside2[0] += nffopen;
      if (njfopen >= 0) buyside2[0] += njfopen;
      
      sellside2[0] = 0;
      if (eufopen < 0) sellside2[0] += eufopen;
      if (ecfopen < 0) sellside2[0] += ecfopen;
      if (effopen < 0) sellside2[0] += effopen;
      if (ejfopen < 0) sellside2[0] += ejfopen;
      if (gufopen < 0) sellside2[0] += gufopen;
      if (gcfopen < 0) sellside2[0] += gcfopen;
      if (gffopen < 0) sellside2[0] += gffopen;
      if (gjfopen < 0) sellside2[0] += gjfopen;
      if (aufopen < 0) sellside2[0] += aufopen;
      if (acfopen < 0) sellside2[0] += acfopen;
      if (affopen < 0) sellside2[0] += affopen;
      if (ajfopen < 0) sellside2[0] += ajfopen;
      if (nufopen < 0) sellside2[0] += nufopen;
      if (ncfopen < 0) sellside2[0] += ncfopen;
      if (nffopen < 0) sellside2[0] += nffopen;
      if (njfopen < 0) sellside2[0] += njfopen;
      
      buysell2[0] = 0;
      buysell2[0] = (buyside2[0] + sellside2[0]) / 3;
      
      //rec[0] = -ajfopen - uffopen - ucfopen + aufopen - cjfopen - fjfopen - ujfopen - gjfopen - ejfopen + gufopen + eufopen;
   }

   static datetime checktime2 = 0;
   bool check2 = false;
   if (checktime2 < iTime(_Symbol, PERIOD_M5, 0))
   {
      checktime2 = iTime(_Symbol, PERIOD_M5, 0);
      check2 = true;
   }
   if (check2)
   {
      if (Hour() >= 5)
      {
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += eur[j];
               isumxy += eur[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophya[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += eur[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(eur[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            euru[j] = trophya[j] + 1 * cd;
            eurd[j] = trophya[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += gbp[j];
               isumxy += gbp[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyb[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += gbp[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(gbp[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            gbpu[j] = trophyb[j] + 1 * cd;
            gbpd[j] = trophyb[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += aud[j];
               isumxy += aud[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyc[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += aud[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(aud[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            audu[j] = trophyc[j] + 1 * cd;
            audd[j] = trophyc[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += nzd[j];
               isumxy += nzd[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyd[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += nzd[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(nzd[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            nzdu[j] = trophyd[j] + 1 * cd;
            nzdd[j] = trophyd[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += usd[j];
               isumxy += usd[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophye[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += usd[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(usd[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            usdu[j] = trophye[j] + 1 * cd;
            usdd[j] = trophye[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += cad[j];
               isumxy += cad[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyf[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += cad[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(cad[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            cadu[j] = trophyf[j] + 1 * cd;
            cadd[j] = trophyf[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += chf[j];
               isumxy += chf[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyg[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += chf[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(chf[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            chfu[j] = trophyg[j] + 1 * cd;
            chfd[j] = trophyg[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += jpy[j];
               isumxy += jpy[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyh[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += jpy[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(jpy[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            jpyu[j] = trophyh[j] + 1 * cd;
            jpyd[j] = trophyh[j] - 1 * cd;
         }
         //LR regression lines on adr bands
         {
            int timesb = lrlength;
            if (timesb > limit) timesb = limit - 1;
            double a = 0.0, b = 0.0, c = 0.0;
            double isumx = 0.0, isumy = 0.0;
            double isumxy = 0.0, isumx2 = 0.0;
            double h = 0.0, l = 0.0;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               isumy += buysell2[j];
               isumxy += buysell2[j] * j;
               isumx += j;
               isumx2 += j * j;
            }
            
            c = isumx2 * timesb - isumx * isumx;
            a = (isumxy * timesb - isumx * isumy) / c;
            b = (isumy - isumx * a) / timesb;
            
            for (int j = 0; j < timesb; j++)
            //for (int j = (shift + timesb - 1); j >= 0 + shift; j--)
            {
               //trophymid1[j] = a * j + b + range;
               trophyi[j] = a * j + b;
               //trophymid2[j] = a * j + b - range;
            }
         }
         for (int j = lrlength - 1; j >= 0; j--)  
         {
            //Inside SD calculation
            double ab = 0;
            double bc = 0;
            double cd = 0;
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               ab += buysell2[x] / lrlength;
            }
            
            for (int x = lrlength - 1 + j; x >= j; x --){
               bc += MathPow(buysell2[x] - ab, 2);
            }
            
            cd = MathSqrt(bc/lrlength);
            
            buysell2u[j] = trophyi[j] + 1 * cd;
            buysell2d[j] = trophyi[j] - 1 * cd;
         }
      }
   
      if (eur[1] > euru[1] && eur[2] < euru[2] && eur[1] < 0) { Alert("Bearish EUR is hitting upper sd *SELL*"); spotdn[0] = 1; }
      if (eur[1] < eurd[1] && eur[2] > eurd[2] && eur[1] > 0) { Alert("Bullish EUR is hitting lower sd *BUY*"); spotup[0] = 1; }
      if (gbp[1] > gbpu[1] && gbp[2] < gbpu[2] && gbp[1] < 0) { Alert("Bearish GBP is hitting upper sd *SELL*"); spotdn[0] = 2; }
      if (gbp[1] < gbpd[1] && gbp[2] > gbpd[2] && gbp[1] > 0) { Alert("Bullish GBP is hitting lower sd *BUY*"); spotup[0] = 2; }
      if (aud[1] > audu[1] && aud[2] < audu[2] && aud[1] < 0) { Alert("Bearish AUD is hitting upper sd *SELL*"); spotdn[0] = 3; }
      if (aud[1] < audd[1] && aud[2] > audd[2] && aud[1] > 0) { Alert("Bullish AUD is hitting lower sd *BUY*"); spotup[0] = 3; }
      if (nzd[1] > nzdu[1] && nzd[2] < nzdu[2] && nzd[1] < 0) { Alert("Bearish NZD is hitting upper sd *SELL*"); spotdn[0] = 4; }
      if (nzd[1] < nzdd[1] && nzd[2] > nzdd[2] && nzd[1] > 0) { Alert("Bullish NZD is hitting lower sd *BUY*"); spotup[0] = 4; }
      if (usd[1] > usdu[1] && usd[2] < usdu[2] && usd[1] < 0) { Alert("Bearish USD is hitting upper sd *SELL*"); spotdn[0] = 5; }
      if (usd[1] < usdd[1] && usd[2] > usdd[2] && usd[1] > 0) { Alert("Bullish USD is hitting lower sd *BUY*"); spotup[0] = 5; }
      if (cad[1] > cadu[1] && cad[2] < cadu[2] && cad[1] < 0) { Alert("Bearish CAD is hitting upper sd *SELL*"); spotdn[0] = 6; }
      if (cad[1] < cadd[1] && cad[2] > cadd[2] && cad[1] > 0) { Alert("Bullish CAD is hitting lower sd *BUY*"); spotup[0] = 6; }
      if (chf[1] > chfu[1] && chf[2] < chfu[2] && chf[1] < 0) { Alert("Bearish CHF is hitting upper sd *SELL*"); spotdn[0] = 7; }
      if (chf[1] < chfd[1] && chf[2] > chfd[2] && chf[1] > 0) { Alert("Bullish CHF is hitting lower sd *BUY*"); spotup[0] = 7; }
      if (jpy[1] > jpyu[1] && jpy[2] < jpyu[2] && jpy[1] < 0) { Alert("Bearish JPY is hitting upper sd *SELL*"); spotdn[0] = 8; }
      if (jpy[1] < jpyd[1] && jpy[2] > jpyd[2] && jpy[1] > 0) { Alert("Bullish JPY is hitting lower sd *BUY*"); spotup[0] = 8; }
      if (buysell2[1] > buysell2u[1] && buysell2[2] < buysell2u[2] && buysell2[1] < 0) { Alert("Bearish BS is hitting upper sd *SELL*"); spotdn[0] = 9; }
      if (buysell2[1] < buysell2d[1] && buysell2[2] > buysell2d[2] && buysell2[1] > 0) { Alert("Bullish BS is hitting lower sd *BUY*"); spotup[0] = 9; }
      
      //if (alertdn == true) spotdn[0] = 1;
      //if (alertup == true) spotup[0] = 1;
      
      alertdn = false;
      alertup = false;
      
      check2 = false;
   }
   
   string obname;
   
   obname = Name + "euro";
   Texter(obname, eur[0], 0, DoubleToString(eur[0], 0), clrDodgerBlue);
   obname = Name + "gbpo";
   Texter(obname, gbp[0], 0, DoubleToString(gbp[0], 0), clrRed);
   obname = Name + "audo";
   Texter(obname, aud[0], 0, DoubleToString(aud[0], 0), clrOrange);
   obname = Name + "nzdo";
   Texter(obname, nzd[0], 0, DoubleToString(nzd[0], 0), clrAqua);
   obname = Name + "cado";
   Texter(obname, cad[0], 0, DoubleToString(cad[0], 0), clrPink);
   obname = Name + "usdo";
   Texter(obname, usd[0], 0, DoubleToString(usd[0], 0), clrGreen);
   obname = Name + "chfo";
   Texter(obname, chf[0], 0, DoubleToString(chf[0], 0), clrGray);
   obname = Name + "jpyo";
   Texter(obname, jpy[0], 0, DoubleToString(jpy[0], 0), clrYellow);
   obname = Name + "bso";
   Texter(obname, buysell2[0], 0, DoubleToString(buysell2[0], 0), clrMagenta);
   
   double Min1 = MathMin(eur[ArrayMinimum(eur, limit, 0)], gbp[ArrayMinimum(gbp, limit, 0)]);
   double Min2 = MathMin(aud[ArrayMinimum(aud, limit, 0)], nzd[ArrayMinimum(nzd, limit, 0)]);
   double Min3 = MathMin(cad[ArrayMinimum(cad, limit, 0)], usd[ArrayMinimum(usd, limit, 0)]);
   double Min4 = MathMin(chf[ArrayMinimum(chf, limit, 0)], jpy[ArrayMinimum(jpy, limit, 0)]);
   
   double Min5 = MathMin(Min1, Min2);
   double Min6 = MathMin(Min3, Min4);
   
   double Min7 = MathMin(Min5, Min6);
   double Min8 = MathMin(Min7, irec[ArrayMinimum(irec, limit - 1, 1)]);
   
   double Max1 = MathMax(eur[ArrayMaximum(eur, limit, 0)], gbp[ArrayMaximum(gbp, limit, 0)]);
   double Max2 = MathMax(aud[ArrayMaximum(aud, limit, 0)], nzd[ArrayMaximum(nzd, limit, 0)]);
   double Max3 = MathMax(cad[ArrayMaximum(cad, limit, 0)], usd[ArrayMaximum(usd, limit, 0)]);
   double Max4 = MathMax(chf[ArrayMaximum(chf, limit, 0)], jpy[ArrayMaximum(jpy, limit, 0)]);
   
   double Max5 = MathMax(Max1, Max2);
   double Max6 = MathMax(Max3, Max4);
   
   double Max7 = MathMax(Max5, Max6);
   double Max8 = MathMax(Max7,  rec[ArrayMaximum(rec, limit - 1, 1)]);
      
   if (!ChartSetDouble(0, CHART_FIXED_MIN, Min8 - 100))
   { GetLastError(); return; }
   if (!ChartSetDouble(0, CHART_FIXED_MAX, Max8 + 100))
   { GetLastError(); return; }
   //ChartSetDouble(0, CHART_PRICE_MIN, Min7 - 100);
   //ChartSetDouble(0, CHART_PRICE_MAX, Max7 + 100);
}
//+------------------------------------------------------------------+

double dblTickValue( string strSymbol )
{
   return( MarketInfo( strSymbol, MODE_TICKVALUE ) );
}

double dblPipValue( string strSymbol )
{
   double dblCalcPipValue = dblTickValue( strSymbol );
   if (MarketInfo(strSymbol, MODE_DIGITS) == 3) dblCalcPipValue *= 10;
   if (MarketInfo(strSymbol, MODE_DIGITS) == 5) dblCalcPipValue *= 10;
   return( dblCalcPipValue );
}

//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//DE-INIT
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//CUSTOM
//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+



void file(double &file[], string fn)
{
   //double arrayToWrite[] = {1.23, 4.56, 7.89, 10.11, 12.13}; // Example array

   string fileName = TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE) + " " + fn + " output.csv";
   int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_CSV);
   
   FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE));
   
   if(fileHandle != INVALID_HANDLE)
   {
      for(int i=0; i<=ArraySize(file) - 1; i++)
      {
         FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_CURRENT, ArraySize(file) - i - 1), TIME_DATE|TIME_MINUTES), NormalizeDouble(file[i], 0));
         //Print(file[i]);
      }
      
      FileClose(fileHandle);
      Print("Data written to ", fileName);
   }
   else
   {
      Print("Failed to open the file!");
   }
}


void files()
{
   int limit = iBarShift(_Symbol, first, iTime(_Symbol, second, days), false);
   
   double filee[], fileg[], filea[], filen[], filec[], fileu[], filef[], filej[];
   ArrayResize(filee, limit + 1);
   ArrayResize(fileg, limit + 1);
   ArrayResize(filea, limit + 1);
   ArrayResize(filen, limit + 1);
   ArrayResize(filec, limit + 1);
   ArrayResize(fileu, limit + 1);
   ArrayResize(filef, limit + 1);
   ArrayResize(filej, limit + 1);
   
   for (int i = limit; i >= 0; i--)
   {
   //Print(i);
      filee[limit - i] = eur[i];
      fileg[limit - i] = gbp[i];
      filea[limit - i] = aud[i];
      filen[limit - i] = nzd[i];
      filec[limit - i] = cad[i];
      fileu[limit - i] = usd[i];
      filef[limit - i] = chf[i];
      filej[limit - i] = jpy[i];
   }
   
   {
      file(filee, "eur");
      file(fileg, "gbp");
      file(filea, "aud");
      file(filen, "nzd");
      file(filec, "cad");
      file(fileu, "usd");
      file(filef, "chf");
      file(filej, "jpy");
   }
}

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[y] + 1800);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_CENTER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
	if(x > 0)
	ObjectSetString(0, name, OBJPROP_TEXT, "+" + text);
	if(x < 0)
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
	//BUTTONS CUT SYMBOLS FROM BASKETS
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " EUR") && noeur == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				noeur = true;
				recheck = true;
				ObjectSetText(Name + "euro", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " EUR") && noeur == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				noeur = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " GBP") && nogbp == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nogbp = true;
				recheck = true;
				ObjectSetText(Name + "gbpo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " GBP") && nogbp == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nogbp = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " AUD") && noaud == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				noaud = true;
				recheck = true;
				ObjectSetText(Name + "audo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " AUD") && noaud == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				noaud = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " NZD") && nonzd == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nonzd = true;
				recheck = true;
				ObjectSetText(Name + "nzdo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " NZD") && nonzd == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nonzd = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " USD") && nousd == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nousd = true;
				recheck = true;
				ObjectSetText(Name + "usdo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " USD") && nousd == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nousd = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " CAD") && nocad == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nocad = true;
				recheck = true;
				ObjectSetText(Name + "cado", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " CAD") && nocad == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nocad = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " CHF") && nochf == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nochf = true;
				recheck = true;
				ObjectSetText(Name + "chfo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " CHF") && nochf == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nochf = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + " JPY") && nojpy == false)
			{
				//GlobalVariableSet("fEUR", false);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nojpy = true;
				recheck = true;
				ObjectSetText(Name + "jpyo", "");
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + " JPY") && nojpy == true)
			{
				//GlobalVariableSet("fEUR", true);
				//EUR = GlobalVariableGet("fEUR");
				//GlobalVariablesFlush();
				nojpy = false;
				recheck = true;
				ChartRedraw();
			}
		}
	}
}
//+------------------------------------------------------------------+
