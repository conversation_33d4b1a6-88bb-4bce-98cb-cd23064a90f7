//+------------------------------------------------------------------+
//|                                                        rsi_a.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

/*
double hori1, hori2;
double action1, action2;
double rev1, rev2;
double touch1, touch2;
*/
input bool alerts = true;
/*
input double rsi_upcheck = 45;
input double rsi_dncheck = 55;

double rsibuy;
double rsisel;
*/
#include <tmsrv.mqh>
input int rsi_period = 11; //RSI period
   
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
void OnTick()
  {
//---

	bool check = false;
	static datetime check_time = 0;
	if (check_time < iTime(_Symbol, PERIOD_CURRENT, 0) && (Hour() >= 2 && Hour() < 23) && (TimeCurrent() >= (iTime(NULL, PERIOD_CURRENT, 0) + 7)))
	{
	   check_time = iTime(_Symbol, PERIOD_CURRENT, 0);
	   check = true;
	}
	if (check)
	{
	   /*
	   //action (rsi max)
   	action1 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 2, 1);
   	action2 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 3, 1);
   	//hori (rsi opposite)
   	hori1 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 0, 1);
   	hori2 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 1, 1);
   	// rev (bb opposite)
   	rev1 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 4, 1);
   	rev2 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 5, 1);
   	// touch (ma200 touch)
   	touch1 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 6, 1);
   	touch2 = iCustom(_Symbol, PERIOD_CURRENT, "rsi_a", 1, false, rsi_upcheck, rsi_dncheck, true, 7, 1);
   	*/
	   
	   //if (_Symbol == "EURUSD") Print(rev1 + " " + hori1 + " " + action1 + " " + touch1);
	   
	   if (alerts)
	   {
	   /*
	      if ((rev1 == 25 && hori1 != 50) && (action1 != 50 && touch1 != 50)) 
	      {
	         //Alert("Watch " + _Symbol + " for bottoming (bb low) on " + IntegerToString(ChartPeriod()));
	         tms_send("Watch " + _Symbol + " for bottoming (bb low) on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      if ((rev2 == -25 && hori2 != -50) && (action2 != -50 && touch2 != -50)) 
	      {
	         //Alert("Watch " + _Symbol + " for topping (bb high) on " + IntegerToString(ChartPeriod()));
	         tms_send("Watch " + _Symbol + " for topping (bb high) on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      if ((rev1 != 25 && hori1 == 50) && (action1 != 50 && touch1 != 50)) 
	      {
	         //Alert("Watch " + _Symbol + " for bottoming (rsi < " + DoubleToString(rsi_upcheck, 0) + ") on " + IntegerToString(ChartPeriod()));
	         tms_send("Watch " + _Symbol + " for bottoming (rsi < " + DoubleToString(rsi_upcheck, 0) + ") on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      if ((rev2 != -25 && hori2 == -50) && (action2 != -50 && touch2 != -50)) 
	      {
	         //Alert("Watch " + _Symbol + " for topping (rsi > " + DoubleToString(rsi_dncheck, 0) + ") on " + IntegerToString(ChartPeriod()));
	         tms_send("Watch " + _Symbol + " for topping (rsi > " + DoubleToString(rsi_dncheck, 0) + ") on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      */
	      /*
	      if ((rev1 == 25 && hori1 == 50) && (action1 != 50 && touch1 != 50)) 
	      {
	         //Alert("***Watch " + _Symbol + " for bottoming (bb low + rsi < " + DoubleToString(rsi_upcheck, 0) + ") on " + IntegerToString(ChartPeriod()));
	         tms_send("***Watch " + _Symbol + " for bottoming (bb low + rsi < " + DoubleToString(rsi_upcheck, 0) + ") on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      if ((rev2 == -25 && hori2 == -50) && (action2 != -50 && touch2 != -50)) 
	      {
	         //Alert("***Watch " + _Symbol + " for topping (bb high + rsi > " + DoubleToString(rsi_dncheck, 0) + ") on " + IntegerToString(ChartPeriod()));
	         tms_send("***Watch " + _Symbol + " for topping (bb high + rsi > " + DoubleToString(rsi_dncheck, 0) + ") on " + IntegerToString(ChartPeriod()), "-1001847152640:37be88a9");
	      }
	      */
	      
	      /*
	      double newbuy = 0, newsel = 0, newbuystop = 0, newselstop = 0, newbuytp = 0, newseltp = 0;
	      newbuy = iCustom(_Symbol, PERIOD_CURRENT, "FS7", 8000, 20, 20, 8, 1);
	      newsel = iCustom(_Symbol, PERIOD_CURRENT, "FS7", 8000, 20, 20, 9, 1);
	      newbuystop = iCustom(_Symbol, PERIOD_CURRENT, "FS7", 8000, 20, 20, 10, 1);
	      newselstop = iCustom(_Symbol, PERIOD_CURRENT, "FS7", 8000, 20, 20, 11, 1);
	      
	      if (newbuy == 1) tms_send(_Symbol + " OB/FVG buy (TF: " + IntegerToString(ChartPeriod()) + ") @ " + DoubleToString(iLow(_Symbol, PERIOD_CURRENT, 1), _Digits) + " SL: " + DoubleToString(newbuystop, _Digits), "-1001847152640:37be88a9");
	      //Alert(_Symbol + " OB/FVG buy @ " + DoubleToString(iLow(_Symbol, PERIOD_CURRENT, 1), _Digits) + " SL: " + DoubleToString(newbuystop, _Digits));
	      if (newsel == 1) tms_send(_Symbol + " OB/FVG sell (TF: " + IntegerToString(ChartPeriod()) + ") @ " + DoubleToString(iHigh(_Symbol, PERIOD_CURRENT, 1), _Digits) + " SL: " + DoubleToString(newselstop, _Digits), "-1001847152640:37be88a9");
	      //Alert(_Symbol + " OB/FVG sell @ " + DoubleToString(iHigh(_Symbol, PERIOD_CURRENT, 1), _Digits) + " SL: " + DoubleToString(newselstop, _Digits));
	      */
	      
	      double newrsib = 0, newrsis = 0, newrsiobvb = 0, newrsiobvs = 0;
	      newrsib = iCustom(_Symbol, PERIOD_CURRENT, "kkk-obv", 2000, 28, 10, 72, 90, rsi_period, false, 3, 0);
	      newrsis = iCustom(_Symbol, PERIOD_CURRENT, "kkk-obv", 2000, 28, 10, 72, 90, rsi_period, false, 4, 0);
	      newrsiobvb = iCustom(_Symbol, PERIOD_CURRENT, "kkk-obv", 2000, 28, 10, 72, 90, rsi_period, false, 5, 0);
	      newrsiobvs = iCustom(_Symbol, PERIOD_CURRENT, "kkk-obv", 2000, 28, 10, 72, 90, rsi_period, false, 6, 0);
	      
	      if (newrsib == 1) tms_send(_Symbol + " *dar* RADIS buy (" + IntegerToString(ChartPeriod()) + " rsi: " + IntegerToString(rsi_period) + ") @ " + DoubleToString(iOpen(_Symbol, PERIOD_CURRENT, 0), _Digits), "-1001847152640:37be88a9");
	      if (newrsis == 1) tms_send(_Symbol + " *dar* RADIS sell (" + IntegerToString(ChartPeriod()) + " rsi: " + IntegerToString(rsi_period) + ") @ " + DoubleToString(iOpen(_Symbol, PERIOD_CURRENT, 0), _Digits), "-1001847152640:37be88a9");
	      if (newrsiobvb == 1) tms_send(_Symbol + " *dar OBV* RADIS buy (" + IntegerToString(ChartPeriod()) + " rsi: " + IntegerToString(rsi_period) + ") @ " + DoubleToString(iOpen(_Symbol, PERIOD_CURRENT, 0), _Digits), "-1001847152640:37be88a9");
	      if (newrsiobvs == 1) tms_send(_Symbol + " *dar OBV* RADIS sell (" + IntegerToString(ChartPeriod()) + " rsi: " + IntegerToString(rsi_period) + ") @ " + DoubleToString(iOpen(_Symbol, PERIOD_CURRENT, 0), _Digits), "-1001847152640:37be88a9");
	      
	   }
	   check = false;
	}
//--- return value of prev_calculated for next call
  }
//+------------------------------------------------------------------+
