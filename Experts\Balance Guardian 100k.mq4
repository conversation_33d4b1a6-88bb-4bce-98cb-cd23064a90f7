//+------------------------------------------------------------------+
//|                                                         test.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2022, sakisf."
#property link      "https://www.forexfactory.com/sakisf"
#property version   "1.00"
#property strict

//input int CheckSeconds = 1; //Check every X seconds (alerts / notifications) (deprecated)

input double minutes_allowed = 16; //Trading hours allowed per day
enum brtf //
{
	LOW = 1,	  // Low risk (1.05% - 10 days to burn)
	MEDIUM = 2,	  // Medium risk (1.50% - 7 days to burn)
	MEDHIGH = 3, // Medium-high risk (1.81% - 6 days to burn)
	HIGH = 4, // High risk (2.09% - 5 days to burn)
};
extern brtf max_loss_multiplier = 1; //MAX LOSS multiplier (risk level - i.e. days to burn)
double lossmulti;
extern double max_profit_multiplier = 4;//MAX PROFIT multiplier (reduce to 1 to get them to actual)
input int first_hour = 23;//Stop trading which hour (night)
input int last_hour = 8;//Stop trading until which hour (morning)
input int maximtrades = 40;//Maximum trades per day
input int maxtradesopen = 4;//Maximum allowed open trades
extern double losstrade = 0; //Close loss per trade (0 for auto)
extern double lossmax = 0; //Close all trades when reached (loss) (0 for auto)
extern double profitmax = 2000; //Close all trades when reached (profit) (0 for auto)
input bool challenge = false; //Challenge account?
input int chaltarget = 20000; //Challenge target
input int initialbal = 200000; //Initial challenge balance

input string actual_tms = "-*************:2cd1af62"; //Insert actual TMS from below
input string challenge_tms= "-*************:2cd1af62"; //Challenge TMS channel (for copy)
input string live_tms="-*************:519d91ea"; //Live TMS channel (for copy)

string tms_init;

static double openbalance;
static bool daytrade = false;
static bool targetdone = false;
static bool maxtrade = false;
double currentequ;
bool closeonprofit, closeonloss, closeonbalance, closeontradesno, closeonmaxvol, closeonmaxalloc, closeonmaxtime, closeonhour, closeonmaxprofit, closeonmaxloss, closeontarget, closeonmaxtrades;
#include <tmsrv.mqh>

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //if (AccountNumber() == ********) first_hour = 23;
	//--- set GVs
	if (GlobalVariableCheck("openeq") == false || (GlobalVariableGet("openeq") != AccountBalance() && totalpl() == 0)) GlobalVariableSet("openeq", AccountBalance());
	openbalance = GlobalVariableGet("openeq");
	if (max_loss_multiplier == 1) lossmulti = 1.05;
	else if (max_loss_multiplier == 2) lossmulti = 1.50;
	else if (max_loss_multiplier == 3) lossmulti = 1.81;
	else if (max_loss_multiplier == 4) lossmulti = 2.09;
	if (losstrade == 0) losstrade = maxbalpl() / 2.5;
	if (lossmax == 0) lossmax = maxbalpl() / 1.5;
	if (profitmax == 0) profitmax = maxbalpl() * 1.5;
	//if (challenge) max_loss_multiplier = setmaxlossaccount();
	tms_init = actual_tms;
	if (challenge) tms_init = challenge_tms;
	printf("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")" + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nTrading disabled today: " + (string)pdchecker(openbalance));
   SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")"  + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nTrading disabled today: " + (string)pdchecker(openbalance));
   tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")" + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nAllowed loss per trade: " + DoubleToString(losstrade, 0) + "\nAllowed loss per combined trades: " + DoubleToString(lossmax, 0) + "\nAllowed profit per trade/combined trades: " + DoubleToString(profitmax, 0), tms_init); //"\nTrading disabled today: " + (string)pdchecker(openbalance), tms_init);
   timecheck();
	//--- create timer
	//EventSetTimer(60);
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	//ObjectsDeleteAll(0, Name);
	//--- destroy timer
	//EventKillTimer();
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{   
   daycheck();
   //heartbeat();
   heartbeat2();
   
   if (maxtrade == false)
   {
      maximtrades();
   }
   
   if (targetdone == false)
   {
      targetcheck();
   }
   
   if (openorders() > 0)
   {  
      checktrade();
      tradechecker();
   }
   
   flush();  
}
//+------------------------------------------------------------------+

//+NEW DAY CHECK-----------------------------------------------------+
void daycheck()
{
	bool m1_check = false;
	static datetime m1_time = 0;
	if (m1_time < iTime(_Symbol, PERIOD_D1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 3660) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 3960)))
	{
	   m1_check = true;
	   m1_time = iTime(_Symbol, PERIOD_D1, 0);
	}
	if (m1_check)
	{
	   if (GlobalVariableGet("openeq") != AccountBalance()) GlobalVariableSet("openeq", AccountBalance());
	   openbalance = GlobalVariableGet("openeq");
	   Sleep(2000);
   	if (losstrade == 0) losstrade = maxbalpl() / 2.5;
   	if (lossmax == 0) lossmax = maxbalpl() / 1.5;
   	if (profitmax == 0) profitmax = maxbalpl() * 1.5;
	   //if (challenge) max_loss_multiplier = setmaxlossaccount();
	   daytrade = false;
	   targetdone = false;
	   maxtrade = false;
	   //if (pdchecker(openbalance)) { daytrade = true; SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nYou have reached absolute % loss for two consecutive days, your are OUT today!"); }
	   printf("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")" + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nTrading disabled today: " + (string)pdchecker(openbalance));
	   SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")" + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nTrading disabled today: " + (string)pdchecker(openbalance));
      tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nMax lot size: " + DoubleToString(lotsize(openbalance), 3) + "\nTarget (%/$): " + DoubleToString(maxbalpl() * 100 / openbalance, 2) + "%" + " / " + DoubleToString(maxbalpl(), 2) + "\nMax Profit: " + DoubleToString(maxbalpl() * max_profit_multiplier, 2) + " - Max Loss: " + DoubleToString(maxbalpl(), 2) + " (" + DoubleToString(lossmulti, 2) + ")" + "\nRisk: " + EnumToString(max_loss_multiplier) + "\nStart Hour: " + IntegerToString(last_hour) + " Last hour: " + IntegerToString(first_hour) + "\nAvailable hours to trade: " + DoubleToString(minutes_allowed, 0) + "\nAllowed loss per trade: " + DoubleToString(losstrade, 0) + "\nAllowed loss per combined trades: " + DoubleToString(lossmax, 0) + "\nAllowed profit per trade/combined trades: " + DoubleToString(profitmax, 0), tms_init); //"\nTrading disabled today: " + (string)pdchecker(openbalance), tms_init);
      m1_check = false;
   }
}
//+------------------------------------------------------------------+

//+NEW DAY CHECK-----------------------------------------------------+
void heartbeat()
{
	bool m1_check = false;
	static datetime m1_time = 0;
	if (m1_time < iTime(_Symbol, PERIOD_H4, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H4, 0) + 60) && TimeCurrent() <= (iTime(NULL, PERIOD_H4, 0) + 300)))
	{
	   m1_check = true;
	   m1_time = iTime(_Symbol, PERIOD_H4, 0);
	}
	if (m1_check)
	{
	   SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING \nToday's P/L is: " + DoubleToString(totalpl(), 2));
      if (timecheck() < minutes_allowed * 60) tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING\nToday's P/L is: " + DoubleToString(totalpl(), 2) + "\nYou have " + DoubleToString(minutes_allowed * 60 - timecheck(), 0) + " minutes left to trade today.", tms_init);
      else if (timecheck() > minutes_allowed * 60) tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING\nToday's P/L is: " + DoubleToString(totalpl(), 2) + "\nYou have exceeded your trading time of " + DoubleToString(minutes_allowed * 60, 0) + " minutes for today.", tms_init);
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
double setmaxlossaccount()
{
   double tgtloss = 0;
   double tgtsize = chaltarget / 8;
   double actual = openbalance - initialbal;
   double y = NormalizeDouble(actual / tgtsize, 0);
   
   if (actual <= 0) tgtloss = 1;
   else if (actual > 0 && y == 1) tgtloss = 1 - 0.07;
   else if (actual > 0 && y == 2) tgtloss = 1 - 0.14;
   else if (actual > 0 && y == 3) tgtloss = 1 - 0.21;
   else if (actual > 0 && y == 4) tgtloss = 1 - 0.28;
   else if (actual > 0 && y == 5) tgtloss = 1 - 0.35;
   else if (actual > 0 && y == 6) tgtloss = 1 - 0.42;
   else if (actual > 0 && y == 7) tgtloss = 1 - 0.49;
   
   return(tgtloss);   
}
//+------------------------------------------------------------------+
   

//+NEW DAY CHECK-----------------------------------------------------+
void heartbeat2()
{
	bool m1_check = false;
	static datetime m1_time = 0;
	if (m1_time < iTime(_Symbol, PERIOD_H4, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H4, 0) + 60) && TimeCurrent() <= (iTime(NULL, PERIOD_H4, 0) + 300)))
	{
	   m1_check = true;
	   m1_time = iTime(_Symbol, PERIOD_H4, 0);
	}
	if (m1_check)
	{
	   //SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING \nToday's P/L is: " + DoubleToString(totalpl(), 2));
      //if (timecheck() < minutes_allowed * 60) tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING\nToday's P/L is: " + DoubleToString(totalpl(), 2) + "\nYou have " + DoubleToString(minutes_allowed * 60 - timecheck(), 0) + " minutes left to trade today.", tms_init);
      //else if (timecheck() > minutes_allowed * 60) tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING\nToday's P/L is: " + DoubleToString(totalpl(), 2) + "\nYou have exceeded your trading time of " + DoubleToString(minutes_allowed * 60, 0) + " minutes for today.", tms_init);
      tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nStarting Balance: " + DoubleToString(openbalance, 2) + "\nIS ALIVE AND RUNNING\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init);
	}
}
//+------------------------------------------------------------------+

//+TARGETCHECK-------------------------------------------------------+
void targetcheck()
{
   if (totalpl() > (maxbalpl()))
   {
      targetdone = true;
      SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nYou have reached today's TARGET (+sl).\nSL moved to Initial Balance: " + DoubleToString(openbalance, 2));
      tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nYou have reached today's TARGET (+sl).\nSL moved to Initial Balance: " + DoubleToString(openbalance, 2), tms_init);
   }
}
//+------------------------------------------------------------------+

//+TARGETCHECK-------------------------------------------------------+
void maximtrades()
{
   if (todaytrades() >= maximtrades)
   {
      //Print("run?");
      maxtrade = true;
      SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nYou have reached today's max trades (" + IntegerToString(maximtrades) + ").\nNo more trading allowed.");
      tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN \nStarting Balance: " + DoubleToString(openbalance, 2) + "\nYou have reached today's max trades (" + IntegerToString(maximtrades) + ").\nNo more trading allowed.", tms_init);
   }
}
//+------------------------------------------------------------------+


//+TRADECHECKER------------------------------------------------------+
void tradechecker()
{
   currentequ = AccountEquity();
   //checktrade();
   if (totalactive() <= -lossmax && lossmax != 0) { closeonmaxloss = true; alerts("10"); }
   if (totalactive() >= profitmax && profitmax != 0) { closeonmaxprofit = true; alerts("11"); }
   
   //if (pdchecker(openbalance)) { closeonbalance = true; alerts("1"); }
   if (targetdone && currentequ <= openbalance) { closeontarget = true; alerts("c"); }
   //2nd
   if (daytrade && AccountEquity() > openbalance) { closeonprofit = true; alerts("a"); }   
   //3rd
   if (daytrade && AccountEquity() < openbalance) { closeonloss = true; alerts("b"); }
   if (totalorders() > maxtradesopen) { closeontradesno = true; alerts("2"); }
   if (totallots() > lotsize(openbalance)) { closeonmaxvol = true; alerts("3"); }
   if (todayorders() > (lotsize(openbalance) * 10)) { closeonmaxalloc = true; alerts("4"); }
   if (maxtrade) { closeonmaxtrades = true; alerts("9"); }
   if (timecheck() > 60 * minutes_allowed) { closeonmaxtime = true; alerts("5"); }
   if (Hour() >= first_hour || Hour() < last_hour) { closeonhour = true; alerts("6"); }
   if ((!daytrade && (currentequ > openbalance + (maxbalpl() * max_profit_multiplier)))) { daytrade = true; closeonmaxprofit = true; alerts("7"); } // || (totalpl() > (maxbalpl() * max_profit_multiplier))
	//if (challenge) max_loss_multiplier = setmaxlossaccount();
   if ((!daytrade && (currentequ < openbalance - (maxbalpl())))) { daytrade = true; closeonmaxloss = true; alerts("8"); }
   
   if (closeonbalance) tradeclose(1);
   if (closeontarget) tradeclose(13);
   if (closeonprofit) tradeclose(11);
   if (closeonloss) tradeclose(12);
   if (closeontradesno) tradeclose(2);
   if (closeonmaxvol) tradeclose(3);
   if (closeonmaxalloc) tradeclose(4);
   if (closeonmaxtime) tradeclose(5);
   if (closeonhour) tradeclose(6);
   if (closeonmaxprofit) tradeclose(7);
   if (closeonmaxloss) tradeclose(8);
   if (closeonmaxtrades) tradeclose(9);
   if ((closeontarget || closeonmaxtrades || closeonprofit || closeonloss || closeonbalance || closeontradesno || closeonmaxvol || closeonmaxalloc || closeonmaxtime || closeonhour || closeonmaxprofit || closeonmaxloss) && openorders() > 0) { tradeclose(0); Print("Caught by catcher"); }
}
//+------------------------------------------------------------------+

//+TG & MT4 NOTIFICATIONS--------------------------------------------+
void alerts(const string alert)
{   
   if (alert == "a") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max profit. No more trading allowed today.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max profit. No more trading allowed today.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
	if (alert == "b") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max loss. No more trading allowed today.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max loss. No more trading allowed today.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "c") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached the TARGET (/2) and lost the profits.\nSL was reset to Initial Balance and you hit it.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached the TARGET (/2) and lost the profits.\nSL was reset to Initial Balance and you hit it.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "1") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are BANNED from trading today.", tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are BANNED from trading today."); }
   if (alert == "2") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to have more than " + IntegerToString(maxtradesopen) + " open trades.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to have more than " + IntegerToString(maxtradesopen) + " open trades.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "3") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to have more than " + DoubleToString(lotsize(openbalance), 3) + " lots open.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to have more than " + DoubleToString(lotsize(openbalance), 3) + " lots open.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "4") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have exceeded max lots " + DoubleToString(lotsize(openbalance) * 10, 3) + " today.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have exceeded max lots " + DoubleToString(lotsize(openbalance) * 10, 3) + " today.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "9") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max trades (" + IntegerToString(maximtrades) + ") today.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have reached max trades (" + IntegerToString(maximtrades) + ") today.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "5") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have exceeded max available trading hours (" + DoubleToString(minutes_allowed, 0) +") today.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou have exceeded max available trading hours (" + DoubleToString(minutes_allowed, 0) +") today.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "6") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to trade between " + IntegerToString(first_hour) + ":00 and " + IntegerToString(last_hour) + ":00.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nYou are not allowed to trade between " + IntegerToString(first_hour) + ":00 and " + IntegerToString(last_hour) + ":00.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "7") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nMax PROFIT reached for the day. You are done.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nMax PROFIT reached for the day. You are done.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "8") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nMax LOSS reached for the day. You are done.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); SendNotification("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nMax LOSS reached for the day. You are done.\nToday's P/L is: " + DoubleToString(totalpl(), 2)); }
   if (alert == "10") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nOpen trades reached " + DoubleToString(-lossmax, 2) + ".\nClosed everything...\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); }
   if (alert == "11") { tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + AccountServer() + "\nGUARDIAN\nOpen trades reached " + DoubleToString(profitmax, 2) + ".\nClosed everything...\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); }
}
//+------------------------------------------------------------------+

//+GV FLUSH----------------------------------------------------------+
void flush()
{
   bool new_checka = false;
	static datetime rechecka_time = 0;
	if (rechecka_time < iTime(_Symbol, PERIOD_H1, 0))
	{
		new_checka = true;
		rechecka_time = iTime(_Symbol, PERIOD_H1, 0);
	}
	if (new_checka)
   {  
      GlobalVariablesFlush();
      new_checka=false;
   }
}
//+------------------------------------------------------------------+

//+CLOSE BY TRADE----------------------------------------------------+
void checktrade()
{
   for (int i = OrdersTotal() - 1; i >= 0; i--)
   {
      int ret;
      bool type;
      type = OrderType();
   	if (type == OP_BUY && (OrderProfit() + OrderSwap() + OrderCommission()) <= -losstrade) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_BID), 0, clrNONE); tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + "\nGUARDIAN\nClosed buy trade on " + OrderSymbol() + " size: " + DoubleToString(OrderLots(), 2) + " orderID: " + IntegerToString(OrderTicket()) + " due to " + DoubleToString(-losstrade, 2) + " loss.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); }
   	//Print(GetLastError());
   	if (type == OP_SELL && (OrderProfit() + OrderSwap() + OrderCommission()) <= -losstrade) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_ASK), 0, clrNONE); tms_send("Acc: " + IntegerToString(AccountNumber()) + " " + AccountName() + " " + "\nGUARDIAN\nClosed sell trade on " + OrderSymbol() + " size: " + DoubleToString(OrderLots(), 2) + " orderID: " + IntegerToString(OrderTicket()) + " due to " + DoubleToString(-losstrade, 2) + " loss.\nToday's P/L is: " + DoubleToString(totalpl(), 2), tms_init); }
      //Print(GetLastError());
   }
}
//+------------------------------------------------------------------+

//+AUTO CLOSE TRADES-------------------------------------------------+
void tradeclose(const int p)
{
   for (int i = OrdersTotal() - 1; i >= 0; i--)
   if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
   {
   	int ret;
   	bool type;
   	type = OrderType();
   	if (type == OP_BUY) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_BID), 0, clrNONE); }
   	if (type == OP_SELL) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_ASK), 0, clrNONE); }
   	if (type == OP_BUYSTOP || type == OP_BUYLIMIT || type == OP_SELLSTOP || type == OP_SELLLIMIT) { ret = OrderDelete(OrderTicket(), clrNONE); }
   	Print(GetLastError());
   }
   
   switch (p)
   {
      case 1:
         closeonbalance = false; break;
      case 13:
         closeontarget = false; break;
      case 11:
         closeonprofit = false; break;
      case 12:
         closeonloss = false; break;
      case 2:
         closeontradesno = false; break;
      case 3:
         closeonmaxvol = false; break;
      case 4:
         closeonmaxalloc = false; break;
      case 9:
         closeonmaxtrades = false; break;
      case 5:
         closeonmaxtime = false; break;
      case 6:
         closeonhour = false; break; 
      case 7:
         closeonmaxprofit = false; break;
      case 8:
         closeonmaxloss = false; break;
      default:
         closeonbalance = false; closeonprofit = false; closeonloss = false; closeontradesno = false; closeonmaxvol = false; closeonmaxalloc = false; closeonmaxtime = false; closeonhour = false; closeonmaxprofit = false; closeonmaxloss = false; closeontarget = false; closeonmaxtrades = false; break;
   }
   
	check2();
}
//+------------------------------------------------------------------+

//+AUTO CLOSE TRADES BACKUP------------------------------------------+
void check2(){
   for (int i = OrdersTotal() - 1; i >= 0; i--)
   if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
   {
   	int ret;
   	bool type;
   	type = OrderType();
   	if (type == OP_BUY) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_BID), 0, clrNONE); }
   	if (type == OP_SELL) { ret = OrderClose(OrderTicket(), OrderLots(), MarketInfo(OrderSymbol(), MODE_ASK), 0, clrNONE); }
   	if (type == OP_BUYSTOP || type == OP_BUYLIMIT || type == OP_SELLSTOP || type == OP_SELLLIMIT) { ret = OrderDelete(OrderTicket(), clrNONE); }
   	Print(GetLastError());
   }
}
//+------------------------------------------------------------------+

/*
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
	//---
}
//+------------------------------------------------------------------+
*/

//+TOTAL ACTIVE CALC-------------------------------------------------+
double totalactive()
{
	double total = 0;
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	return(total);
}
//+------------------------------------------------------------------+

//+P/L CALC----------------------------------------------------------+
double totalpl()
{
	double total = 0;
   datetime today_midnight=TimeCurrent()-(TimeCurrent()%(PERIOD_D1*60));
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	for (int i = OrdersHistoryTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_HISTORY) && OrderCloseTime()>=today_midnight && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (NormalizeDouble(total, 2));
}
//+------------------------------------------------------------------+

//+P/L CALC----------------------------------------------------------+
double totallots()
{
	double total = 0;
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && MarketInfo(OrderSymbol(), MODE_MARGINCALCMODE) < 1 && OrderType() < 2)
		{
			total += OrderLots();
		}
	return (NormalizeDouble(total, 2));
}
//+------------------------------------------------------------------+

//+COUNT ACTIVE ORDERS-----------------------------------------------+
int totalorders()
{
	int total = 0;

	for (int i = OrdersTotal() - 1; i >= 0; i--)
	{
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
		{
			total++;
		}
	}  
	return (total);
}
//+------------------------------------------------------------------+

//+MAX EQUITY DD CALC FROM HIGH--------------------------------------+
double maxbalpl()
{   
   double perc1 = 0;
   double pink = 0;
   if (max_loss_multiplier == 1) pink = 1.05;
   else if (max_loss_multiplier == 2) pink = 1.50;
   else if (max_loss_multiplier == 3) pink = 1.81;
   else if (max_loss_multiplier == 4) pink = 2.09;
   
   perc1 = openbalance * pink / 100;
	
	return (NormalizeDouble(perc1, 2));
}
//+------------------------------------------------------------------+

//+MAX LOTSIZE ALLOWANCE---------------------------------------------+
double lotsize(const double bal)
{
   double pink = 0;
   
   if (bal < 100000) pink = (maxbalpl() / 125 - maxbalpl() / 1000) + 0.15;
   if (bal >= 100000) pink = maxbalpl() / 62.5 - maxbalpl() / 200;
   
   return (NormalizeDouble(pink, 3));
}
//+------------------------------------------------------------------+

//+COUNT TODAY ORDERS------------------------------------------------+
double todayorders()
{
   double closed_lots=0;
   datetime today_midnight=TimeCurrent()-(TimeCurrent()%(PERIOD_D1*60));
   for(int x=OrdersHistoryTotal()-1; x>=0; x--)
      {
         if(OrderSelect(x,SELECT_BY_POS,MODE_HISTORY) && OrderCloseTime()>=today_midnight && OrderType() < 2 && MarketInfo(OrderSymbol(), MODE_MARGINCALCMODE) < 1)
         closed_lots += OrderLots();
      }
   for(int x=OrdersTotal()-1; x>=0; x--)
      {
         if(OrderSelect(x,SELECT_BY_POS,MODE_TRADES) && OrderOpenTime()>=today_midnight && OrderType() < 2 && MarketInfo(OrderSymbol(), MODE_MARGINCALCMODE) < 1)
         closed_lots += OrderLots();
      }
   return (NormalizeDouble(closed_lots, 2));
}
//+------------------------------------------------------------------+

//+COUNT TODAY ORDERS------------------------------------------------+
int todaytrades()
{
   int closed_lots=0;
   datetime today_midnight=TimeCurrent()-(TimeCurrent()%(PERIOD_D1*60));
   for(int x=OrdersHistoryTotal()-1; x>=0; x--)
      {
         if(OrderSelect(x,SELECT_BY_POS,MODE_HISTORY) && OrderCloseTime()>=today_midnight && OrderType() < 2)
         {
            closed_lots++;
         }
      }
   return (closed_lots);
}
//+------------------------------------------------------------------+

//+COUNT PREVIOUS DAY(S) P/L-----------------------------------------+
double yestorders(const int day)
{
   int closed_orders=0;
   datetime startday = iTime(_Symbol, PERIOD_D1, day);
   datetime endday;
   if (day == 0) endday = TimeCurrent();
   else endday = iTime(_Symbol, PERIOD_D1, day - 1)-1;
   double total = 0;
      
   for(int x=OrdersHistoryTotal()-1; x>=0; x--)
   {
      if(OrderSelect(x,SELECT_BY_POS,MODE_HISTORY) && OrderCloseTime() >= startday && OrderCloseTime() < endday && OrderType() < 2)
      {
         closed_orders++;
         total += OrderProfit() + OrderSwap() + OrderCommission();
      }
   }
   return (NormalizeDouble(total, 2));
}
//+------------------------------------------------------------------+

//+TRADE TIMECHECKER-------------------------------------------------+
double timecheck()
{
   datetime old_orders = 0;
   datetime new_orders = 0;
   datetime startday = iTime(_Symbol, PERIOD_D1, 0);
   datetime endday = TimeCurrent();
   double all_time = 0;
   MqlDateTime str1, str2;
   
   for(int x=OrdersTotal()-1; x>=0; x--)
   {
      if(OrderSelect(x,SELECT_BY_POS,MODE_TRADES) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderOpenTime() >= startday && OrderOpenTime() <= endday && OrderType() < 2)
      {
         new_orders += TimeCurrent() - OrderOpenTime();
      }
   }
   TimeToStruct(new_orders, str1);
   
   for(int x=OrdersHistoryTotal()-1; x>=0; x--)
   {
      if(OrderSelect(x,SELECT_BY_POS,MODE_HISTORY) && (OrderType() == OP_BUY || OrderType() == OP_SELL) && OrderCloseTime() >= startday && OrderCloseTime() <= endday && OrderType() < 2)
      {
         old_orders += OrderCloseTime() - OrderOpenTime();
      }
   }
   
   TimeToStruct(old_orders, str2);
   
   all_time = ((60 * str1.hour + str1.min) + (60 * str2.hour + str2.min));
   
   return (all_time);
}
//+------------------------------------------------------------------+

//+CHECK TWO PREVIOUS DAYS FOR > 80% LOSS----------------------------+
bool pdchecker(const double todaybal)
{
   double day1 = 0, day2 = 0;
   day1 = yestorders(1);
   day2 = yestorders(2);
      
   double pdaybal1 = 0;
   double pdaybal2 = 0;
   
   bool day1off = false;
   bool day2off = false;
   
   pdaybal1 = todaybal + MathAbs(day1);
   pdaybal2 = pdaybal1 + MathAbs(day2);
   
   double multiplier1 = 0;
   if (pdaybal1 < 3000) multiplier1 = 10;
   else if (pdaybal1 >= 3000 && pdaybal1 < 5000) multiplier1 = 7;
   else if (pdaybal1 >= 5000 && pdaybal1 < 7500) multiplier1 = 6;
   else if (pdaybal1 >= 7500 && pdaybal1 < 10000) multiplier1 = 5;
   else if (pdaybal1 >= 10000 && pdaybal1 < 15000) multiplier1 = 4;
   else if (pdaybal1 >= 15000 && pdaybal1 < 20000) multiplier1 = 3;
   else if (pdaybal1 >= 20000 && pdaybal1 < 30000) multiplier1 = 2.5;
   else if (pdaybal1 >= 30000 && pdaybal1 < 40000) multiplier1 = 2.3;
   else if (pdaybal1 >= 40000 && pdaybal1 < 50000) multiplier1 = 2.1;
   else if (pdaybal1 >= 50000 && pdaybal1 < 60000) multiplier1 = 1.9;
   else if (pdaybal1 >= 60000 && pdaybal1 < 70000) multiplier1 = 1.75;
   else if (pdaybal1 >= 70000 && pdaybal1 < 80000) multiplier1 = 1.65;
   else if (pdaybal1 >= 80000 && pdaybal1 < 90000) multiplier1 = 1.6;
   else if (pdaybal1 >= 90000 && pdaybal1 < 100000) multiplier1 = 1.5;
   else if (pdaybal1 >= 100000) multiplier1 = 0.75;
   
   double day1reqlos = 0;
   
   day1reqlos = pdaybal1 - multiplier1 * pdaybal1;
   
   if ((pdaybal1 + day1) < day1reqlos) day1off = true;
   
   double multiplier2 = 0;
   if (pdaybal2 < 3000) multiplier2 = 10;
   else if (pdaybal2 >= 3000 && pdaybal2 < 5000) multiplier2 = 7;
   else if (pdaybal2 >= 5000 && pdaybal2 < 7500) multiplier2 = 6;
   else if (pdaybal2 >= 7500 && pdaybal2 < 10000) multiplier2 = 5;
   else if (pdaybal2 >= 10000 && pdaybal2 < 15000) multiplier2 = 4;
   else if (pdaybal2 >= 15000 && pdaybal2 < 20000) multiplier2 = 3;
   else if (pdaybal2 >= 20000 && pdaybal2 < 30000) multiplier2 = 2.5;
   else if (pdaybal2 >= 30000 && pdaybal2 < 40000) multiplier2 = 2.3;
   else if (pdaybal2 >= 40000 && pdaybal2 < 50000) multiplier2 = 2.1;
   else if (pdaybal2 >= 50000 && pdaybal2 < 60000) multiplier2 = 1.9;
   else if (pdaybal2 >= 60000 && pdaybal2 < 70000) multiplier2 = 1.75;
   else if (pdaybal2 >= 70000 && pdaybal2 < 80000) multiplier2 = 1.65;
   else if (pdaybal2 >= 80000 && pdaybal2 < 90000) multiplier2 = 1.6;
   else if (pdaybal2 >= 90000 && pdaybal2 < 100000) multiplier2 = 1.5;
   else if (pdaybal2 >= 100000) multiplier2 = 0.75;
   
   double day2reqlos = 0;
   
   day2reqlos = pdaybal2 - multiplier2 * pdaybal2;
   
   if ((pdaybal2 + day2) < day2reqlos) day2off = true;
   
   double totloss = 0;
   
   if (day1 < 0) totloss += MathAbs(day1);
   if (day2 < 0) totloss += MathAbs(day2);
   
   double reqloss = 0;
   
   reqloss = (multiplier1 * pdaybal1 / 100) + (multiplier2 * pdaybal2 / 100);
   
   if (totloss > reqloss && day1 != 0 && day2 != 0) { day1off = true; day2off = true; }
   
   //Print("day-2: " + day2 + " day-1: " + day1 + " day2reqlos " + day2reqlos + " day1reqlos: " + day1reqlos + " pdaybal2: " + pdaybal2 + " pdaybal1: " + pdaybal1 + " reqloss: " + reqloss + " totloss: " + totloss);
   
   if (day1off && day2off) return (true);
   else return (false);
}
//+------------------------------------------------------------------+

//+CHECKORDERS-------------------------------------------------------+
int openorders()
{
   int total = 0;
   for (int x = OrdersTotal() - 1; x >= 0; x--){
      if(OrderSelect(x, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
         { total++; }
   }
   return (total);
}
//+------------------------------------------------------------------+