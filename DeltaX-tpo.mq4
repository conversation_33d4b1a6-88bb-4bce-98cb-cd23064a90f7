#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property strict

#property indicator_chart_window
#property indicator_buffers 4
#property  indicator_width1  2
#property  indicator_width2  2
#property  indicator_width3  1
#property  indicator_width4  1

#property  indicator_style1  STYLE_SOLID
#property  indicator_style2  STYLE_SOLID
#property  indicator_style3  STYLE_SOLID
#property  indicator_style4  STYLE_SOLID

#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlack
#property indicator_color4 clrWhite

#define Name WindowExpertName()

//+INIT SEQUENCE-----------------------------------------------------+
int OnInit()
{
	IndicatorDigits(0);
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+MAIN RUN----------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("DeltaX expired, contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true) {
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1m_check)
		{
			new_1m_check = false;
		}
		tpocount();
	}//YesStop (expiry) end
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT SEQUENCE---------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

void tpocount() {
	datetime pers0 = iTime(_Symbol, PERIOD_D1, 0);
	int strat0 = iBarShift(_Symbol, PERIOD_M30, pers0, false);
	double Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));

	double tpocount[];
	double dhigh = iHigh(_Symbol, PERIOD_D1, 0);
	double dlow = iLow(_Symbol, PERIOD_D1, 0);
	double ddlow = iLow(_Symbol, PERIOD_D1, 0) / Pip;
	int points = (int)((1.0*(dhigh - dlow) / Pip) + 1);
	ArrayResize(tpocount, points + 1);

	//Print(strat0 + " " + dhigh + " " + dlow + " " + points + " " + ArraySize(tpocount));

	for (int i = strat0; i >= 0; i--) {
		int b = (int)((iLow(_Symbol, PERIOD_M30, i) - dlow) / Pip);
		int x = (int)((iHigh(_Symbol, PERIOD_M30, i) - iLow(_Symbol, PERIOD_M30, i)) / Pip);

		while (x > 0) {
			tpocount[x + b]++;
			x--;
		}
	}
	string obname;
	for (int i = points; i >= 0; i--) {
		//Print(tpocount[i] + " " + (dlow+(i*Pip)));
		obname = Name + i;
		int trip = (int)(tpocount[i] * 3600);
		objbase(obname, Time[iBarShift(_Symbol, PERIOD_CURRENT, pers0)], Time[iBarShift(_Symbol, PERIOD_CURRENT, pers0)] + trip, iLow(_Symbol, PERIOD_M30, strat0) + i*Pip, clrWhite);
	}
}

//+CREATE T-LINES----------------------------------------------------+
void objbase(string oname, datetime t1, datetime t2, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSet(oname, OBJPROP_WIDTH, 1);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_TIME1, t1);
	ObjectSet(oname, OBJPROP_TIME2, t2);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_PRICE2, pr1);
	ObjectSet(oname, OBJPROP_RAY, false);
}
//+------------------------------------------------------------------+

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 0);
}
//+------------------------------------------------------------------+