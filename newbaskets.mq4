//+------------------------------------------------------------------+
//|                                                   newbaskets.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 56
#property indicator_plots 56
input ENUM_TIMEFRAMES firsta = PERIOD_M5;
input ENUM_TIMEFRAMES seconda = PERIOD_D1;
input int daysa = 0; //For how many periods back (0 = current)
int lrlength = 72; //LR lines length
input string prefixa = ""; //Broker symbol prefix (before)
input string suffixa = ""; //Broker symbol suffix (after)
const string Name = MQLInfoString(MQL_PROGRAM_NAME);
   
const double LEVEL_38_2 = 25;
const double LEVEL_50 = 50;
const double LEVEL_61_8 = 75;

//#include <Math\Alglib\alglib.mqh>
#include <StructSort.mqh>
static double eur[], gbp[], aud[], nzd[], cad[], usd[], chf[], jpy[];
static double trophya[], trophyb[], trophyc[], trophyd[], trophye[], trophyf[], trophyg[], trophyh[];
static double eurstdu[], eurstdd[], gbpstdu[], gbpstdd[], audstdu[], audstdd[], nzdstdu[], nzdstdd[], cadstdu[], cadstdd[], usdstdu[], usdstdd[], jpystdu[], jpystdd[], chfstdu[], chfstdd[];
static double eurstdi[], gbpstdi[], audstdi[], nzdstdi[], cadstdi[], usdstdi[], jpystdi[], chfstdi[];
static double eursd[], gbpsd[], audsd[], nzdsd[], cadsd[], usdsd[], chfsd[], jpysd[];
static double eursdi[], gbpsdi[], audsdi[], nzdsdi[], cadsdi[], usdsdi[], chfsdi[], jpysdi[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   ChartSetDouble(0, CHART_FIXED_MIN, -1000);
   ChartSetDouble(0, CHART_FIXED_MAX, 1000);
   ChartSetDouble(0, CHART_PRICE_MIN, -1000);
   ChartSetDouble(0, CHART_PRICE_MAX, 1000);   
   
   IndicatorDigits(1);
   IndicatorBuffers(56);
   SetIndexBuffer(0, eur);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 3, clrDodgerBlue);
   SetIndexBuffer(1, gbp);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexBuffer(2, aud);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexBuffer(3, nzd);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 3, clrAqua);
   SetIndexBuffer(4, cad);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 3, clrPink);
   SetIndexBuffer(5, usd);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   SetIndexBuffer(6, chf);
   SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, 3, clrGray);
   SetIndexBuffer(7, jpy);
   SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, 3, clrYellow);
   SetIndexBuffer(8, trophya);
   SetIndexStyle(8, DRAW_LINE, STYLE_SOLID, 2, clrDodgerBlue);
   SetIndexBuffer(9, trophyb);
   SetIndexStyle(9, DRAW_LINE, STYLE_SOLID, 2, clrRed);
   SetIndexBuffer(10, trophyc);
   SetIndexStyle(10, DRAW_LINE, STYLE_SOLID, 2, clrOrange);
   SetIndexBuffer(11, trophyd);
   SetIndexStyle(11, DRAW_LINE, STYLE_SOLID, 2, clrAqua);
   SetIndexBuffer(12, trophye);
   SetIndexStyle(12, DRAW_LINE, STYLE_SOLID, 2, clrPink);
   SetIndexBuffer(13, trophyf);
   SetIndexStyle(13, DRAW_LINE, STYLE_SOLID, 2, clrGreen);
   SetIndexBuffer(14, trophyg);
   SetIndexStyle(14, DRAW_LINE, STYLE_SOLID, 2, clrGray);
   SetIndexBuffer(15, trophyh);
   SetIndexStyle(15, DRAW_LINE, STYLE_SOLID, 2, clrYellow);
   SetIndexBuffer(16, eurstdu);
   SetIndexStyle(16, DRAW_LINE, STYLE_SOLID, 2, clrDodgerBlue);
   SetIndexBuffer(17, eurstdd);
   SetIndexStyle(17, DRAW_LINE, STYLE_SOLID, 3, clrDodgerBlue);
   SetIndexBuffer(18, gbpstdu);
   SetIndexStyle(18, DRAW_LINE, STYLE_SOLID, 2, clrRed);
   SetIndexBuffer(19, gbpstdd);
   SetIndexStyle(19, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexBuffer(20, audstdu);
   SetIndexStyle(20, DRAW_LINE, STYLE_SOLID, 2, clrOrange);
   SetIndexBuffer(21, audstdd);
   SetIndexStyle(21, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexBuffer(22, nzdstdu);
   SetIndexStyle(22, DRAW_LINE, STYLE_SOLID, 2, clrAqua);
   SetIndexBuffer(23, nzdstdd);
   SetIndexStyle(23, DRAW_LINE, STYLE_SOLID, 3, clrAqua);
   SetIndexBuffer(24, cadstdu);
   SetIndexStyle(24, DRAW_LINE, STYLE_SOLID, 2, clrPink);
   SetIndexBuffer(25, cadstdd);
   SetIndexStyle(25, DRAW_LINE, STYLE_SOLID, 3, clrPink);
   SetIndexBuffer(26, usdstdu);
   SetIndexStyle(26, DRAW_LINE, STYLE_SOLID, 2, clrGreen);
   SetIndexBuffer(27, usdstdd);
   SetIndexStyle(27, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   SetIndexBuffer(28, chfstdu);
   SetIndexStyle(28, DRAW_LINE, STYLE_SOLID, 2, clrGray);
   SetIndexBuffer(29, chfstdd);
   SetIndexStyle(29, DRAW_LINE, STYLE_SOLID, 3, clrGray);
   SetIndexBuffer(30, jpystdu);
   SetIndexStyle(30, DRAW_LINE, STYLE_SOLID, 2, clrYellow);
   SetIndexBuffer(31, jpystdd);
   SetIndexStyle(31, DRAW_LINE, STYLE_SOLID, 3, clrYellow);
   SetIndexBuffer(32, eurstdi);
   SetIndexStyle(32, DRAW_LINE, STYLE_SOLID, 3, clrDodgerBlue);
   SetIndexBuffer(33, gbpstdi);
   SetIndexStyle(33, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexBuffer(34, audstdi);
   SetIndexStyle(34, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexBuffer(35, nzdstdi);
   SetIndexStyle(35, DRAW_LINE, STYLE_SOLID, 3, clrAqua);
   SetIndexBuffer(36, cadstdi);
   SetIndexStyle(36, DRAW_LINE, STYLE_SOLID, 3, clrPink);
   SetIndexBuffer(37, usdstdi);
   SetIndexStyle(37, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   SetIndexBuffer(38, chfstdi);
   SetIndexStyle(38, DRAW_LINE, STYLE_SOLID, 3, clrGray);
   SetIndexBuffer(39, jpystdi);
   SetIndexStyle(39, DRAW_LINE, STYLE_SOLID, 3, clrYellow);
   SetIndexBuffer(40, eursd);
   SetIndexStyle(40, DRAW_LINE, STYLE_SOLID, 2, clrDodgerBlue);
   SetIndexBuffer(41, gbpsd);
   SetIndexStyle(41, DRAW_LINE, STYLE_SOLID, 2, clrRed);
   SetIndexBuffer(42, audsd);
   SetIndexStyle(42, DRAW_LINE, STYLE_SOLID, 2, clrOrange);
   SetIndexBuffer(43, nzdsd);
   SetIndexStyle(43, DRAW_LINE, STYLE_SOLID, 2, clrAqua);
   SetIndexBuffer(44, cadsd);
   SetIndexStyle(44, DRAW_LINE, STYLE_SOLID, 2, clrPink);
   SetIndexBuffer(45, usdsd);
   SetIndexStyle(45, DRAW_LINE, STYLE_SOLID, 2, clrGreen);
   SetIndexBuffer(46, chfsd);
   SetIndexStyle(46, DRAW_LINE, STYLE_SOLID, 2, clrGray);
   SetIndexBuffer(47, jpysd);
   SetIndexStyle(47, DRAW_LINE, STYLE_SOLID, 2, clrYellow);
   SetIndexBuffer(48, eursdi);
   SetIndexStyle(48, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexBuffer(49, gbpsdi);
   SetIndexStyle(49, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexBuffer(50, audsdi);
   SetIndexStyle(50, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexBuffer(51, nzdsdi);
   SetIndexStyle(51, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexBuffer(52, cadsdi);
   SetIndexStyle(52, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexBuffer(53, usdsdi);
   SetIndexStyle(53, DRAW_LINE, STYLE_SOLID, 1, clrGreen);
   SetIndexBuffer(54, chfsdi);
   SetIndexStyle(54, DRAW_LINE, STYLE_SOLID, 1, clrGray);
   SetIndexBuffer(55, jpysdi);
   SetIndexStyle(55, DRAW_LINE, STYLE_SOLID, 1, clrYellow);
//---
   return(INIT_SUCCEEDED);
  }

static double tickval = 0;
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   //MqlTick tick;
     
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);   
   
   if(bid != tickval)
   {
      tickval = bid;
      build();
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
 
// Define a struct to hold basket values and its associated symbols
struct BasketData {
	double fopen;
	double percent;
	string symbol;
};
	
void build()
{
   string prefix = prefixa;
   string suffix = suffixa;
   int first = firsta;
   int second = seconda;
   int days = daysa;
   
   int limit = iBarShift(_Symbol, first, iTime(_Symbol, second, days), false);
      
   static int prevBackgroundColor = 0;
   static int prevForegroundColor = 0;
   static bool prevScaleFix = false;
   if (prevBackgroundColor != clrBlack || prevForegroundColor != 0 || prevScaleFix != true) {
      ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
      ChartSetInteger(0, CHART_FOREGROUND, 0);
      ChartSetInteger(0, CHART_SCALEFIX, 0, true);
      prevBackgroundColor = clrBlack;
      prevForegroundColor = 0;
      prevScaleFix = true;
   }
   
   SymbolInfo symbolInfo( prefix, suffix, first, second, days );
   symbolInfo.CalculateOpenValues( limit );
   
   string obname;
   
   double eurperc = 0;
   if (eur[0] > 0) eurperc = CalculatePercentageChange(eur[0], eur[ArrayMaximum(eur, limit, 0)]);
   if (eur[0] < 0) eurperc = CalculatePercentageChange(eur[0], eur[ArrayMinimum(eur, limit, 0)]);
   string eur1, eur2;
   eur1 = StringFormat("%.0f", ::eur[0]);
   eur2 = StringFormat(" - %.2f", eurperc);
   StringAdd(eur1, eur2);
   double gbpperc = 0;
   if (gbp[0] > 0) gbpperc = CalculatePercentageChange(gbp[0], gbp[ArrayMaximum(gbp, limit, 0)]);
   if (gbp[0] < 0) gbpperc = CalculatePercentageChange(gbp[0], gbp[ArrayMinimum(gbp, limit, 0)]);
   string gbp1, gbp2;
   gbp1 = StringFormat("%.0f", ::gbp[0]);
   gbp2 = StringFormat(" - %.2f", gbpperc);
   StringAdd(gbp1, gbp2);
   double audperc = 0;
   if (aud[0] > 0) audperc = CalculatePercentageChange(aud[0], aud[ArrayMaximum(aud, limit, 0)]);
   if (aud[0] < 0) audperc = CalculatePercentageChange(aud[0], aud[ArrayMinimum(aud, limit, 0)]);
   string aud1, aud2;
   aud1 = StringFormat("%.0f", ::aud[0]);
   aud2 = StringFormat(" - %.2f", audperc);
   StringAdd(aud1, aud2);
   double nzdperc = 0;
   if (nzd[0] > 0) nzdperc = CalculatePercentageChange(nzd[0], nzd[ArrayMaximum(nzd, limit, 0)]);
   if (nzd[0] < 0) nzdperc = CalculatePercentageChange(nzd[0], nzd[ArrayMinimum(nzd, limit, 0)]);
   string nzd1, nzd2;
   nzd1 = StringFormat("%.0f", ::nzd[0]);
   nzd2 = StringFormat(" - %.2f", nzdperc);
   StringAdd(nzd1, nzd2);
   double cadperc = 0;
   if (cad[0] > 0) cadperc = CalculatePercentageChange(cad[0], cad[ArrayMaximum(cad, limit, 0)]);
   if (cad[0] < 0) cadperc = CalculatePercentageChange(cad[0], cad[ArrayMinimum(cad, limit, 0)]);
   string cad1, cad2;
   cad1 = StringFormat("%.0f", ::cad[0]);
   cad2 = StringFormat(" - %.2f", cadperc);
   StringAdd(cad1, cad2);
   double usdperc = 0;
   if (usd[0] > 0) usdperc = CalculatePercentageChange(usd[0], usd[ArrayMaximum(usd, limit, 0)]);
   if (usd[0] < 0) usdperc = CalculatePercentageChange(usd[0], usd[ArrayMinimum(usd, limit, 0)]);
   string usd1, usd2;
   usd1 = StringFormat("%.0f", ::usd[0]);
   usd2 = StringFormat(" - %.2f", usdperc);
   StringAdd(usd1, usd2);
   double chfperc = 0;
   if (chf[0] > 0) chfperc = CalculatePercentageChange(chf[0], chf[ArrayMaximum(chf, limit, 0)]);
   if (chf[0] < 0) chfperc = CalculatePercentageChange(chf[0], chf[ArrayMinimum(chf, limit, 0)]);
   string chf1, chf2;
   chf1 = StringFormat("%.0f", ::chf[0]);
   chf2 = StringFormat(" - %.2f", chfperc);
   StringAdd(chf1, chf2);
   double jpyperc = 0;
   if (jpy[0] > 0) jpyperc = CalculatePercentageChange(jpy[0], jpy[ArrayMaximum(jpy, limit, 0)]);
   if (jpy[0] < 0) jpyperc = CalculatePercentageChange(jpy[0], jpy[ArrayMinimum(jpy, limit, 0)]);
   string jpy1, jpy2;
   jpy1 = StringFormat("%.0f", ::jpy[0]);
   jpy2 = StringFormat(" - %.2f", jpyperc);
   StringAdd(jpy1, jpy2);

	// Declare a dynamic array of PairData
	BasketData baskets[8];
	
	baskets[0].fopen = eur[0]; baskets[0].percent = eurperc; baskets[0].symbol = "EUR";
	baskets[1].fopen = gbp[0]; baskets[1].percent = gbpperc; baskets[1].symbol = "GBP";
	baskets[2].fopen = aud[0]; baskets[2].percent = audperc; baskets[2].symbol = "AUD";
	baskets[3].fopen = nzd[0]; baskets[3].percent = nzdperc; baskets[3].symbol = "NZD";
	baskets[4].fopen = cad[0]; baskets[4].percent = cadperc; baskets[4].symbol = "CAD";
	baskets[5].fopen = usd[0]; baskets[5].percent = usdperc; baskets[5].symbol = "USD";
	baskets[6].fopen = chf[0]; baskets[6].percent = chfperc; baskets[6].symbol = "CHF";
	baskets[7].fopen = jpy[0]; baskets[7].percent = jpyperc; baskets[7].symbol = "JPY";
	
	RankCurrencyPairs(baskets);
	
	// Declare a dynamic array of PairData
	BasketData basketsMin[8];
	
	basketsMin[0].fopen = eur[ArrayMinimum(eur, limit, 1)]; basketsMin[0].percent = eurperc; basketsMin[0].symbol = "EUR";
	basketsMin[1].fopen = gbp[ArrayMinimum(gbp, limit, 1)]; basketsMin[1].percent = gbpperc; basketsMin[1].symbol = "GBP";
	basketsMin[2].fopen = aud[ArrayMinimum(aud, limit, 1)]; basketsMin[2].percent = audperc; basketsMin[2].symbol = "AUD";
	basketsMin[3].fopen = nzd[ArrayMinimum(nzd, limit, 1)]; basketsMin[3].percent = nzdperc; basketsMin[3].symbol = "NZD";
	basketsMin[4].fopen = cad[ArrayMinimum(cad, limit, 1)]; basketsMin[4].percent = cadperc; basketsMin[4].symbol = "CAD";
	basketsMin[5].fopen = usd[ArrayMinimum(usd, limit, 1)]; basketsMin[5].percent = usdperc; basketsMin[5].symbol = "USD";
	basketsMin[6].fopen = chf[ArrayMinimum(chf, limit, 1)]; basketsMin[6].percent = chfperc; basketsMin[6].symbol = "CHF";
	basketsMin[7].fopen = jpy[ArrayMinimum(jpy, limit, 1)]; basketsMin[7].percent = jpyperc; basketsMin[7].symbol = "JPY";
	
	RankCurrencyPairs(basketsMin);
	
	// Declare a dynamic array of PairData
	BasketData basketsMax[8];
	
	basketsMax[0].fopen = eur[ArrayMaximum(eur, limit, 1)]; basketsMax[0].percent = eurperc; basketsMax[0].symbol = "EUR";
	basketsMax[1].fopen = gbp[ArrayMaximum(gbp, limit, 1)]; basketsMax[1].percent = gbpperc; basketsMax[1].symbol = "GBP";
	basketsMax[2].fopen = aud[ArrayMaximum(aud, limit, 1)]; basketsMax[2].percent = audperc; basketsMax[2].symbol = "AUD";
	basketsMax[3].fopen = nzd[ArrayMaximum(nzd, limit, 1)]; basketsMax[3].percent = nzdperc; basketsMax[3].symbol = "NZD";
	basketsMax[4].fopen = cad[ArrayMaximum(cad, limit, 1)]; basketsMax[4].percent = cadperc; basketsMax[4].symbol = "CAD";
	basketsMax[5].fopen = usd[ArrayMaximum(usd, limit, 1)]; basketsMax[5].percent = usdperc; basketsMax[5].symbol = "USD";
	basketsMax[6].fopen = chf[ArrayMaximum(chf, limit, 1)]; basketsMax[6].percent = chfperc; basketsMax[6].symbol = "CHF";
	basketsMax[7].fopen = jpy[ArrayMaximum(jpy, limit, 1)]; basketsMax[7].percent = jpyperc; basketsMax[7].symbol = "JPY";
	
	double eurper = (eurperc + gbpperc) / 2;
	double oceper = (audperc + nzdperc) / 2;
	double namper = (usdperc + cadperc) / 2;
	double risper = (chfperc + jpyperc) / 2;	
	
	RankCurrencyPairs(basketsMax);
	//Print(basketsMin[0].symbol + " " + basketsMax[7].symbol + " " + basketsMax[7].fopen);
	
	string f1 = baskets[7].symbol;
	string f2 = " / " + baskets[0].symbol + " : ";
	StringAdd(f1, f2);
	string f3 = StringFormat("%.2f", (baskets[7].percent + baskets[0].percent) / 2);
	StringAdd(f1, f3);
	string s1 = basketsMax[7].symbol;
	string s2 = " / " + basketsMin[0].symbol + " : ";
	StringAdd(s1, s2);
	string s3 = StringFormat("%.2f", (basketsMax[7].percent + basketsMin[0].percent) / 2);
	StringAdd(s1, s3);
	
	string eu1 = StringFormat("EUR: %.2f", eurper);
	string oc1 = StringFormat("OCE: %.2f", oceper);
	string na1 = StringFormat("NAM: %.2f", namper);
	string ri1 = StringFormat("RIS: %.2f", risper);
	string to1 = StringFormat("TOT: %.2f", eurper + oceper + namper + risper);
	
	//Print(f1);
	
	//String("Best & worse baskets: " + baskets[7].symbol + " / " + baskets[0].symbol + " combined retrace percent: " + DoubleToString(baskets[7].percent + baskets[0].percent, 2));
	
   obname = Name + "euro";
   Texter(obname, ::eur[0], 0, eur1, clrDodgerBlue);
   obname = Name + "gbpo";
   Texter(obname, ::gbp[0], 0, gbp1, clrRed);
   obname = Name + "audo";
   Texter(obname, ::aud[0], 0, aud1, clrOrange);
   obname = Name + "nzdo";
   Texter(obname, ::nzd[0], 0, nzd1, clrAqua);
   obname = Name + "cado";
   Texter(obname, ::cad[0], 0, cad1, clrPink);
   obname = Name + "usdo";
   Texter(obname, ::usd[0], 0, usd1, clrGreen);
   obname = Name + "chfo";
   Texter(obname, ::chf[0], 0, chf1, clrGray);
   obname = Name + "jpyo";
   Texter(obname, ::jpy[0], 0, jpy1, clrYellow);
   obname = Name + "tot";
   Texter(obname, baskets[7].fopen + baskets[0].fopen, 0, f1, clrWhite);
   obname = Name + "totold";
   Texter(obname, baskets[7].fopen + baskets[0].fopen - 75, 0, s1, clrYellow);
   
   obname = Name + "eurper";
   LabelMake(obname, 3, 100, 100, eu1, 10, clrViolet);
   obname = Name + "oceper";
   LabelMake(obname, 3, 100, 80, oc1, 10, clrYellow);
   obname = Name + "namper";
   LabelMake(obname, 3, 100, 60, na1, 10, clrPink);
   obname = Name + "risper";
   LabelMake(obname, 3, 100, 40, ri1, 10, clrRed);
   obname = Name + "totper";
   LabelMake(obname, 3, 100, 20, to1, 10, clrWhite);
   
   double Min1 = MathMin(eur[ArrayMinimum(eur, limit - 1, 0)], gbp[ArrayMinimum(gbp, limit - 1, 0)]);
   double Min2 = MathMin(aud[ArrayMinimum(aud, limit - 1, 0)], nzd[ArrayMinimum(nzd, limit - 1, 0)]);
   double Min3 = MathMin(cad[ArrayMinimum(cad, limit - 1, 0)], usd[ArrayMinimum(usd, limit - 1, 0)]);
   double Min4 = MathMin(chf[ArrayMinimum(chf, limit - 1, 0)], jpy[ArrayMinimum(jpy, limit - 1, 0)]);
   
   double Min5 = MathMin(Min1, Min2);
   double Min6 = MathMin(Min3, Min4);
   
   double Min7 = MathMin(Min5, Min6);
   
   double Max1 = MathMax(eur[ArrayMaximum(eur, limit - 1, 0)], gbp[ArrayMaximum(gbp, limit - 1, 0)]);
   double Max2 = MathMax(aud[ArrayMaximum(aud, limit - 1, 0)], nzd[ArrayMaximum(nzd, limit - 1, 0)]);
   double Max3 = MathMax(cad[ArrayMaximum(cad, limit - 1, 0)], usd[ArrayMaximum(usd, limit - 1, 0)]);
   double Max4 = MathMax(chf[ArrayMaximum(chf, limit - 1, 0)], jpy[ArrayMaximum(jpy, limit - 1, 0)]);
   
   double Max5 = MathMax(Max1, Max2);
   double Max6 = MathMax(Max3, Max4);
   
   double Max7 = MathMax(Max5, Max6);   
   
   //LR regression lines on adr bands   
   LRRegression lr;
   
   if (baskets[0].symbol == "EUR" || baskets[7].symbol == "EUR" || basketsMax[7].symbol == "EUR" || basketsMin[0].symbol == "EUR")
      { //lr.CalculateSD(eursd, eur, trophyh, 0, 0, 1.5); lr.CalculateSD(eursdi, eur, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::eur[0] > 0) CreateATRLines("e", eur[ArrayMaximum(eur, limit, 1)], clrDodgerBlue);
            if (::eur[0] < 0) CreateATRLines("e", eur[ArrayMinimum(eur, limit, 1)], clrDodgerBlue);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(eurstdu, eur, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(eurstdd, eur, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(eurstdi, eur, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "e40"); ObjectDelete(0, Name + "e50"); ObjectDelete(0, Name + "e60"); }
   if (baskets[0].symbol == "GBP" || baskets[7].symbol == "GBP" || basketsMax[7].symbol == "GBP" || basketsMin[0].symbol == "GBP")
      { //lr.CalculateSD(gbpsd, gbp, trophyh, 0, 0, 1.5); lr.CalculateSD(gbpsdi, gbp, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::gbp[0] > 0) CreateATRLines("g", gbp[ArrayMaximum(gbp, limit, 1)], clrRed);
            if (::gbp[0] < 0) CreateATRLines("g", gbp[ArrayMinimum(gbp, limit, 1)], clrRed);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(gbpstdu, gbp, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(gbpstdd, gbp, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(gbpstdi, gbp, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "g40"); ObjectDelete(0, Name + "g50"); ObjectDelete(0, Name + "g60"); }
   if (baskets[0].symbol == "AUD" || baskets[7].symbol == "AUD" || basketsMax[7].symbol == "AUD" || basketsMin[0].symbol == "AUD")
      { //lr.CalculateSD(audsd, aud, trophyh, 0, 0, 1.5); lr.CalculateSD(audsdi, aud, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::aud[0] > 0) CreateATRLines("a", aud[ArrayMaximum(aud, limit, 1)], clrOrange);
            if (::aud[0] < 0) CreateATRLines("a", aud[ArrayMinimum(aud, limit, 1)], clrOrange);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(audstdu, aud, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(audstdd, aud, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(audstdi, aud, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "a40"); ObjectDelete(0, Name + "a50"); ObjectDelete(0, Name + "a60"); }
   if (baskets[0].symbol == "NZD" || baskets[7].symbol == "NZD" || basketsMax[7].symbol == "NZD" || basketsMin[0].symbol == "NZD")
      { //lr.CalculateSD(nzdsd, nzd, trophyh, 0, 0, 1.5); lr.CalculateSD(nzdsdi, nzd, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::nzd[0] > 0) CreateATRLines("n", nzd[ArrayMaximum(nzd, limit, 1)], clrAqua);
            if (::nzd[0] < 0) CreateATRLines("n", nzd[ArrayMinimum(nzd, limit, 1)], clrAqua);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(nzdstdu, nzd, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(nzdstdd, nzd, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(nzdstdi, nzd, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "n40"); ObjectDelete(0, Name + "n50"); ObjectDelete(0, Name + "n60"); }
   if (baskets[0].symbol == "CAD" || baskets[7].symbol == "CAD" || basketsMax[7].symbol == "CAD" || basketsMin[0].symbol == "CAD")
      { //lr.CalculateSD(cadsd, cad, trophyh, 0, 0, 1.5); lr.CalculateSD(cadsdi, cad, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::cad[0] > 0) CreateATRLines("c", cad[ArrayMaximum(cad, limit, 1)], clrPink);
            if (::cad[0] < 0) CreateATRLines("c", cad[ArrayMinimum(cad, limit, 1)], clrPink);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(cadstdu, cad, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(cadstdd, cad, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(cadstdi, cad, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "c40"); ObjectDelete(0, Name + "c50"); ObjectDelete(0, Name + "c60"); }
   if (baskets[0].symbol == "USD" || baskets[7].symbol == "USD" || basketsMax[7].symbol == "USD" || basketsMin[0].symbol == "USD")
      { //lr.CalculateSD(usdsd, usd, trophyh, 0, 0, 1.5); lr.CalculateSD(usdsdi, usd, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::usd[0] > 0) CreateATRLines("u", usd[ArrayMaximum(usd, limit, 1)], clrGreen);
            if (::usd[0] < 0) CreateATRLines("u", usd[ArrayMinimum(usd, limit, 1)], clrGreen);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(usdstdu, usd, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(usdstdd, usd, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(usdstdi, usd, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "u40"); ObjectDelete(0, Name + "u50"); ObjectDelete(0, Name + "u60"); }
   if (baskets[0].symbol == "CHF" || baskets[7].symbol == "CHF" || basketsMax[7].symbol == "CHF" || basketsMin[0].symbol == "CHF")
      { //lr.CalculateSD(chfsd, chf, trophyh, 0, 0, 1.5); lr.CalculateSD(chfsdi, chf, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::chf[0] > 0) CreateATRLines("f", chf[ArrayMaximum(chf, limit, 1)], clrGray);
            if (::chf[0] < 0) CreateATRLines("f", chf[ArrayMinimum(chf, limit, 1)], clrGray);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(chfstdu, chf, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(chfstdd, chf, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(chfstdi, chf, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "f40"); ObjectDelete(0, Name + "f50"); ObjectDelete(0, Name + "f60"); }
   if (baskets[0].symbol == "JPY" || baskets[7].symbol == "JPY" || basketsMax[7].symbol == "JPY" || basketsMin[0].symbol == "JPY")
      { //lr.CalculateSD(jpysd, jpy, trophyh, 0, 0, 1.5); lr.CalculateSD(jpysdi, jpy, trophyh, 0, 0, 2);
         if (Hour() >= 8)
         {
            if (::jpy[0] > 0) CreateATRLines("j", jpy[ArrayMaximum(jpy, limit, 1)], clrYellow);
            if (::jpy[0] < 0) CreateATRLines("j", jpy[ArrayMinimum(jpy, limit, 1)], clrYellow);
         }
         if (Hour() >= 3) {
            lr.CalculateRegressionFromTime(jpystdu, jpy, 3, 30);
         }
         if (Hour() >= 9) {
            lr.CalculateRegressionFromTime(jpystdd, jpy, 9, 30);
         }
         if (Hour() >= 15) {
            lr.CalculateRegressionFromTime(jpystdi, jpy, 15, 30);
         }
      }
   else { ObjectDelete(0, Name + "j40"); ObjectDelete(0, Name + "j50"); ObjectDelete(0, Name + "j60"); }
   //eur
   lr.CalculateRegressionFromTime(trophya, eur, 0, 0);
   //gbp
   lr.CalculateRegressionFromTime(trophyb, gbp, 0, 0);
   //aud
   lr.CalculateRegressionFromTime(trophyc, aud, 0, 0);
   //nzd
   lr.CalculateRegressionFromTime(trophyd, nzd, 0, 0);
   //cad
   lr.CalculateRegressionFromTime(trophye, cad, 0, 0);
   //usd
   lr.CalculateRegressionFromTime(trophyf, usd, 0, 0);
   //chf
   lr.CalculateRegressionFromTime(trophyg, chf, 0, 0);
   //jpy
   lr.CalculateRegressionFromTime(trophyh, jpy, 0, 0);
      
   static double Min = 0;
   static double Max = 0;
   if (Min >= Min7 - 100 || Max <= Max7 + 100) {
      Min = Min7 - 100;
      Max = Max7 + 100;
      ChartSetDouble(0, CHART_FIXED_MIN, Min);
      ChartSetDouble(0, CHART_FIXED_MAX, Max);
      ChartSetDouble(0, CHART_PRICE_MIN, Min);
      ChartSetDouble(0, CHART_PRICE_MAX, Max);
   }
   
   if (Hour() <= 1 && Min > -1000 && Max < 1000)
   {
      ChartSetDouble(0, CHART_FIXED_MIN, -1000);
      ChartSetDouble(0, CHART_FIXED_MAX, 1000);
      ChartSetDouble(0, CHART_PRICE_MIN, -1000);
      ChartSetDouble(0, CHART_PRICE_MAX, 1000);
   }
}
//+------------------------------------------------------------------+
// Function to rank currency pairs
void RankCurrencyPairs(BasketData &baskets[]) {
   ArraySortStruct(baskets, fopen);
}

// Function to calculate percentage change
double CalculatePercentageChange(double currentValue, double previousValue) {
   return MathAbs((currentValue - previousValue) / previousValue) * 100;
}
//+------------------------------------------------------------------+
// Function to create ATR lines
void CreateATRLines(string symbol, double value, color colour) {
   CreateATRLine(symbol + "25", "25", value - LEVEL_38_2 * value / 100, colour, 1);
   CreateATRLine(symbol + "50", "50", value - LEVEL_50 * value / 100, colour, 2);
   CreateATRLine(symbol + "75", "75", value - LEVEL_61_8 * value / 100, colour, 2);
}
//+------------------------------------------------------------------+

int CountBarsFromTime(int hour, int minute)
{
   // Calculate the datetime for 03:00 of the current day
   datetime startTime = iTime(NULL, 0, 0); // Get the time of the most recent bar
   startTime = startTime - TimeHour(startTime) * 3600 - TimeMinute(startTime) * 60; // Midnight
   startTime += hour * 3600 + minute * 60; // Add 03:00 (hour=3, minute=0)
   
   int barsCount = 0;
   
   // Loop through bars starting from the most recent
   for (int i = 0; i < Bars; i++)
   {
      datetime barTime = iTime(NULL, 0, i);
      
      // Stop counting when the bar time is earlier than the target start time
      if (barTime < startTime)
         break;
         
      barsCount++;
   }
   
   return barsCount;
}

class LRRegression {
   private:
      int timesb;
      double a, b; // Removed unnecessary 'c' variable
      double sumx, sumy, sumxy, sumx2; // More descriptive variable names

   public:
      /*
      void CalculateRegression(double &array[], double &marray[], int shift, int limit) {
         timesb = limit - 1;
         if (timesb <= 0) return; // Handle empty data gracefully

         sumx = 0.0;
         sumy = 0.0;
         sumxy = 0.0;
         sumx2 = 0.0;
   
         for (int j = 0; j < timesb; j++) {
            sumy += marray[j];
            sumxy += marray[j] * j;
            sumx += j;
            sumx2 += j * j;
         }
         
         double denominator = timesb * sumx2 - sumx * sumx;
         if (MathAbs(denominator) < 1e-10) { // Check for near-zero denominator to avoid division by zero
            a = 0; // Or handle this case appropriately, e.g., use a default slope
            b = sumy / timesb; // Average value if slope cannot be calculated
         } else {
            a = (sumxy * timesb - sumx * sumy) / denominator;
            b = (sumy - sumx * a) / timesb;
         }
   
         for (int j = 0; j < timesb; j++) {
            array[j] = a * j + b;
         }
      }
      */

      void CalculateRegressionFromTime(double &array[], double &marray[], int hour, int min) {
         timesb = CountBarsFromTime(hour, min); // Use the existing CountBarsFromTime function
         if (timesb <= 0) return; // Handle empty data gracefully
   
         sumx = 0.0;
         sumy = 0.0;
         sumxy = 0.0;
         sumx2 = 0.0;
   
         for (int j = 0; j < timesb; j++) {
            sumy += marray[j];
            sumxy += marray[j] * j;
            sumx += j;
            sumx2 += j * j;
         }

         double denominator = timesb * sumx2 - sumx * sumx;
         if (MathAbs(denominator) < 1e-10) { // Check for near-zero denominator
            a = 0; // Or handle this case appropriately
            b = sumy / timesb; // Average value if slope cannot be calculated
         } else {
            a = (sumxy * timesb - sumx * sumy) / denominator;
            b = (sumy - sumx * a) / timesb;
         }
   
         for (int j = 0; j < timesb; j++) {
            array[j] = a * j + b;
         }
      }

      void CalculateSD(double &outarray[], double &array[], double &marray[], int hour, int min, double sd) {
         timesb = CountBarsFromTime(hour, min); // Use the existing CountBarsFromTime function
         if (timesb <= 0) return; // Handle empty data gracefully
         
         if (2 * lrlength < timesb)
         {
            for (int j = lrlength - 1; j >= 0; j--)  
            {
               //Inside SD calculation
               double ab = 0;
               double bc = 0;
               double cd = 0;
               
               for (int x = lrlength - 1 + j; x >= j; x--){
                  ab += array[x] / lrlength;
               }
               
               for (int x = lrlength - 1 + j; x >= j; x--){
                  bc += MathPow(array[x] - ab, 2);
               }
               
               cd = MathSqrt(bc / lrlength);
               
               if (array[0] > 0) outarray[j] = marray[j] - sd * cd;
               else outarray[j] = marray[j] + sd * cd;
            }
         }
      }
};

class SymbolInfo {
   private:
      string prefix;
      string suffix;
      int first;
      int second;
      int days;

   public:
      SymbolInfo( string symbolprefix, string symbolsuffix, int symbolfirst, int symbolsecond, int symboldays ) {
         this.prefix = symbolprefix;
         this.suffix = symbolsuffix;
         this.first = symbolfirst;
         this.second = symbolsecond;
         this.days = symboldays;
      }

      double GetPipValue( string strSymbol ) {
         double dblCalcPipValue = MarketInfo( strSymbol, MODE_TICKVALUE );
         if (MarketInfo(strSymbol, MODE_DIGITS) >= 3) dblCalcPipValue *= 10;
         return dblCalcPipValue;
      }

      double GetOpenValue( string strSymbol, int i ) {
         double a = iClose(strSymbol, first, i);
         double b = iClose(strSymbol, second, days + 1);
         return (a - b) / MarketInfo(strSymbol, MODE_POINT) / 10 * GetPipValue(strSymbol);
      }

      void CalculateOpenValues( int limit ) {
           
         static string eu = prefix + "EURUSD" + suffix;
         static string eg = prefix + "EURGBP" + suffix;
         static string ea = prefix + "EURAUD" + suffix;
         static string en = prefix + "EURNZD" + suffix;
         static string ec = prefix + "EURCAD" + suffix;
         static string ef = prefix + "EURCHF" + suffix;
         static string ej = prefix + "EURJPY" + suffix;
   
         static string gu = prefix + "GBPUSD" + suffix;
         static string ga = prefix + "GBPAUD" + suffix;
         static string gn = prefix + "GBPNZD" + suffix;
         static string gc = prefix + "GBPCAD" + suffix;
         static string gf = prefix + "GBPCHF" + suffix;
         static string gj = prefix + "GBPJPY" + suffix;
   
         static string au = prefix + "AUDUSD" + suffix;
         static string an = prefix + "AUDNZD" + suffix;
         static string ac = prefix + "AUDCAD" + suffix;
         static string af = prefix + "AUDCHF" + suffix;
         static string aj = prefix + "AUDJPY" + suffix;
   
         static string nu = prefix + "NZDUSD" + suffix;
         static string nc = prefix + "NZDCAD" + suffix;
         static string nf = prefix + "NZDCHF" + suffix;
         static string nj = prefix + "NZDJPY" + suffix;
   
         static string uc = prefix + "USDCAD" + suffix;
         static string cf = prefix + "CADCHF" + suffix;
         static string cj = prefix + "CADJPY" + suffix;
   
         static string uf = prefix + "USDCHF" + suffix;
         static string fj = prefix + "CHFJPY" + suffix;
   
         static string uj = prefix + "USDJPY" + suffix;
         
         static double preveufopen = 0;
         static double eufopen = 0;
         static double prevegfopen = 0;
         static double egfopen = 0;
         static double preveafopen = 0;
         static double eafopen = 0;
         static double prevenfopen = 0;
         static double enfopen = 0;
         static double prevecfopen = 0;
         static double ecfopen = 0;
         static double preveffopen = 0;
         static double effopen = 0;
         static double prevejfopen = 0;
         static double ejfopen = 0;
         
         static double prevgufopen = 0;
         static double gufopen = 0;
         static double prevgafopen = 0;
         static double gafopen = 0;
         static double prevgnfopen = 0;
         static double gnfopen = 0;
         static double prevgcfopen = 0;
         static double gcfopen = 0;
         static double prevgffopen = 0;
         static double gffopen = 0;
         static double prevgjfopen = 0;
         static double gjfopen = 0;
         
         static double prevaufopen = 0;
         static double aufopen = 0;
         static double prevanfopen = 0;
         static double anfopen = 0;
         static double prevacfopen = 0;
         static double acfopen = 0;
         static double prevaffopen = 0;
         static double affopen = 0;
         static double prevajfopen = 0;
         static double ajfopen = 0;
         
         static double prevnufopen = 0;
         static double nufopen = 0;
         static double prevncfopen = 0;
         static double ncfopen = 0;
         static double prevnffopen = 0;
         static double nffopen = 0;
         static double prevnjfopen = 0;
         static double njfopen = 0;
         
         static double prevucfopen = 0;
         static double ucfopen = 0;
         static double prevuffopen = 0;
         static double uffopen = 0;
         static double prevujfopen = 0;
         static double ujfopen = 0;
         
         static double prevcffopen = 0;
         static double cffopen = 0;
         static double prevcjfopen = 0;
         static double cjfopen = 0;
         
         static double prevfjfopen = 0;
         static double fjfopen = 0;
         
         for(int i = limit; i >= 0; i--)
         {
            double neweufopen = GetOpenValue(eu, i);
            if (neweufopen != preveufopen) {
               eufopen = neweufopen;
               preveufopen = neweufopen;
            }
            double newegfopen = GetOpenValue(eg, i);
            if (newegfopen != prevegfopen) {
               egfopen = newegfopen;
               prevegfopen = newegfopen;
            }
            double neweafopen = GetOpenValue(ea, i);
            if (neweafopen != preveafopen) {
               eafopen = neweafopen;
               preveafopen = neweafopen;
            }
            double newenfopen = GetOpenValue(en, i);
            if (newenfopen != prevenfopen) {
               enfopen = newenfopen;
               prevenfopen = newenfopen;
            }
            double newecfopen = GetOpenValue(ec, i);
            if (newecfopen != prevecfopen) {
               ecfopen = newecfopen;
               prevecfopen = newecfopen;
            }
            double neweffopen = GetOpenValue(ef, i);
            if (neweffopen != preveffopen) {
               effopen = neweffopen;
               preveffopen = neweffopen;
            }
            double newejfopen = GetOpenValue(ej, i);
            if (newejfopen != prevejfopen) {
               ejfopen = newejfopen;
               prevejfopen = newejfopen;
            }            
            
            double newgufopen = GetOpenValue(gu, i);
            if (newgufopen != prevgufopen) {
               gufopen = newgufopen;
               prevgufopen = newgufopen;
            }
            double newgafopen = GetOpenValue(ga, i);
            if (newgafopen != prevgafopen) {
               gafopen = newgafopen;
               prevgafopen = newgafopen;
            }
            double newgnfopen = GetOpenValue(gn, i);
            if (newgnfopen != prevgnfopen) {
               gnfopen = newgnfopen;
               prevgnfopen = newgnfopen;
            }
            double newgcfopen = GetOpenValue(gc, i);
            if (newgcfopen != prevgcfopen) {
               gcfopen = newgcfopen;
               prevgcfopen = newgcfopen;
            }
            double newgffopen = GetOpenValue(gf, i);
            if (newgffopen != prevgffopen) {
               gffopen = newgffopen;
               prevgffopen = newgffopen;
            }
            double newgjfopen = GetOpenValue(gj, i);
            if (newgjfopen != prevgjfopen) {
               gjfopen = newgjfopen;
               prevgjfopen = newgjfopen;
            }
            
            double newaufopen = GetOpenValue(au, i);
            if (newaufopen != prevaufopen) {
               aufopen = newaufopen;
               prevaufopen = newaufopen;
            }
            double newanfopen = GetOpenValue(an, i);
            if (newanfopen != prevanfopen) {
               anfopen = newanfopen;
               prevanfopen = newanfopen;
            }
            double newacfopen = GetOpenValue(ac, i);
            if (newacfopen != prevacfopen) {
               acfopen = newacfopen;
               prevacfopen = newacfopen;
            }
            double newaffopen = GetOpenValue(af, i);
            if (newaffopen != prevaffopen) {
               affopen = newaffopen;
               prevaffopen = newaffopen;
            }
            double newajfopen = GetOpenValue(aj, i);
            if (newajfopen != prevajfopen) {
               ajfopen = newajfopen;
               prevajfopen = newajfopen;
            }
            
            double newnufopen = GetOpenValue(nu, i);
            if (newnufopen != prevnufopen) {
               nufopen = newnufopen;
               prevnufopen = newnufopen;
            }
            double newncfopen = GetOpenValue(nc, i);
            if (newncfopen != prevncfopen) {
               ncfopen = newncfopen;
               prevncfopen = newncfopen;
            }
            double newnffopen = GetOpenValue(nf, i);
            if (newnffopen != prevnffopen) {
               nffopen = newnffopen;
               prevnffopen = newnffopen;
            }
            double newnjfopen = GetOpenValue(nj, i);
            if (newnjfopen != prevnjfopen) {
               njfopen = newnjfopen;
               prevnjfopen = newnjfopen;
            }
            
            double newucfopen = GetOpenValue(uc, i);
            if (newucfopen != prevucfopen) {
               ucfopen = newucfopen;
               prevucfopen = newucfopen;
            }
            double newuffopen = GetOpenValue(uf, i);
            if (newuffopen != prevuffopen) {
               uffopen = newuffopen;
               prevuffopen = newuffopen;
            }
            double newujfopen = GetOpenValue(uj, i);
            if (newujfopen != prevujfopen) {
               ujfopen = newujfopen;
               prevujfopen = newujfopen;
            }
            
            double newcffopen = GetOpenValue(cf, i);
            if (newcffopen != prevcffopen) {
               cffopen = newcffopen;
               prevcffopen = newcffopen;
            }
            double newcjfopen = GetOpenValue(cj, i);
            if (newcjfopen != prevcjfopen) {
               cjfopen = newcjfopen;
               prevcjfopen = newcjfopen;
            }
            
            double newfjfopen = GetOpenValue(fj, i);
            if (newfjfopen != prevfjfopen) {
               fjfopen = newfjfopen;
               prevfjfopen = newfjfopen;
            }

            ::eur[i] = (eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen);
            ::gbp[i] = (gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen);
            ::aud[i] = (aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen);
            ::nzd[i] = (nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen);
            ::cad[i] = (- ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen);
            ::usd[i] = (- eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen);
            ::chf[i] = (- effopen - gffopen - affopen - nffopen - cffopen - uffopen + fjfopen);
            ::jpy[i] = (- ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen - ujfopen);
         }
      }
};

void CreateATRLine(const string baseName, const string suffixi, 
                  double price, color clr, int wid) {
    string name = Name + baseName;
    string text = StringFormat("%s (%s)", suffixi, 
                             DoubleToString(price, 2));
                             
    objhoriz(name, price, 2, clr, text);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, wid);
    ObjectSetInteger(0, name, OBJPROP_BACK, true);
}

//+CREATE H-LINES----------------------------------------------------+
void objhoriz(string name,
              double pr1,
              int width,
              color col,
              string stringa)
{
   if (ObjectFind(0, name) < 0)
      if (!ObjectCreate(0, name, OBJ_HLINE, 0, 0, 0))
      {
         Print("error: can't create label_object! code #", GetLastError());
      }
   ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
   ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
   ObjectSetInteger(0, name, OBJPROP_BACK, true);
   ObjectSetInteger(0, name, OBJPROP_COLOR, col);
   ObjectSetString(0, name, OBJPROP_TEXT, stringa);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, name, OBJPROP_READONLY, true);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//+------------------------------------------------------------------+


//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//DE-INIT
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//CUSTOM
//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[y] + 3600);
	if (name == Name + "tot" || name == Name + "totold")
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[y] + 360);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
	if(x > 0)
	ObjectSetString(0, name, OBJPROP_TEXT, "+" + text);
	if(x < 0)
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+