//+------------------------------------------------------------------+
//|                                                     LiteDash.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window

input int f = 40; //Distance from left + 10

bool firstrun = false;
double maxbasket;
double Rstr, R2a1, R2a2, R2a3, R2a4, R2a5, R2a6, R2a7, R2a8, R2a9, R2a10, R2b1, R2b2, R2b3, R2b4;
double totspr;

#include <JAson.mqh>
#include <requests/requests.mqh>
#include <ownfuncs.mqh>

CJAVal mainsort;
CJAVal basketdata;
CJAVal baskets2;
CJAVal mosquitoes;
CJAVal pastdata;
CJAVal percdata;

bool timer, endtime;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	//--- indicator buffers mapping
	firstrun = true;

	EventSetTimer(1);
	timer = true;
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime& time[],
	const double& open[],
	const double& high[],
	const double& low[],
	const double& close[],
	const long& tick_volume[],
	const long& volume[],
	const int& spread[])
{
	//---
	datetime expiry = D'2025.06.29 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("DASH expired on " + TimeToStr(expiry, TIME_DATE));
		YesStop = true;
	}

	if (YesStop != true)
	{
		if (firstrun || New_Daily_Bar())
		{
			pastdata.Clear();
			bufferfills();
			BuildPastBuffer();
			fibstuff();
			csstuff();
			baskets();
			sort();
			firstrun = false;
		}
		bool new_ctf_check = false;
		static datetime start_ctf_time = 0;
		if (start_ctf_time < iTime(_Symbol, PERIOD_H1, 0))
		{
			new_ctf_check = true;
			start_ctf_time = iTime(_Symbol, PERIOD_H1, 0);
		}
		if (new_ctf_check)
		{
			fibstuff();
			build();
			ChartRedraw();
			new_ctf_check = false;
		}
		if (DayOfWeek() == 5 && TimeHour(TimeCurrent()) == 23 && TimeMinute(TimeCurrent()) >= 52 && timer == true) { Print("Timer dead"); EventKillTimer(); timer = false; endtime = true; }
		else { endtime = false; }
		if (DayOfWeek() >= 1 && DayOfWeek() <= 5 && endtime == false && !(TimeHour(TimeCurrent()) == 23 && TimeMinute(TimeCurrent()) >= 55)) { EventSetTimer(1); timer = true; }
	}

	//--- return value of prev_calculated for next call
	return(rates_total);
}
//+------------------------------------------------------------------+

void OnTimer()
{
	datetime expiry = D'2025.06.29 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("DASH expired on " + TimeToStr(expiry, TIME_DATE));
		YesStop = true;
	}

	if (YesStop != true && timer && !endtime)
	{
		csstuff();
		baskets();
		sort();
		ChartRedraw();
	}
}

//+BUILD LIMITS------------------------------------------------------+
void build()
{
	ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
	ChartSetInteger(0, CHART_COLOR_GRID, clrNONE);
	ChartSetInteger(0, CHART_COLOR_CHART_LINE, clrNONE);
	ChartSetInteger(0, CHART_COLOR_CANDLE_BEAR, clrNONE);
	ChartSetInteger(0, CHART_COLOR_CANDLE_BULL, clrNONE);
	ChartSetInteger(0, CHART_COLOR_CHART_DOWN, clrNONE);
	ChartSetInteger(0, CHART_COLOR_CHART_UP, clrNONE);
	ChartSetInteger(0, CHART_FOREGROUND, 0);
	ChartSetInteger(0, CHART_SCALEFIX, 0, true);
	ChartSetDouble(0, CHART_FIXED_MIN, 0);
	ChartSetDouble(0, CHART_FIXED_MAX, 20000);
	ChartSetDouble(0, CHART_PRICE_MIN, 0);
	ChartSetDouble(0, CHART_PRICE_MAX, 20000);
}
//+------------------------------------------------------------------+

void bufferfills()
{
	SymbolData eu(eu_p);
	SymbolData eg(eg_p);
	SymbolData ea(ea_p);
	SymbolData en(en_p);
	SymbolData ec(ec_p);
	SymbolData ef(ef_p);
	SymbolData ej(ej_p);
	SymbolData gu(gu_p);
	SymbolData ga(ga_p);
	SymbolData gn(gn_p);
	SymbolData gc(gc_p);
	SymbolData gf(gf_p);
	SymbolData gj(gj_p);
	SymbolData au(au_p);
	SymbolData an(an_p);
	SymbolData ac(ac_p);
	SymbolData af(af_p);
	SymbolData aj(aj_p);
	SymbolData nu(nu_p);
	SymbolData nc(nc_p);
	SymbolData nf(nf_p);
	SymbolData nj(nj_p);
	SymbolData uc(uc_p);
	SymbolData cf(cf_p);
	SymbolData cj(cj_p);
	SymbolData uf(uf_p);
	SymbolData fj(fj_p);
	SymbolData uj(uj_p);

	eu.AddADR(5);
	eg.AddADR(5);
	ea.AddADR(5);
	en.AddADR(5);
	ec.AddADR(5);
	ef.AddADR(5);
	ej.AddADR(5);
	gu.AddADR(5);
	ga.AddADR(5);
	gn.AddADR(5);
	gc.AddADR(5);
	gf.AddADR(5);
	gj.AddADR(5);
	au.AddADR(5);
	an.AddADR(5);
	ac.AddADR(5);
	af.AddADR(5);
	aj.AddADR(5);
	nu.AddADR(5);
	nc.AddADR(5);
	nf.AddADR(5);
	nj.AddADR(5);
	uc.AddADR(5);
	cf.AddADR(5);
	cj.AddADR(5);
	uf.AddADR(5);
	fj.AddADR(5);
	uj.AddADR(5);

	pastdata["adr"].Clear();
	pastdata["adr"].Add(eu.GetADRValue(0));
	pastdata["adr"].Add(eg.GetADRValue(0));
	pastdata["adr"].Add(ea.GetADRValue(0));
	pastdata["adr"].Add(en.GetADRValue(0));
	pastdata["adr"].Add(ec.GetADRValue(0));
	pastdata["adr"].Add(ef.GetADRValue(0));
	pastdata["adr"].Add(ej.GetADRValue(0));
	pastdata["adr"].Add(gu.GetADRValue(0));
	pastdata["adr"].Add(ga.GetADRValue(0));
	pastdata["adr"].Add(gn.GetADRValue(0));
	pastdata["adr"].Add(gc.GetADRValue(0));
	pastdata["adr"].Add(gf.GetADRValue(0));
	pastdata["adr"].Add(gj.GetADRValue(0));
	pastdata["adr"].Add(au.GetADRValue(0));
	pastdata["adr"].Add(an.GetADRValue(0));
	pastdata["adr"].Add(ac.GetADRValue(0));
	pastdata["adr"].Add(af.GetADRValue(0));
	pastdata["adr"].Add(aj.GetADRValue(0));
	pastdata["adr"].Add(nu.GetADRValue(0));
	pastdata["adr"].Add(nc.GetADRValue(0));
	pastdata["adr"].Add(nf.GetADRValue(0));
	pastdata["adr"].Add(nj.GetADRValue(0));
	pastdata["adr"].Add(uc.GetADRValue(0));
	pastdata["adr"].Add(cf.GetADRValue(0));
	pastdata["adr"].Add(cj.GetADRValue(0));
	pastdata["adr"].Add(uf.GetADRValue(0));
	pastdata["adr"].Add(fj.GetADRValue(0));
	pastdata["adr"].Add(uj.GetADRValue(0));
}

void fibstuff()
{
	int hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10;

	// Array of symbols

	string symbols[28];

	symbols[0] = eu_p; symbols[1] = eg_p; symbols[2] = ea_p; symbols[3] = en_p; symbols[4] = ec_p; symbols[5] = ef_p; symbols[6] = ej_p;
	symbols[7] = gu_p; symbols[8] = ga_p; symbols[9] = gn_p; symbols[10] = gc_p; symbols[11] = gf_p; symbols[12] = gj_p; symbols[13] = au_p;
	symbols[14] = an_p; symbols[15] = ac_p; symbols[16] = af_p; symbols[17] = aj_p; symbols[18] = nu_p; symbols[19] = nc_p; symbols[20] = nf_p;
	symbols[21] = nj_p; symbols[22] = uc_p; symbols[23] = cf_p; symbols[24] = cj_p; symbols[25] = uf_p; symbols[26] = fj_p; symbols[27] = uj_p;

	string symbols1[] = { "eu", "eg", "ea", "en", "ec", "ef", "ej", "gu", "ga", "gn", "gc", "gf", "gj", "au",
						  "an", "ac", "af", "aj", "nu", "nc", "nf", "nj", "uc", "cf", "cj", "uf", "fj", "uj" };

	for (int i = 0; i < ArraySize(symbols); i++)
	{
		double d2up, d2dn, d3up, d3dn, d4up, d4dn;

		// Calculate levels
		CalculateFibLevels(symbols[i], hr, d2up, d2dn, d3up, d3dn, d4up, d4dn);

		// Store results
		string key = symbols1[i] + "fib";
		pastdata[key].Clear();
		pastdata[key][3].Add(d2up);
		pastdata[key][3].Add(d2dn);
		pastdata[key][4].Add(d3up);
		pastdata[key][4].Add(d3dn);
		pastdata[key][5].Add(d4up);
		pastdata[key][5].Add(d4dn);
	}
}

//+MAIN CS-----------------------------------------------------------+
void csstuff()
{
	//uint start1 = GetTickCount();
	// 0 = EUR
	// 1 = GBP
	// 2 = AUD
	// 3 = NZD
	// 4 = CAD
	// 5 = USD
	// 6 = CHF
	// 7 = USD

	SymbolData eu(eu_p);
	SymbolData eg(eg_p);
	SymbolData ea(ea_p);
	SymbolData en(en_p);
	SymbolData ec(ec_p);
	SymbolData ef(ef_p);
	SymbolData ej(ej_p);
	SymbolData gu(gu_p);
	SymbolData ga(ga_p);
	SymbolData gn(gn_p);
	SymbolData gc(gc_p);
	SymbolData gf(gf_p);
	SymbolData gj(gj_p);
	SymbolData au(au_p);
	SymbolData an(an_p);
	SymbolData ac(ac_p);
	SymbolData af(af_p);
	SymbolData aj(aj_p);
	SymbolData nu(nu_p);
	SymbolData nc(nc_p);
	SymbolData nf(nf_p);
	SymbolData nj(nj_p);
	SymbolData uc(uc_p);
	SymbolData cf(cf_p);
	SymbolData cj(cj_p);
	SymbolData uf(uf_p);
	SymbolData fj(fj_p);
	SymbolData uj(uj_p);

	SymbolData euM(eu_p);
	SymbolData egM(eg_p);
	SymbolData eaM(ea_p);
	SymbolData enM(en_p);
	SymbolData ecM(ec_p);
	SymbolData efM(ef_p);
	SymbolData ejM(ej_p);
	SymbolData guM(gu_p);
	SymbolData gaM(ga_p);
	SymbolData gnM(gn_p);
	SymbolData gcM(gc_p);
	SymbolData gfM(gf_p);
	SymbolData gjM(gj_p);
	SymbolData auM(au_p);
	SymbolData anM(an_p);
	SymbolData acM(ac_p);
	SymbolData afM(af_p);
	SymbolData ajM(aj_p);
	SymbolData nuM(nu_p);
	SymbolData ncM(nc_p);
	SymbolData nfM(nf_p);
	SymbolData njM(nj_p);
	SymbolData ucM(uc_p);
	SymbolData cfM(cf_p);
	SymbolData cjM(cj_p);
	SymbolData ufM(uf_p);
	SymbolData fjM(fj_p);
	SymbolData ujM(uj_p);

	eu.InitializeCloseBuffer(PERIOD_D1, 7);
	eg.InitializeCloseBuffer(PERIOD_D1, 7);
	ea.InitializeCloseBuffer(PERIOD_D1, 7);
	en.InitializeCloseBuffer(PERIOD_D1, 7);
	ec.InitializeCloseBuffer(PERIOD_D1, 7);
	ef.InitializeCloseBuffer(PERIOD_D1, 7);
	ej.InitializeCloseBuffer(PERIOD_D1, 7);
	gu.InitializeCloseBuffer(PERIOD_D1, 7);
	ga.InitializeCloseBuffer(PERIOD_D1, 7);
	gn.InitializeCloseBuffer(PERIOD_D1, 7);
	gc.InitializeCloseBuffer(PERIOD_D1, 7);
	gf.InitializeCloseBuffer(PERIOD_D1, 7);
	gj.InitializeCloseBuffer(PERIOD_D1, 7);
	au.InitializeCloseBuffer(PERIOD_D1, 7);
	an.InitializeCloseBuffer(PERIOD_D1, 7);
	ac.InitializeCloseBuffer(PERIOD_D1, 7);
	af.InitializeCloseBuffer(PERIOD_D1, 7);
	aj.InitializeCloseBuffer(PERIOD_D1, 7);
	nu.InitializeCloseBuffer(PERIOD_D1, 7);
	nc.InitializeCloseBuffer(PERIOD_D1, 7);
	nf.InitializeCloseBuffer(PERIOD_D1, 7);
	nj.InitializeCloseBuffer(PERIOD_D1, 7);
	uc.InitializeCloseBuffer(PERIOD_D1, 7);
	cf.InitializeCloseBuffer(PERIOD_D1, 7);
	cj.InitializeCloseBuffer(PERIOD_D1, 7);
	uf.InitializeCloseBuffer(PERIOD_D1, 7);
	fj.InitializeCloseBuffer(PERIOD_D1, 7);
	uj.InitializeCloseBuffer(PERIOD_D1, 7);

	eu.InitializeHighBuffer(PERIOD_D1, 1);
	eg.InitializeHighBuffer(PERIOD_D1, 1);
	ea.InitializeHighBuffer(PERIOD_D1, 1);
	en.InitializeHighBuffer(PERIOD_D1, 1);
	ec.InitializeHighBuffer(PERIOD_D1, 1);
	ef.InitializeHighBuffer(PERIOD_D1, 1);
	ej.InitializeHighBuffer(PERIOD_D1, 1);
	gu.InitializeHighBuffer(PERIOD_D1, 1);
	ga.InitializeHighBuffer(PERIOD_D1, 1);
	gn.InitializeHighBuffer(PERIOD_D1, 1);
	gc.InitializeHighBuffer(PERIOD_D1, 1);
	gf.InitializeHighBuffer(PERIOD_D1, 1);
	gj.InitializeHighBuffer(PERIOD_D1, 1);
	au.InitializeHighBuffer(PERIOD_D1, 1);
	an.InitializeHighBuffer(PERIOD_D1, 1);
	ac.InitializeHighBuffer(PERIOD_D1, 1);
	af.InitializeHighBuffer(PERIOD_D1, 1);
	aj.InitializeHighBuffer(PERIOD_D1, 1);
	nu.InitializeHighBuffer(PERIOD_D1, 1);
	nc.InitializeHighBuffer(PERIOD_D1, 1);
	nf.InitializeHighBuffer(PERIOD_D1, 1);
	nj.InitializeHighBuffer(PERIOD_D1, 1);
	uc.InitializeHighBuffer(PERIOD_D1, 1);
	cf.InitializeHighBuffer(PERIOD_D1, 1);
	cj.InitializeHighBuffer(PERIOD_D1, 1);
	uf.InitializeHighBuffer(PERIOD_D1, 1);
	fj.InitializeHighBuffer(PERIOD_D1, 1);
	uj.InitializeHighBuffer(PERIOD_D1, 1);

	eu.InitializeLowBuffer(PERIOD_D1, 1);
	eg.InitializeLowBuffer(PERIOD_D1, 1);
	ea.InitializeLowBuffer(PERIOD_D1, 1);
	en.InitializeLowBuffer(PERIOD_D1, 1);
	ec.InitializeLowBuffer(PERIOD_D1, 1);
	ef.InitializeLowBuffer(PERIOD_D1, 1);
	ej.InitializeLowBuffer(PERIOD_D1, 1);
	gu.InitializeLowBuffer(PERIOD_D1, 1);
	ga.InitializeLowBuffer(PERIOD_D1, 1);
	gn.InitializeLowBuffer(PERIOD_D1, 1);
	gc.InitializeLowBuffer(PERIOD_D1, 1);
	gf.InitializeLowBuffer(PERIOD_D1, 1);
	gj.InitializeLowBuffer(PERIOD_D1, 1);
	au.InitializeLowBuffer(PERIOD_D1, 1);
	an.InitializeLowBuffer(PERIOD_D1, 1);
	ac.InitializeLowBuffer(PERIOD_D1, 1);
	af.InitializeLowBuffer(PERIOD_D1, 1);
	aj.InitializeLowBuffer(PERIOD_D1, 1);
	nu.InitializeLowBuffer(PERIOD_D1, 1);
	nc.InitializeLowBuffer(PERIOD_D1, 1);
	nf.InitializeLowBuffer(PERIOD_D1, 1);
	nj.InitializeLowBuffer(PERIOD_D1, 1);
	uc.InitializeLowBuffer(PERIOD_D1, 1);
	cf.InitializeLowBuffer(PERIOD_D1, 1);
	cj.InitializeLowBuffer(PERIOD_D1, 1);
	uf.InitializeLowBuffer(PERIOD_D1, 1);
	fj.InitializeLowBuffer(PERIOD_D1, 1);
	uj.InitializeLowBuffer(PERIOD_D1, 1);

	euM.InitializeCloseBuffer(PERIOD_MN1, 2);
	egM.InitializeCloseBuffer(PERIOD_MN1, 2);
	eaM.InitializeCloseBuffer(PERIOD_MN1, 2);
	enM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ecM.InitializeCloseBuffer(PERIOD_MN1, 2);
	efM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ejM.InitializeCloseBuffer(PERIOD_MN1, 2);
	guM.InitializeCloseBuffer(PERIOD_MN1, 2);
	gaM.InitializeCloseBuffer(PERIOD_MN1, 2);
	gnM.InitializeCloseBuffer(PERIOD_MN1, 2);
	gcM.InitializeCloseBuffer(PERIOD_MN1, 2);
	gfM.InitializeCloseBuffer(PERIOD_MN1, 2);
	gjM.InitializeCloseBuffer(PERIOD_MN1, 2);
	auM.InitializeCloseBuffer(PERIOD_MN1, 2);
	anM.InitializeCloseBuffer(PERIOD_MN1, 2);
	acM.InitializeCloseBuffer(PERIOD_MN1, 2);
	afM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ajM.InitializeCloseBuffer(PERIOD_MN1, 2);
	nuM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ncM.InitializeCloseBuffer(PERIOD_MN1, 2);
	nfM.InitializeCloseBuffer(PERIOD_MN1, 2);
	njM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ucM.InitializeCloseBuffer(PERIOD_MN1, 2);
	cfM.InitializeCloseBuffer(PERIOD_MN1, 2);
	cjM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ufM.InitializeCloseBuffer(PERIOD_MN1, 2);
	fjM.InitializeCloseBuffer(PERIOD_MN1, 2);
	ujM.InitializeCloseBuffer(PERIOD_MN1, 2);

	//PIP values
	double eupl = eu.GetPipValue();
	double egpl = eg.GetPipValue();
	double eapl = ea.GetPipValue();
	double enpl = en.GetPipValue();
	double ecpl = ec.GetPipValue();
	double efpl = ef.GetPipValue();
	double ejpl = ej.GetPipValue();

	double gupl = gu.GetPipValue();
	double gapl = ga.GetPipValue();
	double gnpl = gn.GetPipValue();
	double gcpl = gc.GetPipValue();
	double gfpl = gf.GetPipValue();
	double gjpl = gj.GetPipValue();

	double aupl = au.GetPipValue();
	double anpl = an.GetPipValue();
	double acpl = ac.GetPipValue();
	double afpl = af.GetPipValue();
	double ajpl = aj.GetPipValue();

	double nupl = nu.GetPipValue();
	double ncpl = nc.GetPipValue();
	double nfpl = nf.GetPipValue();
	double njpl = nj.GetPipValue();

	double ucpl = uc.GetPipValue();
	double cfpl = cf.GetPipValue();
	double cjpl = cj.GetPipValue();

	double ufpl = uf.GetPipValue();
	double fjpl = fj.GetPipValue();

	double ujpl = uj.GetPipValue();

	//Daily / Weekly / Monthly % moves from current close CC[0] = current day close, WC[0] = previous week close, MC[0] = previous week close - FOR PAIRS
	DWM dwm[];

	//0. EU
	AddToDWM(dwm, ((eu.GetCloseValue(0) - eu.GetCloseValue(1)) / eu.GetCloseValue(1)) * 100, eu_p);
	//1. EG
	AddToDWM(dwm, ((eg.GetCloseValue(0) - eg.GetCloseValue(1)) / eg.GetCloseValue(1)) * 100, eg_p);
	//2. EA
	AddToDWM(dwm, ((ea.GetCloseValue(0) - ea.GetCloseValue(1)) / ea.GetCloseValue(1)) * 100, ea_p);
	//3. EN
	AddToDWM(dwm, ((en.GetCloseValue(0) - en.GetCloseValue(1)) / en.GetCloseValue(1)) * 100, en_p);
	//4. EC
	AddToDWM(dwm, ((ec.GetCloseValue(0) - ec.GetCloseValue(1)) / ec.GetCloseValue(1)) * 100, ec_p);
	//5. EF
	AddToDWM(dwm, ((ef.GetCloseValue(0) - ef.GetCloseValue(1)) / ef.GetCloseValue(1)) * 100, ef_p);
	//6. EJ
	AddToDWM(dwm, ((ej.GetCloseValue(0) - ej.GetCloseValue(1)) / ej.GetCloseValue(1)) * 100, ej_p);
	//7. GU
	AddToDWM(dwm, ((gu.GetCloseValue(0) - gu.GetCloseValue(1)) / gu.GetCloseValue(1)) * 100, gu_p);
	//8. GA
	AddToDWM(dwm, ((ga.GetCloseValue(0) - ga.GetCloseValue(1)) / ga.GetCloseValue(1)) * 100, ga_p);
	//9. GN
	AddToDWM(dwm, ((gn.GetCloseValue(0) - gn.GetCloseValue(1)) / gn.GetCloseValue(1)) * 100, gn_p);
	//10. GC
	AddToDWM(dwm, ((gc.GetCloseValue(0) - gc.GetCloseValue(1)) / gc.GetCloseValue(1)) * 100, gc_p);
	//11. GF
	AddToDWM(dwm, ((gf.GetCloseValue(0) - gf.GetCloseValue(1)) / gf.GetCloseValue(1)) * 100, gf_p);
	//12. GJ
	AddToDWM(dwm, ((gj.GetCloseValue(0) - gj.GetCloseValue(1)) / gj.GetCloseValue(1)) * 100, gj_p);
	//13. AU
	AddToDWM(dwm, ((au.GetCloseValue(0) - au.GetCloseValue(1)) / au.GetCloseValue(1)) * 100, au_p);
	//14. AN
	AddToDWM(dwm, ((an.GetCloseValue(0) - an.GetCloseValue(1)) / an.GetCloseValue(1)) * 100, an_p);
	//15. AC
	AddToDWM(dwm, ((ac.GetCloseValue(0) - ac.GetCloseValue(1)) / ac.GetCloseValue(1)) * 100, ac_p);
	//16. AF
	AddToDWM(dwm, ((af.GetCloseValue(0) - af.GetCloseValue(1)) / af.GetCloseValue(1)) * 100, af_p);
	//17. AJ
	AddToDWM(dwm, ((aj.GetCloseValue(0) - aj.GetCloseValue(1)) / aj.GetCloseValue(1)) * 100, aj_p);
	//18. NU
	AddToDWM(dwm, ((nu.GetCloseValue(0) - nu.GetCloseValue(1)) / nu.GetCloseValue(1)) * 100, nu_p);
	//19. NC
	AddToDWM(dwm, ((nc.GetCloseValue(0) - nc.GetCloseValue(1)) / nc.GetCloseValue(1)) * 100, nc_p);
	//20. NF
	AddToDWM(dwm, ((nf.GetCloseValue(0) - nf.GetCloseValue(1)) / nf.GetCloseValue(1)) * 100, nf_p);
	//21. NJ
	AddToDWM(dwm, ((nj.GetCloseValue(0) - nj.GetCloseValue(1)) / nj.GetCloseValue(1)) * 100, nj_p);
	//22. UC
	AddToDWM(dwm, ((uc.GetCloseValue(0) - uc.GetCloseValue(1)) / uc.GetCloseValue(1)) * 100, uc_p);
	//23. CF
	AddToDWM(dwm, ((cf.GetCloseValue(0) - cf.GetCloseValue(1)) / cf.GetCloseValue(1)) * 100, cf_p);
	//24. CJ
	AddToDWM(dwm, ((cj.GetCloseValue(0) - cj.GetCloseValue(1)) / cj.GetCloseValue(1)) * 100, cj_p);
	//25. UF
	AddToDWM(dwm, ((uf.GetCloseValue(0) - uf.GetCloseValue(1)) / uf.GetCloseValue(1)) * 100, uf_p);
	//26. FJ
	AddToDWM(dwm, ((fj.GetCloseValue(0) - fj.GetCloseValue(1)) / fj.GetCloseValue(1)) * 100, fj_p);
	//27. UJ
	AddToDWM(dwm, ((uj.GetCloseValue(0) - uj.GetCloseValue(1)) / uj.GetCloseValue(1)) * 100, uj_p);

	basketdata["eg_s"] = dwm[1].daily;
	basketdata["ea_s"] = dwm[2].daily;
	basketdata["en_s"] = dwm[3].daily;
	basketdata["eu_s"] = dwm[0].daily;
	basketdata["ec_s"] = dwm[4].daily;
	basketdata["ef_s"] = dwm[5].daily;
	basketdata["ej_s"] = dwm[6].daily;
	basketdata["ga_s"] = dwm[8].daily;
	basketdata["gn_s"] = dwm[9].daily;
	basketdata["gu_s"] = dwm[7].daily;
	basketdata["gc_s"] = dwm[10].daily;
	basketdata["gf_s"] = dwm[11].daily;
	basketdata["gj_s"] = dwm[12].daily;
	basketdata["an_s"] = dwm[14].daily;
	basketdata["au_s"] = dwm[13].daily;
	basketdata["ac_s"] = dwm[15].daily;
	basketdata["af_s"] = dwm[16].daily;
	basketdata["aj_s"] = dwm[17].daily;
	basketdata["nu_s"] = dwm[18].daily;
	basketdata["nc_s"] = dwm[19].daily;
	basketdata["nf_s"] = dwm[20].daily;
	basketdata["nj_s"] = dwm[21].daily;
	basketdata["uc_s"] = dwm[22].daily;
	basketdata["uf_s"] = dwm[25].daily;
	basketdata["uj_s"] = dwm[27].daily;
	basketdata["cf_s"] = dwm[23].daily;
	basketdata["cj_s"] = dwm[24].daily;
	basketdata["fj_s"] = dwm[26].daily;

	double eufopen = (eu.GetCloseValue(0) - eu.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double egfopen = (eg.GetCloseValue(0) - eg.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double eafopen = (ea.GetCloseValue(0) - ea.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double enfopen = (en.GetCloseValue(0) - en.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ecfopen = (ec.GetCloseValue(0) - ec.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double effopen = (ef.GetCloseValue(0) - ef.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ejfopen = (ej.GetCloseValue(0) - ej.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gufopen = (gu.GetCloseValue(0) - gu.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double gafopen = (ga.GetCloseValue(0) - ga.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gnfopen = (gn.GetCloseValue(0) - gn.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gcfopen = (gc.GetCloseValue(0) - gc.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gffopen = (gf.GetCloseValue(0) - gf.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gjfopen = (gj.GetCloseValue(0) - gj.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double aufopen = (au.GetCloseValue(0) - au.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double anfopen = (an.GetCloseValue(0) - an.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double acfopen = (ac.GetCloseValue(0) - ac.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double affopen = (af.GetCloseValue(0) - af.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double ajfopen = (aj.GetCloseValue(0) - aj.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nufopen = (nu.GetCloseValue(0) - nu.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double ncfopen = (nc.GetCloseValue(0) - nc.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nffopen = (nf.GetCloseValue(0) - nf.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double njfopen = (nj.GetCloseValue(0) - nj.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double ucfopen = (uc.GetCloseValue(0) - uc.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cffopen = (cf.GetCloseValue(0) - cf.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cjfopen = (cj.GetCloseValue(0) - cj.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uffopen = (uf.GetCloseValue(0) - uf.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fjfopen = (fj.GetCloseValue(0) - fj.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double ujfopen = (uj.GetCloseValue(0) - uj.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	basketdata["eg"] = egfopen;
	basketdata["ea"] = eafopen;
	basketdata["en"] = enfopen;
	basketdata["eu"] = eufopen;
	basketdata["ec"] = ecfopen;
	basketdata["ef"] = effopen;
	basketdata["ej"] = ejfopen;
	basketdata["ga"] = gafopen;
	basketdata["gn"] = gnfopen;
	basketdata["gu"] = gufopen;
	basketdata["gc"] = gcfopen;
	basketdata["gf"] = gffopen;
	basketdata["gj"] = gjfopen;
	basketdata["an"] = anfopen;
	basketdata["au"] = aufopen;
	basketdata["ac"] = acfopen;
	basketdata["af"] = affopen;
	basketdata["aj"] = ajfopen;
	basketdata["nu"] = nufopen;
	basketdata["nc"] = ncfopen;
	basketdata["nf"] = nffopen;
	basketdata["nj"] = njfopen;
	basketdata["uc"] = ucfopen;
	basketdata["uf"] = uffopen;
	basketdata["uj"] = ujfopen;
	basketdata["cf"] = cffopen;
	basketdata["cj"] = cjfopen;
	basketdata["fj"] = fjfopen;

	double euftop = MathMax((eu.GetHighValue(0) - eu.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl, MathAbs(eu.GetLowValue(0) - eu.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl);
	double egftop = MathMax((eg.GetHighValue(0) - eg.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl, MathAbs(eg.GetLowValue(0) - eg.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl);
	double eaftop = MathMax((ea.GetHighValue(0) - ea.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl, MathAbs(ea.GetLowValue(0) - ea.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl);
	double enftop = MathMax((en.GetHighValue(0) - en.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl, MathAbs(en.GetLowValue(0) - en.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl);
	double ecftop = MathMax((ec.GetHighValue(0) - ec.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl, MathAbs(ec.GetLowValue(0) - ec.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl);
	double efftop = MathMax((ef.GetHighValue(0) - ef.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl, MathAbs(ef.GetLowValue(0) - ef.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl);
	double ejftop = MathMax((ej.GetHighValue(0) - ej.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl, MathAbs(ej.GetLowValue(0) - ej.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl);
	double guftop = MathMax((gu.GetHighValue(0) - gu.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl, MathAbs(gu.GetLowValue(0) - gu.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl);
	double gaftop = MathMax((ga.GetHighValue(0) - ga.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl, MathAbs(ga.GetLowValue(0) - ga.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl);
	double gnftop = MathMax((gn.GetHighValue(0) - gn.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl, MathAbs(gn.GetLowValue(0) - gn.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl);
	double gcftop = MathMax((gc.GetHighValue(0) - gc.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl, MathAbs(gc.GetLowValue(0) - gc.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl);
	double gfftop = MathMax((gf.GetHighValue(0) - gf.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl, MathAbs(gf.GetLowValue(0) - gf.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl);
	double gjftop = MathMax((gj.GetHighValue(0) - gj.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl, MathAbs(gj.GetLowValue(0) - gj.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl);
	double auftop = MathMax((au.GetHighValue(0) - au.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl, MathAbs(au.GetLowValue(0) - au.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl);
	double anftop = MathMax((an.GetHighValue(0) - an.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl, MathAbs(an.GetLowValue(0) - an.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl);
	double acftop = MathMax((ac.GetHighValue(0) - ac.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl, MathAbs(ac.GetLowValue(0) - ac.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl);
	double afftop = MathMax((af.GetHighValue(0) - af.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl, MathAbs(af.GetLowValue(0) - af.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl);
	double ajftop = MathMax((aj.GetHighValue(0) - aj.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl, MathAbs(aj.GetLowValue(0) - aj.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl);
	double nuftop = MathMax((nu.GetHighValue(0) - nu.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl, MathAbs(nu.GetLowValue(0) - nu.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl);
	double ncftop = MathMax((nc.GetHighValue(0) - nc.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl, MathAbs(nc.GetLowValue(0) - nc.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl);
	double nfftop = MathMax((nf.GetHighValue(0) - nf.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl, MathAbs(nf.GetLowValue(0) - nf.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl);
	double njftop = MathMax((nj.GetHighValue(0) - nj.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl, MathAbs(nj.GetLowValue(0) - nj.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl);
	double ucftop = MathMax((uc.GetHighValue(0) - uc.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl, MathAbs(uc.GetLowValue(0) - uc.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl);
	double cfftop = MathMax((cf.GetHighValue(0) - cf.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl, MathAbs(cf.GetLowValue(0) - cf.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl);
	double cjftop = MathMax((cj.GetHighValue(0) - cj.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl, MathAbs(cj.GetLowValue(0) - cj.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl);
	double ufftop = MathMax((uf.GetHighValue(0) - uf.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl, MathAbs(uf.GetLowValue(0) - uf.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl);
	double fjftop = MathMax((fj.GetHighValue(0) - fj.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl, MathAbs(fj.GetLowValue(0) - fj.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl);
	double ujftop = MathMax((uj.GetHighValue(0) - uj.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl, MathAbs(uj.GetLowValue(0) - uj.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl);

	double euretrace = 0, egretrace = 0, earetrace = 0, enretrace = 0, ecretrace = 0, efretrace = 0, ejretrace = 0, guretrace = 0, garetrace = 0, gnretrace = 0, gcretrace = 0, gfretrace = 0, gjretrace = 0, auretrace = 0,
		anretrace = 0, acretrace = 0, afretrace = 0, ajretrace = 0, nuretrace = 0, ncretrace = 0, nfretrace = 0, njretrace = 0, ucretrace = 0, cfretrace = 0, cjretrace = 0, ufretrace = 0, fjretrace = 0, ujretrace = 0;

	if (euftop >= 250) euretrace = (euftop - MathAbs(eufopen)) / euftop;
	if (egftop >= 250) egretrace = (egftop - MathAbs(egfopen)) / egftop;
	if (eaftop >= 250) earetrace = (eaftop - MathAbs(eafopen)) / eaftop;
	if (enftop >= 250) enretrace = (enftop - MathAbs(enfopen)) / enftop;
	if (ecftop >= 250) ecretrace = (ecftop - MathAbs(ecfopen)) / ecftop;
	if (efftop >= 250) efretrace = (efftop - MathAbs(effopen)) / efftop;
	if (ejftop >= 250) ejretrace = (ejftop - MathAbs(ejfopen)) / ejftop;
	if (guftop >= 250) guretrace = (guftop - MathAbs(gufopen)) / guftop;
	if (gaftop >= 250) garetrace = (gaftop - MathAbs(gafopen)) / gaftop;
	if (gnftop >= 250) gnretrace = (gnftop - MathAbs(gnfopen)) / gnftop;
	if (gcftop >= 250) gcretrace = (gcftop - MathAbs(gcfopen)) / gcftop;
	if (gfftop >= 250) gfretrace = (gfftop - MathAbs(gffopen)) / gfftop;
	if (gjftop >= 250) gjretrace = (gjftop - MathAbs(gjfopen)) / gjftop;
	if (auftop >= 250) auretrace = (auftop - MathAbs(aufopen)) / auftop;
	if (anftop >= 250) anretrace = (anftop - MathAbs(anfopen)) / anftop;
	if (acftop >= 250) acretrace = (acftop - MathAbs(acfopen)) / acftop;
	if (afftop >= 250) afretrace = (afftop - MathAbs(affopen)) / afftop;
	if (ajftop >= 250) ajretrace = (ajftop - MathAbs(ajfopen)) / ajftop;
	if (nuftop >= 250) nuretrace = (nuftop - MathAbs(nufopen)) / nuftop;
	if (ncftop >= 250) ncretrace = (ncftop - MathAbs(ncfopen)) / ncftop;
	if (nfftop >= 250) nfretrace = (nfftop - MathAbs(nffopen)) / nfftop;
	if (njftop >= 250) njretrace = (njftop - MathAbs(njfopen)) / njftop;
	if (ucftop >= 250) ucretrace = (ucftop - MathAbs(ucfopen)) / ucftop;
	if (cfftop >= 250) cfretrace = (cfftop - MathAbs(cffopen)) / cfftop;
	if (cjftop >= 250) cjretrace = (cjftop - MathAbs(cjfopen)) / cjftop;
	if (ufftop >= 250) ufretrace = (ufftop - MathAbs(uffopen)) / ufftop;
	if (fjftop >= 250) fjretrace = (fjftop - MathAbs(fjfopen)) / fjftop;
	if (ujftop >= 250) ujretrace = (ujftop - MathAbs(ujfopen)) / ujftop;

	//5D
	double eu5fopen = (eu.GetCloseValue(0) - eu.GetCloseValue(5)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg5fopen = (eg.GetCloseValue(0) - eg.GetCloseValue(5)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea5fopen = (ea.GetCloseValue(0) - ea.GetCloseValue(5)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en5fopen = (en.GetCloseValue(0) - en.GetCloseValue(5)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec5fopen = (ec.GetCloseValue(0) - ec.GetCloseValue(5)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef5fopen = (ef.GetCloseValue(0) - ef.GetCloseValue(5)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej5fopen = (ej.GetCloseValue(0) - ej.GetCloseValue(5)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu5fopen = (gu.GetCloseValue(0) - gu.GetCloseValue(5)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga5fopen = (ga.GetCloseValue(0) - ga.GetCloseValue(5)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn5fopen = (gn.GetCloseValue(0) - gn.GetCloseValue(5)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc5fopen = (gc.GetCloseValue(0) - gc.GetCloseValue(5)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf5fopen = (gf.GetCloseValue(0) - gf.GetCloseValue(5)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj5fopen = (gj.GetCloseValue(0) - gj.GetCloseValue(5)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au5fopen = (au.GetCloseValue(0) - au.GetCloseValue(5)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an5fopen = (an.GetCloseValue(0) - an.GetCloseValue(5)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac5fopen = (ac.GetCloseValue(0) - ac.GetCloseValue(5)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af5fopen = (af.GetCloseValue(0) - af.GetCloseValue(5)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj5fopen = (aj.GetCloseValue(0) - aj.GetCloseValue(5)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu5fopen = (nu.GetCloseValue(0) - nu.GetCloseValue(5)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc5fopen = (nc.GetCloseValue(0) - nc.GetCloseValue(5)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf5fopen = (nf.GetCloseValue(0) - nf.GetCloseValue(5)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj5fopen = (nj.GetCloseValue(0) - nj.GetCloseValue(5)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc5fopen = (uc.GetCloseValue(0) - uc.GetCloseValue(5)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf5fopen = (cf.GetCloseValue(0) - cf.GetCloseValue(5)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj5fopen = (cj.GetCloseValue(0) - cj.GetCloseValue(5)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf5fopen = (uf.GetCloseValue(0) - uf.GetCloseValue(5)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj5fopen = (fj.GetCloseValue(0) - fj.GetCloseValue(5)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj5fopen = (uj.GetCloseValue(0) - uj.GetCloseValue(5)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//Month
	double eufmopen = (eu.GetCloseValue(0) - euM.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double egfmopen = (eg.GetCloseValue(0) - egM.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double eafmopen = (ea.GetCloseValue(0) - eaM.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double enfmopen = (en.GetCloseValue(0) - enM.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ecfmopen = (ec.GetCloseValue(0) - ecM.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double effmopen = (ef.GetCloseValue(0) - efM.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ejfmopen = (ej.GetCloseValue(0) - ejM.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gufmopen = (gu.GetCloseValue(0) - guM.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double gafmopen = (ga.GetCloseValue(0) - gaM.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gnfmopen = (gn.GetCloseValue(0) - gnM.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gcfmopen = (gc.GetCloseValue(0) - gcM.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gffmopen = (gf.GetCloseValue(0) - gfM.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gjfmopen = (gj.GetCloseValue(0) - gjM.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double aufmopen = (au.GetCloseValue(0) - auM.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double anfmopen = (an.GetCloseValue(0) - anM.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double acfmopen = (ac.GetCloseValue(0) - acM.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double affmopen = (af.GetCloseValue(0) - afM.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double ajfmopen = (aj.GetCloseValue(0) - ajM.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nufmopen = (nu.GetCloseValue(0) - nuM.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double ncfmopen = (nc.GetCloseValue(0) - ncM.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nffmopen = (nf.GetCloseValue(0) - nfM.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double njfmopen = (nj.GetCloseValue(0) - njM.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double ucfmopen = (uc.GetCloseValue(0) - ucM.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cffmopen = (cf.GetCloseValue(0) - cfM.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cjfmopen = (cj.GetCloseValue(0) - cjM.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uffmopen = (uf.GetCloseValue(0) - ufM.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fjfmopen = (fj.GetCloseValue(0) - fjM.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double ujfmopen = (uj.GetCloseValue(0) - ujM.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	Baskets basketsMain[];

	AddToBaskets(basketsMain, -eufopen, -gufopen, -aufopen, -nufopen, +ucfopen, +uffopen, +ujfopen, usd);
	AddToBaskets(basketsMain, eufopen, +egfopen, +eafopen, +enfopen, +ecfopen, +effopen, +ejfopen, eur);
	AddToBaskets(basketsMain, gufopen, -egfopen, +gafopen, +gnfopen, +gcfopen, +gffopen, +gjfopen, gbp);
	AddToBaskets(basketsMain, aufopen, -eafopen, -gafopen, +anfopen, +acfopen, +affopen, +ajfopen, aud);
	AddToBaskets(basketsMain, nufopen, -enfopen, -gnfopen, -anfopen, +ncfopen, +nffopen, +njfopen, nzd);
	AddToBaskets(basketsMain, -ucfopen, -ecfopen, -gcfopen, -acfopen, -ncfopen, +cffopen, +cjfopen, cad);
	AddToBaskets(basketsMain, -uffopen, -effopen, -gffopen, -affopen, -nffopen, -cffopen, +fjfopen, chf);
	AddToBaskets(basketsMain, -ejfopen, -gjfopen, -ajfopen, -njfopen, -cjfopen, -fjfopen, -ujfopen, jpy);

	basketdata["usd"] = basketsMain[0].pips;
	basketdata["eur"] = basketsMain[1].pips;
	basketdata["gbp"] = basketsMain[2].pips;
	basketdata["aud"] = basketsMain[3].pips;
	basketdata["nzd"] = basketsMain[4].pips;
	basketdata["cad"] = basketsMain[5].pips;
	basketdata["chf"] = basketsMain[6].pips;
	basketdata["jpy"] = basketsMain[7].pips;

	ArraySortStruct(basketsMain, pips);

	for (int x = 7; x >= 0; x--)
	{
		basketdata["Day"][7 - x] = (basketsMain[x].name);
		basketdata["Day"][7 - x] = (basketsMain[x].pips);
	}

	Baskets basketsMain1[];

	AddToBaskets(basketsMain1, -eu5fopen, -gu5fopen, -au5fopen, -nu5fopen, +uc5fopen, +uf5fopen, +uj5fopen, "U");
	AddToBaskets(basketsMain1, eu5fopen, +eg5fopen, +ea5fopen, +en5fopen, +ec5fopen, +ef5fopen, +ej5fopen, "E");
	AddToBaskets(basketsMain1, gu5fopen, -eg5fopen, +ga5fopen, +gn5fopen, +gc5fopen, +gf5fopen, +gj5fopen, "G");
	AddToBaskets(basketsMain1, au5fopen, -ea5fopen, -ga5fopen, +an5fopen, +ac5fopen, +af5fopen, +aj5fopen, "A");
	AddToBaskets(basketsMain1, nu5fopen, -en5fopen, -gn5fopen, -an5fopen, +nc5fopen, +nf5fopen, +nj5fopen, "N");
	AddToBaskets(basketsMain1, -uc5fopen, -ec5fopen, -gc5fopen, -ac5fopen, -nc5fopen, +cf5fopen, +cj5fopen, "C");
	AddToBaskets(basketsMain1, -uf5fopen, -ef5fopen, -gf5fopen, -af5fopen, -nf5fopen, -cf5fopen, +fj5fopen, "F");
	AddToBaskets(basketsMain1, -ej5fopen, -gj5fopen, -aj5fopen, -nj5fopen, -cj5fopen, -fj5fopen, -uj5fopen, "J");

	ArraySortStruct(basketsMain1, pips);

	for (int x = 7; x >= 0; x--)
	{
		basketdata["5D"][7 - x] = (basketsMain1[x].name);
		basketdata["5D"][7 - x] = (basketsMain1[x].pips);
	}

	ArrayFree(basketsMain1);

	Baskets basketsMain2[];

	AddToBaskets(basketsMain2, -eufmopen, -gufmopen, -aufmopen, -nufmopen, +ucfmopen, +uffmopen, +ujfmopen, "U");
	AddToBaskets(basketsMain2, eufmopen, +egfmopen, +eafmopen, +enfmopen, +ecfmopen, +effmopen, +ejfmopen, "E");
	AddToBaskets(basketsMain2, gufmopen, -egfmopen, +gafmopen, +gnfmopen, +gcfmopen, +gffmopen, +gjfmopen, "G");
	AddToBaskets(basketsMain2, aufmopen, -eafmopen, -gafmopen, +anfmopen, +acfmopen, +affmopen, +ajfmopen, "A");
	AddToBaskets(basketsMain2, nufmopen, -enfmopen, -gnfmopen, -anfmopen, +ncfmopen, +nffmopen, +njfmopen, "N");
	AddToBaskets(basketsMain2, -ucfmopen, -ecfmopen, -gcfmopen, -acfmopen, -ncfmopen, +cffmopen, +cjfmopen, "C");
	AddToBaskets(basketsMain2, -uffmopen, -effmopen, -gffmopen, -affmopen, -nffmopen, -cffmopen, +fjfmopen, "F");
	AddToBaskets(basketsMain2, -ejfmopen, -gjfmopen, -ajfmopen, -njfmopen, -cjfmopen, -fjfmopen, -ujfmopen, "J");

	ArraySortStruct(basketsMain2, pips);

	for (int x = 7; x >= 0; x--)
	{
		basketdata["Month"][7 - x] = (basketsMain2[x].name);
		basketdata["Month"][7 - x] = (basketsMain2[x].pips);
	}

	ArrayFree(basketsMain2);

	//JSON pass sorted data to sort()
	Data dynamicArray[];

	AddToSortData(dynamicArray, dwm[0].daily, eufopen, euretrace, euftop, eu_p);
	AddToSortData(dynamicArray, dwm[1].daily, egfopen, egretrace, egftop, eg_p);
	AddToSortData(dynamicArray, dwm[2].daily, eafopen, earetrace, eaftop, ea_p);
	AddToSortData(dynamicArray, dwm[3].daily, enfopen, enretrace, enftop, en_p);
	AddToSortData(dynamicArray, dwm[4].daily, ecfopen, ecretrace, ecftop, ec_p);
	AddToSortData(dynamicArray, dwm[5].daily, effopen, efretrace, efftop, ef_p);
	AddToSortData(dynamicArray, dwm[6].daily, ejfopen, ejretrace, ejftop, ej_p);
	AddToSortData(dynamicArray, dwm[7].daily, gufopen, guretrace, guftop, gu_p);
	AddToSortData(dynamicArray, dwm[8].daily, gafopen, garetrace, gaftop, ga_p);
	AddToSortData(dynamicArray, dwm[9].daily, gnfopen, gnretrace, gnftop, gn_p);
	AddToSortData(dynamicArray, dwm[10].daily, gcfopen, gcretrace, gcftop, gc_p);
	AddToSortData(dynamicArray, dwm[11].daily, gffopen, gfretrace, gfftop, gf_p);
	AddToSortData(dynamicArray, dwm[12].daily, gjfopen, gjretrace, gjftop, gj_p);
	AddToSortData(dynamicArray, dwm[13].daily, aufopen, auretrace, auftop, au_p);
	AddToSortData(dynamicArray, dwm[14].daily, anfopen, anretrace, anftop, an_p);
	AddToSortData(dynamicArray, dwm[15].daily, acfopen, acretrace, acftop, ac_p);
	AddToSortData(dynamicArray, dwm[16].daily, affopen, afretrace, afftop, af_p);
	AddToSortData(dynamicArray, dwm[17].daily, ajfopen, ajretrace, ajftop, aj_p);
	AddToSortData(dynamicArray, dwm[18].daily, nufopen, nuretrace, nuftop, nu_p);
	AddToSortData(dynamicArray, dwm[19].daily, ncfopen, ncretrace, ncftop, nc_p);
	AddToSortData(dynamicArray, dwm[20].daily, nffopen, nfretrace, nfftop, nf_p);
	AddToSortData(dynamicArray, dwm[21].daily, njfopen, njretrace, njftop, nj_p);
	AddToSortData(dynamicArray, dwm[22].daily, ucfopen, ucretrace, ucftop, uc_p);
	AddToSortData(dynamicArray, dwm[23].daily, cffopen, cfretrace, cfftop, cf_p);
	AddToSortData(dynamicArray, dwm[24].daily, cjfopen, cjretrace, cjftop, cj_p);
	AddToSortData(dynamicArray, dwm[25].daily, uffopen, ufretrace, ufftop, uf_p);
	AddToSortData(dynamicArray, dwm[26].daily, fjfopen, fjretrace, fjftop, fj_p);
	AddToSortData(dynamicArray, dwm[27].daily, ujfopen, ujretrace, ujftop, uj_p);

	ArraySortStruct(dynamicArray, daily);

	for (int x = 27; x >= 0; x--)
	{
		mainsort["a"][27 - x].Add(dynamicArray[x].pair);
		mainsort["a"][27 - x].Add(dynamicArray[x].pips, 1);
		mainsort["retrace"][27 - x].Add(dynamicArray[x].rsis * 100, 1);
		mainsort["ftop"][27 - x].Add(dynamicArray[x].rsid, 1);
	}

	ArrayFree(dynamicArray);

	Pairs pairsEP[];
	Pairs pairsEN[];
	if (eufopen >= 0)
		AddToPairs(pairsEP, eufopen, eu_p);
	else
		AddToPairs(pairsEN, eufopen, eu_p);

	if (egfopen >= 0)
		AddToPairs(pairsEP, egfopen, eg_p);
	else
		AddToPairs(pairsEN, egfopen, eg_p);

	if (eafopen >= 0)
		AddToPairs(pairsEP, eafopen, ea_p);
	else
		AddToPairs(pairsEN, eafopen, ea_p);

	if (enfopen >= 0)
		AddToPairs(pairsEP, enfopen, en_p);
	else
		AddToPairs(pairsEN, enfopen, en_p);

	if (ecfopen >= 0)
		AddToPairs(pairsEP, ecfopen, ec_p);
	else
		AddToPairs(pairsEN, ecfopen, ec_p);

	if (effopen >= 0)
		AddToPairs(pairsEP, effopen, ef_p);
	else
		AddToPairs(pairsEN, effopen, ef_p);

	if (ejfopen >= 0)
		AddToPairs(pairsEP, ejfopen, ej_p);
	else
		AddToPairs(pairsEN, ejfopen, ej_p);

	ArraySortStruct(pairsEP, money);
	ArraySortStruct(pairsEN, money);

	Pairs pairsGP[];
	Pairs pairsGN[];
	if (gufopen >= 0)
		AddToPairs(pairsGP, gufopen, gu_p);
	else
		AddToPairs(pairsGN, gufopen, gu_p);

	if (-egfopen >= 0)
		AddToPairs(pairsGP, -egfopen, "-" + eg_p);
	else
		AddToPairs(pairsGN, -egfopen, "-" + eg_p);

	if (gafopen >= 0)
		AddToPairs(pairsGP, gafopen, ga_p);
	else
		AddToPairs(pairsGN, gafopen, ga_p);

	if (gnfopen >= 0)
		AddToPairs(pairsGP, gnfopen, gn_p);
	else
		AddToPairs(pairsGN, gnfopen, gn_p);

	if (gcfopen >= 0)
		AddToPairs(pairsGP, gcfopen, gc_p);
	else
		AddToPairs(pairsGN, gcfopen, gc_p);

	if (gffopen >= 0)
		AddToPairs(pairsGP, gffopen, gf_p);
	else
		AddToPairs(pairsGN, gffopen, gf_p);

	if (gjfopen >= 0)
		AddToPairs(pairsGP, gjfopen, gj_p);
	else
		AddToPairs(pairsGN, gjfopen, gj_p);

	ArraySortStruct(pairsGP, money);
	ArraySortStruct(pairsGN, money);

	Pairs pairsAP[];
	Pairs pairsAN[];
	if (aufopen >= 0)
		AddToPairs(pairsAP, aufopen, au_p);
	else
		AddToPairs(pairsAN, aufopen, au_p);

	if (-eafopen >= 0)
		AddToPairs(pairsAP, -eafopen, "-" + ea_p);
	else
		AddToPairs(pairsAN, -eafopen, "-" + ea_p);

	if (-gafopen >= 0)
		AddToPairs(pairsAP, -gafopen, "-" + ga_p);
	else
		AddToPairs(pairsAN, -gafopen, "-" + ga_p);

	if (anfopen >= 0)
		AddToPairs(pairsAP, anfopen, an_p);
	else
		AddToPairs(pairsAN, anfopen, an_p);

	if (acfopen >= 0)
		AddToPairs(pairsAP, acfopen, ac_p);
	else
		AddToPairs(pairsAN, acfopen, ac_p);

	if (affopen >= 0)
		AddToPairs(pairsAP, affopen, af_p);
	else
		AddToPairs(pairsAN, affopen, af_p);

	if (ajfopen >= 0)
		AddToPairs(pairsAP, ajfopen, aj_p);
	else
		AddToPairs(pairsAN, ajfopen, aj_p);

	ArraySortStruct(pairsAP, money);
	ArraySortStruct(pairsAN, money);

	Pairs pairsNP[];
	Pairs pairsNN[];
	if (nufopen >= 0)
		AddToPairs(pairsNP, nufopen, nu_p);
	else
		AddToPairs(pairsNN, nufopen, nu_p);

	if (-enfopen >= 0)
		AddToPairs(pairsNP, -enfopen, "-" + en_p);
	else
		AddToPairs(pairsNN, -enfopen, "-" + en_p);

	if (-gnfopen >= 0)
		AddToPairs(pairsNP, -gnfopen, "-" + gn_p);
	else
		AddToPairs(pairsNN, -gnfopen, "-" + gn_p);

	if (-anfopen >= 0)
		AddToPairs(pairsNP, -anfopen, "-" + an_p);
	else
		AddToPairs(pairsNN, -anfopen, "-" + an_p);

	if (ncfopen >= 0)
		AddToPairs(pairsNP, ncfopen, nc_p);
	else
		AddToPairs(pairsNN, ncfopen, nc_p);

	if (nffopen >= 0)
		AddToPairs(pairsNP, nffopen, nf_p);
	else
		AddToPairs(pairsNN, nffopen, nf_p);

	if (njfopen >= 0)
		AddToPairs(pairsNP, njfopen, nj_p);
	else
		AddToPairs(pairsNN, njfopen, nj_p);

	ArraySortStruct(pairsNP, money);
	ArraySortStruct(pairsNN, money);

	Pairs pairsCP[];
	Pairs pairsCN[];
	if (-ucfopen >= 0)
		AddToPairs(pairsCP, -ucfopen, "-" + uc_p);
	else
		AddToPairs(pairsCN, -ucfopen, "-" + uc_p);

	if (-ecfopen >= 0)
		AddToPairs(pairsCP, -ecfopen, "-" + ec_p);
	else
		AddToPairs(pairsCN, -ecfopen, "-" + ec_p);

	if (-gcfopen >= 0)
		AddToPairs(pairsCP, -gcfopen, "-" + gc_p);
	else
		AddToPairs(pairsCN, -gcfopen, "-" + gc_p);

	if (-acfopen >= 0)
		AddToPairs(pairsCP, -acfopen, "-" + ac_p);
	else
		AddToPairs(pairsCN, -acfopen, "-" + ac_p);

	if (-ncfopen >= 0)
		AddToPairs(pairsCP, -ncfopen, "-" + nc_p);
	else
		AddToPairs(pairsCN, -ncfopen, "-" + nc_p);

	if (cffopen >= 0)
		AddToPairs(pairsCP, cffopen, cf_p);
	else
		AddToPairs(pairsCN, cffopen, cf_p);

	if (cjfopen >= 0)
		AddToPairs(pairsCP, cjfopen, cj_p);
	else
		AddToPairs(pairsCN, cjfopen, cj_p);

	ArraySortStruct(pairsCP, money);
	ArraySortStruct(pairsCN, money);

	Pairs pairsUP[];
	Pairs pairsUN[];
	if (ucfopen >= 0)
		AddToPairs(pairsUP, ucfopen, uc_p);
	else
		AddToPairs(pairsUN, ucfopen, uc_p);

	if (-eufopen >= 0)
		AddToPairs(pairsUP, -eufopen, "-" + eu_p);
	else
		AddToPairs(pairsUN, -eufopen, "-" + eu_p);

	if (-gufopen >= 0)
		AddToPairs(pairsUP, -gufopen, "-" + gu_p);
	else
		AddToPairs(pairsUN, -gufopen, "-" + gu_p);

	if (-aufopen >= 0)
		AddToPairs(pairsUP, -aufopen, "-" + au_p);
	else
		AddToPairs(pairsUN, -aufopen, "-" + au_p);

	if (-nufopen >= 0)
		AddToPairs(pairsUP, -nufopen, "-" + nu_p);
	else
		AddToPairs(pairsUN, -nufopen, "-" + nu_p);

	if (uffopen >= 0)
		AddToPairs(pairsUP, uffopen, uf_p);
	else
		AddToPairs(pairsUN, uffopen, uf_p);

	if (ujfopen >= 0)
		AddToPairs(pairsUP, ujfopen, uj_p);
	else
		AddToPairs(pairsUN, ujfopen, uj_p);

	ArraySortStruct(pairsUP, money);
	ArraySortStruct(pairsUN, money);

	Pairs pairsFP[];
	Pairs pairsFN[];
	if (-uffopen >= 0)
		AddToPairs(pairsFP, -uffopen, "-" + uf_p);
	else
		AddToPairs(pairsFN, -uffopen, "-" + uf_p);

	if (-effopen >= 0)
		AddToPairs(pairsFP, -effopen, "-" + ef_p);
	else
		AddToPairs(pairsFN, -effopen, "-" + ef_p);

	if (-gffopen >= 0)
		AddToPairs(pairsFP, -gffopen, "-" + gf_p);
	else
		AddToPairs(pairsFN, -gffopen, "-" + gf_p);

	if (-affopen >= 0)
		AddToPairs(pairsFP, -affopen, "-" + af_p);
	else
		AddToPairs(pairsFN, -affopen, "-" + af_p);

	if (-nffopen >= 0)
		AddToPairs(pairsFP, -nffopen, "-" + nf_p);
	else
		AddToPairs(pairsFN, -nffopen, "-" + nf_p);

	if (-cffopen >= 0)
		AddToPairs(pairsFP, -cffopen, "-" + cf_p);
	else
		AddToPairs(pairsFN, -cffopen, "-" + cf_p);

	if (fjfopen >= 0)
		AddToPairs(pairsFP, fjfopen, fj_p);
	else
		AddToPairs(pairsFN, fjfopen, fj_p);

	ArraySortStruct(pairsFP, money);
	ArraySortStruct(pairsFN, money);

	Pairs pairsJP[];
	Pairs pairsJN[];
	if (-ujfopen >= 0)
		AddToPairs(pairsJP, -ujfopen, "-" + uj_p);
	else
		AddToPairs(pairsJN, -ujfopen, "-" + uj_p);

	if (-ejfopen >= 0)
		AddToPairs(pairsJP, -ejfopen, "-" + ej_p);
	else
		AddToPairs(pairsJN, -ejfopen, "-" + ej_p);

	if (-gjfopen >= 0)
		AddToPairs(pairsJP, -gjfopen, "-" + gj_p);
	else
		AddToPairs(pairsJN, -gjfopen, "-" + gj_p);

	if (-ajfopen >= 0)
		AddToPairs(pairsJP, -ajfopen, "-" + aj_p);
	else
		AddToPairs(pairsJN, -ajfopen, "-" + aj_p);

	if (-njfopen >= 0)
		AddToPairs(pairsJP, -njfopen, "-" + nj_p);
	else
		AddToPairs(pairsJN, -njfopen, "-" + nj_p);

	if (-cjfopen >= 0)
		AddToPairs(pairsJP, -cjfopen, "-" + cj_p);
	else
		AddToPairs(pairsJN, -cjfopen, "-" + cj_p);

	if (-fjfopen >= 0)
		AddToPairs(pairsJP, -fjfopen, "-" + fj_p);
	else
		AddToPairs(pairsJN, -fjfopen, "-" + fj_p);

	ArraySortStruct(pairsJP, money);
	ArraySortStruct(pairsJN, money);
	{
		if (ArraySize(pairsEP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "E", 0) == 0) { baskets2["Pa1"] = pairsEP[ArraySize(pairsEP) - 1].name; baskets2["Pa1"] = pairsEP[ArraySize(pairsEP) - 1].money; } //Print(pairsEP[ArraySize(pairsEP) - 1].money + " " + pairsEP[ArraySize(pairsEP) - 1].name);
		if (ArraySize(pairsEP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "E", 0) == 0) { baskets2["Pa2"] = pairsEP[ArraySize(pairsEP) - 2].name; baskets2["Pa2"] = pairsEP[ArraySize(pairsEP) - 2].money; } //Print(pairsEP[ArraySize(pairsEP) - 2].money + " " + pairsEP[ArraySize(pairsEP) - 2].name);
		if (ArraySize(pairsEP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "E", 0) == 0) { baskets2["Pa3"] = pairsEP[ArraySize(pairsEP) - 3].name; baskets2["Pa3"] = pairsEP[ArraySize(pairsEP) - 3].money; } //Print(pairsEP[ArraySize(pairsEP) - 3].money + " " + pairsEP[ArraySize(pairsEP) - 3].name);
		if (ArraySize(pairsEP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "E", 0) == 0) { baskets2["Pa4"] = pairsEP[ArraySize(pairsEP) - 4].name; baskets2["Pa4"] = pairsEP[ArraySize(pairsEP) - 4].money; } //Print(pairsEP[ArraySize(pairsEP) - 3].money + " " + pairsEP[ArraySize(pairsEP) - 3].name);

		if (ArraySize(pairsEP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "E", 0) == 0) { baskets2["Pb1"] = pairsEP[ArraySize(pairsEP) - 1].name; baskets2["Pb1"] = pairsEP[ArraySize(pairsEP) - 1].money; } //Print(pairsEP[ArraySize(pairsEP) - 1].money + " " + pairsEP[ArraySize(pairsEP) - 1].name);
		if (ArraySize(pairsEP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "E", 0) == 0) { baskets2["Pb2"] = pairsEP[ArraySize(pairsEP) - 2].name; baskets2["Pb2"] = pairsEP[ArraySize(pairsEP) - 2].money; } //Print(pairsEP[ArraySize(pairsEP) - 2].money + " " + pairsEP[ArraySize(pairsEP) - 2].name);
		if (ArraySize(pairsEP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "E", 0) == 0) { baskets2["Pb3"] = pairsEP[ArraySize(pairsEP) - 3].name; baskets2["Pb3"] = pairsEP[ArraySize(pairsEP) - 3].money; } //Print(pairsEP[ArraySize(pairsEP) - 2].money + " " + pairsEP[ArraySize(pairsEP) - 2].name);

		if (ArraySize(pairsEP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "E", 0) == 0) { baskets2["Pc1"] = pairsEP[ArraySize(pairsEP) - 1].name; baskets2["Pc1"] = pairsEP[ArraySize(pairsEP) - 1].money; } //Print(pairsEP[ArraySize(pairsEP) - 1].money + " " + pairsEP[ArraySize(pairsEP) - 1].name);
		if (ArraySize(pairsEP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "E", 0) == 0) { baskets2["Pc2"] = pairsEP[ArraySize(pairsEP) - 2].name; baskets2["Pc2"] = pairsEP[ArraySize(pairsEP) - 2].money; } //Print(pairsEP[ArraySize(pairsEP) - 1].money + " " + pairsEP[ArraySize(pairsEP) - 1].name);

		if (ArraySize(pairsEN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "E", 0) == 0) { baskets2["Nc1"] = pairsEN[0].name; baskets2["Nc1"] = pairsEN[0].money; } //Print(pairsEN[0].money + " " + pairsEN[0].name);
		if (ArraySize(pairsEN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "E", 0) == 0) { baskets2["Nc2"] = pairsEN[1].name; baskets2["Nc2"] = pairsEN[1].money; } //Print(pairsEN[0].money + " " + pairsEN[0].name);

		if (ArraySize(pairsEN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "E", 0) == 0) { baskets2["Nb1"] = pairsEN[0].name; baskets2["Nb1"] = pairsEN[0].money; } //Print(pairsEN[0].money + " " + pairsEN[0].name);
		if (ArraySize(pairsEN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "E", 0) == 0) { baskets2["Nb2"] = pairsEN[1].name; baskets2["Nb2"] = pairsEN[1].money; } //Print(pairsEN[1].money + " " + pairsEN[1].name);
		if (ArraySize(pairsEN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "E", 0) == 0) { baskets2["Nb3"] = pairsEN[2].name; baskets2["Nb3"] = pairsEN[2].money; } //Print(pairsEN[1].money + " " + pairsEN[1].name);

		if (ArraySize(pairsEN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "E", 0) == 0) { baskets2["Na1"] = pairsEN[0].name; baskets2["Na1"] = pairsEN[0].money; } //Print(pairsEN[0].money + " " + pairsEN[0].name);
		if (ArraySize(pairsEN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "E", 0) == 0) { baskets2["Na2"] = pairsEN[1].name; baskets2["Na2"] = pairsEN[1].money; } //Print(pairsEN[1].money + " " + pairsEN[1].name);
		if (ArraySize(pairsEN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "E", 0) == 0) { baskets2["Na3"] = pairsEN[2].name; baskets2["Na3"] = pairsEN[2].money; } //Print(pairsEN[2].money + " " + pairsEN[2].name);
		if (ArraySize(pairsEN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "E", 0) == 0) { baskets2["Na4"] = pairsEN[3].name; baskets2["Na4"] = pairsEN[3].money; }
	}
	{
		if (ArraySize(pairsGP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "G", 0) == 0) { baskets2["Pa1"] = pairsGP[ArraySize(pairsGP) - 1].name; baskets2["Pa1"] = pairsGP[ArraySize(pairsGP) - 1].money; } //Print(pairsGP[ArraySize(pairsGP) - 1].money + " " + pairsGP[ArraySize(pairsGP) - 1].name);
		if (ArraySize(pairsGP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "G", 0) == 0) { baskets2["Pa2"] = pairsGP[ArraySize(pairsGP) - 2].name; baskets2["Pa2"] = pairsGP[ArraySize(pairsGP) - 2].money; } //Print(pairsGP[ArraySize(pairsGP) - 2].money + " " + pairsGP[ArraySize(pairsGP) - 2].name);
		if (ArraySize(pairsGP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "G", 0) == 0) { baskets2["Pa3"] = pairsGP[ArraySize(pairsGP) - 3].name; baskets2["Pa3"] = pairsGP[ArraySize(pairsGP) - 3].money; } //Print(pairsGP[ArraySize(pairsGP) - 3].money + " " + pairsGP[ArraySize(pairsGP) - 3].name);
		if (ArraySize(pairsGP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "G", 0) == 0) { baskets2["Pa4"] = pairsGP[ArraySize(pairsGP) - 4].name; baskets2["Pa4"] = pairsGP[ArraySize(pairsGP) - 4].money; } //Print(pairsGP[ArraySize(pairsGP) - 3].money + " " + pairsGP[ArraySize(pairsGP) - 3].name);

		if (ArraySize(pairsGP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "G", 0) == 0) { baskets2["Pb1"] = pairsGP[ArraySize(pairsGP) - 1].name; baskets2["Pb1"] = pairsGP[ArraySize(pairsGP) - 1].money; } //Print(pairsGP[ArraySize(pairsGP) - 1].money + " " + pairsGP[ArraySize(pairsGP) - 1].name);
		if (ArraySize(pairsGP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "G", 0) == 0) { baskets2["Pb2"] = pairsGP[ArraySize(pairsGP) - 2].name; baskets2["Pb2"] = pairsGP[ArraySize(pairsGP) - 2].money; } //Print(pairsGP[ArraySize(pairsGP) - 2].money + " " + pairsGP[ArraySize(pairsGP) - 2].name);
		if (ArraySize(pairsGP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "G", 0) == 0) { baskets2["Pb3"] = pairsGP[ArraySize(pairsGP) - 3].name; baskets2["Pb3"] = pairsGP[ArraySize(pairsGP) - 3].money; } //Print(pairsGP[ArraySize(pairsGP) - 2].money + " " + pairsGP[ArraySize(pairsGP) - 2].name);

		if (ArraySize(pairsGP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "G", 0) == 0) { baskets2["Pc1"] = pairsGP[ArraySize(pairsGP) - 1].name; baskets2["Pc1"] = pairsGP[ArraySize(pairsGP) - 1].money; } //Print(pairsGP[ArraySize(pairsGP) - 1].money + " " + pairsGP[ArraySize(pairsGP) - 1].name);
		if (ArraySize(pairsGP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "G", 0) == 0) { baskets2["Pc2"] = pairsGP[ArraySize(pairsGP) - 2].name; baskets2["Pc2"] = pairsGP[ArraySize(pairsGP) - 2].money; } //Print(pairsGP[ArraySize(pairsGP) - 1].money + " " + pairsGP[ArraySize(pairsGP) - 1].name);

		if (ArraySize(pairsGN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "G", 0) == 0) { baskets2["Nc1"] = pairsGN[0].name; baskets2["Nc1"] = pairsGN[0].money; } //Print(pairsGN[0].money + " " + pairsGN[0].name);
		if (ArraySize(pairsGN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "G", 0) == 0) { baskets2["Nc2"] = pairsGN[1].name; baskets2["Nc2"] = pairsGN[1].money; } //Print(pairsGN[0].money + " " + pairsGN[0].name);

		if (ArraySize(pairsGN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "G", 0) == 0) { baskets2["Nb1"] = pairsGN[0].name; baskets2["Nb1"] = pairsGN[0].money; } //Print(pairsGN[0].money + " " + pairsGN[0].name);
		if (ArraySize(pairsGN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "G", 0) == 0) { baskets2["Nb2"] = pairsGN[1].name; baskets2["Nb2"] = pairsGN[1].money; } //Print(pairsGN[1].money + " " + pairsGN[1].name);
		if (ArraySize(pairsGN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "G", 0) == 0) { baskets2["Nb3"] = pairsGN[2].name; baskets2["Nb3"] = pairsGN[2].money; } //Print(pairsGN[1].money + " " + pairsGN[1].name);

		if (ArraySize(pairsGN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "G", 0) == 0) { baskets2["Na1"] = pairsGN[0].name; baskets2["Na1"] = pairsGN[0].money; } //Print(pairsGN[0].money + " " + pairsGN[0].name);
		if (ArraySize(pairsGN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "G", 0) == 0) { baskets2["Na2"] = pairsGN[1].name; baskets2["Na2"] = pairsGN[1].money; } //Print(pairsGN[1].money + " " + pairsGN[1].name);
		if (ArraySize(pairsGN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "G", 0) == 0) { baskets2["Na3"] = pairsGN[2].name; baskets2["Na3"] = pairsGN[2].money; } //Print(pairsGN[2].money + " " + pairsGN[2].name);
		if (ArraySize(pairsGN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "G", 0) == 0) { baskets2["Na4"] = pairsGN[3].name; baskets2["Na4"] = pairsGN[3].money; }
	}
	{
		if (ArraySize(pairsAP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "A", 0) == 0) { baskets2["Pa1"] = pairsAP[ArraySize(pairsAP) - 1].name; baskets2["Pa1"] = pairsAP[ArraySize(pairsAP) - 1].money; } //Print(pairsAP[ArraySize(pairsAP) - 1].money + " " + pairsAP[ArraySize(pairsAP) - 1].name);
		if (ArraySize(pairsAP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "A", 0) == 0) { baskets2["Pa2"] = pairsAP[ArraySize(pairsAP) - 2].name; baskets2["Pa2"] = pairsAP[ArraySize(pairsAP) - 2].money; } //Print(pairsAP[ArraySize(pairsAP) - 2].money + " " + pairsAP[ArraySize(pairsAP) - 2].name);
		if (ArraySize(pairsAP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "A", 0) == 0) { baskets2["Pa3"] = pairsAP[ArraySize(pairsAP) - 3].name; baskets2["Pa3"] = pairsAP[ArraySize(pairsAP) - 3].money; } //Print(pairsAP[ArraySize(pairsAP) - 3].money + " " + pairsAP[ArraySize(pairsAP) - 3].name);
		if (ArraySize(pairsAP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "A", 0) == 0) { baskets2["Pa4"] = pairsAP[ArraySize(pairsAP) - 4].name; baskets2["Pa4"] = pairsAP[ArraySize(pairsAP) - 4].money; } //Print(pairsAP[ArraySize(pairsAP) - 3].money + " " + pairsAP[ArraySize(pairsAP) - 3].name);

		if (ArraySize(pairsAP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "A", 0) == 0) { baskets2["Pb1"] = pairsAP[ArraySize(pairsAP) - 1].name; baskets2["Pb1"] = pairsAP[ArraySize(pairsAP) - 1].money; } //Print(pairsAP[ArraySize(pairsAP) - 1].money + " " + pairsAP[ArraySize(pairsAP) - 1].name);
		if (ArraySize(pairsAP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "A", 0) == 0) { baskets2["Pb2"] = pairsAP[ArraySize(pairsAP) - 2].name; baskets2["Pb2"] = pairsAP[ArraySize(pairsAP) - 2].money; } //Print(pairsAP[ArraySize(pairsAP) - 2].money + " " + pairsAP[ArraySize(pairsAP) - 2].name);
		if (ArraySize(pairsAP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "A", 0) == 0) { baskets2["Pb3"] = pairsAP[ArraySize(pairsAP) - 3].name; baskets2["Pb3"] = pairsAP[ArraySize(pairsAP) - 3].money; } //Print(pairsAP[ArraySize(pairsAP) - 2].money + " " + pairsAP[ArraySize(pairsAP) - 2].name);

		if (ArraySize(pairsAP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "A", 0) == 0) { baskets2["Pc1"] = pairsAP[ArraySize(pairsAP) - 1].name; baskets2["Pc1"] = pairsAP[ArraySize(pairsAP) - 1].money; } //Print(pairsAP[ArraySize(pairsAP) - 1].money + " " + pairsAP[ArraySize(pairsAP) - 1].name);
		if (ArraySize(pairsAP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "A", 0) == 0) { baskets2["Pc2"] = pairsAP[ArraySize(pairsAP) - 2].name; baskets2["Pc2"] = pairsAP[ArraySize(pairsAP) - 2].money; } //Print(pairsAP[ArraySize(pairsAP) - 1].money + " " + pairsAP[ArraySize(pairsAP) - 1].name);

		if (ArraySize(pairsAN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "A", 0) == 0) { baskets2["Nc1"] = pairsAN[0].name; baskets2["Nc1"] = pairsAN[0].money; } //Print(pairsAN[0].money + " " + pairsAN[0].name);
		if (ArraySize(pairsAN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "A", 0) == 0) { baskets2["Nc2"] = pairsAN[1].name; baskets2["Nc2"] = pairsAN[1].money; } //Print(pairsAN[0].money + " " + pairsAN[0].name);

		if (ArraySize(pairsAN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "A", 0) == 0) { baskets2["Nb1"] = pairsAN[0].name; baskets2["Nb1"] = pairsAN[0].money; } //Print(pairsAN[0].money + " " + pairsAN[0].name);
		if (ArraySize(pairsAN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "A", 0) == 0) { baskets2["Nb2"] = pairsAN[1].name; baskets2["Nb2"] = pairsAN[1].money; } //Print(pairsAN[1].money + " " + pairsAN[1].name);
		if (ArraySize(pairsAN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "A", 0) == 0) { baskets2["Nb3"] = pairsAN[2].name; baskets2["Nb3"] = pairsAN[2].money; } //Print(pairsAN[1].money + " " + pairsAN[1].name);

		if (ArraySize(pairsAN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "A", 0) == 0) { baskets2["Na1"] = pairsAN[0].name; baskets2["Na1"] = pairsAN[0].money; } //Print(pairsAN[0].money + " " + pairsAN[0].name);
		if (ArraySize(pairsAN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "A", 0) == 0) { baskets2["Na2"] = pairsAN[1].name; baskets2["Na2"] = pairsAN[1].money; } //Print(pairsAN[1].money + " " + pairsAN[1].name);
		if (ArraySize(pairsAN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "A", 0) == 0) { baskets2["Na3"] = pairsAN[2].name; baskets2["Na3"] = pairsAN[2].money; } //Print(pairsAN[2].money + " " + pairsAN[2].name);
		if (ArraySize(pairsAN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "A", 0) == 0) { baskets2["Na4"] = pairsAN[3].name; baskets2["Na4"] = pairsAN[3].money; }
	}
	{
		if (ArraySize(pairsNP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "N", 0) == 0) { baskets2["Pa1"] = pairsNP[ArraySize(pairsNP) - 1].name; baskets2["Pa1"] = pairsNP[ArraySize(pairsNP) - 1].money; } //Print(pairsNP[ArraySize(pairsNP) - 1].money + " " + pairsNP[ArraySize(pairsNP) - 1].name);
		if (ArraySize(pairsNP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "N", 0) == 0) { baskets2["Pa2"] = pairsNP[ArraySize(pairsNP) - 2].name; baskets2["Pa2"] = pairsNP[ArraySize(pairsNP) - 2].money; } //Print(pairsNP[ArraySize(pairsNP) - 2].money + " " + pairsNP[ArraySize(pairsNP) - 2].name);
		if (ArraySize(pairsNP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "N", 0) == 0) { baskets2["Pa3"] = pairsNP[ArraySize(pairsNP) - 3].name; baskets2["Pa3"] = pairsNP[ArraySize(pairsNP) - 3].money; } //Print(pairsNP[ArraySize(pairsNP) - 3].money + " " + pairsNP[ArraySize(pairsNP) - 3].name);
		if (ArraySize(pairsNP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "N", 0) == 0) { baskets2["Pa4"] = pairsNP[ArraySize(pairsNP) - 4].name; baskets2["Pa4"] = pairsNP[ArraySize(pairsNP) - 4].money; } //Print(pairsNP[ArraySize(pairsNP) - 3].money + " " + pairsNP[ArraySize(pairsNP) - 3].name);

		if (ArraySize(pairsNP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "N", 0) == 0) { baskets2["Pb1"] = pairsNP[ArraySize(pairsNP) - 1].name; baskets2["Pb1"] = pairsNP[ArraySize(pairsNP) - 1].money; } //Print(pairsNP[ArraySize(pairsNP) - 1].money + " " + pairsNP[ArraySize(pairsNP) - 1].name);
		if (ArraySize(pairsNP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "N", 0) == 0) { baskets2["Pb2"] = pairsNP[ArraySize(pairsNP) - 2].name; baskets2["Pb2"] = pairsNP[ArraySize(pairsNP) - 2].money; } //Print(pairsNP[ArraySize(pairsNP) - 2].money + " " + pairsNP[ArraySize(pairsNP) - 2].name);
		if (ArraySize(pairsNP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "N", 0) == 0) { baskets2["Pb3"] = pairsNP[ArraySize(pairsNP) - 3].name; baskets2["Pb3"] = pairsNP[ArraySize(pairsNP) - 3].money; } //Print(pairsNP[ArraySize(pairsNP) - 2].money + " " + pairsNP[ArraySize(pairsNP) - 2].name);

		if (ArraySize(pairsNP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "N", 0) == 0) { baskets2["Pc1"] = pairsNP[ArraySize(pairsNP) - 1].name; baskets2["Pc1"] = pairsNP[ArraySize(pairsNP) - 1].money; } //Print(pairsNP[ArraySize(pairsNP) - 1].money + " " + pairsNP[ArraySize(pairsNP) - 1].name);
		if (ArraySize(pairsNP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "N", 0) == 0) { baskets2["Pc2"] = pairsNP[ArraySize(pairsNP) - 2].name; baskets2["Pc2"] = pairsNP[ArraySize(pairsNP) - 2].money; } //Print(pairsNP[ArraySize(pairsNP) - 1].money + " " + pairsNP[ArraySize(pairsNP) - 1].name);

		if (ArraySize(pairsNN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "N", 0) == 0) { baskets2["Nc1"] = pairsNN[0].name; baskets2["Nc1"] = pairsNN[0].money; } //Print(pairsNN[0].money + " " + pairsNN[0].name);
		if (ArraySize(pairsNN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "N", 0) == 0) { baskets2["Nc2"] = pairsNN[1].name; baskets2["Nc2"] = pairsNN[1].money; } //Print(pairsNN[0].money + " " + pairsNN[0].name);

		if (ArraySize(pairsNN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "N", 0) == 0) { baskets2["Nb1"] = pairsNN[0].name; baskets2["Nb1"] = pairsNN[0].money; } //Print(pairsNN[0].money + " " + pairsNN[0].name);
		if (ArraySize(pairsNN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "N", 0) == 0) { baskets2["Nb2"] = pairsNN[1].name; baskets2["Nb2"] = pairsNN[1].money; } //Print(pairsNN[1].money + " " + pairsNN[1].name);
		if (ArraySize(pairsNN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "N", 0) == 0) { baskets2["Nb3"] = pairsNN[2].name; baskets2["Nb3"] = pairsNN[2].money; } //Print(pairsNN[1].money + " " + pairsNN[1].name);

		if (ArraySize(pairsNN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "N", 0) == 0) { baskets2["Na1"] = pairsNN[0].name; baskets2["Na1"] = pairsNN[0].money; } //Print(pairsNN[0].money + " " + pairsNN[0].name);
		if (ArraySize(pairsNN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "N", 0) == 0) { baskets2["Na2"] = pairsNN[1].name; baskets2["Na2"] = pairsNN[1].money; } //Print(pairsNN[1].money + " " + pairsNN[1].name);
		if (ArraySize(pairsNN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "N", 0) == 0) { baskets2["Na3"] = pairsNN[2].name; baskets2["Na3"] = pairsNN[2].money; } //Print(pairsNN[2].money + " " + pairsNN[2].name);
		if (ArraySize(pairsNN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "N", 0) == 0) { baskets2["Na4"] = pairsNN[3].name; baskets2["Na4"] = pairsNN[3].money; }
	}
	{
		if (ArraySize(pairsCP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "CA", 0) == 0) { baskets2["Pa1"] = pairsCP[ArraySize(pairsCP) - 1].name; baskets2["Pa1"] = pairsCP[ArraySize(pairsCP) - 1].money; } //Print(pairsCP[ArraySize(pairsCP) - 1].money + " " + pairsCP[ArraySize(pairsCP) - 1].name);
		if (ArraySize(pairsCP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "CA", 0) == 0) { baskets2["Pa2"] = pairsCP[ArraySize(pairsCP) - 2].name; baskets2["Pa2"] = pairsCP[ArraySize(pairsCP) - 2].money; } //Print(pairsCP[ArraySize(pairsCP) - 2].money + " " + pairsCP[ArraySize(pairsCP) - 2].name);
		if (ArraySize(pairsCP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "CA", 0) == 0) { baskets2["Pa3"] = pairsCP[ArraySize(pairsCP) - 3].name; baskets2["Pa3"] = pairsCP[ArraySize(pairsCP) - 3].money; } //Print(pairsCP[ArraySize(pairsCP) - 3].money + " " + pairsCP[ArraySize(pairsCP) - 3].name);
		if (ArraySize(pairsCP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "CA", 0) == 0) { baskets2["Pa4"] = pairsCP[ArraySize(pairsCP) - 4].name; baskets2["Pa4"] = pairsCP[ArraySize(pairsCP) - 4].money; } //Print(pairsCP[ArraySize(pairsCP) - 3].money + " " + pairsCP[ArraySize(pairsCP) - 3].name);

		if (ArraySize(pairsCP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "CA", 0) == 0) { baskets2["Pb1"] = pairsCP[ArraySize(pairsCP) - 1].name; baskets2["Pb1"] = pairsCP[ArraySize(pairsCP) - 1].money; } //Print(pairsCP[ArraySize(pairsCP) - 1].money + " " + pairsCP[ArraySize(pairsCP) - 1].name);
		if (ArraySize(pairsCP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "CA", 0) == 0) { baskets2["Pb2"] = pairsCP[ArraySize(pairsCP) - 2].name; baskets2["Pb2"] = pairsCP[ArraySize(pairsCP) - 2].money; } //Print(pairsCP[ArraySize(pairsCP) - 2].money + " " + pairsCP[ArraySize(pairsCP) - 2].name);
		if (ArraySize(pairsCP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "CA", 0) == 0) { baskets2["Pb3"] = pairsCP[ArraySize(pairsCP) - 3].name; baskets2["Pb3"] = pairsCP[ArraySize(pairsCP) - 3].money; } //Print(pairsCP[ArraySize(pairsCP) - 2].money + " " + pairsCP[ArraySize(pairsCP) - 2].name);

		if (ArraySize(pairsCP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "CA", 0) == 0) { baskets2["Pc1"] = pairsCP[ArraySize(pairsCP) - 1].name; baskets2["Pc1"] = pairsCP[ArraySize(pairsCP) - 1].money; } //Print(pairsCP[ArraySize(pairsCP) - 1].money + " " + pairsCP[ArraySize(pairsCP) - 1].name);
		if (ArraySize(pairsCP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "CA", 0) == 0) { baskets2["Pc2"] = pairsCP[ArraySize(pairsCP) - 2].name; baskets2["Pc2"] = pairsCP[ArraySize(pairsCP) - 2].money; } //Print(pairsCP[ArraySize(pairsCP) - 1].money + " " + pairsCP[ArraySize(pairsCP) - 1].name);

		if (ArraySize(pairsCN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "CA", 0) == 0) { baskets2["Nc1"] = pairsCN[0].name; baskets2["Nc1"] = pairsCN[0].money; } //Print(pairsCN[0].money + " " + pairsCN[0].name);
		if (ArraySize(pairsCN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "CA", 0) == 0) { baskets2["Nc2"] = pairsCN[1].name; baskets2["Nc2"] = pairsCN[1].money; } //Print(pairsCN[0].money + " " + pairsCN[0].name);

		if (ArraySize(pairsCN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "CA", 0) == 0) { baskets2["Nb1"] = pairsCN[0].name; baskets2["Nb1"] = pairsCN[0].money; } //Print(pairsCN[0].money + " " + pairsCN[0].name);
		if (ArraySize(pairsCN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "CA", 0) == 0) { baskets2["Nb2"] = pairsCN[1].name; baskets2["Nb2"] = pairsCN[1].money; } //Print(pairsCN[1].money + " " + pairsCN[1].name);
		if (ArraySize(pairsCN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "CA", 0) == 0) { baskets2["Nb3"] = pairsCN[2].name; baskets2["Nb3"] = pairsCN[2].money; } //Print(pairsCN[1].money + " " + pairsCN[1].name);

		if (ArraySize(pairsCN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "CA", 0) == 0) { baskets2["Na1"] = pairsCN[0].name; baskets2["Na1"] = pairsCN[0].money; } //Print(pairsCN[0].money + " " + pairsCN[0].name);
		if (ArraySize(pairsCN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "CA", 0) == 0) { baskets2["Na2"] = pairsCN[1].name; baskets2["Na2"] = pairsCN[1].money; } //Print(pairsCN[1].money + " " + pairsCN[1].name);
		if (ArraySize(pairsCN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "CA", 0) == 0) { baskets2["Na3"] = pairsCN[2].name; baskets2["Na3"] = pairsCN[2].money; } //Print(pairsCN[2].money + " " + pairsCN[2].name);
		if (ArraySize(pairsCN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "CA", 0) == 0) { baskets2["Na4"] = pairsCN[3].name; baskets2["Na4"] = pairsCN[3].money; }
	}
	{
		if (ArraySize(pairsUP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "U", 0) == 0) { baskets2["Pa1"] = pairsUP[ArraySize(pairsUP) - 1].name; baskets2["Pa1"] = pairsUP[ArraySize(pairsUP) - 1].money; } //Print(pairsUP[ArraySize(pairsUP) - 1].money + " " + pairsUP[ArraySize(pairsUP) - 1].name);
		if (ArraySize(pairsUP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "U", 0) == 0) { baskets2["Pa2"] = pairsUP[ArraySize(pairsUP) - 2].name; baskets2["Pa2"] = pairsUP[ArraySize(pairsUP) - 2].money; } //Print(pairsUP[ArraySize(pairsUP) - 2].money + " " + pairsUP[ArraySize(pairsUP) - 2].name);
		if (ArraySize(pairsUP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "U", 0) == 0) { baskets2["Pa3"] = pairsUP[ArraySize(pairsUP) - 3].name; baskets2["Pa3"] = pairsUP[ArraySize(pairsUP) - 3].money; } //Print(pairsUP[ArraySize(pairsUP) - 3].money + " " + pairsUP[ArraySize(pairsUP) - 3].name);
		if (ArraySize(pairsUP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "U", 0) == 0) { baskets2["Pa4"] = pairsUP[ArraySize(pairsUP) - 4].name; baskets2["Pa4"] = pairsUP[ArraySize(pairsUP) - 4].money; } //Print(pairsUP[ArraySize(pairsUP) - 3].money + " " + pairsUP[ArraySize(pairsUP) - 3].name);

		if (ArraySize(pairsUP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "U", 0) == 0) { baskets2["Pb1"] = pairsUP[ArraySize(pairsUP) - 1].name; baskets2["Pb1"] = pairsUP[ArraySize(pairsUP) - 1].money; } //Print(pairsUP[ArraySize(pairsUP) - 1].money + " " + pairsUP[ArraySize(pairsUP) - 1].name);
		if (ArraySize(pairsUP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "U", 0) == 0) { baskets2["Pb2"] = pairsUP[ArraySize(pairsUP) - 2].name; baskets2["Pb2"] = pairsUP[ArraySize(pairsUP) - 2].money; } //Print(pairsUP[ArraySize(pairsUP) - 2].money + " " + pairsUP[ArraySize(pairsUP) - 2].name);
		if (ArraySize(pairsUP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "U", 0) == 0) { baskets2["Pb3"] = pairsUP[ArraySize(pairsUP) - 3].name; baskets2["Pb3"] = pairsUP[ArraySize(pairsUP) - 3].money; } //Print(pairsUP[ArraySize(pairsUP) - 2].money + " " + pairsUP[ArraySize(pairsUP) - 2].name);

		if (ArraySize(pairsUP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "U", 0) == 0) { baskets2["Pc1"] = pairsUP[ArraySize(pairsUP) - 1].name; baskets2["Pc1"] = pairsUP[ArraySize(pairsUP) - 1].money; } //Print(pairsUP[ArraySize(pairsUP) - 1].money + " " + pairsUP[ArraySize(pairsUP) - 1].name);
		if (ArraySize(pairsUP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "U", 0) == 0) { baskets2["Pc2"] = pairsUP[ArraySize(pairsUP) - 2].name; baskets2["Pc2"] = pairsUP[ArraySize(pairsUP) - 2].money; } //Print(pairsUP[ArraySize(pairsUP) - 1].money + " " + pairsUP[ArraySize(pairsUP) - 1].name);

		if (ArraySize(pairsUN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "U", 0) == 0) { baskets2["Nc1"] = pairsUN[0].name; baskets2["Nc1"] = pairsUN[0].money; } //Print(pairsUN[0].money + " " + pairsUN[0].name);
		if (ArraySize(pairsUN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "U", 0) == 0) { baskets2["Nc2"] = pairsUN[1].name; baskets2["Nc2"] = pairsUN[1].money; } //Print(pairsUN[0].money + " " + pairsUN[0].name);

		if (ArraySize(pairsUN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "U", 0) == 0) { baskets2["Nb1"] = pairsUN[0].name; baskets2["Nb1"] = pairsUN[0].money; } //Print(pairsUN[0].money + " " + pairsUN[0].name);
		if (ArraySize(pairsUN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "U", 0) == 0) { baskets2["Nb2"] = pairsUN[1].name; baskets2["Nb2"] = pairsUN[1].money; } //Print(pairsUN[1].money + " " + pairsUN[1].name);
		if (ArraySize(pairsUN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "U", 0) == 0) { baskets2["Nb3"] = pairsUN[2].name; baskets2["Nb3"] = pairsUN[2].money; } //Print(pairsUN[1].money + " " + pairsUN[1].name);

		if (ArraySize(pairsUN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "U", 0) == 0) { baskets2["Na1"] = pairsUN[0].name; baskets2["Na1"] = pairsUN[0].money; } //Print(pairsUN[0].money + " " + pairsUN[0].name);
		if (ArraySize(pairsUN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "U", 0) == 0) { baskets2["Na2"] = pairsUN[1].name; baskets2["Na2"] = pairsUN[1].money; } //Print(pairsUN[1].money + " " + pairsUN[1].name);
		if (ArraySize(pairsUN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "U", 0) == 0) { baskets2["Na3"] = pairsUN[2].name; baskets2["Na3"] = pairsUN[2].money; } //Print(pairsUN[2].money + " " + pairsUN[2].name);
		if (ArraySize(pairsUN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "U", 0) == 0) { baskets2["Na4"] = pairsUN[3].name; baskets2["Na4"] = pairsUN[3].money; }
	}
	{
		if (ArraySize(pairsFP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "CH", 0) == 0) { baskets2["Pa1"] = pairsFP[ArraySize(pairsFP) - 1].name; baskets2["Pa1"] = pairsFP[ArraySize(pairsFP) - 1].money; } //Print(pairsFP[ArraySize(pairsFP) - 1].money + " " + pairsFP[ArraySize(pairsFP) - 1].name);
		if (ArraySize(pairsFP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "CH", 0) == 0) { baskets2["Pa2"] = pairsFP[ArraySize(pairsFP) - 2].name; baskets2["Pa2"] = pairsFP[ArraySize(pairsFP) - 2].money; } //Print(pairsFP[ArraySize(pairsFP) - 2].money + " " + pairsFP[ArraySize(pairsFP) - 2].name);
		if (ArraySize(pairsFP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "CH", 0) == 0) { baskets2["Pa3"] = pairsFP[ArraySize(pairsFP) - 3].name; baskets2["Pa3"] = pairsFP[ArraySize(pairsFP) - 3].money; } //Print(pairsFP[ArraySize(pairsFP) - 3].money + " " + pairsFP[ArraySize(pairsFP) - 3].name);
		if (ArraySize(pairsFP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "CH", 0) == 0) { baskets2["Pa4"] = pairsFP[ArraySize(pairsFP) - 4].name; baskets2["Pa4"] = pairsFP[ArraySize(pairsFP) - 4].money; } //Print(pairsFP[ArraySize(pairsFP) - 3].money + " " + pairsFP[ArraySize(pairsFP) - 3].name);

		if (ArraySize(pairsFP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "CH", 0) == 0) { baskets2["Pb1"] = pairsFP[ArraySize(pairsFP) - 1].name; baskets2["Pb1"] = pairsFP[ArraySize(pairsFP) - 1].money; } //Print(pairsFP[ArraySize(pairsFP) - 1].money + " " + pairsFP[ArraySize(pairsFP) - 1].name);
		if (ArraySize(pairsFP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "CH", 0) == 0) { baskets2["Pb2"] = pairsFP[ArraySize(pairsFP) - 2].name; baskets2["Pb2"] = pairsFP[ArraySize(pairsFP) - 2].money; } //Print(pairsFP[ArraySize(pairsFP) - 2].money + " " + pairsFP[ArraySize(pairsFP) - 2].name);
		if (ArraySize(pairsFP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "CH", 0) == 0) { baskets2["Pb3"] = pairsFP[ArraySize(pairsFP) - 3].name; baskets2["Pb3"] = pairsFP[ArraySize(pairsFP) - 3].money; } //Print(pairsFP[ArraySize(pairsFP) - 2].money + " " + pairsFP[ArraySize(pairsFP) - 2].name);

		if (ArraySize(pairsFP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "CH", 0) == 0) { baskets2["Pc1"] = pairsFP[ArraySize(pairsFP) - 1].name; baskets2["Pc1"] = pairsFP[ArraySize(pairsFP) - 1].money; } //Print(pairsFP[ArraySize(pairsFP) - 1].money + " " + pairsFP[ArraySize(pairsFP) - 1].name);
		if (ArraySize(pairsFP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "CH", 0) == 0) { baskets2["Pc2"] = pairsFP[ArraySize(pairsFP) - 2].name; baskets2["Pc2"] = pairsFP[ArraySize(pairsFP) - 2].money; } //Print(pairsFP[ArraySize(pairsFP) - 1].money + " " + pairsFP[ArraySize(pairsFP) - 1].name);

		if (ArraySize(pairsFN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "CH", 0) == 0) { baskets2["Nc1"] = pairsFN[0].name; baskets2["Nc1"] = pairsFN[0].money; } //Print(pairsFN[0].money + " " + pairsFN[0].name);
		if (ArraySize(pairsFN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "CH", 0) == 0) { baskets2["Nc2"] = pairsFN[1].name; baskets2["Nc2"] = pairsFN[1].money; } //Print(pairsFN[0].money + " " + pairsFN[0].name);

		if (ArraySize(pairsFN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "CH", 0) == 0) { baskets2["Nb1"] = pairsFN[0].name; baskets2["Nb1"] = pairsFN[0].money; } //Print(pairsFN[0].money + " " + pairsFN[0].name);
		if (ArraySize(pairsFN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "CH", 0) == 0) { baskets2["Nb2"] = pairsFN[1].name; baskets2["Nb2"] = pairsFN[1].money; } //Print(pairsFN[1].money + " " + pairsFN[1].name);
		if (ArraySize(pairsFN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "CH", 0) == 0) { baskets2["Nb3"] = pairsFN[2].name; baskets2["Nb3"] = pairsFN[2].money; } //Print(pairsFN[1].money + " " + pairsFN[1].name);

		if (ArraySize(pairsFN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "CH", 0) == 0) { baskets2["Na1"] = pairsFN[0].name; baskets2["Na1"] = pairsFN[0].money; } //Print(pairsFN[0].money + " " + pairsFN[0].name);
		if (ArraySize(pairsFN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "CH", 0) == 0) { baskets2["Na2"] = pairsFN[1].name; baskets2["Na2"] = pairsFN[1].money; } //Print(pairsFN[1].money + " " + pairsFN[1].name);
		if (ArraySize(pairsFN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "CH", 0) == 0) { baskets2["Na3"] = pairsFN[2].name; baskets2["Na3"] = pairsFN[2].money; } //Print(pairsFN[2].money + " " + pairsFN[2].name);
		if (ArraySize(pairsFN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "CH", 0) == 0) { baskets2["Na4"] = pairsFN[3].name; baskets2["Na4"] = pairsFN[3].money; }
	}
	{
		if (ArraySize(pairsJP) - 1 > 0)
			if (StringFind(basketsMain[7].name, "J", 0) == 0) { baskets2["Pa1"] = pairsJP[ArraySize(pairsJP) - 1].name; baskets2["Pa1"] = pairsJP[ArraySize(pairsJP) - 1].money; } //Print(pairsJP[ArraySize(pairsJP) - 1].money + " " + pairsJP[ArraySize(pairsJP) - 1].name);
		if (ArraySize(pairsJP) - 1 > 1)
			if (StringFind(basketsMain[7].name, "J", 0) == 0) { baskets2["Pa2"] = pairsJP[ArraySize(pairsJP) - 2].name; baskets2["Pa2"] = pairsJP[ArraySize(pairsJP) - 2].money; } //Print(pairsJP[ArraySize(pairsJP) - 2].money + " " + pairsJP[ArraySize(pairsJP) - 2].name);
		if (ArraySize(pairsJP) - 1 > 2)
			if (StringFind(basketsMain[7].name, "J", 0) == 0) { baskets2["Pa3"] = pairsJP[ArraySize(pairsJP) - 3].name; baskets2["Pa3"] = pairsJP[ArraySize(pairsJP) - 3].money; } //Print(pairsJP[ArraySize(pairsJP) - 3].money + " " + pairsJP[ArraySize(pairsJP) - 3].name);
		if (ArraySize(pairsJP) - 1 > 3)
			if (StringFind(basketsMain[7].name, "J", 0) == 0) { baskets2["Pa4"] = pairsJP[ArraySize(pairsJP) - 4].name; baskets2["Pa4"] = pairsJP[ArraySize(pairsJP) - 4].money; } //Print(pairsJP[ArraySize(pairsJP) - 3].money + " " + pairsJP[ArraySize(pairsJP) - 3].name);

		if (ArraySize(pairsJP) - 1 > 0)
			if (StringFind(basketsMain[6].name, "J", 0) == 0) { baskets2["Pb1"] = pairsJP[ArraySize(pairsJP) - 1].name; baskets2["Pb1"] = pairsJP[ArraySize(pairsJP) - 1].money; } //Print(pairsJP[ArraySize(pairsJP) - 1].money + " " + pairsJP[ArraySize(pairsJP) - 1].name);
		if (ArraySize(pairsJP) - 1 > 1)
			if (StringFind(basketsMain[6].name, "J", 0) == 0) { baskets2["Pb2"] = pairsJP[ArraySize(pairsJP) - 2].name; baskets2["Pb2"] = pairsJP[ArraySize(pairsJP) - 2].money; } //Print(pairsJP[ArraySize(pairsJP) - 2].money + " " + pairsJP[ArraySize(pairsJP) - 2].name);
		if (ArraySize(pairsJP) - 1 > 2)
			if (StringFind(basketsMain[6].name, "J", 0) == 0) { baskets2["Pb3"] = pairsJP[ArraySize(pairsJP) - 3].name; baskets2["Pb3"] = pairsJP[ArraySize(pairsJP) - 3].money; } //Print(pairsJP[ArraySize(pairsJP) - 2].money + " " + pairsJP[ArraySize(pairsJP) - 2].name);

		if (ArraySize(pairsJP) - 1 > 0)
			if (StringFind(basketsMain[5].name, "J", 0) == 0) { baskets2["Pc1"] = pairsJP[ArraySize(pairsJP) - 1].name; baskets2["Pc1"] = pairsJP[ArraySize(pairsJP) - 1].money; } //Print(pairsJP[ArraySize(pairsJP) - 1].money + " " + pairsJP[ArraySize(pairsJP) - 1].name);
		if (ArraySize(pairsJP) - 1 > 1)
			if (StringFind(basketsMain[5].name, "J", 0) == 0) { baskets2["Pc2"] = pairsJP[ArraySize(pairsJP) - 2].name; baskets2["Pc2"] = pairsJP[ArraySize(pairsJP) - 2].money; } //Print(pairsJP[ArraySize(pairsJP) - 1].money + " " + pairsJP[ArraySize(pairsJP) - 1].name);

		if (ArraySize(pairsJN) - 1 > 0)
			if (StringFind(basketsMain[2].name, "J", 0) == 0) { baskets2["Nc1"] = pairsJN[0].name; baskets2["Nc1"] = pairsJN[0].money; } //Print(pairsJN[0].money + " " + pairsJN[0].name);
		if (ArraySize(pairsJN) - 1 > 1)
			if (StringFind(basketsMain[2].name, "J", 0) == 0) { baskets2["Nc2"] = pairsJN[1].name; baskets2["Nc2"] = pairsJN[1].money; } //Print(pairsJN[0].money + " " + pairsJN[0].name);

		if (ArraySize(pairsJN) - 1 > 0)
			if (StringFind(basketsMain[1].name, "J", 0) == 0) { baskets2["Nb1"] = pairsJN[0].name; baskets2["Nb1"] = pairsJN[0].money; } //Print(pairsJN[0].money + " " + pairsJN[0].name);
		if (ArraySize(pairsJN) - 1 > 1)
			if (StringFind(basketsMain[1].name, "J", 0) == 0) { baskets2["Nb2"] = pairsJN[1].name; baskets2["Nb2"] = pairsJN[1].money; } //Print(pairsJN[1].money + " " + pairsJN[1].name);
		if (ArraySize(pairsJN) - 1 > 2)
			if (StringFind(basketsMain[1].name, "J", 0) == 0) { baskets2["Nb3"] = pairsJN[2].name; baskets2["Nb3"] = pairsJN[2].money; } //Print(pairsJN[1].money + " " + pairsJN[1].name);

		if (ArraySize(pairsJN) - 1 > 0)
			if (StringFind(basketsMain[0].name, "J", 0) == 0) { baskets2["Na1"] = pairsJN[0].name; baskets2["Na1"] = pairsJN[0].money; } //Print(pairsJN[0].money + " " + pairsJN[0].name);
		if (ArraySize(pairsJN) - 1 > 1)
			if (StringFind(basketsMain[0].name, "J", 0) == 0) { baskets2["Na2"] = pairsJN[1].name; baskets2["Na2"] = pairsJN[1].money; } //Print(pairsJN[1].money + " " + pairsJN[1].name);
		if (ArraySize(pairsJN) - 1 > 2)
			if (StringFind(basketsMain[0].name, "J", 0) == 0) { baskets2["Na3"] = pairsJN[2].name; baskets2["Na3"] = pairsJN[2].money; } //Print(pairsJN[2].money + " " + pairsJN[2].name);
		if (ArraySize(pairsJN) - 1 > 3)
			if (StringFind(basketsMain[0].name, "J", 0) == 0) { baskets2["Na4"] = pairsJN[3].name; baskets2["Na4"] = pairsJN[3].money; }
	}

	ArrayFree(basketsMain2);
	ArrayFree(pairsEP);
	ArrayFree(pairsEN);
	ArrayFree(pairsGP);
	ArrayFree(pairsGN);
	ArrayFree(pairsAP);
	ArrayFree(pairsAN);
	ArrayFree(pairsNP);
	ArrayFree(pairsNN);
	ArrayFree(pairsCP);
	ArrayFree(pairsCN);
	ArrayFree(pairsUP);
	ArrayFree(pairsUN);
	ArrayFree(pairsFP);
	ArrayFree(pairsFN);
	ArrayFree(pairsJP);
	ArrayFree(pairsJN);

	//LIVE FIBS
	FibData Fibz[];

	string symbols[28];

	symbols[0] = eu_p; symbols[1] = eg_p; symbols[2] = ea_p; symbols[3] = en_p; symbols[4] = ec_p; symbols[5] = ef_p; symbols[6] = ej_p;
	symbols[7] = gu_p; symbols[8] = ga_p; symbols[9] = gn_p; symbols[10] = gc_p; symbols[11] = gf_p; symbols[12] = gj_p; symbols[13] = au_p;
	symbols[14] = an_p; symbols[15] = ac_p; symbols[16] = af_p; symbols[17] = aj_p; symbols[18] = nu_p; symbols[19] = nc_p; symbols[20] = nf_p;
	symbols[21] = nj_p; symbols[22] = uc_p; symbols[23] = cf_p; symbols[24] = cj_p; symbols[25] = uf_p; symbols[26] = fj_p; symbols[27] = uj_p;

	string symbols1[] = { "eu", "eg", "ea", "en", "ec", "ef", "ej", "gu", "ga", "gn", "gc", "gf", "gj", "au",
						  "an", "ac", "af", "aj", "nu", "nc", "nf", "nj", "uc", "cf", "cj", "uf", "fj", "uj" };

	int hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10;

	for (int x = 0; x < 28; x++)
	{
		int ascheck, wmidcheck;
		bool midcheck, d1check, d2check, d3check, d4check;
		string kingkey = symbols1[x] + "fib";

		CalculateFibLevels2(symbols[x], pastdata[kingkey][3][0].ToDbl(), pastdata[kingkey][3][1].ToDbl(), pastdata[kingkey][4][0].ToDbl(), pastdata[kingkey][4][1].ToDbl(), pastdata[kingkey][5][0].ToDbl(), pastdata[kingkey][5][1].ToDbl(), hr, MarketInfo(symbols[x], MODE_BID), ascheck, midcheck, wmidcheck, d1check, d2check, d3check, d4check);
		AddToDrata(Fibz, dwm[x].daily, ascheck, midcheck, wmidcheck, d1check, d2check, d3check, d4check, eu_p);
	}

	ArraySortStruct(Fibz, dailyscore);

	for (int x = 27; x >= 0; x--)
	{
		mainsort["b"][27 - x].Add(Fibz[x].dailyscore);
		mainsort["b"][27 - x].Add(Fibz[x].ashilo);
		mainsort["b"][27 - x].Add(Fibz[x].asmid);
		mainsort["b"][27 - x].Add(Fibz[x].wmid);
		mainsort["b"][27 - x].Add(Fibz[x].d1ch);
		mainsort["b"][27 - x].Add(Fibz[x].d2ch);
		mainsort["b"][27 - x].Add(Fibz[x].d3ch);
		mainsort["b"][27 - x].Add(Fibz[x].d4ch);
	}

	ArrayFree(Fibz);

	Data rvola[];

	{
		AddToData(rvola, dwm[0].daily, cdr(eu_p, 0) / pastdata["adr"][0].ToDbl(), dtdspr(eu_p) * eupl);
		AddToData(rvola, dwm[1].daily, cdr(eg_p, 0) / pastdata["adr"][1].ToDbl(), dtdspr(eg_p) * egpl);
		AddToData(rvola, dwm[2].daily, cdr(ea_p, 0) / pastdata["adr"][2].ToDbl(), dtdspr(ea_p) * eapl);
		AddToData(rvola, dwm[3].daily, cdr(en_p, 0) / pastdata["adr"][3].ToDbl(), dtdspr(en_p) * enpl);
		AddToData(rvola, dwm[4].daily, cdr(ec_p, 0) / pastdata["adr"][4].ToDbl(), dtdspr(ec_p) * ecpl);
		AddToData(rvola, dwm[5].daily, cdr(ef_p, 0) / pastdata["adr"][5].ToDbl(), dtdspr(ef_p) * efpl);
		AddToData(rvola, dwm[6].daily, cdr(ej_p, 0) / pastdata["adr"][6].ToDbl(), dtdspr(ej_p) * ejpl);
		AddToData(rvola, dwm[7].daily, cdr(gu_p, 0) / pastdata["adr"][7].ToDbl(), dtdspr(gu_p) * gupl);
		AddToData(rvola, dwm[8].daily, cdr(ga_p, 0) / pastdata["adr"][8].ToDbl(), dtdspr(ga_p) * gapl);
		AddToData(rvola, dwm[9].daily, cdr(gn_p, 0) / pastdata["adr"][9].ToDbl(), dtdspr(gn_p) * gnpl);
		AddToData(rvola, dwm[10].daily, cdr(gc_p, 0) / pastdata["adr"][10].ToDbl(), dtdspr(gc_p) * gcpl);
		AddToData(rvola, dwm[11].daily, cdr(gf_p, 0) / pastdata["adr"][11].ToDbl(), dtdspr(gf_p) * gfpl);
		AddToData(rvola, dwm[12].daily, cdr(gj_p, 0) / pastdata["adr"][12].ToDbl(), dtdspr(gj_p) * gjpl);
		AddToData(rvola, dwm[13].daily, cdr(au_p, 0) / pastdata["adr"][13].ToDbl(), dtdspr(au_p) * aupl);
		AddToData(rvola, dwm[14].daily, cdr(an_p, 0) / pastdata["adr"][14].ToDbl(), dtdspr(an_p) * anpl);
		AddToData(rvola, dwm[15].daily, cdr(ac_p, 0) / pastdata["adr"][15].ToDbl(), dtdspr(ac_p) * acpl);
		AddToData(rvola, dwm[16].daily, cdr(af_p, 0) / pastdata["adr"][16].ToDbl(), dtdspr(af_p) * afpl);
		AddToData(rvola, dwm[17].daily, cdr(aj_p, 0) / pastdata["adr"][17].ToDbl(), dtdspr(aj_p) * ajpl);
		AddToData(rvola, dwm[18].daily, cdr(nu_p, 0) / pastdata["adr"][18].ToDbl(), dtdspr(nu_p) * nupl);
		AddToData(rvola, dwm[19].daily, cdr(nc_p, 0) / pastdata["adr"][19].ToDbl(), dtdspr(nc_p) * ncpl);
		AddToData(rvola, dwm[20].daily, cdr(nf_p, 0) / pastdata["adr"][20].ToDbl(), dtdspr(nf_p) * nfpl);
		AddToData(rvola, dwm[21].daily, cdr(nj_p, 0) / pastdata["adr"][21].ToDbl(), dtdspr(nj_p) * njpl);
		AddToData(rvola, dwm[22].daily, cdr(uc_p, 0) / pastdata["adr"][22].ToDbl(), dtdspr(uc_p) * ucpl);
		AddToData(rvola, dwm[23].daily, cdr(cf_p, 0) / pastdata["adr"][23].ToDbl(), dtdspr(cf_p) * cfpl);
		AddToData(rvola, dwm[24].daily, cdr(cj_p, 0) / pastdata["adr"][24].ToDbl(), dtdspr(cj_p) * cjpl);
		AddToData(rvola, dwm[25].daily, cdr(uf_p, 0) / pastdata["adr"][25].ToDbl(), dtdspr(uf_p) * ufpl);
		AddToData(rvola, dwm[26].daily, cdr(fj_p, 0) / pastdata["adr"][26].ToDbl(), dtdspr(fj_p) * fjpl);
		AddToData(rvola, dwm[27].daily, cdr(uj_p, 0) / pastdata["adr"][27].ToDbl(), dtdspr(uj_p) * ujpl);
	}

	ArraySortStruct(rvola, daily);

	for (int x = 27; x >= 0; x--)
	{
		mainsort["c"][27 - x].Add(rvola[x].rsid, 2);
	}

	totspr = 0;
	for (int x = 27; x >= 0; x--)
	{
		totspr += rvola[x].dtd / 28;
	}

	ArrayFree(rvola);

	Baskets b200[];

	AddToBaskets(b200, dtd200(eu_p) * eupl, 0, 0, 0, 0, 0, 0, "E");
	AddToBaskets(b200, dtd200(gu_p) * gupl, 0, 0, 0, 0, 0, 0, "G");
	AddToBaskets(b200, dtd200(au_p) * aupl, 0, 0, 0, 0, 0, 0, "A");
	AddToBaskets(b200, dtd200(nu_p) * nupl, 0, 0, 0, 0, 0, 0, "N");
	AddToBaskets(b200, -dtd200(uc_p) * ucpl, 0, 0, 0, 0, 0, 0, "C");
	AddToBaskets(b200, -dtd200(uf_p) * ufpl, 0, 0, 0, 0, 0, 0, "F");
	AddToBaskets(b200, -dtd200(uj_p) * ujpl, 0, 0, 0, 0, 0, 0, "J");

	ArraySortStruct(b200, pips);

	for (int x = 6; x >= 0; x--)
	{
		basketdata["200"][6 - x] = b200[x].name;
		basketdata["200"][6 - x] = b200[x].pips;
	}

	ArrayFree(b200);

	//DAY
	//HIGHS
	double eu_dh = (eu.GetHighValue(0) - eu.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_dh = (eg.GetHighValue(0) - eg.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_dh = (ea.GetHighValue(0) - ea.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_dh = (en.GetHighValue(0) - en.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_dh = (ec.GetHighValue(0) - ec.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_dh = (ef.GetHighValue(0) - ef.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_dh = (ej.GetHighValue(0) - ej.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_dh = (gu.GetHighValue(0) - gu.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_dh = (ga.GetHighValue(0) - ga.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_dh = (gn.GetHighValue(0) - gn.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_dh = (gc.GetHighValue(0) - gc.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_dh = (gf.GetHighValue(0) - gf.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_dh = (gj.GetHighValue(0) - gj.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_dh = (au.GetHighValue(0) - au.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_dh = (an.GetHighValue(0) - an.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_dh = (ac.GetHighValue(0) - ac.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_dh = (af.GetHighValue(0) - af.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_dh = (aj.GetHighValue(0) - aj.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_dh = (nu.GetHighValue(0) - nu.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_dh = (nc.GetHighValue(0) - nc.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_dh = (nf.GetHighValue(0) - nf.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_dh = (nj.GetHighValue(0) - nj.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_dh = (uc.GetHighValue(0) - uc.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_dh = (cf.GetHighValue(0) - cf.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_dh = (cj.GetHighValue(0) - cj.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_dh = (uf.GetHighValue(0) - uf.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_dh = (fj.GetHighValue(0) - fj.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_dh = (uj.GetHighValue(0) - uj.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//LOWS
	double eu_dl = (eu.GetCloseValue(1) - eu.GetLowValue(0)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_dl = (eg.GetCloseValue(1) - eg.GetLowValue(0)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_dl = (ea.GetCloseValue(1) - ea.GetLowValue(0)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_dl = (en.GetCloseValue(1) - en.GetLowValue(0)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_dl = (ec.GetCloseValue(1) - ec.GetLowValue(0)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_dl = (ef.GetCloseValue(1) - ef.GetLowValue(0)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_dl = (ej.GetCloseValue(1) - ej.GetLowValue(0)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_dl = (gu.GetCloseValue(1) - gu.GetLowValue(0)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_dl = (ga.GetCloseValue(1) - ga.GetLowValue(0)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_dl = (gn.GetCloseValue(1) - gn.GetLowValue(0)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_dl = (gc.GetCloseValue(1) - gc.GetLowValue(0)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_dl = (gf.GetCloseValue(1) - gf.GetLowValue(0)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_dl = (gj.GetCloseValue(1) - gj.GetLowValue(0)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_dl = (au.GetCloseValue(1) - au.GetLowValue(0)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_dl = (an.GetCloseValue(1) - an.GetLowValue(0)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_dl = (ac.GetCloseValue(1) - ac.GetLowValue(0)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_dl = (af.GetCloseValue(1) - af.GetLowValue(0)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_dl = (aj.GetCloseValue(1) - aj.GetLowValue(0)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_dl = (nu.GetCloseValue(1) - nu.GetLowValue(0)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_dl = (nc.GetCloseValue(1) - nc.GetLowValue(0)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_dl = (nf.GetCloseValue(1) - nf.GetLowValue(0)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_dl = (nj.GetCloseValue(1) - nj.GetLowValue(0)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_dl = (uc.GetCloseValue(1) - uc.GetLowValue(0)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_dl = (cf.GetCloseValue(1) - cf.GetLowValue(0)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_dl = (cj.GetCloseValue(1) - cj.GetLowValue(0)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_dl = (uf.GetCloseValue(1) - uf.GetLowValue(0)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_dl = (fj.GetCloseValue(1) - fj.GetLowValue(0)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_dl = (uj.GetCloseValue(1) - uj.GetLowValue(0)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//CLOSES
	double eu_dc = (eu.GetCloseValue(0) - eu.GetCloseValue(1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_dc = (eg.GetCloseValue(0) - eg.GetCloseValue(1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_dc = (ea.GetCloseValue(0) - ea.GetCloseValue(1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_dc = (en.GetCloseValue(0) - en.GetCloseValue(1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_dc = (ec.GetCloseValue(0) - ec.GetCloseValue(1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_dc = (ef.GetCloseValue(0) - ef.GetCloseValue(1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_dc = (ej.GetCloseValue(0) - ej.GetCloseValue(1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_dc = (gu.GetCloseValue(0) - gu.GetCloseValue(1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_dc = (ga.GetCloseValue(0) - ga.GetCloseValue(1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_dc = (gn.GetCloseValue(0) - gn.GetCloseValue(1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_dc = (gc.GetCloseValue(0) - gc.GetCloseValue(1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_dc = (gf.GetCloseValue(0) - gf.GetCloseValue(1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_dc = (gj.GetCloseValue(0) - gj.GetCloseValue(1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_dc = (au.GetCloseValue(0) - au.GetCloseValue(1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_dc = (an.GetCloseValue(0) - an.GetCloseValue(1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_dc = (ac.GetCloseValue(0) - ac.GetCloseValue(1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_dc = (af.GetCloseValue(0) - af.GetCloseValue(1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_dc = (aj.GetCloseValue(0) - aj.GetCloseValue(1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_dc = (nu.GetCloseValue(0) - nu.GetCloseValue(1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_dc = (nc.GetCloseValue(0) - nc.GetCloseValue(1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_dc = (nf.GetCloseValue(0) - nf.GetCloseValue(1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_dc = (nj.GetCloseValue(0) - nj.GetCloseValue(1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_dc = (uc.GetCloseValue(0) - uc.GetCloseValue(1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_dc = (cf.GetCloseValue(0) - cf.GetCloseValue(1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_dc = (cj.GetCloseValue(0) - cj.GetCloseValue(1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_dc = (uf.GetCloseValue(0) - uf.GetCloseValue(1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_dc = (fj.GetCloseValue(0) - fj.GetCloseValue(1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_dc = (uj.GetCloseValue(0) - uj.GetCloseValue(1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//WEEK
	//HIGHS
	double eu_wh = (eu.GetHighValue(0) - iClose(eu_p, PERIOD_W1, 1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_wh = (eg.GetHighValue(0) - iClose(eg_p, PERIOD_W1, 1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_wh = (ea.GetHighValue(0) - iClose(ea_p, PERIOD_W1, 1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_wh = (en.GetHighValue(0) - iClose(en_p, PERIOD_W1, 1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_wh = (ec.GetHighValue(0) - iClose(ec_p, PERIOD_W1, 1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_wh = (ef.GetHighValue(0) - iClose(ef_p, PERIOD_W1, 1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_wh = (ej.GetHighValue(0) - iClose(ej_p, PERIOD_W1, 1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_wh = (gu.GetHighValue(0) - iClose(gu_p, PERIOD_W1, 1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_wh = (ga.GetHighValue(0) - iClose(ga_p, PERIOD_W1, 1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_wh = (gn.GetHighValue(0) - iClose(gn_p, PERIOD_W1, 1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_wh = (gc.GetHighValue(0) - iClose(gc_p, PERIOD_W1, 1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_wh = (gf.GetHighValue(0) - iClose(gf_p, PERIOD_W1, 1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_wh = (gj.GetHighValue(0) - iClose(gj_p, PERIOD_W1, 1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_wh = (au.GetHighValue(0) - iClose(au_p, PERIOD_W1, 1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_wh = (an.GetHighValue(0) - iClose(an_p, PERIOD_W1, 1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_wh = (ac.GetHighValue(0) - iClose(ac_p, PERIOD_W1, 1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_wh = (af.GetHighValue(0) - iClose(af_p, PERIOD_W1, 1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_wh = (aj.GetHighValue(0) - iClose(aj_p, PERIOD_W1, 1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_wh = (nu.GetHighValue(0) - iClose(nu_p, PERIOD_W1, 1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_wh = (nc.GetHighValue(0) - iClose(nc_p, PERIOD_W1, 1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_wh = (nf.GetHighValue(0) - iClose(nf_p, PERIOD_W1, 1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_wh = (nj.GetHighValue(0) - iClose(nj_p, PERIOD_W1, 1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_wh = (uc.GetHighValue(0) - iClose(uc_p, PERIOD_W1, 1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_wh = (cf.GetHighValue(0) - iClose(cf_p, PERIOD_W1, 1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_wh = (cj.GetHighValue(0) - iClose(cj_p, PERIOD_W1, 1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_wh = (uf.GetHighValue(0) - iClose(uf_p, PERIOD_W1, 1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_wh = (fj.GetHighValue(0) - iClose(fj_p, PERIOD_W1, 1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_wh = (uj.GetHighValue(0) - iClose(uj_p, PERIOD_W1, 1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//LOWS
	double eu_wl = (iClose(eu_p, PERIOD_W1, 1) - eu.GetLowValue(0)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_wl = (iClose(eg_p, PERIOD_W1, 1) - eg.GetLowValue(0)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_wl = (iClose(ea_p, PERIOD_W1, 1) - ea.GetLowValue(0)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_wl = (iClose(en_p, PERIOD_W1, 1) - en.GetLowValue(0)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_wl = (iClose(ec_p, PERIOD_W1, 1) - ec.GetLowValue(0)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_wl = (iClose(ef_p, PERIOD_W1, 1) - ef.GetLowValue(0)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_wl = (iClose(ej_p, PERIOD_W1, 1) - ej.GetLowValue(0)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_wl = (iClose(gu_p, PERIOD_W1, 1) - gu.GetLowValue(0)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_wl = (iClose(ga_p, PERIOD_W1, 1) - ga.GetLowValue(0)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_wl = (iClose(gn_p, PERIOD_W1, 1) - gn.GetLowValue(0)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_wl = (iClose(gc_p, PERIOD_W1, 1) - gc.GetLowValue(0)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_wl = (iClose(gf_p, PERIOD_W1, 1) - gf.GetLowValue(0)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_wl = (iClose(gj_p, PERIOD_W1, 1) - gj.GetLowValue(0)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_wl = (iClose(au_p, PERIOD_W1, 1) - au.GetLowValue(0)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_wl = (iClose(an_p, PERIOD_W1, 1) - an.GetLowValue(0)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_wl = (iClose(ac_p, PERIOD_W1, 1) - ac.GetLowValue(0)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_wl = (iClose(af_p, PERIOD_W1, 1) - af.GetLowValue(0)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_wl = (iClose(aj_p, PERIOD_W1, 1) - aj.GetLowValue(0)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_wl = (iClose(nu_p, PERIOD_W1, 1) - nu.GetLowValue(0)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_wl = (iClose(nc_p, PERIOD_W1, 1) - nc.GetLowValue(0)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_wl = (iClose(nf_p, PERIOD_W1, 1) - nf.GetLowValue(0)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_wl = (iClose(nj_p, PERIOD_W1, 1) - nj.GetLowValue(0)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_wl = (iClose(uc_p, PERIOD_W1, 1) - uc.GetLowValue(0)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_wl = (iClose(cf_p, PERIOD_W1, 1) - cf.GetLowValue(0)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_wl = (iClose(cj_p, PERIOD_W1, 1) - cj.GetLowValue(0)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_wl = (iClose(uf_p, PERIOD_W1, 1) - uf.GetLowValue(0)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_wl = (iClose(fj_p, PERIOD_W1, 1) - fj.GetLowValue(0)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_wl = (iClose(uj_p, PERIOD_W1, 1) - uj.GetLowValue(0)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//CLOSES
	double eu_wc = (eu.GetCloseValue(0) - iClose(eu_p, PERIOD_W1, 1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_wc = (eg.GetCloseValue(0) - iClose(eg_p, PERIOD_W1, 1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_wc = (ea.GetCloseValue(0) - iClose(ea_p, PERIOD_W1, 1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_wc = (en.GetCloseValue(0) - iClose(en_p, PERIOD_W1, 1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_wc = (ec.GetCloseValue(0) - iClose(ec_p, PERIOD_W1, 1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_wc = (ef.GetCloseValue(0) - iClose(ef_p, PERIOD_W1, 1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_wc = (ej.GetCloseValue(0) - iClose(ej_p, PERIOD_W1, 1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_wc = (gu.GetCloseValue(0) - iClose(gu_p, PERIOD_W1, 1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_wc = (ga.GetCloseValue(0) - iClose(ga_p, PERIOD_W1, 1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_wc = (gn.GetCloseValue(0) - iClose(gn_p, PERIOD_W1, 1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_wc = (gc.GetCloseValue(0) - iClose(gc_p, PERIOD_W1, 1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_wc = (gf.GetCloseValue(0) - iClose(gf_p, PERIOD_W1, 1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_wc = (gj.GetCloseValue(0) - iClose(gj_p, PERIOD_W1, 1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_wc = (au.GetCloseValue(0) - iClose(au_p, PERIOD_W1, 1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_wc = (an.GetCloseValue(0) - iClose(an_p, PERIOD_W1, 1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_wc = (ac.GetCloseValue(0) - iClose(ac_p, PERIOD_W1, 1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_wc = (af.GetCloseValue(0) - iClose(af_p, PERIOD_W1, 1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_wc = (aj.GetCloseValue(0) - iClose(aj_p, PERIOD_W1, 1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_wc = (nu.GetCloseValue(0) - iClose(nu_p, PERIOD_W1, 1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_wc = (nc.GetCloseValue(0) - iClose(nc_p, PERIOD_W1, 1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_wc = (nf.GetCloseValue(0) - iClose(nf_p, PERIOD_W1, 1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_wc = (nj.GetCloseValue(0) - iClose(nj_p, PERIOD_W1, 1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_wc = (uc.GetCloseValue(0) - iClose(uc_p, PERIOD_W1, 1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_wc = (cf.GetCloseValue(0) - iClose(cf_p, PERIOD_W1, 1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_wc = (cj.GetCloseValue(0) - iClose(cj_p, PERIOD_W1, 1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_wc = (uf.GetCloseValue(0) - iClose(uf_p, PERIOD_W1, 1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_wc = (fj.GetCloseValue(0) - iClose(fj_p, PERIOD_W1, 1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_wc = (uj.GetCloseValue(0) - iClose(uj_p, PERIOD_W1, 1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//WEEK
	//HIGHS
	double eu_mh = (eu.GetHighValue(0) - iClose(eu_p, PERIOD_MN1, 1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_mh = (eg.GetHighValue(0) - iClose(eg_p, PERIOD_MN1, 1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_mh = (ea.GetHighValue(0) - iClose(ea_p, PERIOD_MN1, 1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_mh = (en.GetHighValue(0) - iClose(en_p, PERIOD_MN1, 1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_mh = (ec.GetHighValue(0) - iClose(ec_p, PERIOD_MN1, 1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_mh = (ef.GetHighValue(0) - iClose(ef_p, PERIOD_MN1, 1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_mh = (ej.GetHighValue(0) - iClose(ej_p, PERIOD_MN1, 1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_mh = (gu.GetHighValue(0) - iClose(gu_p, PERIOD_MN1, 1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_mh = (ga.GetHighValue(0) - iClose(ga_p, PERIOD_MN1, 1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_mh = (gn.GetHighValue(0) - iClose(gn_p, PERIOD_MN1, 1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_mh = (gc.GetHighValue(0) - iClose(gc_p, PERIOD_MN1, 1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_mh = (gf.GetHighValue(0) - iClose(gf_p, PERIOD_MN1, 1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_mh = (gj.GetHighValue(0) - iClose(gj_p, PERIOD_MN1, 1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_mh = (au.GetHighValue(0) - iClose(au_p, PERIOD_MN1, 1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_mh = (an.GetHighValue(0) - iClose(an_p, PERIOD_MN1, 1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_mh = (ac.GetHighValue(0) - iClose(ac_p, PERIOD_MN1, 1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_mh = (af.GetHighValue(0) - iClose(af_p, PERIOD_MN1, 1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_mh = (aj.GetHighValue(0) - iClose(aj_p, PERIOD_MN1, 1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_mh = (nu.GetHighValue(0) - iClose(nu_p, PERIOD_MN1, 1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_mh = (nc.GetHighValue(0) - iClose(nc_p, PERIOD_MN1, 1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_mh = (nf.GetHighValue(0) - iClose(nf_p, PERIOD_MN1, 1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_mh = (nj.GetHighValue(0) - iClose(nj_p, PERIOD_MN1, 1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_mh = (uc.GetHighValue(0) - iClose(uc_p, PERIOD_MN1, 1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_mh = (cf.GetHighValue(0) - iClose(cf_p, PERIOD_MN1, 1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_mh = (cj.GetHighValue(0) - iClose(cj_p, PERIOD_MN1, 1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_mh = (uf.GetHighValue(0) - iClose(uf_p, PERIOD_MN1, 1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_mh = (fj.GetHighValue(0) - iClose(fj_p, PERIOD_MN1, 1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_mh = (uj.GetHighValue(0) - iClose(uj_p, PERIOD_MN1, 1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//LOWS
	double eu_ml = (iClose(eu_p, PERIOD_MN1, 1) - eu.GetLowValue(0)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_ml = (iClose(eg_p, PERIOD_MN1, 1) - eg.GetLowValue(0)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_ml = (iClose(ea_p, PERIOD_MN1, 1) - ea.GetLowValue(0)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_ml = (iClose(en_p, PERIOD_MN1, 1) - en.GetLowValue(0)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_ml = (iClose(ec_p, PERIOD_MN1, 1) - ec.GetLowValue(0)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_ml = (iClose(ef_p, PERIOD_MN1, 1) - ef.GetLowValue(0)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_ml = (iClose(ej_p, PERIOD_MN1, 1) - ej.GetLowValue(0)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_ml = (iClose(gu_p, PERIOD_MN1, 1) - gu.GetLowValue(0)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_ml = (iClose(ga_p, PERIOD_MN1, 1) - ga.GetLowValue(0)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_ml = (iClose(gn_p, PERIOD_MN1, 1) - gn.GetLowValue(0)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_ml = (iClose(gc_p, PERIOD_MN1, 1) - gc.GetLowValue(0)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_ml = (iClose(gf_p, PERIOD_MN1, 1) - gf.GetLowValue(0)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_ml = (iClose(gj_p, PERIOD_MN1, 1) - gj.GetLowValue(0)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_ml = (iClose(au_p, PERIOD_MN1, 1) - au.GetLowValue(0)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_ml = (iClose(an_p, PERIOD_MN1, 1) - an.GetLowValue(0)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_ml = (iClose(ac_p, PERIOD_MN1, 1) - ac.GetLowValue(0)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_ml = (iClose(af_p, PERIOD_MN1, 1) - af.GetLowValue(0)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_ml = (iClose(aj_p, PERIOD_MN1, 1) - aj.GetLowValue(0)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_ml = (iClose(nu_p, PERIOD_MN1, 1) - nu.GetLowValue(0)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_ml = (iClose(nc_p, PERIOD_MN1, 1) - nc.GetLowValue(0)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_ml = (iClose(nf_p, PERIOD_MN1, 1) - nf.GetLowValue(0)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_ml = (iClose(nj_p, PERIOD_MN1, 1) - nj.GetLowValue(0)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_ml = (iClose(uc_p, PERIOD_MN1, 1) - uc.GetLowValue(0)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_ml = (iClose(cf_p, PERIOD_MN1, 1) - cf.GetLowValue(0)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_ml = (iClose(cj_p, PERIOD_MN1, 1) - cj.GetLowValue(0)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_ml = (iClose(uf_p, PERIOD_MN1, 1) - uf.GetLowValue(0)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_ml = (iClose(fj_p, PERIOD_MN1, 1) - fj.GetLowValue(0)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_ml = (iClose(uj_p, PERIOD_MN1, 1) - uj.GetLowValue(0)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//CLOSES
	double eu_mc = (eu.GetCloseValue(0) - iClose(eu_p, PERIOD_MN1, 1)) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;
	double eg_mc = (eg.GetCloseValue(0) - iClose(eg_p, PERIOD_MN1, 1)) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
	double ea_mc = (ea.GetCloseValue(0) - iClose(ea_p, PERIOD_MN1, 1)) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
	double en_mc = (en.GetCloseValue(0) - iClose(en_p, PERIOD_MN1, 1)) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
	double ec_mc = (ec.GetCloseValue(0) - iClose(ec_p, PERIOD_MN1, 1)) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
	double ef_mc = (ef.GetCloseValue(0) - iClose(ef_p, PERIOD_MN1, 1)) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
	double ej_mc = (ej.GetCloseValue(0) - iClose(ej_p, PERIOD_MN1, 1)) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
	double gu_mc = (gu.GetCloseValue(0) - iClose(gu_p, PERIOD_MN1, 1)) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
	double ga_mc = (ga.GetCloseValue(0) - iClose(ga_p, PERIOD_MN1, 1)) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
	double gn_mc = (gn.GetCloseValue(0) - iClose(gn_p, PERIOD_MN1, 1)) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
	double gc_mc = (gc.GetCloseValue(0) - iClose(gc_p, PERIOD_MN1, 1)) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
	double gf_mc = (gf.GetCloseValue(0) - iClose(gf_p, PERIOD_MN1, 1)) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
	double gj_mc = (gj.GetCloseValue(0) - iClose(gj_p, PERIOD_MN1, 1)) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
	double au_mc = (au.GetCloseValue(0) - iClose(au_p, PERIOD_MN1, 1)) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
	double an_mc = (an.GetCloseValue(0) - iClose(an_p, PERIOD_MN1, 1)) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
	double ac_mc = (ac.GetCloseValue(0) - iClose(ac_p, PERIOD_MN1, 1)) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
	double af_mc = (af.GetCloseValue(0) - iClose(af_p, PERIOD_MN1, 1)) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
	double aj_mc = (aj.GetCloseValue(0) - iClose(aj_p, PERIOD_MN1, 1)) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
	double nu_mc = (nu.GetCloseValue(0) - iClose(nu_p, PERIOD_MN1, 1)) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
	double nc_mc = (nc.GetCloseValue(0) - iClose(nc_p, PERIOD_MN1, 1)) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
	double nf_mc = (nf.GetCloseValue(0) - iClose(nf_p, PERIOD_MN1, 1)) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
	double nj_mc = (nj.GetCloseValue(0) - iClose(nj_p, PERIOD_MN1, 1)) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
	double uc_mc = (uc.GetCloseValue(0) - iClose(uc_p, PERIOD_MN1, 1)) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
	double cf_mc = (cf.GetCloseValue(0) - iClose(cf_p, PERIOD_MN1, 1)) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
	double cj_mc = (cj.GetCloseValue(0) - iClose(cj_p, PERIOD_MN1, 1)) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
	double uf_mc = (uf.GetCloseValue(0) - iClose(uf_p, PERIOD_MN1, 1)) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
	double fj_mc = (fj.GetCloseValue(0) - iClose(fj_p, PERIOD_MN1, 1)) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
	double uj_mc = (uj.GetCloseValue(0) - iClose(uj_p, PERIOD_MN1, 1)) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

	//DAY
	//BASKET HIGHS
	double ohdayseur = MathAbs(eu_dh + eg_dh + ea_dh + en_dh + ec_dh + ef_dh + ej_dh);
	double ohdaysgbp = MathAbs(eg_dl + gu_dh + ga_dh + gn_dh + gc_dh + gf_dh + gj_dh);
	double ohdaysaud = MathAbs(ea_dl + ga_dl + au_dh + an_dh + ac_dh + af_dh + aj_dh);
	double ohdaysnzd = MathAbs(en_dl + gn_dl + an_dl + nu_dh + nc_dh + nf_dh + nj_dh);
	double ohdayscad = MathAbs(ec_dl + gc_dl + ac_dl + nc_dl + uc_dl + cf_dh + cj_dh);
	double ohdaysusd = MathAbs(eu_dl + gu_dl + au_dl + nu_dl + uc_dh + uf_dh + uj_dh);
	double ohdayschf = MathAbs(ef_dl + gf_dl + af_dl + nf_dl + uf_dl + cf_dl + fj_dh);
	double ohdaysjpy = MathAbs(ej_dl + gj_dl + aj_dl + nj_dl + uj_dl + cj_dl + fj_dl);

	//BASKET LOWS
	double oldayseur = MathAbs(eu_dl + eg_dl + ea_dl + en_dl + ec_dl + ef_dl + ej_dl);
	double oldaysgbp = MathAbs(eg_dh + gu_dl + ga_dl + gn_dl + gc_dl + gf_dl + gj_dl);
	double oldaysaud = MathAbs(ea_dh + ga_dh + au_dl + an_dl + ac_dl + af_dl + aj_dl);
	double oldaysnzd = MathAbs(en_dh + gn_dh + an_dh + nu_dl + nc_dl + nf_dl + nj_dl);
	double oldayscad = MathAbs(ec_dh + gc_dh + ac_dh + nc_dh + uc_dh + cf_dl + cj_dl);
	double oldaysusd = MathAbs(eu_dh + gu_dh + au_dh + nu_dh + uc_dl + uf_dl + uj_dl);
	double oldayschf = MathAbs(ef_dh + gf_dh + af_dh + nf_dh + uf_dh + cf_dh + fj_dl);
	double oldaysjpy = MathAbs(ej_dh + gj_dh + aj_dh + nj_dh + uj_dh + cj_dh + fj_dh);

	//BASKET CLOSES
	double ocdayseur = +eu_dc + eg_dc + ea_dc + en_dc + ec_dc + ef_dc + ej_dc;
	double ocdaysgbp = -eg_dc + gu_dc + ga_dc + gn_dc + gc_dc + gf_dc + gj_dc;
	double ocdaysaud = -ea_dc - ga_dc + au_dc + an_dc + ac_dc + af_dc + aj_dc;
	double ocdaysnzd = -en_dc - gn_dc - an_dc + nu_dc + nc_dc + nf_dc + nj_dc;
	double ocdayscad = -ec_dc - gc_dc - ac_dc - nc_dc - uc_dc + cf_dc + cj_dc;
	double ocdaysusd = -eu_dc - gu_dc - au_dc - nu_dc + uc_dc + uf_dc + uj_dc;
	double ocdayschf = -ef_dc - gf_dc - af_dc - nf_dc - uf_dc - cf_dc + fj_dc;
	double ocdaysjpy = -ej_dc - gj_dc - aj_dc - nj_dc - uj_dc - cj_dc - fj_dc;

	//WEEK
   //BASKET HIGHS
	double ohwayseur = MathAbs(eu_wh + eg_wh + ea_wh + en_wh + ec_wh + ef_wh + ej_wh);
	double ohwaysgbp = MathAbs(eg_wl + gu_wh + ga_wh + gn_wh + gc_wh + gf_wh + gj_wh);
	double ohwaysaud = MathAbs(ea_wl + ga_wl + au_wh + an_wh + ac_wh + af_wh + aj_wh);
	double ohwaysnzd = MathAbs(en_wl + gn_wl + an_wl + nu_wh + nc_wh + nf_wh + nj_wh);
	double ohwayscad = MathAbs(ec_wl + gc_wl + ac_wl + nc_wl + uc_wl + cf_wh + cj_wh);
	double ohwaysusd = MathAbs(eu_wl + gu_wl + au_wl + nu_wl + uc_wh + uf_wh + uj_wh);
	double ohwayschf = MathAbs(ef_wl + gf_wl + af_wl + nf_wl + uf_wl + cf_wl + fj_wh);
	double ohwaysjpy = MathAbs(ej_wl + gj_wl + aj_wl + nj_wl + uj_wl + cj_wl + fj_wl);

	//BASKET LOWS
	double olwayseur = MathAbs(eu_wl + eg_wl + ea_wl + en_wl + ec_wl + ef_wl + ej_wl);
	double olwaysgbp = MathAbs(eg_wh + gu_wl + ga_wl + gn_wl + gc_wl + gf_wl + gj_wl);
	double olwaysaud = MathAbs(ea_wh + ga_wh + au_wl + an_wl + ac_wl + af_wl + aj_wl);
	double olwaysnzd = MathAbs(en_wh + gn_wh + an_wh + nu_wl + nc_wl + nf_wl + nj_wl);
	double olwayscad = MathAbs(ec_wh + gc_wh + ac_wh + nc_wh + uc_wh + cf_wl + cj_wl);
	double olwaysusd = MathAbs(eu_wh + gu_wh + au_wh + nu_wh + uc_wl + uf_wl + uj_wl);
	double olwayschf = MathAbs(ef_wh + gf_wh + af_wh + nf_wh + uf_wh + cf_wh + fj_wl);
	double olwaysjpy = MathAbs(ej_wh + gj_wh + aj_wh + nj_wh + uj_wh + cj_wh + fj_wh);

	//BASKET CLOSES
	double ocwayseur = +eu_wc + eg_wc + ea_wc + en_wc + ec_wc + ef_wc + ej_wc;
	double ocwaysgbp = -eg_wc + gu_wc + ga_wc + gn_wc + gc_wc + gf_wc + gj_wc;
	double ocwaysaud = -ea_wc - ga_wc + au_wc + an_wc + ac_wc + af_wc + aj_wc;
	double ocwaysnzd = -en_wc - gn_wc - an_wc + nu_wc + nc_wc + nf_wc + nj_wc;
	double ocwayscad = -ec_wc - gc_wc - ac_wc - nc_wc - uc_wc + cf_wc + cj_wc;
	double ocwaysusd = -eu_wc - gu_wc - au_wc - nu_wc + uc_wc + uf_wc + uj_wc;
	double ocwayschf = -ef_wc - gf_wc - af_wc - nf_wc - uf_wc - cf_wc + fj_wc;
	double ocwaysjpy = -ej_wc - gj_wc - aj_wc - nj_wc - uj_wc - cj_wc - fj_wc;

	//MONTH
   //BASKET HIGHS
	double ohmayseur = MathAbs(eu_mh + eg_mh + ea_mh + en_mh + ec_mh + ef_mh + ej_mh);
	double ohmaysgbp = MathAbs(eg_ml + gu_mh + ga_mh + gn_mh + gc_mh + gf_mh + gj_mh);
	double ohmaysaud = MathAbs(ea_ml + ga_ml + au_mh + an_mh + ac_mh + af_mh + aj_mh);
	double ohmaysnzd = MathAbs(en_ml + gn_ml + an_ml + nu_mh + nc_mh + nf_mh + nj_mh);
	double ohmayscad = MathAbs(ec_ml + gc_ml + ac_ml + nc_ml + uc_ml + cf_mh + cj_mh);
	double ohmaysusd = MathAbs(eu_ml + gu_ml + au_ml + nu_ml + uc_mh + uf_mh + uj_mh);
	double ohmayschf = MathAbs(ef_ml + gf_ml + af_ml + nf_ml + uf_ml + cf_ml + fj_mh);
	double ohmaysjpy = MathAbs(ej_ml + gj_ml + aj_ml + nj_ml + uj_ml + cj_ml + fj_ml);

	//BASKET LOWS
	double olmayseur = MathAbs(eu_ml + eg_ml + ea_ml + en_ml + ec_ml + ef_ml + ej_ml);
	double olmaysgbp = MathAbs(eg_mh + gu_ml + ga_ml + gn_ml + gc_ml + gf_ml + gj_ml);
	double olmaysaud = MathAbs(ea_mh + ga_mh + au_ml + an_ml + ac_ml + af_ml + aj_ml);
	double olmaysnzd = MathAbs(en_mh + gn_mh + an_mh + nu_ml + nc_ml + nf_ml + nj_ml);
	double olmayscad = MathAbs(ec_mh + gc_mh + ac_mh + nc_mh + uc_mh + cf_ml + cj_ml);
	double olmaysusd = MathAbs(eu_mh + gu_mh + au_mh + nu_mh + uc_ml + uf_ml + uj_ml);
	double olmayschf = MathAbs(ef_mh + gf_mh + af_mh + nf_mh + uf_mh + cf_mh + fj_ml);
	double olmaysjpy = MathAbs(ej_mh + gj_mh + aj_mh + nj_mh + uj_mh + cj_mh + fj_mh);

	//BASKET CLOSES
	double ocmayseur = +eu_mc + eg_mc + ea_mc + en_mc + ec_mc + ef_mc + ej_mc;
	double ocmaysgbp = -eg_mc + gu_mc + ga_mc + gn_mc + gc_mc + gf_mc + gj_mc;
	double ocmaysaud = -ea_mc - ga_mc + au_mc + an_mc + ac_mc + af_mc + aj_mc;
	double ocmaysnzd = -en_mc - gn_mc - an_mc + nu_mc + nc_mc + nf_mc + nj_mc;
	double ocmayscad = -ec_mc - gc_mc - ac_mc - nc_mc - uc_mc + cf_mc + cj_mc;
	double ocmaysusd = -eu_mc - gu_mc - au_mc - nu_mc + uc_mc + uf_mc + uj_mc;
	double ocmayschf = -ef_mc - gf_mc - af_mc - nf_mc - uf_mc - cf_mc + fj_mc;
	double ocmaysjpy = -ej_mc - gj_mc - aj_mc - nj_mc - uj_mc - cj_mc - fj_mc;

	PercData percent[];

	AddToPrata(percent, +eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen, (ohdayseur + oldayseur) / (pastdata["AVGd"][0][1].ToDbl() + pastdata["AVGd"][0][2].ToDbl()), (ohdayseur + oldayseur) / (pastdata["AVGd"][0][1].ToDbl() + pastdata["AVGd"][0][2].ToDbl() + pastdata["STD"][0][1].ToDbl()), ocdayseur / (pastdata["AVGd"][0][1].ToDbl() + pastdata["AVGd"][0][2].ToDbl()), eur);
	AddToPrata(percent, +gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen, (ohdaysgbp + oldaysgbp) / (pastdata["AVGd"][1][1].ToDbl() + pastdata["AVGd"][1][2].ToDbl()), (ohdaysgbp + oldaysgbp) / (pastdata["AVGd"][1][1].ToDbl() + pastdata["AVGd"][1][2].ToDbl() + pastdata["STD"][1][1].ToDbl()), ocdaysgbp / (pastdata["AVGd"][1][1].ToDbl() + pastdata["AVGd"][1][2].ToDbl()), gbp);
	AddToPrata(percent, +aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen, (ohdaysaud + oldaysaud) / (pastdata["AVGd"][2][1].ToDbl() + pastdata["AVGd"][2][2].ToDbl()), (ohdaysaud + oldaysaud) / (pastdata["AVGd"][2][1].ToDbl() + pastdata["AVGd"][2][2].ToDbl() + pastdata["STD"][2][1].ToDbl()), ocdaysaud / (pastdata["AVGd"][2][1].ToDbl() + pastdata["AVGd"][2][2].ToDbl()), aud);
	AddToPrata(percent, +nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen, (ohdaysnzd + oldaysnzd) / (pastdata["AVGd"][3][1].ToDbl() + pastdata["AVGd"][3][2].ToDbl()), (ohdaysnzd + oldaysnzd) / (pastdata["AVGd"][3][1].ToDbl() + pastdata["AVGd"][3][2].ToDbl() + pastdata["STD"][3][1].ToDbl()), ocdaysnzd / (pastdata["AVGd"][3][1].ToDbl() + pastdata["AVGd"][3][2].ToDbl()), nzd);
	AddToPrata(percent, -ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen, (ohdayscad + oldayscad) / (pastdata["AVGd"][4][1].ToDbl() + pastdata["AVGd"][4][2].ToDbl()), (ohdayscad + oldayscad) / (pastdata["AVGd"][4][1].ToDbl() + pastdata["AVGd"][4][2].ToDbl() + pastdata["STD"][4][1].ToDbl()), ocdayscad / (pastdata["AVGd"][4][1].ToDbl() + pastdata["AVGd"][4][2].ToDbl()), cad);
	AddToPrata(percent, -eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen, (ohdaysusd + oldaysusd) / (pastdata["AVGd"][5][1].ToDbl() + pastdata["AVGd"][5][2].ToDbl()), (ohdaysusd + oldaysusd) / (pastdata["AVGd"][5][1].ToDbl() + pastdata["AVGd"][5][2].ToDbl() + pastdata["STD"][5][1].ToDbl()), ocdaysusd / (pastdata["AVGd"][5][1].ToDbl() + pastdata["AVGd"][5][2].ToDbl()), usd);
	AddToPrata(percent, -uffopen - effopen - gffopen - affopen - nffopen - cffopen + fjfopen, (ohdayschf + oldayschf) / (pastdata["AVGd"][6][1].ToDbl() + pastdata["AVGd"][6][2].ToDbl()), (ohdayschf + oldayschf) / (pastdata["AVGd"][6][1].ToDbl() + pastdata["AVGd"][6][2].ToDbl() + pastdata["STD"][6][1].ToDbl()), ocdayschf / (pastdata["AVGd"][6][1].ToDbl() + pastdata["AVGd"][6][2].ToDbl()), chf);
	AddToPrata(percent, -ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen - ujfopen, (ohdaysjpy + oldaysjpy) / (pastdata["AVGd"][7][1].ToDbl() + pastdata["AVGd"][7][2].ToDbl()), (ohdaysjpy + oldaysjpy) / (pastdata["AVGd"][7][1].ToDbl() + pastdata["AVGd"][7][2].ToDbl() + pastdata["STD"][7][1].ToDbl()), ocdaysjpy / (pastdata["AVGd"][7][1].ToDbl() + pastdata["AVGd"][7][2].ToDbl()), jpy);

	ArraySortStruct(percent, dailyscore);

	for (int x = 7; x >= 0; x--)
	{
		percdata["Day"][7 - x].Add(percent[x].pair);
		percdata["Day"][7 - x].Add(percent[x].dpercn * 100, 1);
		percdata["Day"][7 - x].Add(percent[x].dpercs * 100, 1);
		percdata["Day"][7 - x].Add(percent[x].dperca * 100, 1);
	}

	//DAYstats
	percdata["DayAll"].Add((ohdayseur + ohdaysgbp + ohdaysaud + ohdaysnzd + ohdayscad + ohdaysusd + ohdayschf + ohdaysjpy + oldayseur + oldaysgbp + oldaysaud + oldaysnzd + oldayscad + oldaysusd + oldayschf + oldaysjpy) / (pastdata["DayAVG"][0].ToDbl()) * 100, 1);
	percdata["DayAllS"].Add((ohdayseur + ohdaysgbp + ohdaysaud + ohdaysnzd + ohdayscad + ohdaysusd + ohdayschf + ohdaysjpy + oldayseur + oldaysgbp + oldaysaud + oldaysnzd + oldayscad + oldaysusd + oldayschf + oldaysjpy) / (pastdata["DayAVG"][0].ToDbl() + pastdata["STD"][8][1].ToDbl()) * 100, 1);
	percdata["DayAllA"].Add((MathAbs(ocdayseur) + MathAbs(ocdaysgbp) + MathAbs(ocdaysaud) + MathAbs(ocdaysnzd) + MathAbs(ocdayscad) + MathAbs(ocdaysusd) + MathAbs(ocdayschf) + MathAbs(ocdaysjpy)) / (pastdata["DayAVG"][0].ToDbl()) * 100, 1);
	//percdata["DayAllA"].Add((MathAbs(ocdayseur) + MathAbs(ocdaysgbp) + MathAbs(ocdaysaud) + MathAbs(ocdaysnzd) + MathAbs(ocdayscad) + MathAbs(ocdaysusd) + MathAbs(ocdayschf) + MathAbs(ocdaysjpy)) / (pastdata["DayAVG"][0].ToDbl() + pastdata["STD"][8][1].ToDbl()) * 100, 1);
	//WEEKstats
	percdata["WeekAll"].Add((ohwayseur + ohwaysgbp + ohwaysaud + ohwaysnzd + ohwayscad + ohwaysusd + ohwayschf + ohwaysjpy + olwayseur + olwaysgbp + olwaysaud + olwaysnzd + olwayscad + olwaysusd + olwayschf + olwaysjpy) / (pastdata["WeekAVG"][0].ToDbl()) * 100, 1);
	percdata["WeekAllS"].Add((ohwayseur + ohwaysgbp + ohwaysaud + ohwaysnzd + ohwayscad + ohwaysusd + ohwayschf + ohwaysjpy + olwayseur + olwaysgbp + olwaysaud + olwaysnzd + olwayscad + olwaysusd + olwayschf + olwaysjpy) / (pastdata["WeekAVG"][0].ToDbl() + pastdata["STD"][9][1].ToDbl()) * 100, 1);
	percdata["WeekAllA"].Add((MathAbs(ocwayseur) + MathAbs(ocwaysgbp) + MathAbs(ocwaysaud) + MathAbs(ocwaysnzd) + MathAbs(ocwayscad) + MathAbs(ocwaysusd) + MathAbs(ocwayschf) + MathAbs(ocwaysjpy)) / (pastdata["WeekAVG"][0].ToDbl()) * 100, 1);
	//percdata["WeekAllA"].Add((MathAbs(ocwayseur) + MathAbs(ocwaysgbp) + MathAbs(ocwaysaud) + MathAbs(ocwaysnzd) + MathAbs(ocwayscad) + MathAbs(ocwaysusd) + MathAbs(ocwayschf) + MathAbs(ocwaysjpy)) / (pastdata["WeekAVG"][0].ToDbl() + pastdata["STD"][9][1].ToDbl()) * 100, 1);
	//WEEKstats
	percdata["MonthAll"].Add((ohmayseur + ohmaysgbp + ohmaysaud + ohmaysnzd + ohmayscad + ohmaysusd + ohmayschf + ohmaysjpy + olmayseur + olmaysgbp + olmaysaud + olmaysnzd + olmayscad + olmaysusd + olmayschf + olmaysjpy) / (pastdata["MonthAVG"][0].ToDbl()) * 100, 1);
	percdata["MonthAllS"].Add((ohmayseur + ohmaysgbp + ohmaysaud + ohmaysnzd + ohmayscad + ohmaysusd + ohmayschf + ohmaysjpy + olmayseur + olmaysgbp + olmaysaud + olmaysnzd + olmayscad + olmaysusd + olmayschf + olmaysjpy) / (pastdata["MonthAVG"][0].ToDbl() + pastdata["STD"][10][1].ToDbl()) * 100, 1);
	percdata["MonthAllA"].Add((MathAbs(ocmayseur) + MathAbs(ocmaysgbp) + MathAbs(ocmaysaud) + MathAbs(ocmaysnzd) + MathAbs(ocmayscad) + MathAbs(ocmaysusd) + MathAbs(ocmayschf) + MathAbs(ocmaysjpy)) / (pastdata["MonthAVG"][0].ToDbl()) * 100, 1);
	//percdata["MonthAllA"].Add((MathAbs(ocmayseur) + MathAbs(ocmaysgbp) + MathAbs(ocmaysaud) + MathAbs(ocmaysnzd) + MathAbs(ocmayscad) + MathAbs(ocmaysusd) + MathAbs(ocmayschf) + MathAbs(ocmaysjpy)) / (pastdata["MonthAVG"][0].ToDbl() + pastdata["STD"][10][1].ToDbl()) * 100, 1);

	percdata["CurStateHL"] = (ohdayseur + ohdaysgbp + ohdaysaud + ohdaysnzd + ohdayscad + ohdaysusd + ohdayschf + ohdaysjpy + oldayseur + oldaysgbp + oldaysaud + oldaysnzd + oldayscad + oldaysusd + oldayschf + oldaysjpy);
	percdata["CurStateC"] = MathAbs(ocdayseur) + MathAbs(ocdaysgbp) + MathAbs(ocdaysaud) + MathAbs(ocdaysnzd) + MathAbs(ocdayscad) + MathAbs(ocdaysusd) + MathAbs(ocdayschf) + MathAbs(ocdaysjpy);
	percdata["CurStateRat"] = (percdata["CurStateHL"].ToDbl() / percdata["CurStateC"].ToDbl());

	ArrayFree(percent);
	ArrayFree(basketsMain);

	string moff = basketdata["Day"][0].ToStr();
	string meff = basketdata["Day"][7].ToStr();

	double sumx = 0, sumy = 0;

	// Define a struct to hold "fopen" and its associated symbols
	struct PairData {
		double fopen;
		string symbol1; // Primary currency
		string symbol2; // Secondary currency
	};

	// Declare a dynamic array of PairData
	PairData pairs[];

	// Populate the array dynamically
	ArrayResize(pairs, 28);

	// Explicitly assign values to struct fields
	pairs[0].fopen = eufopen; pairs[0].symbol1 = "EUR"; pairs[0].symbol2 = "USD";
	pairs[1].fopen = egfopen; pairs[1].symbol1 = "EUR"; pairs[1].symbol2 = "GBP";
	pairs[2].fopen = eafopen; pairs[2].symbol1 = "EUR"; pairs[2].symbol2 = "AUD";
	pairs[3].fopen = enfopen; pairs[3].symbol1 = "EUR"; pairs[3].symbol2 = "NZD";
	pairs[4].fopen = ecfopen; pairs[4].symbol1 = "EUR"; pairs[4].symbol2 = "CAD";
	pairs[5].fopen = effopen; pairs[5].symbol1 = "EUR"; pairs[5].symbol2 = "CHF";
	pairs[6].fopen = ejfopen; pairs[6].symbol1 = "EUR"; pairs[6].symbol2 = "JPY";
	pairs[7].fopen = gufopen; pairs[7].symbol1 = "GBP"; pairs[7].symbol2 = "USD";
	pairs[8].fopen = gafopen; pairs[8].symbol1 = "GBP"; pairs[8].symbol2 = "AUD";
	pairs[9].fopen = gnfopen; pairs[9].symbol1 = "GBP"; pairs[9].symbol2 = "NZD";
	pairs[10].fopen = gcfopen; pairs[10].symbol1 = "GBP"; pairs[10].symbol2 = "CAD";
	pairs[11].fopen = gffopen; pairs[11].symbol1 = "GBP"; pairs[11].symbol2 = "CHF";
	pairs[12].fopen = gjfopen; pairs[12].symbol1 = "GBP"; pairs[12].symbol2 = "JPY";
	pairs[13].fopen = aufopen; pairs[13].symbol1 = "AUD"; pairs[13].symbol2 = "USD";
	pairs[14].fopen = anfopen; pairs[14].symbol1 = "AUD"; pairs[14].symbol2 = "NZD";
	pairs[15].fopen = acfopen; pairs[15].symbol1 = "AUD"; pairs[15].symbol2 = "CAD";
	pairs[16].fopen = affopen; pairs[16].symbol1 = "AUD"; pairs[16].symbol2 = "CHF";
	pairs[17].fopen = ajfopen; pairs[17].symbol1 = "AUD"; pairs[17].symbol2 = "JPY";
	pairs[18].fopen = nufopen; pairs[18].symbol1 = "NZD"; pairs[18].symbol2 = "USD";
	pairs[19].fopen = ncfopen; pairs[19].symbol1 = "NZD"; pairs[19].symbol2 = "CAD";
	pairs[20].fopen = nffopen; pairs[20].symbol1 = "NZD"; pairs[20].symbol2 = "CHF";
	pairs[21].fopen = njfopen; pairs[21].symbol1 = "NZD"; pairs[21].symbol2 = "JPY";
	pairs[22].fopen = ucfopen; pairs[22].symbol1 = "USD"; pairs[22].symbol2 = "CAD";
	pairs[23].fopen = cffopen; pairs[23].symbol1 = "CAD"; pairs[23].symbol2 = "CHF";
	pairs[24].fopen = cjfopen; pairs[24].symbol1 = "CAD"; pairs[24].symbol2 = "JPY";
	pairs[25].fopen = uffopen; pairs[25].symbol1 = "USD"; pairs[25].symbol2 = "CHF";
	pairs[26].fopen = fjfopen; pairs[26].symbol1 = "CHF"; pairs[26].symbol2 = "JPY";
	pairs[27].fopen = ujfopen; pairs[27].symbol1 = "USD"; pairs[27].symbol2 = "JPY";

	// Loop through pairs and calculate sumx and sumy
	for (int i = 0; i < ArraySize(pairs); i++)
	{
		double fopenValue = MathAbs(pairs[i].fopen);
		string symbol1 = pairs[i].symbol1;
		string symbol2 = pairs[i].symbol2;

		if (fopenValue >= 250)
		{
			// Calculate sumy
			if (moff != symbol1 && meff != symbol1 && moff != symbol2 && meff != symbol2)
				sumy += fopenValue;

			// Calculate sumx
			if (moff == symbol1 || meff == symbol1 || moff == symbol2 || meff == symbol2)
				sumx += fopenValue;
		}
	}

	ArrayFree(pairs);

	// Add to mainsort based on calculated values
	if (sumx != 0)
		mainsort["d"].Add(sumy / sumx, 2);
	else
		mainsort["d"].Add(0);
}

void baskets()
{
	{
		string obname = Name + " bnb ";
		LabelMake(obname, 0, f + 10, 740, "Baskets D", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " bnb " + IntegerToString(x);
			LabelMake(obname, 0, f + 10, 755 + x * 15, basketdata["Day"][x].ToStr() + "   " + DoubleToString(basketdata["Day"][x].ToDbl(), 0), 10, clrWhite);
			if (StringFind(basketdata["Day"][x].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(basketdata["Day"][x].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(basketdata["Day"][x].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(basketdata["Day"][x].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(basketdata["Day"][x].ToStr(), "CA", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(basketdata["Day"][x].ToStr(), "CH", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(basketdata["Day"][x].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (StringFind(basketdata["Day"][x].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		}

		obname = Name + " bnbTlw ";
		LabelMake(obname, 0, f + 10, 890, "WK :", 7, clrWhite);
		obname = Name + " bnbTmw ";
		LabelMake(obname, 0, f + 40, 890, percdata["WeekAll"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbTsw ";
		LabelMake(obname, 0, f + 70, 890, percdata["WeekAllS"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbTaw ";
		LabelMake(obname, 0, f + 100, 890, percdata["WeekAllA"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbTlm ";
		LabelMake(obname, 0, f + 125, 890, "/ MN :", 7, clrWhite);
		obname = Name + " bnbTmm ";
		LabelMake(obname, 0, f + 160, 890, percdata["MonthAll"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbTsm ";
		LabelMake(obname, 0, f + 190, 890, percdata["MonthAllS"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbTam ";
		LabelMake(obname, 0, f + 220, 890, percdata["MonthAllA"][0].ToStr(), 7, clrWhite);
		obname = Name + " bnbT ";
		LabelMake(obname, 0, f + 20, 875, "TOT M/S/A:", 10, clrWhite);
		obname = Name + " bnbTm ";
		LabelMake(obname, 0, f + 95, 875, percdata["DayAll"][0].ToStr(), 10, clrWhite);
		if (percdata["DayAll"][0].ToDbl() >= 75 && percdata["DayAll"][0].ToDbl() < 100) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (percdata["DayAll"][0].ToDbl() >= 100) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + " bnbTs ";
		LabelMake(obname, 0, f + 145, 875, percdata["DayAllS"][0].ToStr(), 10, clrWhite);
		if (percdata["DayAllS"][0].ToDbl() >= 75 && percdata["DayAllS"][0].ToDbl() < 100) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (percdata["DayAllS"][0].ToDbl() >= 100) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		obname = Name + " bnbTa ";
		LabelMake(obname, 0, f + 195, 875, percdata["DayAllA"][0].ToStr(), 10, clrWhite);
		if (percdata["DayAllA"][0].ToDbl() >= 50 && percdata["DayAllA"][0].ToDbl() < 80) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (percdata["DayAllA"][0].ToDbl() >= 80) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

		obname = Name + " brb ";
		LabelMake(obname, 0, f + 95, 740, "Mv%", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " brb " + IntegerToString(x);
			LabelMake(obname, 0, f + 95, 755 + x * 15, percdata["Day"][x][1].ToStr(), 10, clrWhite);
			if (StringFind(percdata["Day"][x][0].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(percdata["Day"][x][0].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(percdata["Day"][x][0].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(percdata["Day"][x][0].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(percdata["Day"][x][0].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CA", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CH", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(percdata["Day"][x][0].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (percdata["Day"][x][1].ToDbl() > 100) ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		}
		obname = Name + " bsb ";
		LabelMake(obname, 0, f + 145, 740, "Sd%", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " bsb " + IntegerToString(x);
			LabelMake(obname, 0, f + 145, 755 + x * 15, percdata["Day"][x][2].ToStr(), 10, clrWhite);
			if (StringFind(percdata["Day"][x][0].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(percdata["Day"][x][0].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(percdata["Day"][x][0].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(percdata["Day"][x][0].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(percdata["Day"][x][0].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CA", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CH", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(percdata["Day"][x][0].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (percdata["Day"][x][2].ToDbl() > 75) ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		}
		obname = Name + " bqb ";
		LabelMake(obname, 0, f + 195, 740, "Act%", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " bqb " + IntegerToString(x);
			LabelMake(obname, 0, f + 195, 755 + x * 15, percdata["Day"][x][3].ToStr(), 10, clrWhite);
			if (StringFind(percdata["Day"][x][0].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(percdata["Day"][x][0].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(percdata["Day"][x][0].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(percdata["Day"][x][0].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(percdata["Day"][x][0].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CA", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(percdata["Day"][x][0].ToStr(), "CH", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(percdata["Day"][x][0].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (percdata["Day"][x][3].ToDbl() > 50) ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		}

		obname = Name + " btb ";
		LabelMake(obname, 0, f + 245, 740, "S", 10, clrWhite);
		for (int x = 0; x <= 6; x++)
		{
			obname = Name + " btb " + IntegerToString(x);
			LabelMake(obname, 0, f + 245, 755 + x * 15, basketdata["200"][x].ToStr(), 10, clrWhite);
			if (StringFind(basketdata["200"][x].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(basketdata["200"][x].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(basketdata["200"][x].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(basketdata["200"][x].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(basketdata["200"][x].ToStr(), "C", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(basketdata["200"][x].ToStr(), "F", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(basketdata["200"][x].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
		}
		obname = Name + " bnb1 ";
		LabelMake(obname, 0, f + 265, 740, "5D", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " bnb1 " + IntegerToString(x);
			LabelMake(obname, 0, f + 265, 755 + x * 15, basketdata["5D"][x].ToStr(), 10, clrWhite);
			if (StringFind(basketdata["5D"][x].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(basketdata["5D"][x].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(basketdata["5D"][x].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(basketdata["5D"][x].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(basketdata["5D"][x].ToStr(), "C", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(basketdata["5D"][x].ToStr(), "F", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(basketdata["5D"][x].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (StringFind(basketdata["5D"][x].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		}
		obname = Name + " bnb2 ";
		LabelMake(obname, 0, f + 285, 740, "M", 10, clrWhite);
		for (int x = 0; x <= 7; x++)
		{
			obname = Name + " bnb2 " + IntegerToString(x);
			LabelMake(obname, 0, f + 285, 755 + x * 15, basketdata["Month"][x].ToStr(), 10, clrWhite);
			if (StringFind(basketdata["Month"][x].ToStr(), "E", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (StringFind(basketdata["Month"][x].ToStr(), "G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(basketdata["Month"][x].ToStr(), "A", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (StringFind(basketdata["Month"][x].ToStr(), "N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
			if (StringFind(basketdata["Month"][x].ToStr(), "C", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
			if (StringFind(basketdata["Month"][x].ToStr(), "F", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (StringFind(basketdata["Month"][x].ToStr(), "J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
			if (StringFind(basketdata["Month"][x].ToStr(), "U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		}

		maxbasket = MathMax(MathAbs(basketdata["Day"][0].ToDbl()), MathAbs(basketdata["Day"][7].ToDbl()));

		obname = Name + "MXUS";
		LabelMake(obname, 0, f + 265, 885, "", 10, clrWhite);

		double dxyn = 0, dxyo = 0, mx = 0;
		double bid1 = MarketInfo("USDSEK", MODE_BID);
		if (GetLastError() == 4106)
		{ // unknown symbol
			ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "No calc");
		}
		else
		{
			dxyn = NormalizeDouble(50.14348112 * MathPow(MarketInfo(eu_p, MODE_BID), -0.576) * MathPow(MarketInfo(uj_p, MODE_BID), 0.136) * MathPow(MarketInfo(gu_p, MODE_BID), -0.119) * MathPow(MarketInfo(uc_p, MODE_BID), 0.091) * MathPow(MarketInfo("USDSEK", MODE_BID), 0.042) * MathPow(MarketInfo(uf_p, MODE_BID), 0.036), 3);
			dxyo = NormalizeDouble(50.14348112 * MathPow(iClose(eu_p, PERIOD_D1, 1), -0.576) * MathPow(iClose(uj_p, PERIOD_D1, 1), 0.136) * MathPow(iClose(gu_p, PERIOD_D1, 1), -0.119) * MathPow(iClose(uc_p, PERIOD_D1, 1), 0.091) * MathPow(MarketInfo("USDSEK", MODE_BID), 0.042) * MathPow(iClose(uf_p, PERIOD_D1, 1), 0.036), 3);
		}
		double bid2 = MarketInfo("USDMXN", MODE_BID);
		if (GetLastError() == 4106)
		{ // unknown symbol
			ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "No calc");
		}
		else
		{
			mx = ((1 / MarketInfo("USDMXN", MODE_BID)) * MarketInfo("USDJPY", MODE_BID)) - ((1 / iClose("USDMXN", PERIOD_D1, 1)) * iClose("USDJPY", PERIOD_D1, 1));
		}

		if ((dxyn - dxyo) > 0 && mx > 0) ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "M+ U+");
		if ((dxyn - dxyo) > 0 && mx < 0) ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "M- U+");
		if ((dxyn - dxyo) < 0 && mx > 0) ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "M+ U-");
		if ((dxyn - dxyo) < 0 && mx < 0) ObjectSetString(0, Name + "MXUS", OBJPROP_TEXT, "M- U-");

		ObjectSetString(0, Name + "MXUS", OBJPROP_TOOLTIP, "DXY O/C: " + DoubleToString(dxyn, 3) + "/" + DoubleToString(dxyo, 3) + " / MJ O/C: " + DoubleToString((1 / MarketInfo("USDMXN", MODE_BID)) * MarketInfo("USDJPY", MODE_BID), 4) + "/" + DoubleToString(((1 / iClose("USDMXN", PERIOD_D1, 1)) * iClose("USDJPY", PERIOD_D1, 1)), 4));
	}
}

void sort()
{
	int y = 27;
	string obname;
	double totbuy = 0, totsel = 0;
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " sort " + IntegerToString(x);
		LabelMake(obname, 0, f + 10, 30 + x * 20, mainsort["a"][x][0].ToStr() + "  /  $" + mainsort["a"][x][1].ToStr(), 8, clrDodgerBlue);
		ObjectSetString(0, obname, OBJPROP_TOOLTIP, DoubleToString(MarketInfo(mainsort["a"][x][0].ToStr(), MODE_BID), (int)MarketInfo(mainsort["a"][x][0].ToStr(), MODE_DIGITS)));
		if (x < 7) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0x8d,0x63,0xff');
		if (x >= 7 && x <= 20) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0x00,0x78,0x00' + x * 10);
		if (x > 20) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xff,0x62,0x3e');
		if (mainsort["a"][x][1].ToDbl() >= 250) ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (mainsort["a"][x][1].ToDbl() <= -250) ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (mainsort["a"][x][1].ToDbl() >= 0) totbuy += mainsort["a"][x][1].ToDbl();
		if (mainsort["a"][x][1].ToDbl() < 0) totsel += mainsort["a"][x][1].ToDbl();
	}

	double poscount = 0;
	double negcount = 0;
	for (int x = 0; x <= 27; x++)
	{
		if ((StringFind(mainsort["a"][x][0].ToStr(), "EU", 0) == 0 || StringFind(mainsort["a"][x][0].ToStr(), "GB", 0) == 0 || StringFind(mainsort["a"][x][0].ToStr(), "AU", 0) == 0 || StringFind(mainsort["a"][x][0].ToStr(), "NZ", 0) == 0) && StringFind(mainsort["a"][x][0].ToStr(), "US", 0) != 0 && StringFind(mainsort["a"][x][0].ToStr(), "CA", 0) != 0 && StringFind(mainsort["a"][x][0].ToStr(), "CH", 0) != 0 && mainsort["a"][x][0].ToStr() != "EURGBP" && mainsort["a"][x][0].ToStr() != "EURAUD" && mainsort["a"][x][0].ToStr() != "EURNZD" && mainsort["a"][x][0].ToStr() != "GBPAUD" && mainsort["a"][x][0].ToStr() != "GBPNZD" && mainsort["a"][x][0].ToStr() != "AUDNZD" && mainsort["a"][x][1].ToDbl() >= 0)
		{
			poscount += mainsort["a"][x][1].ToDbl();
		}
		if ((StringFind(mainsort["a"][x][0].ToStr(), "US", 0) == 3 || StringFind(mainsort["a"][x][0].ToStr(), "CA", 0) == 3 || StringFind(mainsort["a"][x][0].ToStr(), "CH", 0) == 3 || StringFind(mainsort["a"][x][0].ToStr(), "JP", 0) == 3) && StringFind(mainsort["a"][x][0].ToStr(), "GB", 0) != 3 && StringFind(mainsort["a"][x][0].ToStr(), "AU", 0) != 3 && StringFind(mainsort["a"][x][0].ToStr(), "NZ", 0) != 3 && mainsort["a"][x][0].ToStr() != "USDCAD" && mainsort["a"][x][0].ToStr() != "USDCHF" && mainsort["a"][x][0].ToStr() != "USDJPY" && mainsort["a"][x][0].ToStr() != "CADCHF" && mainsort["a"][x][0].ToStr() != "CADJPY" && mainsort["a"][x][0].ToStr() != "CHFJPY" && mainsort["a"][x][1].ToDbl() <= 0)
		{
			negcount += mainsort["a"][x][1].ToDbl();
		}
	}

	int count = 0;
	double cunt = 0;
	for (int x = 0; x <= 27; x++)
	{
		if (mainsort["b"][x][1].ToInt() != 2) count++;
		cunt += MathAbs(mainsort["b"][x][0].ToDbl());
	}

	//Print (count + " " + cunt);

	//R4 - breakout
	double highplus = 0;
	if (MarketInfo(eg_p, MODE_BID) > iHigh(eg_p, PERIOD_D1, 1)) highplus += (MarketInfo(eg_p, MODE_BID) - iHigh(eg_p, PERIOD_D1, 1)) / iHigh(eg_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) > iHigh(ea_p, PERIOD_D1, 1)) highplus += (MarketInfo(ea_p, MODE_BID) - iHigh(ea_p, PERIOD_D1, 1)) / iHigh(ea_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) > iHigh(en_p, PERIOD_D1, 1)) highplus += (MarketInfo(en_p, MODE_BID) - iHigh(en_p, PERIOD_D1, 1)) / iHigh(en_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) > iHigh(eu_p, PERIOD_D1, 1)) highplus += (MarketInfo(eu_p, MODE_BID) - iHigh(eu_p, PERIOD_D1, 1)) / iHigh(eu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) > iHigh(ec_p, PERIOD_D1, 1)) highplus += (MarketInfo(ec_p, MODE_BID) - iHigh(ec_p, PERIOD_D1, 1)) / iHigh(ec_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) > iHigh(ef_p, PERIOD_D1, 1)) highplus += (MarketInfo(ef_p, MODE_BID) - iHigh(ef_p, PERIOD_D1, 1)) / iHigh(ef_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) > iHigh(ej_p, PERIOD_D1, 1)) highplus += (MarketInfo(ej_p, MODE_BID) - iHigh(ej_p, PERIOD_D1, 1)) / iHigh(ej_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) > iHigh(ga_p, PERIOD_D1, 1)) highplus += (MarketInfo(ga_p, MODE_BID) - iHigh(ga_p, PERIOD_D1, 1)) / iHigh(ga_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) > iHigh(gn_p, PERIOD_D1, 1)) highplus += (MarketInfo(gn_p, MODE_BID) - iHigh(gn_p, PERIOD_D1, 1)) / iHigh(gn_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) > iHigh(gu_p, PERIOD_D1, 1)) highplus += (MarketInfo(gu_p, MODE_BID) - iHigh(gu_p, PERIOD_D1, 1)) / iHigh(gu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) > iHigh(gc_p, PERIOD_D1, 1)) highplus += (MarketInfo(gc_p, MODE_BID) - iHigh(gc_p, PERIOD_D1, 1)) / iHigh(gc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) > iHigh(gf_p, PERIOD_D1, 1)) highplus += (MarketInfo(gf_p, MODE_BID) - iHigh(gf_p, PERIOD_D1, 1)) / iHigh(gf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) > iHigh(gj_p, PERIOD_D1, 1)) highplus += (MarketInfo(gj_p, MODE_BID) - iHigh(gj_p, PERIOD_D1, 1)) / iHigh(gj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) > iHigh(an_p, PERIOD_D1, 1)) highplus += (MarketInfo(an_p, MODE_BID) - iHigh(an_p, PERIOD_D1, 1)) / iHigh(an_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) > iHigh(au_p, PERIOD_D1, 1)) highplus += (MarketInfo(au_p, MODE_BID) - iHigh(au_p, PERIOD_D1, 1)) / iHigh(au_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) > iHigh(ac_p, PERIOD_D1, 1)) highplus += (MarketInfo(ac_p, MODE_BID) - iHigh(ac_p, PERIOD_D1, 1)) / iHigh(ac_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) > iHigh(af_p, PERIOD_D1, 1)) highplus += (MarketInfo(af_p, MODE_BID) - iHigh(af_p, PERIOD_D1, 1)) / iHigh(af_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) > iHigh(aj_p, PERIOD_D1, 1)) highplus += (MarketInfo(aj_p, MODE_BID) - iHigh(aj_p, PERIOD_D1, 1)) / iHigh(aj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) > iHigh(nu_p, PERIOD_D1, 1)) highplus += (MarketInfo(nu_p, MODE_BID) - iHigh(nu_p, PERIOD_D1, 1)) / iHigh(nu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) > iHigh(nc_p, PERIOD_D1, 1)) highplus += (MarketInfo(nc_p, MODE_BID) - iHigh(nc_p, PERIOD_D1, 1)) / iHigh(nc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) > iHigh(nf_p, PERIOD_D1, 1)) highplus += (MarketInfo(nf_p, MODE_BID) - iHigh(nf_p, PERIOD_D1, 1)) / iHigh(nf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) > iHigh(nj_p, PERIOD_D1, 1)) highplus += (MarketInfo(nj_p, MODE_BID) - iHigh(nj_p, PERIOD_D1, 1)) / iHigh(nj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) > iHigh(uc_p, PERIOD_D1, 1)) highplus += (MarketInfo(uc_p, MODE_BID) - iHigh(uc_p, PERIOD_D1, 1)) / iHigh(uc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) > iHigh(uf_p, PERIOD_D1, 1)) highplus += (MarketInfo(uf_p, MODE_BID) - iHigh(uf_p, PERIOD_D1, 1)) / iHigh(uf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) > iHigh(uj_p, PERIOD_D1, 1)) highplus += (MarketInfo(uj_p, MODE_BID) - iHigh(uj_p, PERIOD_D1, 1)) / iHigh(uj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) > iHigh(cf_p, PERIOD_D1, 1)) highplus += (MarketInfo(cf_p, MODE_BID) - iHigh(cf_p, PERIOD_D1, 1)) / iHigh(cf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) > iHigh(cj_p, PERIOD_D1, 1)) highplus += (MarketInfo(cj_p, MODE_BID) - iHigh(cj_p, PERIOD_D1, 1)) / iHigh(cj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) > iHigh(fj_p, PERIOD_D1, 1)) highplus += (MarketInfo(fj_p, MODE_BID) - iHigh(fj_p, PERIOD_D1, 1)) / iHigh(fj_p, PERIOD_D1, 1) * 100;

	double lowplus = 0;
	if (MarketInfo(eg_p, MODE_BID) < iLow(eg_p, PERIOD_D1, 1)) lowplus += (MarketInfo(eg_p, MODE_BID) - iLow(eg_p, PERIOD_D1, 1)) / iLow(eg_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) < iLow(ea_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ea_p, MODE_BID) - iLow(ea_p, PERIOD_D1, 1)) / iLow(ea_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) < iLow(en_p, PERIOD_D1, 1)) lowplus += (MarketInfo(en_p, MODE_BID) - iLow(en_p, PERIOD_D1, 1)) / iLow(en_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) < iLow(eu_p, PERIOD_D1, 1)) lowplus += (MarketInfo(eu_p, MODE_BID) - iLow(eu_p, PERIOD_D1, 1)) / iLow(eu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) < iLow(ec_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ec_p, MODE_BID) - iLow(ec_p, PERIOD_D1, 1)) / iLow(ec_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) < iLow(ef_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ef_p, MODE_BID) - iLow(ef_p, PERIOD_D1, 1)) / iLow(ef_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) < iLow(ej_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ej_p, MODE_BID) - iLow(ej_p, PERIOD_D1, 1)) / iLow(ej_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) < iLow(ga_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ga_p, MODE_BID) - iLow(ga_p, PERIOD_D1, 1)) / iLow(ga_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) < iLow(gn_p, PERIOD_D1, 1)) lowplus += (MarketInfo(gn_p, MODE_BID) - iLow(gn_p, PERIOD_D1, 1)) / iLow(gn_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) < iLow(gu_p, PERIOD_D1, 1)) lowplus += (MarketInfo(gu_p, MODE_BID) - iLow(gu_p, PERIOD_D1, 1)) / iLow(gu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) < iLow(gc_p, PERIOD_D1, 1)) lowplus += (MarketInfo(gc_p, MODE_BID) - iLow(gc_p, PERIOD_D1, 1)) / iLow(gc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) < iLow(gf_p, PERIOD_D1, 1)) lowplus += (MarketInfo(gf_p, MODE_BID) - iLow(gf_p, PERIOD_D1, 1)) / iLow(gf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) < iLow(gj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(gj_p, MODE_BID) - iLow(gj_p, PERIOD_D1, 1)) / iLow(gj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) < iLow(an_p, PERIOD_D1, 1)) lowplus += (MarketInfo(an_p, MODE_BID) - iLow(an_p, PERIOD_D1, 1)) / iLow(an_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) < iLow(au_p, PERIOD_D1, 1)) lowplus += (MarketInfo(au_p, MODE_BID) - iLow(au_p, PERIOD_D1, 1)) / iLow(au_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) < iLow(ac_p, PERIOD_D1, 1)) lowplus += (MarketInfo(ac_p, MODE_BID) - iLow(ac_p, PERIOD_D1, 1)) / iLow(ac_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) < iLow(af_p, PERIOD_D1, 1)) lowplus += (MarketInfo(af_p, MODE_BID) - iLow(af_p, PERIOD_D1, 1)) / iLow(af_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) < iLow(aj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(aj_p, MODE_BID) - iLow(aj_p, PERIOD_D1, 1)) / iLow(aj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) < iLow(nu_p, PERIOD_D1, 1)) lowplus += (MarketInfo(nu_p, MODE_BID) - iLow(nu_p, PERIOD_D1, 1)) / iLow(nu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) < iLow(nc_p, PERIOD_D1, 1)) lowplus += (MarketInfo(nc_p, MODE_BID) - iLow(nc_p, PERIOD_D1, 1)) / iLow(nc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) < iLow(nf_p, PERIOD_D1, 1)) lowplus += (MarketInfo(nf_p, MODE_BID) - iLow(nf_p, PERIOD_D1, 1)) / iLow(nf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) < iLow(nj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(nj_p, MODE_BID) - iLow(nj_p, PERIOD_D1, 1)) / iLow(nj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) < iLow(uc_p, PERIOD_D1, 1)) lowplus += (MarketInfo(uc_p, MODE_BID) - iLow(uc_p, PERIOD_D1, 1)) / iLow(uc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) < iLow(uf_p, PERIOD_D1, 1)) lowplus += (MarketInfo(uf_p, MODE_BID) - iLow(uf_p, PERIOD_D1, 1)) / iLow(uf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) < iLow(uj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(uj_p, MODE_BID) - iLow(uj_p, PERIOD_D1, 1)) / iLow(uj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) < iLow(cf_p, PERIOD_D1, 1)) lowplus += (MarketInfo(cf_p, MODE_BID) - iLow(cf_p, PERIOD_D1, 1)) / iLow(cf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) < iLow(cj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(cj_p, MODE_BID) - iLow(cj_p, PERIOD_D1, 1)) / iLow(cj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) < iLow(fj_p, PERIOD_D1, 1)) lowplus += (MarketInfo(fj_p, MODE_BID) - iLow(fj_p, PERIOD_D1, 1)) / iLow(fj_p, PERIOD_D1, 1) * 100;

	//RC - breakout continuation
	double highplus1 = 0;
	if (MarketInfo(eg_p, MODE_BID) > iHigh(eg_p, PERIOD_D1, 1) && iClose(eg_p, PERIOD_D1, 1) > iClose(eg_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(eg_p, MODE_BID) - iHigh(eg_p, PERIOD_D1, 1)) / iHigh(eg_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) > iHigh(ea_p, PERIOD_D1, 1) && iClose(ea_p, PERIOD_D1, 1) > iClose(ea_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ea_p, MODE_BID) - iHigh(ea_p, PERIOD_D1, 1)) / iHigh(ea_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) > iHigh(en_p, PERIOD_D1, 1) && iClose(en_p, PERIOD_D1, 1) > iClose(en_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(en_p, MODE_BID) - iHigh(en_p, PERIOD_D1, 1)) / iHigh(en_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) > iHigh(eu_p, PERIOD_D1, 1) && iClose(eu_p, PERIOD_D1, 1) > iClose(eu_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(eu_p, MODE_BID) - iHigh(eu_p, PERIOD_D1, 1)) / iHigh(eu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) > iHigh(ec_p, PERIOD_D1, 1) && iClose(ec_p, PERIOD_D1, 1) > iClose(ec_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ec_p, MODE_BID) - iHigh(ec_p, PERIOD_D1, 1)) / iHigh(ec_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) > iHigh(ef_p, PERIOD_D1, 1) && iClose(ef_p, PERIOD_D1, 1) > iClose(ef_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ef_p, MODE_BID) - iHigh(ef_p, PERIOD_D1, 1)) / iHigh(ef_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) > iHigh(ej_p, PERIOD_D1, 1) && iClose(ej_p, PERIOD_D1, 1) > iClose(ej_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ej_p, MODE_BID) - iHigh(ej_p, PERIOD_D1, 1)) / iHigh(ej_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) > iHigh(ga_p, PERIOD_D1, 1) && iClose(ga_p, PERIOD_D1, 1) > iClose(ga_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ga_p, MODE_BID) - iHigh(ga_p, PERIOD_D1, 1)) / iHigh(ga_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) > iHigh(gn_p, PERIOD_D1, 1) && iClose(gn_p, PERIOD_D1, 1) > iClose(gn_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(gn_p, MODE_BID) - iHigh(gn_p, PERIOD_D1, 1)) / iHigh(gn_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) > iHigh(gu_p, PERIOD_D1, 1) && iClose(gu_p, PERIOD_D1, 1) > iClose(gu_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(gu_p, MODE_BID) - iHigh(gu_p, PERIOD_D1, 1)) / iHigh(gu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) > iHigh(gc_p, PERIOD_D1, 1) && iClose(gc_p, PERIOD_D1, 1) > iClose(gc_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(gc_p, MODE_BID) - iHigh(gc_p, PERIOD_D1, 1)) / iHigh(gc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) > iHigh(gf_p, PERIOD_D1, 1) && iClose(gf_p, PERIOD_D1, 1) > iClose(gf_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(gf_p, MODE_BID) - iHigh(gf_p, PERIOD_D1, 1)) / iHigh(gf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) > iHigh(gj_p, PERIOD_D1, 1) && iClose(gj_p, PERIOD_D1, 1) > iClose(gj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(gj_p, MODE_BID) - iHigh(gj_p, PERIOD_D1, 1)) / iHigh(gj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) > iHigh(an_p, PERIOD_D1, 1) && iClose(an_p, PERIOD_D1, 1) > iClose(an_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(an_p, MODE_BID) - iHigh(an_p, PERIOD_D1, 1)) / iHigh(an_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) > iHigh(au_p, PERIOD_D1, 1) && iClose(au_p, PERIOD_D1, 1) > iClose(au_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(au_p, MODE_BID) - iHigh(au_p, PERIOD_D1, 1)) / iHigh(au_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) > iHigh(ac_p, PERIOD_D1, 1) && iClose(ac_p, PERIOD_D1, 1) > iClose(ac_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(ac_p, MODE_BID) - iHigh(ac_p, PERIOD_D1, 1)) / iHigh(ac_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) > iHigh(af_p, PERIOD_D1, 1) && iClose(af_p, PERIOD_D1, 1) > iClose(af_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(af_p, MODE_BID) - iHigh(af_p, PERIOD_D1, 1)) / iHigh(af_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) > iHigh(aj_p, PERIOD_D1, 1) && iClose(aj_p, PERIOD_D1, 1) > iClose(aj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(aj_p, MODE_BID) - iHigh(aj_p, PERIOD_D1, 1)) / iHigh(aj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) > iHigh(nu_p, PERIOD_D1, 1) && iClose(nu_p, PERIOD_D1, 1) > iClose(nu_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(nu_p, MODE_BID) - iHigh(nu_p, PERIOD_D1, 1)) / iHigh(nu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) > iHigh(nc_p, PERIOD_D1, 1) && iClose(nc_p, PERIOD_D1, 1) > iClose(nc_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(nc_p, MODE_BID) - iHigh(nc_p, PERIOD_D1, 1)) / iHigh(nc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) > iHigh(nf_p, PERIOD_D1, 1) && iClose(nf_p, PERIOD_D1, 1) > iClose(nf_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(nf_p, MODE_BID) - iHigh(nf_p, PERIOD_D1, 1)) / iHigh(nf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) > iHigh(nj_p, PERIOD_D1, 1) && iClose(nj_p, PERIOD_D1, 1) > iClose(nj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(nj_p, MODE_BID) - iHigh(nj_p, PERIOD_D1, 1)) / iHigh(nj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) > iHigh(uc_p, PERIOD_D1, 1) && iClose(uc_p, PERIOD_D1, 1) > iClose(uc_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(uc_p, MODE_BID) - iHigh(uc_p, PERIOD_D1, 1)) / iHigh(uc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) > iHigh(uf_p, PERIOD_D1, 1) && iClose(uf_p, PERIOD_D1, 1) > iClose(uf_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(uf_p, MODE_BID) - iHigh(uf_p, PERIOD_D1, 1)) / iHigh(uf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) > iHigh(uj_p, PERIOD_D1, 1) && iClose(uj_p, PERIOD_D1, 1) > iClose(uj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(uj_p, MODE_BID) - iHigh(uj_p, PERIOD_D1, 1)) / iHigh(uj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) > iHigh(cf_p, PERIOD_D1, 1) && iClose(cf_p, PERIOD_D1, 1) > iClose(cf_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(cf_p, MODE_BID) - iHigh(cf_p, PERIOD_D1, 1)) / iHigh(cf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) > iHigh(cj_p, PERIOD_D1, 1) && iClose(cj_p, PERIOD_D1, 1) > iClose(cj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(cj_p, MODE_BID) - iHigh(cj_p, PERIOD_D1, 1)) / iHigh(cj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) > iHigh(fj_p, PERIOD_D1, 1) && iClose(fj_p, PERIOD_D1, 1) > iClose(fj_p, PERIOD_D1, 2)) highplus1 += (MarketInfo(fj_p, MODE_BID) - iHigh(fj_p, PERIOD_D1, 1)) / iHigh(fj_p, PERIOD_D1, 1) * 100;

	double lowplus1 = 0;
	if (MarketInfo(eg_p, MODE_BID) < iLow(eg_p, PERIOD_D1, 1) && iClose(eg_p, PERIOD_D1, 1) < iClose(eg_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(eg_p, MODE_BID) - iLow(eg_p, PERIOD_D1, 1)) / iLow(eg_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) < iLow(ea_p, PERIOD_D1, 1) && iClose(ea_p, PERIOD_D1, 1) < iClose(ea_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ea_p, MODE_BID) - iLow(ea_p, PERIOD_D1, 1)) / iLow(ea_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) < iLow(en_p, PERIOD_D1, 1) && iClose(en_p, PERIOD_D1, 1) < iClose(en_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(en_p, MODE_BID) - iLow(en_p, PERIOD_D1, 1)) / iLow(en_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) < iLow(eu_p, PERIOD_D1, 1) && iClose(eu_p, PERIOD_D1, 1) < iClose(eu_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(eu_p, MODE_BID) - iLow(eu_p, PERIOD_D1, 1)) / iLow(eu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) < iLow(ec_p, PERIOD_D1, 1) && iClose(ec_p, PERIOD_D1, 1) < iClose(ec_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ec_p, MODE_BID) - iLow(ec_p, PERIOD_D1, 1)) / iLow(ec_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) < iLow(ef_p, PERIOD_D1, 1) && iClose(ef_p, PERIOD_D1, 1) < iClose(ef_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ef_p, MODE_BID) - iLow(ef_p, PERIOD_D1, 1)) / iLow(ef_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) < iLow(ej_p, PERIOD_D1, 1) && iClose(ej_p, PERIOD_D1, 1) < iClose(ej_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ej_p, MODE_BID) - iLow(ej_p, PERIOD_D1, 1)) / iLow(ej_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) < iLow(ga_p, PERIOD_D1, 1) && iClose(ga_p, PERIOD_D1, 1) < iClose(ga_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ga_p, MODE_BID) - iLow(ga_p, PERIOD_D1, 1)) / iLow(ga_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) < iLow(gn_p, PERIOD_D1, 1) && iClose(gn_p, PERIOD_D1, 1) < iClose(gn_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(gn_p, MODE_BID) - iLow(gn_p, PERIOD_D1, 1)) / iLow(gn_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) < iLow(gu_p, PERIOD_D1, 1) && iClose(gu_p, PERIOD_D1, 1) < iClose(gu_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(gu_p, MODE_BID) - iLow(gu_p, PERIOD_D1, 1)) / iLow(gu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) < iLow(gc_p, PERIOD_D1, 1) && iClose(gc_p, PERIOD_D1, 1) < iClose(gc_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(gc_p, MODE_BID) - iLow(gc_p, PERIOD_D1, 1)) / iLow(gc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) < iLow(gf_p, PERIOD_D1, 1) && iClose(gf_p, PERIOD_D1, 1) < iClose(gf_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(gf_p, MODE_BID) - iLow(gf_p, PERIOD_D1, 1)) / iLow(gf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) < iLow(gj_p, PERIOD_D1, 1) && iClose(gj_p, PERIOD_D1, 1) < iClose(gj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(gj_p, MODE_BID) - iLow(gj_p, PERIOD_D1, 1)) / iLow(gj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) < iLow(an_p, PERIOD_D1, 1) && iClose(an_p, PERIOD_D1, 1) < iClose(an_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(an_p, MODE_BID) - iLow(an_p, PERIOD_D1, 1)) / iLow(an_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) < iLow(au_p, PERIOD_D1, 1) && iClose(au_p, PERIOD_D1, 1) < iClose(au_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(au_p, MODE_BID) - iLow(au_p, PERIOD_D1, 1)) / iLow(au_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) < iLow(ac_p, PERIOD_D1, 1) && iClose(ac_p, PERIOD_D1, 1) < iClose(ac_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(ac_p, MODE_BID) - iLow(ac_p, PERIOD_D1, 1)) / iLow(ac_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) < iLow(af_p, PERIOD_D1, 1) && iClose(af_p, PERIOD_D1, 1) < iClose(af_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(af_p, MODE_BID) - iLow(af_p, PERIOD_D1, 1)) / iLow(af_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) < iLow(aj_p, PERIOD_D1, 1) && iClose(aj_p, PERIOD_D1, 1) < iClose(aj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(aj_p, MODE_BID) - iLow(aj_p, PERIOD_D1, 1)) / iLow(aj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) < iLow(nu_p, PERIOD_D1, 1) && iClose(nu_p, PERIOD_D1, 1) < iClose(nu_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(nu_p, MODE_BID) - iLow(nu_p, PERIOD_D1, 1)) / iLow(nu_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) < iLow(nc_p, PERIOD_D1, 1) && iClose(nc_p, PERIOD_D1, 1) < iClose(nc_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(nc_p, MODE_BID) - iLow(nc_p, PERIOD_D1, 1)) / iLow(nc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) < iLow(nf_p, PERIOD_D1, 1) && iClose(nf_p, PERIOD_D1, 1) < iClose(nf_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(nf_p, MODE_BID) - iLow(nf_p, PERIOD_D1, 1)) / iLow(nf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) < iLow(nj_p, PERIOD_D1, 1) && iClose(nj_p, PERIOD_D1, 1) < iClose(nj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(nj_p, MODE_BID) - iLow(nj_p, PERIOD_D1, 1)) / iLow(nj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) < iLow(uc_p, PERIOD_D1, 1) && iClose(uc_p, PERIOD_D1, 1) < iClose(uc_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(uc_p, MODE_BID) - iLow(uc_p, PERIOD_D1, 1)) / iLow(uc_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) < iLow(uf_p, PERIOD_D1, 1) && iClose(uf_p, PERIOD_D1, 1) < iClose(uf_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(uf_p, MODE_BID) - iLow(uf_p, PERIOD_D1, 1)) / iLow(uf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) < iLow(uj_p, PERIOD_D1, 1) && iClose(uj_p, PERIOD_D1, 1) < iClose(uj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(uj_p, MODE_BID) - iLow(uj_p, PERIOD_D1, 1)) / iLow(uj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) < iLow(cf_p, PERIOD_D1, 1) && iClose(cf_p, PERIOD_D1, 1) < iClose(cf_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(cf_p, MODE_BID) - iLow(cf_p, PERIOD_D1, 1)) / iLow(cf_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) < iLow(cj_p, PERIOD_D1, 1) && iClose(cj_p, PERIOD_D1, 1) < iClose(cj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(cj_p, MODE_BID) - iLow(cj_p, PERIOD_D1, 1)) / iLow(cj_p, PERIOD_D1, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) < iLow(fj_p, PERIOD_D1, 1) && iClose(fj_p, PERIOD_D1, 1) < iClose(fj_p, PERIOD_D1, 2)) lowplus1 += (MarketInfo(fj_p, MODE_BID) - iLow(fj_p, PERIOD_D1, 1)) / iLow(fj_p, PERIOD_D1, 1) * 100;

	//R5 - > DMA5 ratio (trend)
	double highplus2 = 0;
	if (MarketInfo(eg_p, MODE_BID) > iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(eg_p, MODE_BID) - iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) > iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ea_p, MODE_BID) - iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) > iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(en_p, MODE_BID) - iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) > iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(eu_p, MODE_BID) - iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) > iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ec_p, MODE_BID) - iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) > iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ef_p, MODE_BID) - iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) > iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ej_p, MODE_BID) - iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) > iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ga_p, MODE_BID) - iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) > iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(gn_p, MODE_BID) - iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) > iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(gu_p, MODE_BID) - iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) > iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(gc_p, MODE_BID) - iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) > iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(gf_p, MODE_BID) - iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) > iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(gj_p, MODE_BID) - iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) > iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(an_p, MODE_BID) - iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) > iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(au_p, MODE_BID) - iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) > iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(ac_p, MODE_BID) - iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) > iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(af_p, MODE_BID) - iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) > iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(aj_p, MODE_BID) - iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) > iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(nu_p, MODE_BID) - iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) > iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(nc_p, MODE_BID) - iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) > iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(nf_p, MODE_BID) - iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) > iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(nj_p, MODE_BID) - iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) > iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(uc_p, MODE_BID) - iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) > iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(uf_p, MODE_BID) - iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) > iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(uj_p, MODE_BID) - iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) > iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(cf_p, MODE_BID) - iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) > iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(cj_p, MODE_BID) - iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) > iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus2 += (MarketInfo(fj_p, MODE_BID) - iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;

	double lowplus2 = 0;
	if (MarketInfo(eg_p, MODE_BID) < iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(eg_p, MODE_BID) - iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ea_p, MODE_BID) < iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ea_p, MODE_BID) - iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(en_p, MODE_BID) < iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(en_p, MODE_BID) - iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(eu_p, MODE_BID) < iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(eu_p, MODE_BID) - iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ec_p, MODE_BID) < iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ec_p, MODE_BID) - iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ef_p, MODE_BID) < iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ef_p, MODE_BID) - iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ej_p, MODE_BID) < iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ej_p, MODE_BID) - iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ga_p, MODE_BID) < iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ga_p, MODE_BID) - iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gn_p, MODE_BID) < iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(gn_p, MODE_BID) - iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gu_p, MODE_BID) < iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(gu_p, MODE_BID) - iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gc_p, MODE_BID) < iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(gc_p, MODE_BID) - iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gf_p, MODE_BID) < iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(gf_p, MODE_BID) - iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(gj_p, MODE_BID) < iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(gj_p, MODE_BID) - iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(an_p, MODE_BID) < iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(an_p, MODE_BID) - iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(au_p, MODE_BID) < iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(au_p, MODE_BID) - iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(ac_p, MODE_BID) < iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(ac_p, MODE_BID) - iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(af_p, MODE_BID) < iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(af_p, MODE_BID) - iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(aj_p, MODE_BID) < iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(aj_p, MODE_BID) - iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nu_p, MODE_BID) < iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(nu_p, MODE_BID) - iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nc_p, MODE_BID) < iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(nc_p, MODE_BID) - iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nf_p, MODE_BID) < iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(nf_p, MODE_BID) - iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(nj_p, MODE_BID) < iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(nj_p, MODE_BID) - iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uc_p, MODE_BID) < iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(uc_p, MODE_BID) - iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uf_p, MODE_BID) < iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(uf_p, MODE_BID) - iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(uj_p, MODE_BID) < iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(uj_p, MODE_BID) - iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(cf_p, MODE_BID) < iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(cf_p, MODE_BID) - iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(cj_p, MODE_BID) < iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(cj_p, MODE_BID) - iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (MarketInfo(fj_p, MODE_BID) < iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus2 += (MarketInfo(fj_p, MODE_BID) - iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;

	//R5o - > DMA5 ratio (trend) of previous day
	double highplus3 = 0;
	if (iOpen(eg_p, PERIOD_D1, 0) > iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(eg_p, PERIOD_D1, 0) - iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ea_p, PERIOD_D1, 0) > iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ea_p, PERIOD_D1, 0) - iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(en_p, PERIOD_D1, 0) > iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(en_p, PERIOD_D1, 0) - iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(eu_p, PERIOD_D1, 0) > iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(eu_p, PERIOD_D1, 0) - iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ec_p, PERIOD_D1, 0) > iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ec_p, PERIOD_D1, 0) - iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ef_p, PERIOD_D1, 0) > iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ef_p, PERIOD_D1, 0) - iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ej_p, PERIOD_D1, 0) > iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ej_p, PERIOD_D1, 0) - iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ga_p, PERIOD_D1, 0) > iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ga_p, PERIOD_D1, 0) - iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gn_p, PERIOD_D1, 0) > iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(gn_p, PERIOD_D1, 0) - iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gu_p, PERIOD_D1, 0) > iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(gu_p, PERIOD_D1, 0) - iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gc_p, PERIOD_D1, 0) > iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(gc_p, PERIOD_D1, 0) - iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gf_p, PERIOD_D1, 0) > iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(gf_p, PERIOD_D1, 0) - iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gj_p, PERIOD_D1, 0) > iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(gj_p, PERIOD_D1, 0) - iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(an_p, PERIOD_D1, 0) > iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(an_p, PERIOD_D1, 0) - iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(au_p, PERIOD_D1, 0) > iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(au_p, PERIOD_D1, 0) - iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ac_p, PERIOD_D1, 0) > iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(ac_p, PERIOD_D1, 0) - iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(af_p, PERIOD_D1, 0) > iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(af_p, PERIOD_D1, 0) - iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(aj_p, PERIOD_D1, 0) > iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(aj_p, PERIOD_D1, 0) - iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nu_p, PERIOD_D1, 0) > iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(nu_p, PERIOD_D1, 0) - iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nc_p, PERIOD_D1, 0) > iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(nc_p, PERIOD_D1, 0) - iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nf_p, PERIOD_D1, 0) > iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(nf_p, PERIOD_D1, 0) - iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nj_p, PERIOD_D1, 0) > iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(nj_p, PERIOD_D1, 0) - iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uc_p, PERIOD_D1, 0) > iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(uc_p, PERIOD_D1, 0) - iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uf_p, PERIOD_D1, 0) > iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(uf_p, PERIOD_D1, 0) - iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uj_p, PERIOD_D1, 0) > iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(uj_p, PERIOD_D1, 0) - iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(cf_p, PERIOD_D1, 0) > iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(cf_p, PERIOD_D1, 0) - iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(cj_p, PERIOD_D1, 0) > iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(cj_p, PERIOD_D1, 0) - iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(fj_p, PERIOD_D1, 0) > iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) highplus3 += (iOpen(fj_p, PERIOD_D1, 0) - iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;

	double lowplus3 = 0;
	if (iOpen(eg_p, PERIOD_D1, 0) < iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(eg_p, PERIOD_D1, 0) - iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eg_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ea_p, PERIOD_D1, 0) < iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ea_p, PERIOD_D1, 0) - iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ea_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(en_p, PERIOD_D1, 0) < iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(en_p, PERIOD_D1, 0) - iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(en_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(eu_p, PERIOD_D1, 0) < iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(eu_p, PERIOD_D1, 0) - iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(eu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ec_p, PERIOD_D1, 0) < iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ec_p, PERIOD_D1, 0) - iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ec_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ef_p, PERIOD_D1, 0) < iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ef_p, PERIOD_D1, 0) - iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ef_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ej_p, PERIOD_D1, 0) < iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ej_p, PERIOD_D1, 0) - iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ej_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ga_p, PERIOD_D1, 0) < iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ga_p, PERIOD_D1, 0) - iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ga_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gn_p, PERIOD_D1, 0) < iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(gn_p, PERIOD_D1, 0) - iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gn_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gu_p, PERIOD_D1, 0) < iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(gu_p, PERIOD_D1, 0) - iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gc_p, PERIOD_D1, 0) < iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(gc_p, PERIOD_D1, 0) - iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gf_p, PERIOD_D1, 0) < iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(gf_p, PERIOD_D1, 0) - iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(gj_p, PERIOD_D1, 0) < iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(gj_p, PERIOD_D1, 0) - iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(gj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(an_p, PERIOD_D1, 0) < iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(an_p, PERIOD_D1, 0) - iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(an_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(au_p, PERIOD_D1, 0) < iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(au_p, PERIOD_D1, 0) - iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(au_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(ac_p, PERIOD_D1, 0) < iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(ac_p, PERIOD_D1, 0) - iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(ac_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(af_p, PERIOD_D1, 0) < iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(af_p, PERIOD_D1, 0) - iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(af_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(aj_p, PERIOD_D1, 0) < iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(aj_p, PERIOD_D1, 0) - iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(aj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nu_p, PERIOD_D1, 0) < iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(nu_p, PERIOD_D1, 0) - iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nu_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nc_p, PERIOD_D1, 0) < iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(nc_p, PERIOD_D1, 0) - iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nf_p, PERIOD_D1, 0) < iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(nf_p, PERIOD_D1, 0) - iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(nj_p, PERIOD_D1, 0) < iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(nj_p, PERIOD_D1, 0) - iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(nj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uc_p, PERIOD_D1, 0) < iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(uc_p, PERIOD_D1, 0) - iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uc_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uf_p, PERIOD_D1, 0) < iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(uf_p, PERIOD_D1, 0) - iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(uj_p, PERIOD_D1, 0) < iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(uj_p, PERIOD_D1, 0) - iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(uj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(cf_p, PERIOD_D1, 0) < iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(cf_p, PERIOD_D1, 0) - iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cf_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(cj_p, PERIOD_D1, 0) < iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(cj_p, PERIOD_D1, 0) - iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(cj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;
	if (iOpen(fj_p, PERIOD_D1, 0) < iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) lowplus3 += (iOpen(fj_p, PERIOD_D1, 0) - iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1)) / iMA(fj_p, PERIOD_D1, 5, 0, MODE_SMA, PRICE_CLOSE, 1) * 100;

	//R4 breakout ratio
	//RC breakout continuation ratio
	//R5 trend (DMA5)
	//R5o trend (DMA5) previous day
	double R4 = highplus + MathAbs(lowplus);
	double RC = highplus1 + MathAbs(lowplus1);
	double R5 = highplus2 + MathAbs(lowplus2);
	double R5o = highplus3 + MathAbs(lowplus3);

	double skunk = (28 * cunt) / (28 * Rstr * 28);

	double R = (count * cunt) / (28 * Rstr * 14);

	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort1 " + IntegerToString(x);
		LabelMake(obname, 0, f + 130, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][1].ToInt() == 1) { ObjectSetText(obname, CharToStr(233), 8, "Wingdings", clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][1].ToInt() == 2) { ObjectSetText(obname, CharToStr(108), 9, "Wingdings", clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][1].ToInt() == 3) { ObjectSetText(obname, CharToStr(234), 8, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort2 " + IntegerToString(x);
		LabelMake(obname, 0, f + 150, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][2].ToBool() == true) { ObjectSetText(obname, CharToStr(233), 8, "Wingdings", clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][2].ToBool() == false) { ObjectSetText(obname, CharToStr(234), 8, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort3 " + IntegerToString(x);
		LabelMake(obname, 0, f + 170, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][3].ToInt() == 1) { ObjectSetText(obname, CharToStr(233), 8, "Wingdings", clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][3].ToInt() == 2) { ObjectSetText(obname, CharToStr(110), 10, "Wingdings", clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][3].ToInt() == 3) { ObjectSetText(obname, CharToStr(234), 8, "Wingdings", clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort4 " + IntegerToString(x);
		LabelMake(obname, 0, f + 200, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][4].ToBool() == false) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][4].ToBool() == true) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort5 " + IntegerToString(x);
		LabelMake(obname, 0, f + 220, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][5].ToBool() == false) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][5].ToBool() == true) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort6 " + IntegerToString(x);
		LabelMake(obname, 0, f + 240, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][6].ToBool() == false) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrDarkBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][6].ToBool() == true) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrDarkRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort7 " + IntegerToString(x);
		LabelMake(obname, 0, f + 260, 30 + x * 20, "", 8, clrDodgerBlue);
		if (mainsort["b"][x][7].ToBool() == false) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrDarkBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][7].ToBool() == true) { ObjectSetText(obname, CharToStr(108), 8, "Wingdings", clrDarkRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}
	for (int x = 0; x <= 27; x++)
	{
		obname = Name + " fibsort8 " + IntegerToString(x);
		LabelMake(obname, 0, f + 280, 30 + x * 20, "", 8, clrDodgerBlue);
		if (((mainsort["b"][x][1].ToInt() == 1 && mainsort["b"][x][3].ToInt() == 1 && mainsort["c"][x][0].ToDbl() <= 0.75) || (mainsort["b"][x][1].ToInt() == 3 && mainsort["b"][x][3].ToInt() == 3 && mainsort["c"][x][0].ToDbl() <= 0.75)) && (mainsort["b"][x][4].ToBool() == false && mainsort["b"][x][5].ToBool() == false)) { ObjectSetText(obname, "M", 12, "Wingdings", clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (((mainsort["b"][x][1].ToInt() == 1 && mainsort["b"][x][3].ToInt() == 1 && mainsort["c"][x][0].ToDbl() <= 0.75) || (mainsort["b"][x][1].ToInt() == 3 && mainsort["b"][x][3].ToInt() == 3 && mainsort["c"][x][0].ToDbl() <= 0.75)) && (mainsort["b"][x][6].ToBool() == true || mainsort["b"][x][7].ToBool() == true)) { ObjectSetText(obname, "M", 12, "Wingdings", clrOrange); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (((mainsort["b"][x][1].ToInt() == 1 && mainsort["b"][x][3].ToInt() == 1 && mainsort["c"][x][0].ToDbl() > 0.75) || (mainsort["b"][x][1].ToInt() == 3 && mainsort["b"][x][3].ToInt() == 3 && mainsort["c"][x][0].ToDbl() > 0.75)) && (mainsort["b"][x][4].ToBool() == true || mainsort["b"][x][5].ToBool() == true || mainsort["b"][x][6].ToBool() == true || mainsort["b"][x][7].ToBool() == true)) { ObjectSetText(obname, "O", 12, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
		if (mainsort["b"][x][1].ToInt() == 2 && mainsort["b"][x][3].ToInt() == 2) { ObjectSetText(obname, "N", 12, "Wingdings", clrWhite); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
	}

	obname = Name + " time";
	LabelMake(obname, 0, f + 360, 20, TimeToString(TimeCurrent(), TIME_SECONDS), 24, clrLightGray);

	obname = Name + " sort buyside";
	LabelMake(obname, 0, f + 10, 590, "Buyside: " + DoubleToString(totbuy, 1), 11, clrGreen);
	obname = Name + " sort sellside";
	LabelMake(obname, 0, f + 10, 610, "Sellside: " + DoubleToString(totsel, 1), 11, clrRed);

	double ratio1 = 0;
	if (totsel != 0 && totbuy != 0)
	{
		if (totbuy - MathAbs(totsel) >= 0) ratio1 = -((totbuy - MathAbs(totsel)) / totsel);
		else ratio1 = (totbuy - MathAbs(totsel)) / totbuy;
	}
	obname = Name + " sort ratio";
	LabelMake(obname, 0, f + 130, 600, "R: " + DoubleToString(ratio1, 2), 11, clrYellow);
	if (totsel != 0)
	{
		if (totbuy / MathAbs(totsel) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
		if (totbuy / MathAbs(totsel) < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);
	}

	obname = Name + " buyside EGAN";
	LabelMake(obname, 0, f + 210, 590, "B (EGAN): " + DoubleToString(poscount, 1), 11, clrGreen);
	obname = Name + " sellside UCFJ";
	LabelMake(obname, 0, f + 210, 610, "S (UCFJ): " + DoubleToString(negcount, 1), 11, clrRed);

	double ratio2 = 0;
	if (negcount != 0 && poscount != 0)
	{
		if (poscount - MathAbs(negcount) > 0) ratio2 = -((poscount - MathAbs(negcount)) / negcount);
		else ratio2 = (poscount - MathAbs(negcount)) / poscount;
	}
	obname = Name + " bs2";
	LabelMake(obname, 0, f + 330, 600, "Risk: " + DoubleToString(ratio2, 2), 11, clrGreen);
	if (ratio2 > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
	if (ratio2 < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);

	{
		string a[9];
		a[0] = "Pa1";
		a[1] = "Pa2";
		a[2] = "Pa3";
		a[3] = "Pa4";
		a[4] = "Pb1";
		a[5] = "Pb2";
		a[6] = "Pb3";
		a[7] = "Pc1";
		a[8] = "Pc2";

		string b[9];
		b[0] = "Na1";
		b[1] = "Na2";
		b[2] = "Na3";
		b[3] = "Na4";
		b[4] = "Nb1";
		b[5] = "Nb2";
		b[6] = "Nb3";
		b[7] = "Nc1";
		b[8] = "Nc2";

		int result;
		for (int x = 0; x <= 8; x++)
		{
			result = StringCompare(StringConcatenate("-" + baskets2[a[0]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[1]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[2]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[3]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[4]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[5]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[6]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[7]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(StringConcatenate("-" + baskets2[a[8]].ToStr()), baskets2[b[x]].ToStr());
			if (result == 0) baskets2[b[x]] = "";
			result = StringCompare(baskets2[a[0]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[0]] = "";
			result = StringCompare(baskets2[a[1]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[1]] = "";
			result = StringCompare(baskets2[a[2]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[2]] = "";
			result = StringCompare(baskets2[a[3]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[3]] = "";
			result = StringCompare(baskets2[a[4]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[4]] = "";
			result = StringCompare(baskets2[a[5]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[5]] = "";
			result = StringCompare(baskets2[a[6]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[6]] = "";
			result = StringCompare(baskets2[a[7]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[7]] = "";
			result = StringCompare(baskets2[a[8]].ToStr(), StringConcatenate("-" + baskets2[b[x]].ToStr()));
			if (result == 0) baskets2[a[8]] = "";
		}

		if (MathAbs(baskets2[a[0]].ToDbl()) < 250) baskets2[a[0]] = "";
		if (MathAbs(baskets2[a[1]].ToDbl()) < 250) baskets2[a[1]] = "";
		if (MathAbs(baskets2[a[2]].ToDbl()) < 250) baskets2[a[2]] = "";
		if (MathAbs(baskets2[a[3]].ToDbl()) < 250) baskets2[a[3]] = "";
		if (MathAbs(baskets2[a[4]].ToDbl()) < 250) baskets2[a[4]] = "";
		if (MathAbs(baskets2[a[5]].ToDbl()) < 250) baskets2[a[5]] = "";
		if (MathAbs(baskets2[a[6]].ToDbl()) < 250) baskets2[a[6]] = "";
		if (MathAbs(baskets2[a[7]].ToDbl()) < 250) baskets2[a[7]] = "";
		if (MathAbs(baskets2[a[8]].ToDbl()) < 250) baskets2[a[8]] = "";

		if (MathAbs(baskets2[b[0]].ToDbl()) < 250) baskets2[b[0]] = "";
		if (MathAbs(baskets2[b[1]].ToDbl()) < 250) baskets2[b[1]] = "";
		if (MathAbs(baskets2[b[2]].ToDbl()) < 250) baskets2[b[2]] = "";
		if (MathAbs(baskets2[b[3]].ToDbl()) < 250) baskets2[b[3]] = "";
		if (MathAbs(baskets2[b[4]].ToDbl()) < 250) baskets2[b[4]] = "";
		if (MathAbs(baskets2[b[5]].ToDbl()) < 250) baskets2[b[5]] = "";
		if (MathAbs(baskets2[b[6]].ToDbl()) < 250) baskets2[b[6]] = "";
		if (MathAbs(baskets2[b[7]].ToDbl()) < 250) baskets2[b[7]] = "";
		if (MathAbs(baskets2[b[8]].ToDbl()) < 250) baskets2[b[8]] = "";

		string tokillb[] = {}; //Long strings
		string tokills[] = {}; //Short strings

		for (int x = 0; x <= 8; x++)
		{
			if (baskets2[a[x]].ToStr() != "" && StringLen(baskets2[a[x]].ToStr()) == 6)
			{
				int CurrentSize = ArraySize(tokillb); ArrayResize(tokillb, CurrentSize + 1); tokillb[CurrentSize] = baskets2[a[x]].ToStr();
			}
			if (baskets2[a[x]].ToStr() != "" && StringLen(baskets2[a[x]].ToStr()) == 7)
			{
				int CurrentSize = ArraySize(tokills); ArrayResize(tokills, CurrentSize + 1); tokills[CurrentSize] = StringSubstr(baskets2[a[x]].ToStr(), 1, 6);
			}
			if (baskets2[b[x]].ToStr() != "" && StringLen(baskets2[b[x]].ToStr()) == 6)
			{
				int CurrentSize = ArraySize(tokills); ArrayResize(tokills, CurrentSize + 1); tokills[CurrentSize] = baskets2[b[x]].ToStr();
			}
			if (baskets2[b[x]].ToStr() != "" && StringLen(baskets2[b[x]].ToStr()) == 7)
			{
				int CurrentSize = ArraySize(tokillb); ArrayResize(tokillb, CurrentSize + 1); tokillb[CurrentSize] = StringSubstr(baskets2[b[x]].ToStr(), 1, 6);
			}
		}

		double retrace = 0;
		int rrcount = 0;
		double retrtop = 0;
		static double retrtop5m = 0;
		static double retrtop15m = 0;
		for (int x = 0; x <= 27; x++)
		{
			retrace += mainsort["retrace"][x][0].ToDbl();
			retrtop += mainsort["ftop"][x][0].ToDbl();
			if (mainsort["retrace"][x][0].ToDbl() > 0) rrcount++;
		}
		static datetime rnew5m = 0;
		bool rnew5mc = false;
		if (rnew5m < iTime(_Symbol, PERIOD_M5, 0))
		{
			rnew5m = iTime(_Symbol, PERIOD_M5, 0);
			rnew5mc = true;
		}
		if (rnew5mc)
		{
			retrtop5m = retrtop;
			rnew5mc = false;
		}
		static datetime rnew15m = 0;
		bool rnew15mc = false;
		if (rnew15m < iTime(_Symbol, PERIOD_M15, 0))
		{
			rnew15m = iTime(_Symbol, PERIOD_M15, 0);
			rnew15mc = true;
		}
		if (rnew15mc)
		{
			retrtop15m = retrtop;
			rnew15mc = false;
		}
		double fretr = 0;
		if (rrcount != 0) fretr = retrace / rrcount; else fretr = 0;
		obname = Name + "retrace";
		LabelMake(obname, 0, f + 355, 400, "*RETRACE* : " + DoubleToString(fretr, 1), 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (fretr <= 38.2)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (fretr <= 25)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		if (fretr <= 10)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		obname = Name + "retrtop";
		LabelMake(obname, 0, f + 355, 415, "Retrtop : " + DoubleToString(retrtop, 1), 10, clrRed);
		obname = Name + "retrtop5";
		LabelMake(obname, 0, f + 355, 430, "Retrtop(5m) : " + DoubleToString(retrtop5m, 1), 10, clrRed);
		obname = Name + "retrtop15";
		LabelMake(obname, 0, f + 355, 445, "Retrtop(15m) : " + DoubleToString(retrtop15m, 1), 10, clrRed);

		double recretrace = 0;
		int reccount = 0;
		double rectop = 0;
		static double rectop5m = 0;
		static double rectop15m = 0;
		for (int x = 0; x <= 27; x++)
		{
			for (int cc = ArraySize(tokillb) - 1; cc >= 0; cc--)
			{
				if (mainsort["a"][x][0].ToStr() == tokillb[cc]) { recretrace += mainsort["retrace"][x][0].ToDbl(); rectop += mainsort["ftop"][x][0].ToDbl(); reccount++; }
			}
			for (int cc = ArraySize(tokills) - 1; cc >= 0; cc--)
			{
				if (mainsort["a"][x][0].ToStr() == tokills[cc]) { recretrace += mainsort["retrace"][x][0].ToDbl(); rectop += mainsort["ftop"][x][0].ToDbl(); reccount++; }
			}
		}
		static datetime new5m = 0;
		bool new5mc = false;
		if (new5m < iTime(_Symbol, PERIOD_M5, 0))
		{
			new5m = iTime(_Symbol, PERIOD_M5, 0);
			new5mc = true;
		}
		if (new5mc)
		{
			rectop5m = rectop;
			new5mc = false;
		}
		static datetime new15m = 0;
		bool new15mc = false;
		if (new15m < iTime(_Symbol, PERIOD_M15, 0))
		{
			new15m = iTime(_Symbol, PERIOD_M15, 0);
			new15mc = true;
		}
		if (new15mc)
		{
			rectop15m = rectop;
			new15mc = false;
		}
		double frecretr = 0;
		if (reccount != 0) frecretr = recretrace / reccount; else frecretr = 0;
		obname = Name + "recretrace";
		LabelMake(obname, 0, f + 355, 460, "*RECRETR* : " + DoubleToString(frecretr, 1), 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (frecretr <= 38.2)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (frecretr <= 25)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		if (frecretr <= 10)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		obname = Name + "rectop";
		LabelMake(obname, 0, f + 355, 475, "Rectop : " + DoubleToString(rectop, 1), 10, clrRed);
		obname = Name + "rectop5";
		LabelMake(obname, 0, f + 355, 490, "Rectop(5m) : " + DoubleToString(rectop5m, 1), 10, clrRed);
		obname = Name + "rectop15";
		LabelMake(obname, 0, f + 355, 505, "Rectop(15m) : " + DoubleToString(rectop15m, 1), 10, clrRed);

		double orecretrace = 0;
		int oreccount = 0;
		double orectop = 0;
		static double orectop5m = 0;
		static double orectop15m = 0;
		string trades[] = {};
		ArrayResize(trades, OrdersTotal());

		for (int i = OrdersTotal() - 1; i >= 0; i--)
		{
			if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
			{
				trades[i] = OrderSymbol();
			}
		}

		string uniqueArray[];
		int uniqueCount = 0;

		for (int i = 0; i < ArraySize(trades); i++)
		{
			bool isDuplicate = false;

			for (int j = 0; j < uniqueCount; j++)
			{
				if (trades[i] == uniqueArray[j])
				{
					isDuplicate = true;
					break;
				}
			}
			if (!isDuplicate)
			{
				ArrayResize(uniqueArray, uniqueCount + 1);
				uniqueArray[uniqueCount] = trades[i];
				uniqueCount++;
			}
		}

		ArrayResize(trades, uniqueCount);
		for (int k = 0; k < uniqueCount; k++)
		{
			trades[k] = uniqueArray[k];
		}

		for (int x = 0; x <= 27; x++)
		{
			for (int cc = ArraySize(trades) - 1; cc >= 0; cc--)
			{
				if (mainsort["a"][x][0].ToStr() == trades[cc]) { orecretrace += mainsort["retrace"][x][0].ToDbl(); orectop += mainsort["ftop"][x][0].ToDbl(); oreccount++; }
			}
		}

		static datetime onew5m = 0;
		bool onew5mc = false;
		if (onew5m < iTime(_Symbol, PERIOD_M5, 0))
		{
			onew5m = iTime(_Symbol, PERIOD_M5, 0);
			onew5mc = true;
		}
		if (onew5mc)
		{
			orectop5m = orectop;
			onew5mc = false;
		}
		static datetime onew15m = 0;
		bool onew15mc = false;
		if (onew15m < iTime(_Symbol, PERIOD_M15, 0))
		{
			onew15m = iTime(_Symbol, PERIOD_M15, 0);
			onew15mc = true;
		}
		if (onew15mc)
		{
			orectop15m = orectop;
			onew15mc = false;
		}
		double ofrecretr = 0;
		if (oreccount != 0) ofrecretr = orecretrace / oreccount; else ofrecretr = 0;
		obname = Name + "orecretrace";
		LabelMake(obname, 0, f + 355, 520, "*TRADERETR* : " + DoubleToString(ofrecretr, 1), 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (ofrecretr <= 38.2)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		if (ofrecretr <= 25)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		if (ofrecretr <= 10)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		obname = Name + "orectop";
		LabelMake(obname, 0, f + 355, 535, "Tradtop : " + DoubleToString(orectop, 1), 10, clrRed);
		obname = Name + "orectop5";
		LabelMake(obname, 0, f + 355, 550, "Tradtop(5m) : " + DoubleToString(orectop5m, 1), 10, clrRed);
		obname = Name + "orectop15";
		LabelMake(obname, 0, f + 355, 565, "Tradtop(15m) : " + DoubleToString(orectop15m, 1), 10, clrRed);

		ObjectsDeleteAll(0, Name + " fibsort9 ");
		for (int x = 0; x <= 27; x++)
		{
			obname = Name + " fibsort9 " + IntegerToString(x);
			if (mainsort["retrace"][x][0].ToDbl() > 0) LabelMake(obname, 0, f + 310, 30 + x * 20, mainsort["retrace"][x][0].ToStr(), 8, clrRed);
			if (mainsort["retrace"][x][0].ToDbl() <= 38.2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			}
			if (mainsort["retrace"][x][0].ToDbl() <= 25)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			}
			if (mainsort["retrace"][x][0].ToDbl() <= 10)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			}
			obname = Name + " fibsort10 " + IntegerToString(x);
			LabelMake(obname, 0, f + 340, 30 + x * 20, "", 8, clrLightGray);
			ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
			if (mainsort["c"][x][0].ToDbl() < 0.9)
			{
				ObjectSetText(obname, "è", 8, "Wingdings", clrBlue);
			}
			else if (mainsort["c"][x][0].ToDbl() >= 0.9 && mainsort["c"][x][0].ToDbl() <= 1.0)
			{
				ObjectSetText(obname, "î", 8, "Wingdings", clrGreen);
			}
			else if (mainsort["c"][x][0].ToDbl() > 1.0)
			{
				ObjectSetText(obname, "ê", 8, "Wingdings", clrRed);
			}

			obname = Name + " dot " + IntegerToString(x);
			LabelMake(obname, 0, f + 331, 30 + x * 20, "", 8, clrLightGray);
			for (int cc = ArraySize(tokillb) - 1; cc >= 0; cc--)
			{
				if (mainsort["a"][x][0].ToStr() == tokillb[cc]) { ObjectSetText(obname, CharToString(174), 9, "Wingdings", clrLime); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			}
			for (int cc = ArraySize(tokills) - 1; cc >= 0; cc--)
			{
				if (mainsort["a"][x][0].ToStr() == tokills[cc]) { ObjectSetText(obname, CharToString(174), 9, "Wingdings", clrMagenta); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP); }
			}
		}

		double buy = baskets2[a[0]].ToDbl() + baskets2[a[1]].ToDbl() + baskets2[a[2]].ToDbl() + baskets2[a[3]].ToDbl() + baskets2[a[4]].ToDbl() + baskets2[a[5]].ToDbl() + baskets2[a[6]].ToDbl() + baskets2[a[7]].ToDbl() + baskets2[a[8]].ToDbl();
		double sell = -(baskets2[b[0]].ToDbl() + baskets2[b[1]].ToDbl() + baskets2[b[2]].ToDbl() + baskets2[b[3]].ToDbl() + baskets2[b[4]].ToDbl() + baskets2[b[5]].ToDbl() + baskets2[b[6]].ToDbl() + baskets2[b[7]].ToDbl() + baskets2[b[8]].ToDbl());

		double rpreavgp = 0;
		double tr3 = 0;
		double tr5 = 0;

		double rpre1 = 0, rpre2 = 0;

		{//R ratio
			static double Rpre[7];
			static double Repre[11];

			static datetime checkr = 0;
			bool checkra = false;
			if (checkr < iTime(_Symbol, PERIOD_M5, 0))
			{
				checkr = iTime(_Symbol, PERIOD_M5, 0);
				checkra = true;
			}
			if (checkra)
			{
				for (int x = 6; x >= 1; x--)
				{
					Rpre[x] = Rpre[x - 1];
				}
			}

			Rpre[0] = R;

			static datetime checkr1 = 0;
			bool checkra1 = false;
			if (checkr1 < iTime(_Symbol, PERIOD_M1, 0))
			{
				checkr1 = iTime(_Symbol, PERIOD_M1, 0);
				checkra1 = true;
			}
			if (checkra1)
			{
				for (int x = 9; x >= 1; x--)
				{
					Repre[x] = Repre[x - 1];
				}
			}

			Repre[0] = R;

			for (int x = 9; x >= 1; x--)
			{
				rpreavgp += Repre[x];
			}

			double rpreavg = 0;
			rpreavg = (rpreavgp + R) / 10;

			obname = Name + "r1ENTRYa";
			LabelMake(obname, 0, f + 10, 700, "R", 10, clrYellow);
			obname = Name + "r1m1ENTRY";
			LabelMake(obname, 0, f + 27, 700, "M1: " + DoubleToString(Repre[9], 2) + "  " + DoubleToString(Repre[8], 2) + "  " + DoubleToString(Repre[7], 2) + "  " + DoubleToString(Repre[6], 2) + "  " + DoubleToString(Repre[5], 2) + "  " + DoubleToString(Repre[4], 2) + "  " + DoubleToString(Repre[3], 2) + "  " + DoubleToString(Repre[2], 2) + "  " + DoubleToString(Repre[1], 2) + "  " + DoubleToString(Repre[0], 2), 8, clrYellow);
			if (rpreavg < 1.00)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (rpreavg >= 1 && rpreavg < 1.10)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (rpreavg >= 1.10 && rpreavg < 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
				ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			}
			if (rpreavg >= 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
				ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			}
			obname = Name + "rENTRY";
			LabelMake(obname, 0, f + 67, 725, "M1avg: " + DoubleToString(rpreavg, 2), 8, clrYellow);
			ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			if (rpreavg < 1.00)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (rpreavg >= 1.00 && rpreavg < 1.10)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (rpreavg >= 1.10 && rpreavg < 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			}
			if (rpreavg >= 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			}
			obname = Name + "rpreENTRY";
			LabelMake(obname, 0, f + 27, 710, "M5: " + DoubleToString(Rpre[6], 2) + "  " + DoubleToString(Rpre[5], 2) + "  " + DoubleToString(Rpre[4], 2) + "  " + DoubleToString(Rpre[3], 2) + "  " + DoubleToString(Rpre[2], 2) + "  " + DoubleToString(Rpre[1], 2) + "  " + DoubleToString(Rpre[0], 2), 8, clrYellow);
			ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			if (rpreavg < 1.00)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (rpreavg >= 1.00 && rpreavg < 1.10)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			if (rpreavg >= 1.10 && rpreavg < 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			}
			if (rpreavg >= 2)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			}

			rpre1 = Rpre[1]; rpre2 = Rpre[2];
		}

		//VERTICAL RATIOS
		//R1
		static double R1pre[101];

		static datetime checkr1pre1 = 0;
		bool checkr1prea1 = false;
		if (checkr1pre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr1pre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr1prea1 = true;
		}
		if (checkr1prea1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R1pre[r] = R1pre[r - 1];
			}
			R1pre[0] = R;
			checkr1prea1 = false;
		}

		//R1s
		static double R1stdpre[101];

		static datetime checkr1stdpre1 = 0;
		bool checkr1stdprea1 = false;
		if (checkr1stdpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr1stdpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr1stdprea1 = true;
		}
		if (checkr1stdprea1)
		{
			double rstd = 0;
			rstd = CalculateVolatility(R1pre, 20);

			for (int r = 100; r >= 1; r--)
			{
				R1stdpre[r] = R1stdpre[r - 1];
			}
			R1stdpre[0] = rstd;
			checkr1stdprea1 = false;
		}

		//R2
		static double R2pre[101];

		static datetime checkr21 = 0;
		bool checkr2a1 = false;
		if (checkr21 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr21 = iTime(_Symbol, PERIOD_M1, 0);
			checkr2a1 = true;
		}
		if (checkr2a1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R2pre[r] = R2pre[r - 1];
			}
			R2pre[0] = skunk;
			checkr2a1 = false;
		}

		//R4
		static double R4pre[101];

		static datetime checkr41 = 0;
		bool checkr4a1 = false;
		if (checkr41 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr41 = iTime(_Symbol, PERIOD_M1, 0);
			checkr4a1 = true;
		}
		if (checkr4a1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R4pre[r] = R4pre[r - 1];
			}
			R4pre[0] = R4;
			checkr4a1 = false;
		}

		//RC
		static double RCpre[101];

		static datetime checkrc1 = 0;
		bool checkrca1 = false;
		if (checkrc1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkrc1 = iTime(_Symbol, PERIOD_M1, 0);
			checkrca1 = true;
		}
		if (checkrca1)
		{
			for (int r = 100; r >= 1; r--)
			{
				RCpre[r] = RCpre[r - 1];
			}
			RCpre[0] = RC;
			checkrca1 = false;
		}

		//R5
		static double R5pre[101];

		static datetime checkr51 = 0;
		bool checkr5a1 = false;
		if (checkr51 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr51 = iTime(_Symbol, PERIOD_M1, 0);
			checkr5a1 = true;
		}
		if (checkr5a1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R5pre[r] = R5pre[r - 1];
			}
			R5pre[0] = R5;
			checkr5a1 = false;
		}

		//R3  
		static double R3pre[101];
		static datetime checkr31 = 0;
		bool checkr3a1 = false;
		if (checkr31 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr31 = iTime(_Symbol, PERIOD_M1, 0);
			checkr3a1 = true;
		}
		if (checkr3a1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R3pre[r] = R3pre[r - 1];
			}
			R3pre[0] = mainsort["d"][0].ToDbl();
			checkr3a1 = false;
		}

		//RRR
		static double RRRpre[101];

		double rrr = R + skunk - 2 * mainsort["d"][0].ToDbl();

		static datetime checkrrr1 = 0;
		bool checkrrra1 = false;
		if (checkrrr1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkrrr1 = iTime(_Symbol, PERIOD_M1, 0);
			checkrrra1 = true;
		}
		if (checkrrra1)
		{
			for (int r = 100; r >= 1; r--)
			{
				RRRpre[r] = RRRpre[r - 1];
			}
			RRRpre[0] = rrr;
			checkrrra1 = false;

			tr3 = (RRRpre[0] + RRRpre[1] + RRRpre[2]) / 3;
			tr5 = (RRRpre[0] + RRRpre[1] + RRRpre[2] + RRRpre[3] + RRRpre[4]) / 5;

			obname = Name + "rrr3ENTRY";
			LabelMake(obname, 0, f + 147, 725, "3r3: " + DoubleToString(tr3, 2), 8, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			if (tr3 >= 1)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			obname = Name + "rrr5ENTRY";
			LabelMake(obname, 0, f + 207, 725, "3r5: " + DoubleToString(tr5, 2), 8, clrRed);
			ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			if (tr5 >= 1)
				ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		}

		//TOTAL
		static double Totalpre[101];

		double totti = 0;
		for (int x = 27; x >= 0; x--)
		{
			if (MathAbs(mainsort["a"][x][1].ToDbl()) >= 200) totti += MathAbs(mainsort["a"][x][1].ToDbl());
		}

		static datetime checktotal1 = 0;
		bool checktotala1 = false;
		if (checktotal1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checktotal1 = iTime(_Symbol, PERIOD_M1, 0);
			checktotala1 = true;
		}
		if (checktotala1)
		{
			for (int r = 100; r >= 1; r--)
			{
				Totalpre[r] = Totalpre[r - 1];
			}
			Totalpre[0] = totti;
			checktotala1 = false;
		}

		//TOTALema
		static double Brecpre[101];

		static datetime checkbrec1 = 0;
		bool checkbreca1 = false;
		if (checkbrec1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkbrec1 = iTime(_Symbol, PERIOD_M1, 0);
			checkbreca1 = true;
		}
		if (checkbreca1)
		{
			double brec[];
			ArrayResize(brec, ArraySize(Totalpre));
			ArraySetAsSeries(brec, true);

			for (int x = ArraySize(brec) - 1; x >= 0; x--)
			{
				if (Totalpre[x] > 0) brec[x] = Totalpre[x] - (1.8 * 250);
			}

			for (int r = 100; r >= 1; r--)
			{
				Brecpre[r] = Brecpre[r - 1];
			}
			Brecpre[0] = iMAOnArray(brec, 0, 20, 0, MODE_EMA, 0);
			checkbreca1 = false;
		}

		//TOTALstd
		static double Recpre[101];

		static datetime checkrecr1 = 0;
		bool checkrecra1 = false;
		if (checkrecr1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkrecr1 = iTime(_Symbol, PERIOD_M1, 0);
			checkrecra1 = true;
		}
		if (checkrecra1)
		{
			double brec[];
			ArrayResize(brec, ArraySize(Totalpre));
			ArraySetAsSeries(brec, true);

			for (int x = ArraySize(Totalpre) - 1; x >= 0; x--)
			{
				brec[x] = Totalpre[x];
			}

			double totsd = 0;
			totsd = iStdDevOnArray(brec, 0, 20, 0, MODE_SMA, 0);

			for (int r = 100; r >= 1; r--)
			{
				Recpre[r] = Recpre[r - 1];
			}
			Recpre[0] = totsd;
			checkrecra1 = false;
		}

		//EQ
		double total = 0;

		for (int i = OrdersTotal() - 1; i >= 0; i--)
		{
			if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
			{
				total += OrderProfit() + OrderSwap() + OrderCommission();
			}
		}

		static double EQpre[101];

		static datetime checkeq1 = 0;
		bool checkeqa1 = false;
		if (checkeq1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkeq1 = iTime(_Symbol, PERIOD_M1, 0);
			checkeqa1 = true;
		}
		if (checkeqa1)
		{
			for (int r = 100; r >= 1; r--)
			{
				EQpre[r] = EQpre[r - 1];
			}
			EQpre[0] = total;
			checkeqa1 = false;
		}

		//RHLCs
		static double RHLCpre[101];

		static datetime checkrhlcpre1 = 0;
		bool checkrhlcprea1 = false;
		if (checkrhlcpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkrhlcpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkrhlcprea1 = true;
		}
		if (checkrhlcprea1)
		{
			for (int r = 100; r >= 1; r--)
			{
				RHLCpre[r] = RHLCpre[r - 1];
			}
			//RHLCpre[0] = percdata["CurStateRat"].ToDbl();
			RHLCpre[0] = frecretr;
			checkrhlcprea1 = false;
		}

		//R1avg
		static double R1avgpre[101];
		static datetime checkr1avgpre1 = 0;
		double r1avgp = 0;
		for (int x = 9; x >= 1; x--)
		{
			r1avgp += R1pre[x];
		}

		double r1avg = 0;
		r1avg = (r1avgp + R) / 10;

		bool checkr1avgprea1 = false;
		if (checkr1avgpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr1avgpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr1avgprea1 = true;
		}
		if (checkr1avgprea1)
		{
			for (int r = 100; r >= 1; r--)
			{
				R1avgpre[r] = R1avgpre[r - 1];
			}
			R1avgpre[0] = r1avg;
			checkr1avgprea1 = false;
		}

		//HLTr
		static double HLTrpre[101];
		static datetime checkhltrpre1 = 0;
		double hltr = 0;
		if (Totalpre[0] != 0) hltr = (percdata["CurStateRat"].ToDbl() * 1000) / Totalpre[0];

		bool checkhltrprea1 = false;
		if (checkhltrpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkhltrpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkhltrprea1 = true;
		}
		if (checkhltrprea1)
		{
			for (int r = 100; r >= 1; r--)
			{
				HLTrpre[r] = HLTrpre[r - 1];
			}
			HLTrpre[0] = hltr;
			checkhltrprea1 = false;
		}

		//ACTr
		static double ACTrpre[101];
		static datetime checkactrpre1 = 0;

		bool checkactrprea1 = false;
		if (checkactrpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkactrpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkactrprea1 = true;
		}
		if (checkactrprea1)
		{
			for (int r = 100; r >= 1; r--)
			{
				ACTrpre[r] = ACTrpre[r - 1];
			}
			ACTrpre[0] = percdata["DayAllA"][0].ToDbl();
			checkactrprea1 = false;
		}

		//R1TPCs
		static double R1TPCstdpre[101];

		static datetime checkr1tpcstdpre1 = 0;
		bool checkr1tpcstdprea1 = false;
		if (checkr1tpcstdpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr1tpcstdpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr1tpcstdprea1 = true;
		}
		if (checkr1tpcstdprea1)
		{
			double rstd = 0;
			rstd = CalculatePearsonCorrelation(R1pre, Totalpre, 31);

			for (int r = 100; r >= 1; r--)
			{
				R1TPCstdpre[r] = R1TPCstdpre[r - 1];
			}
			R1TPCstdpre[0] = rstd;
			checkr1tpcstdprea1 = false;
		}

		//R2TPCs
		static double R2TPCstdpre[101];

		static datetime checkr2tpcstdpre1 = 0;
		bool checkr2tpcstdprea1 = false;
		if (checkr2tpcstdpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr2tpcstdpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr2tpcstdprea1 = true;
		}
		if (checkr2tpcstdprea1)
		{
			double rstd = 0;
			rstd = CalculatePearsonCorrelation(R2pre, Totalpre, 31);

			for (int r = 100; r >= 1; r--)
			{
				R2TPCstdpre[r] = R2TPCstdpre[r - 1];
			}
			R2TPCstdpre[0] = rstd;
			checkr2tpcstdprea1 = false;
		}

		//R1TDCs
		static double R1TDCstdpre[101];

		static datetime checkr1tdcstdpre1 = 0;
		bool checkr1tdcstdprea1 = false;
		if (checkr1tdcstdpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr1tdcstdpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr1tdcstdprea1 = true;
		}
		if (checkr1tdcstdprea1)
		{
			double rstd = 0;
			rstd = CalculateDistanceCorrelation(R1pre, Totalpre, 31);

			for (int r = 100; r >= 1; r--)
			{
				R1TDCstdpre[r] = R1TDCstdpre[r - 1];
			}
			R1TDCstdpre[0] = rstd;
			checkr1tdcstdprea1 = false;
		}

		//R2TDCs
		static double R2TDCstdpre[101];

		static datetime checkr2tdcstdpre1 = 0;
		bool checkr2tdcstdprea1 = false;
		if (checkr2tdcstdpre1 < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkr2tdcstdpre1 = iTime(_Symbol, PERIOD_M1, 0);
			checkr2tdcstdprea1 = true;
		}
		if (checkr2tdcstdprea1)
		{
			double rstd = 0;
			rstd = CalculateDistanceCorrelation(R2pre, Totalpre, 31);

			for (int r = 100; r >= 1; r--)
			{
				R2TDCstdpre[r] = R2TDCstdpre[r - 1];
			}
			R2TDCstdpre[0] = rstd;
			checkr2tdcstdprea1 = false;
		}

		obname = Name + "R1";
		LabelMake(obname, 0, f + 520, 20, "r", 8, clrWhite);
		obname = Name + "R1A";
		LabelMake(obname, 0, f + 555, 20, "r1a", 8, clrWhite);
		obname = Name + "R1TPC";
		LabelMake(obname, 0, f + 590, 20, "r1tPC", 8, clrWhite);
		obname = Name + "R1TDC";
		LabelMake(obname, 0, f + 625, 20, "r1tDC", 8, clrWhite);
		obname = Name + "R2TPC";
		LabelMake(obname, 0, f + 660, 20, "r2tPC", 8, clrWhite);
		obname = Name + "R2TDC";
		LabelMake(obname, 0, f + 695, 20, "r2tDC", 8, clrWhite);
		obname = Name + "R2";
		LabelMake(obname, 0, f + 730, 20, "r2", 8, clrWhite);
		obname = Name + "STAR";
		LabelMake(obname, 0, f + 765, 20, "star", 8, clrWhite);
		obname = Name + "R4";
		LabelMake(obname, 0, f + 800, 20, "R4", 8, clrWhite);
		obname = Name + "RC";
		LabelMake(obname, 0, f + 835, 20, "RC", 8, clrWhite);
		obname = Name + "R5";
		LabelMake(obname, 0, f + 870, 20, "R5", 8, clrWhite);
		obname = Name + "HLAR";
		LabelMake(obname, 0, f + 905, 20, "retr", 8, clrWhite);
		obname = Name + "ACT";
		LabelMake(obname, 0, f + 940, 20, "act%", 8, clrWhite);
		obname = Name + "TOTAL";
		LabelMake(obname, 0, f + 975, 20, "tot", 8, clrWhite);
		obname = Name + "EQ";
		LabelMake(obname, 0, f + 1015, 20, "eq", 8, clrWhite);

      R1pre[0] = R;
      R1avgpre[0] = r1avg;
      R1TPCstdpre[0] = CalculatePearsonCorrelation(R1pre, Totalpre, 31);
      R1TDCstdpre[0] = CalculateDistanceCorrelation(R1pre, Totalpre, 31);
      R2TPCstdpre[0] = CalculatePearsonCorrelation(R2pre, Totalpre, 31);
      R2TDCstdpre[0] = CalculateDistanceCorrelation(R2pre, Totalpre, 31);
      R2pre[0] = skunk;
      R4pre[0] = R4;
      RCpre[0] = RC;
      R5pre[0] = R5;      

		for (int p = 45; p >= 0; p--)
		{
			obname = Name + "time1ENTRY" + IntegerToString(p);
			LabelMake(obname, 0, f + 485, 30 + p * 10, TimeToString(TimeCurrent() - p * 60, TIME_MINUTES), 8, clrWhite);

			obname = Name + "R1" + IntegerToString(p);
			LabelMake(obname, 0, f + 520, 30 + p * 10, DoubleToString(R1pre[p], 2), 8, clrYellow);
			if (R1pre[p] > R1pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (R1pre[p] < R1pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (R1pre[p] > R1pre[p + 1] && R1pre[p + 1] > R1pre[p + 2] && R1pre[p + 2] > R1pre[p + 3]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);

			obname = Name + "R1A" + IntegerToString(p);
			LabelMake(obname, 0, f + 555, 30 + p * 10, DoubleToString(R1avgpre[p], 2), 8, clrYellow);
			if (R1avgpre[p] < 1.00) ObjectSetString(0, obname, OBJPROP_TEXT, "");
			else if (R1avgpre[p] <= R1avgpre[p + 1] && R1avgpre[p] >= 1.00) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			else if (R1avgpre[p] > R1avgpre[p + 1] && R1avgpre[p] >= 1.00 && R1avgpre[p] < 1.10) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
			else if (R1avgpre[p] > R1avgpre[p + 1] && R1avgpre[p] >= 1.10 && R1avgpre[p] < 2.00) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (R1avgpre[p] > R1avgpre[p + 1] && R1avgpre[p] >= 2.00) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);

			obname = Name + "R1TPC" + IntegerToString(p);
			LabelMake(obname, 0, f + 590, 30 + p * 10, DoubleToString(R1TPCstdpre[p], 2), 8, clrYellow);
			if (R1TPCstdpre[p] >= 0.70) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			obname = Name + "R1TDC" + IntegerToString(p);
			LabelMake(obname, 0, f + 625, 30 + p * 10, DoubleToString(R1TDCstdpre[p], 2), 8, clrYellow);
			if (R1TDCstdpre[p] >= 0.70) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			obname = Name + "R2TPC" + IntegerToString(p);
			LabelMake(obname, 0, f + 660, 30 + p * 10, DoubleToString(R2TPCstdpre[p], 2), 8, clrYellow);
			if (R2TPCstdpre[p] >= 0.70) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			obname = Name + "R2TDC" + IntegerToString(p);
			LabelMake(obname, 0, f + 695, 30 + p * 10, DoubleToString(R2TDCstdpre[p], 2), 8, clrYellow);
			if (R2TDCstdpre[p] >= 0.70) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			obname = Name + "R2" + IntegerToString(p);
			LabelMake(obname, 0, f + 730, 30 + p * 10, DoubleToString(R2pre[p], 2), 8, clrYellow);
			if (R2pre[p] > R2pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (R2pre[p] <= R2pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			obname = Name + "RSTAR" + IntegerToString(p);
			LabelMake(obname, 0, f + 765, 30 + p * 10, "", 8, clrYellow);
			if (R1avgpre[p] >= 1.10)
			{
				ObjectSetText(obname, CharToStr(254), 10, "Wingdings", clrAqua); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}
			else
			{
				ObjectSetText(obname, CharToStr(253), 10, "Wingdings", clrPink); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}
			if (R1stdpre[p] > R1stdpre[p + 1] && R1avgpre[p] >= 1.00 && HLTrpre[p] > HLTrpre[p + 1] && ACTrpre[p] < ACTrpre[p + 1])
			{
				ObjectSetText(obname, CharToStr(89), 10, "Wingdings", clrDodgerBlue); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}

			obname = Name + "RSTARb" + IntegerToString(p);
			LabelMake(obname, 0, f + 780, 30 + p * 10, "", 8, clrYellow);
			if (R1pre[p] > R1pre[p + 1] && R3pre[p] < R3pre[p + 1] && HLTrpre[p] < HLTrpre[p + 1] && ACTrpre[p] > ACTrpre[p + 1] && RRRpre[p] > 1
				&& R1stdpre[p + 1] > R1stdpre[p + 2] && R1avgpre[p + 1] >= 1.00 && HLTrpre[p + 1] > HLTrpre[p + 2] && ACTrpre[p + 1] < ACTrpre[p + 2])
			{
				ObjectSetText(obname, CharToStr(236), 9, "Wingdings", clrGold); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}
			else if (R1pre[p] < R1pre[p + 1] && R3pre[p] > R3pre[p + 1] && HLTrpre[p] < HLTrpre[p + 1] && ACTrpre[p] > ACTrpre[p + 1] && RRRpre[p] > 1
				&& R1stdpre[p + 1] > R1stdpre[p + 2] && R1avgpre[p + 1] >= 1.00 && HLTrpre[p + 1] > HLTrpre[p + 2] && ACTrpre[p + 1] < ACTrpre[p + 2])
			{
				ObjectSetText(obname, CharToStr(233), 8, "Wingdings", clrRed); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}
			else if (R1pre[p] > R1pre[p + 1] && R3pre[p] < R3pre[p + 1] && ((HLTrpre[p] < HLTrpre[p + 1] && ACTrpre[p] < ACTrpre[p + 1]) || (HLTrpre[p] > HLTrpre[p + 1] && ACTrpre[p] > ACTrpre[p + 1])) && RRRpre[p] > 1
				&& R1stdpre[p + 1] > R1stdpre[p + 2] && R1avgpre[p + 1] >= 1.00 && HLTrpre[p + 1] > HLTrpre[p + 2] && ACTrpre[p + 1] < ACTrpre[p + 2])
			{
				ObjectSetText(obname, CharToStr(234), 9, "Wingdings", clrGreen); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}
			else if (R1stdpre[p] > R1stdpre[p + 1] && R1avgpre[p] >= 1.00 && R1pre[p] < R1pre[p + 1] && R3pre[p] > R3pre[p + 1] && HLTrpre[p] > HLTrpre[p + 1] && ACTrpre[p] < ACTrpre[p + 1] && RRRpre[p] > 1)
			{
				ObjectSetText(obname, CharToStr(232), 9, "Wingdings", clrOrange); ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_TOP);
			}

			obname = Name + "R4" + IntegerToString(p);
			LabelMake(obname, 0, f + 800, 30 + p * 10, DoubleToString(R4pre[p], 2), 8, clrGray);
			if (R4pre[p] > R4pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);

			obname = Name + "RC" + IntegerToString(p);
			LabelMake(obname, 0, f + 835, 30 + p * 10, DoubleToString(RCpre[p], 2), 8, clrGray);
			if (RCpre[p] > RCpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);

			obname = Name + "R5" + IntegerToString(p);
			LabelMake(obname, 0, f + 870, 30 + p * 10, DoubleToString(R5pre[p], 2), 8, clrGray);
			if (R5 > R5o && R5pre[p] > R5pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			if (R5 > R5o && R5pre[p] < R5pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
			if (R5 < R5o && R5pre[p] > R5pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
			if (R5 < R5o && R5pre[p] < R5pre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
		}

      RHLCpre[0] = frecretr;
      ACTrpre[0] = percdata["DayAllA"][0].ToDbl();
      Totalpre[0] = totti;
      EQpre[0] = total;

		for (int p = 85; p >= 0; p--)
		{
			obname = Name + "HLAr" + IntegerToString(p);
			LabelMake(obname, 0, f + 905, 30 + p * 10, DoubleToString(RHLCpre[p], 1), 8, clrYellow);
			if (RHLCpre[p] < RHLCpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (RHLCpre[p] >= RHLCpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (RHLCpre[p] < RHLCpre[p + 1] && RHLCpre[p + 1] > RHLCpre[p + 2] && RHLCpre[p + 2] > RHLCpre[p + 3]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (p <= 30 && RHLCpre[p] == RHLCpre[ArrayMinimum(RHLCpre, 30, 0)]) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); ObjectSetString(0, obname, OBJPROP_TEXT, DoubleToString(RHLCpre[p], 1) + "*"); }
			if (p <= 30 && RHLCpre[p] == RHLCpre[ArrayMaximum(RHLCpre, 30, 0)]) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite); ObjectSetString(0, obname, OBJPROP_TEXT, DoubleToString(RHLCpre[p], 1) + "*"); }

			obname = Name + "ACT" + IntegerToString(p);
			LabelMake(obname, 0, f + 940, 30 + p * 10, DoubleToString(ACTrpre[p], 2), 8, clrYellow);
			if (ACTrpre[p] > ACTrpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (ACTrpre[p] <= ACTrpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ACTrpre[p] > ACTrpre[p + 1] && ACTrpre[p + 1] < ACTrpre[p + 2] && ACTrpre[p + 2] < ACTrpre[p + 3]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ACTrpre[p] >= 50) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);

			obname = Name + "TOTAL" + IntegerToString(p);
			LabelMake(obname, 0, f + 975, 30 + p * 10, "$" + DoubleToString(Totalpre[p], 0), 8, clrYellow);
			if (Totalpre[p] < 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			else if (Totalpre[p] >= 2000 && Totalpre[p] < 12000)
			{
				if ((Totalpre[p] < Totalpre[p + 1] + 0.618 * Recpre[p + 1]) && (Totalpre[p] > Totalpre[p + 1] - 0.618 * Recpre[p + 1])) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrWhite);
				else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
			}
			else if (Totalpre[p] >= 12000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (Totalpre[p] < Brecpre[p]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);

			obname = Name + "EQ" + IntegerToString(p);
			LabelMake(obname, 0, f + 1015, 30 + p * 10, "$" + DoubleToString(EQpre[p], 0), 8, clrYellow);
			ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
			if (EQpre[p] > EQpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
			else if (EQpre[p] < EQpre[p + 1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (EQpre[p] == 0) ObjectSetString(0, obname, OBJPROP_TEXT, ""); //
		}

		//areas
		static double eurarea[31];
		static double ocearea[31];
		static double risarea[31];
		static double namarea[31];
		static double asiarea[31];
		static double feuarea[31];
		static double friarea[31];
		static double comarea[31];

		//pc
		static double euroce[12];
		static double eurris[12];
		static double eurnam[12];
		static double oceris[12];
		static double ocenam[12];
		static double namris[12];
		static double feucom[12];
		static double feunam[12];
		static double feuasi[12];
		static double feufri[12];
		static double comfri[12];
		static double namasi[12];

		//dc
		static double euroce_d[12];
		static double eurris_d[12];
		static double eurnam_d[12];
		static double oceris_d[12];
		static double ocenam_d[12];
		static double namris_d[12];
		static double feucom_d[12];
		static double feunam_d[12];
		static double feuasi_d[12];
		static double feufri_d[12];
		static double comfri_d[12];
		static double namasi_d[12];

		//price
		static double eurocecur[12];
		static double eurriscur[12];
		static double eurnamcur[12];
		static double oceriscur[12];
		static double ocenamcur[12];
		static double namriscur[12];
		static double feucomcur[12];
		static double feunamcur[12];
		static double feuasicur[12];
		static double feufricur[12];
		static double comfricur[12];
		static double namasicur[12];

		//5m avg
		static double euroce5m[12];
		static double eurris5m[12];
		static double eurnam5m[12];
		static double oceris5m[12];
		static double ocenam5m[12];
		static double namris5m[12];
		static double feucom5m[12];
		static double feunam5m[12];
		static double feuasi5m[12];
		static double feufri5m[12];
		static double comfri5m[12];
		static double namasi5m[12];

		//mid-areas
		static double eguccur[31];
		static double ucfjcur[31];
		static double fjancur[31];
		static double egancur[31];
		static double egfjcur[31];
		static double ucancur[31];

		static double egvfjan[12];
		static double egvucan[12];
		static double egvucfj[12];
		static double anvegfj[12];
		static double anveguc[12];
		static double anvucfj[12];
		static double ucvfjan[12];
		static double ucvegan[12];
		static double ucvegfj[12];
		static double fjvegan[12];
		static double fjveguc[12];
		static double fjvucan[12];

		static double egvfjan_d[12];
		static double egvucan_d[12];
		static double egvucfj_d[12];
		static double anvegfj_d[12];
		static double anveguc_d[12];
		static double anvucfj_d[12];
		static double ucvfjan_d[12];
		static double ucvegan_d[12];
		static double ucvegfj_d[12];
		static double fjvegan_d[12];
		static double fjveguc_d[12];
		static double fjvucan_d[12];

		static double egvfjancur[12];
		static double egvucancur[12];
		static double egvucfjcur[12];
		static double anvegfjcur[12];
		static double anveguccur[12];
		static double anvucfjcur[12];
		static double ucvfjancur[12];
		static double ucvegancur[12];
		static double ucvegfjcur[12];
		static double fjvegancur[12];
		static double fjveguccur[12];
		static double fjvucancur[12];

		static double egvfjan5m[12];
		static double egvucan5m[12];
		static double egvucfj5m[12];
		static double anvegfj5m[12];
		static double anveguc5m[12];
		static double anvucfj5m[12];
		static double ucvfjan5m[12];
		static double ucvegan5m[12];
		static double ucvegfj5m[12];
		static double fjvegan5m[12];
		static double fjveguc5m[12];
		static double fjvucan5m[12];

		//big areas
		//static double eurcur[31];
		//static double comcur[31];
		//static double riscur[31];
		static double gancur[31];
		static double eucfjcur[31];

		static double gacur[31]; //ga
		static double nucfjcur[31];
		static double eucur[31]; //eu
		static double ganfjcur[31];
		static double gccur[31]; //gc
		static double eanfjcur[31];
		static double efcur[31]; //ef
		static double anucjcur[31];
		static double nccur[31]; //nc
		static double egufjcur[31];
		static double ajcur[31]; //aj
		static double egucfcur[31];
		static double ujcur[31]; //uj
		static double eganccur[31];

		static double eurvanucj[12]; //eur
		static double comvegufj[12]; //com
		static double risveganc[12]; //risk
		static double ganveucfj[12]; //gan
		static double asivegucf[12]; //asia
		//-
		static double gavnucfj[12]; //ga
		static double euvganfj[12]; //eu
		static double gcveanfj[12]; //gc
		static double efvanucj[12]; //ef
		static double ajvegucf[12]; //aj
		static double ncvegufj[12]; //nc
		static double ujveganc[12]; //uj

		static double eurvanucj_d[12]; //eur
		static double comvegufj_d[12]; //com
		static double risveganc_d[12]; //risk
		static double ganveucfj_d[12]; //gan
		static double asivegucf_d[12]; //asia
		//-
		static double gavnucfj_d[12]; //ga
		static double euvganfj_d[12]; //eu
		static double gcveanfj_d[12]; //gc
		static double efvanucj_d[12]; //ef
		static double ajvegucf_d[12]; //aj
		static double ncvegufj_d[12]; //nc
		static double ujveganc_d[12]; //uj

		static double eurvanucjcur[12]; //eur
		static double comvegufjcur[12]; //com
		static double risveganccur[12]; //risk
		static double ganveucfjcur[12]; //gan
		static double asivegucfcur[12]; //asia
		//-
		static double gavnucfjcur[12]; //ga
		static double euvganfjcur[12]; //eu
		static double gcveanfjcur[12]; //gc
		static double efvanucjcur[12]; //ef
		static double ajvegucfcur[12]; //aj
		static double ncvegufjcur[12]; //nc
		static double ujveganccur[12]; //uj

		static double eurvanucj5m[12]; //eur
		static double comvegufj5m[12]; //com
		static double risveganc5m[12]; //risk
		static double ganveucfj5m[12]; //gan
		static double asivegucf5m[12]; //asia
		//-
		static double gavnucfj5m[12]; //ga
		static double euvganfj5m[12]; //eu
		static double gcveanfj5m[12]; //gc
		static double efvanucj5m[12]; //ef
		static double ajvegucf5m[12]; //aj
		static double ncvegufj5m[12]; //nc
		static double ujveganc5m[12]; //uj

		static datetime checkbasket = 0;
		bool checkbasket1 = false;
		if (checkbasket < iTime(_Symbol, PERIOD_M1, 0))
		{
			checkbasket = iTime(_Symbol, PERIOD_M1, 0);
			checkbasket1 = true;
		}
		if (checkbasket1)
		{
			for (int r = 30; r >= 1; r--)
			{
				eurarea[r] = eurarea[r - 1];
				ocearea[r] = ocearea[r - 1];
				risarea[r] = risarea[r - 1];
				namarea[r] = namarea[r - 1];
				asiarea[r] = asiarea[r - 1];
				feuarea[r] = feuarea[r - 1];
				friarea[r] = friarea[r - 1];
				comarea[r] = comarea[r - 1];

				eguccur[r] = eguccur[r - 1];
				ucfjcur[r] = ucfjcur[r - 1];
				fjancur[r] = fjancur[r - 1];
				egancur[r] = egancur[r - 1];
				egfjcur[r] = egfjcur[r - 1];
				ucancur[r] = ucancur[r - 1];

				// eurcur[r] = [r - 1];
				anucjcur[r] = anucjcur[r - 1];
				// comcur[r] = [r - 1];
				egufjcur[r] = egufjcur[r - 1];
				// riscur[r] = [r - 1];
				eganccur[r] = eganccur[r - 1];
				gancur[r] = gancur[r - 1];
				eucfjcur[r] = eucfjcur[r - 1];

				gacur[r] = gacur[r - 1]; //ga
				nucfjcur[r] = nucfjcur[r - 1];
				eucur[r] = eucur[r - 1]; //eu
				ganfjcur[r] = ganfjcur[r - 1];
				gccur[r] = gccur[r - 1]; //gc
				eanfjcur[r] = eanfjcur[r - 1];
				efcur[r] = efcur[r - 1]; //ef
				// anucjcur[r] = [r - 1];
				nccur[r] = nccur[r - 1]; //nc
				// egufjcur[r] = [r - 1];
				ajcur[r] = ajcur[r - 1]; //aj
				egucfcur[r] = egucfcur[r - 1];
				ujcur[r] = ujcur[r - 1]; //uj
				// eganccur[r] = [r - 1];
			}

			for (int x = 11; x >= 1; x--)
			{
				euroce[x] = euroce[x - 1];
				eurris[x] = eurris[x - 1];
				eurnam[x] = eurnam[x - 1];
				oceris[x] = oceris[x - 1];
				ocenam[x] = ocenam[x - 1];
				namris[x] = namris[x - 1];
				feucom[x] = feucom[x - 1];
				feunam[x] = feunam[x - 1];
				feuasi[x] = feuasi[x - 1];
				feufri[x] = feufri[x - 1];
				comfri[x] = comfri[x - 1];
				namasi[x] = namasi[x - 1];

				euroce_d[x] = euroce_d[x - 1];
				eurris_d[x] = eurris_d[x - 1];
				eurnam_d[x] = eurnam_d[x - 1];
				oceris_d[x] = oceris_d[x - 1];
				ocenam_d[x] = ocenam_d[x - 1];
				namris_d[x] = namris_d[x - 1];
				feucom_d[x] = feucom_d[x - 1];
				feunam_d[x] = feunam_d[x - 1];
				feuasi_d[x] = feuasi_d[x - 1];
				feufri_d[x] = feufri_d[x - 1];
				comfri_d[x] = comfri_d[x - 1];
				namasi_d[x] = namasi_d[x - 1];

				egvfjan[x] = egvfjan[x - 1];
				egvucan[x] = egvucan[x - 1];
				egvucfj[x] = egvucfj[x - 1];
				anvegfj[x] = anvegfj[x - 1];
				anveguc[x] = anveguc[x - 1];
				anvucfj[x] = anvucfj[x - 1];
				ucvfjan[x] = ucvfjan[x - 1];
				ucvegan[x] = ucvegan[x - 1];
				ucvegfj[x] = ucvegfj[x - 1];
				fjvegan[x] = fjvegan[x - 1];
				fjveguc[x] = fjveguc[x - 1];
				fjvucan[x] = fjvucan[x - 1];

				egvfjan_d[x] = egvfjan_d[x - 1];
				egvucan_d[x] = egvucan_d[x - 1];
				egvucfj_d[x] = egvucfj_d[x - 1];
				anvegfj_d[x] = anvegfj_d[x - 1];
				anveguc_d[x] = anveguc_d[x - 1];
				anvucfj_d[x] = anvucfj_d[x - 1];
				ucvfjan_d[x] = ucvfjan_d[x - 1];
				ucvegan_d[x] = ucvegan_d[x - 1];
				ucvegfj_d[x] = ucvegfj_d[x - 1];
				fjvegan_d[x] = fjvegan_d[x - 1];
				fjveguc_d[x] = fjveguc_d[x - 1];
				fjvucan_d[x] = fjvucan_d[x - 1];

				eurvanucj[x] = eurvanucj[x - 1]; //eur
				comvegufj[x] = comvegufj[x - 1]; //coms
				risveganc[x] = risveganc[x - 1]; //risk
				ganveucfj[x] = ganveucfj[x - 1]; //gan
				asivegucf[x] = asivegucf[x - 1]; //asia
				//-
				gavnucfj[x] = gavnucfj[x - 1]; //ga
				euvganfj[x] = euvganfj[x - 1]; //eu
				gcveanfj[x] = gcveanfj[x - 1]; //gc
				efvanucj[x] = efvanucj[x - 1]; //ef
				ncvegufj[x] = ncvegufj[x - 1]; //nc
				ajvegucf[x] = ajvegucf[x - 1]; //aj
				ujveganc[x] = ujveganc[x - 1]; //uj

				eurvanucj_d[x] = eurvanucj_d[x - 1]; //eur
				comvegufj_d[x] = comvegufj_d[x - 1]; //coms
				risveganc_d[x] = risveganc_d[x - 1]; //risk
				ganveucfj_d[x] = ganveucfj_d[x - 1]; //gan
				asivegucf_d[x] = asivegucf_d[x - 1]; //asia
				//-
				gavnucfj_d[x] = gavnucfj_d[x - 1]; //ga
				euvganfj_d[x] = euvganfj_d[x - 1]; //eu
				gcveanfj_d[x] = gcveanfj_d[x - 1]; //gc
				efvanucj_d[x] = efvanucj_d[x - 1]; //ef
				ncvegufj_d[x] = ncvegufj_d[x - 1]; //nc
				ajvegucf_d[x] = ajvegucf_d[x - 1]; //aj
				ujveganc_d[x] = ujveganc_d[x - 1]; //uj
			}

			for (int x = 11; x >= 1; x--)
			{
				eurocecur[x] = eurocecur[x - 1];
				eurriscur[x] = eurriscur[x - 1];
				eurnamcur[x] = eurnamcur[x - 1];
				oceriscur[x] = oceriscur[x - 1];
				ocenamcur[x] = ocenamcur[x - 1];
				namriscur[x] = namriscur[x - 1];
				feucomcur[x] = feucomcur[x - 1];
				feunamcur[x] = feunamcur[x - 1];
				feuasicur[x] = feuasicur[x - 1];
				feufricur[x] = feufricur[x - 1];
				comfricur[x] = comfricur[x - 1];
				namasicur[x] = namasicur[x - 1];

				egvfjancur[x] = egvfjancur[x - 1];
				egvucancur[x] = egvucancur[x - 1];
				egvucfjcur[x] = egvucfjcur[x - 1];
				anvegfjcur[x] = anvegfjcur[x - 1];
				anveguccur[x] = anveguccur[x - 1];
				anvucfjcur[x] = anvucfjcur[x - 1];
				ucvfjancur[x] = ucvfjancur[x - 1];
				ucvegancur[x] = ucvegancur[x - 1];
				ucvegfjcur[x] = ucvegfjcur[x - 1];
				fjvegancur[x] = fjvegancur[x - 1];
				fjveguccur[x] = fjveguccur[x - 1];
				fjvucancur[x] = fjvucancur[x - 1];

				eurvanucjcur[x] = eurvanucjcur[x - 1]; //eur
				comvegufjcur[x] = comvegufjcur[x - 1]; //com
				risveganccur[x] = risveganccur[x - 1]; //risk
				ganveucfjcur[x] = ganveucfjcur[x - 1]; //gan
				asivegucfcur[x] = asivegucfcur[x - 1]; //asia
				//-
				gavnucfjcur[x] = gavnucfjcur[x - 1]; //ga
				euvganfjcur[x] = euvganfjcur[x - 1]; //eu
				gcveanfjcur[x] = gcveanfjcur[x - 1]; //gc
				efvanucjcur[x] = efvanucjcur[x - 1]; //ef
				ajvegucfcur[x] = ajvegucfcur[x - 1]; //aj
				ncvegufjcur[x] = ncvegufjcur[x - 1]; //nc
				ujveganccur[x] = ujveganccur[x - 1]; //uj
			}

			for (int x = 10; x >= 1; x--)
			{
				euroce5m[x] = euroce5m[x - 1];
				eurris5m[x] = eurris5m[x - 1];
				eurnam5m[x] = eurnam5m[x - 1];
				oceris5m[x] = oceris5m[x - 1];
				ocenam5m[x] = ocenam5m[x - 1];
				namris5m[x] = namris5m[x - 1];
				feucom5m[x] = feucom5m[x - 1];
				feunam5m[x] = feucom5m[x - 1];
				feuasi5m[x] = feuasi5m[x - 1];
				feufri5m[x] = feufri5m[x - 1];
				comfri5m[x] = comfri5m[x - 1];
				namasi5m[x] = namasi5m[x - 1];

				egvfjan5m[x] = egvfjan5m[x - 1];
				egvucan5m[x] = egvucan5m[x - 1];
				egvucfj5m[x] = egvucfj5m[x - 1];
				anvegfj5m[x] = anvegfj5m[x - 1];
				anveguc5m[x] = anveguc5m[x - 1];
				anvucfj5m[x] = anvucfj5m[x - 1];
				ucvfjan5m[x] = ucvfjan5m[x - 1];
				ucvegan5m[x] = ucvegan5m[x - 1];
				ucvegfj5m[x] = ucvegfj5m[x - 1];
				fjvegan5m[x] = fjvegan5m[x - 1];
				fjveguc5m[x] = fjveguc5m[x - 1];
				fjvucan5m[x] = fjvucan5m[x - 1];

				eurvanucj5m[x] = eurvanucj5m[x - 1]; //eur
				comvegufj5m[x] = comvegufj5m[x - 1]; //com
				risveganc5m[x] = risveganc5m[x - 1]; //risk
				ganveucfj5m[x] = ganveucfj5m[x - 1]; //gan
				asivegucf5m[x] = asivegucf5m[x - 1]; //asia
				//-
				gavnucfj5m[x] = gavnucfj5m[x - 1]; //ga
				euvganfj5m[x] = euvganfj5m[x - 1]; //eu
				gcveanfj5m[x] = gcveanfj5m[x - 1]; //gc
				efvanucj5m[x] = efvanucj5m[x - 1]; //ef
				ajvegucf5m[x] = ajvegucf5m[x - 1]; //aj
				ncvegufj5m[x] = ncvegufj5m[x - 1]; //nc
				ujveganc5m[x] = ujveganc5m[x - 1]; //uj
			}

			euroce5m[0] = 0;
			eurris5m[0] = 0;
			eurnam5m[0] = 0;
			oceris5m[0] = 0;
			ocenam5m[0] = 0;
			namris5m[0] = 0;
			feucom5m[0] = 0;
			feunam5m[0] = 0;
			feuasi5m[0] = 0;
			feufri5m[0] = 0;
			comfri5m[0] = 0;
			namasi5m[0] = 0;

			egvfjan5m[0] = 0;
			egvucan5m[0] = 0;
			egvucfj5m[0] = 0;
			anvegfj5m[0] = 0;
			anveguc5m[0] = 0;
			anvucfj5m[0] = 0;
			ucvfjan5m[0] = 0;
			ucvegan5m[0] = 0;
			ucvegfj5m[0] = 0;
			fjvegan5m[0] = 0;
			fjveguc5m[0] = 0;
			fjvucan5m[0] = 0;

			eurvanucj5m[0] = 0; //eur
			comvegufj5m[0] = 0; //com
			risveganc5m[0] = 0; //risk
			ganveucfj5m[0] = 0; //gan
			asivegucf5m[0] = 0; //asia
			//-
			gavnucfj5m[0] = 0; //ga
			euvganfj5m[0] = 0; //eu
			gcveanfj5m[0] = 0; //gc
			efvanucj5m[0] = 0; //ef
			ajvegucf5m[0] = 0; //aj
			ncvegufj5m[0] = 0; //nc
			ujveganc5m[0] = 0; //uj

			for (int x = 5; x >= 1; x--)
			{
				euroce5m[0] += eurocecur[x] / 5;
				eurris5m[0] += eurriscur[x] / 5;
				eurnam5m[0] += eurnamcur[x] / 5;
				oceris5m[0] += oceriscur[x] / 5;
				ocenam5m[0] += ocenamcur[x] / 5;
				namris5m[0] += namriscur[x] / 5;
				feucom5m[0] += feucomcur[x] / 5;
				feunam5m[0] += feunamcur[x] / 5;
				feuasi5m[0] += feuasicur[x] / 5;
				feufri5m[0] += feufricur[x] / 5;
				comfri5m[0] += comfricur[x] / 5;
				namasi5m[0] += namasicur[x] / 5;

				egvfjan5m[0] += egvfjancur[x] / 5;
				egvucan5m[0] += egvucancur[x] / 5;
				egvucfj5m[0] += egvucfjcur[x] / 5;
				anvegfj5m[0] += anvegfjcur[x] / 5;
				anveguc5m[0] += anveguccur[x] / 5;
				anvucfj5m[0] += anvucfjcur[x] / 5;
				ucvfjan5m[0] += ucvfjancur[x] / 5;
				ucvegan5m[0] += ucvegancur[x] / 5;
				ucvegfj5m[0] += ucvegfjcur[x] / 5;
				fjvegan5m[0] += fjvegancur[x] / 5;
				fjveguc5m[0] += fjveguccur[x] / 5;
				fjvucan5m[0] += fjvucancur[x] / 5;

				eurvanucj5m[0] += eurvanucjcur[x] / 5; //eur
				comvegufj5m[0] += comvegufjcur[x] / 5; //com
				risveganc5m[0] += risveganccur[x] / 5; //risk
				ganveucfj5m[0] += ganveucfjcur[x] / 5; //gan
				asivegucf5m[0] += asivegucfcur[x] / 5; //asia
				//-
				gavnucfj5m[0] += gavnucfjcur[x] / 5; //ga
				euvganfj5m[0] += euvganfjcur[x] / 5; //eu
				gcveanfj5m[0] += gcveanfjcur[x] / 5; //gc
				efvanucj5m[0] += efvanucjcur[x] / 5; //ef
				ajvegucf5m[0] += ajvegucfcur[x] / 5; //aj
				ncvegufj5m[0] += ncvegufjcur[x] / 5; //nc
				ujveganc5m[0] += ujveganccur[x] / 5; //uj
			}
			checkbasket1 = false;
		}

		double eurbas = basketdata["eur"].ToDbl();
		double gbpbas = basketdata["gbp"].ToDbl();
		double audbas = basketdata["aud"].ToDbl();
		double nzdbas = basketdata["nzd"].ToDbl();
		double usdbas = basketdata["usd"].ToDbl();
		double cadbas = basketdata["cad"].ToDbl();
		double chfbas = basketdata["chf"].ToDbl();
		double jpybas = basketdata["jpy"].ToDbl();

		double egp = basketdata["eg"].ToDbl();
		double eap = basketdata["ea"].ToDbl();
		double enp = basketdata["en"].ToDbl();
		double eup = basketdata["eu"].ToDbl();
		double ecp = basketdata["ec"].ToDbl();
		double efp = basketdata["ef"].ToDbl();
		double ejp = basketdata["ej"].ToDbl();
		double gap = basketdata["ga"].ToDbl();
		double gnp = basketdata["gn"].ToDbl();
		double gup = basketdata["gu"].ToDbl();
		double gcp = basketdata["gc"].ToDbl();
		double gfp = basketdata["gf"].ToDbl();
		double gjp = basketdata["gj"].ToDbl();
		double anp = basketdata["an"].ToDbl();
		double aup = basketdata["au"].ToDbl();
		double acp = basketdata["ac"].ToDbl();
		double afp = basketdata["af"].ToDbl();
		double ajp = basketdata["aj"].ToDbl();
		double nup = basketdata["nu"].ToDbl();
		double ncp = basketdata["nc"].ToDbl();
		double nfp = basketdata["nf"].ToDbl();
		double njp = basketdata["nj"].ToDbl();
		double ucp = basketdata["uc"].ToDbl();
		double cfp = basketdata["cf"].ToDbl();
		double cjp = basketdata["cj"].ToDbl();
		double ufp = basketdata["uf"].ToDbl();
		double fjp = basketdata["fj"].ToDbl();
		double ujp = basketdata["uj"].ToDbl();

		//AREAS
		eurarea[0] = +eap + enp + eup + ecp + efp + ejp + gap + gnp + gup + gcp + gfp + gjp;
		ocearea[0] = -eap - gap + aup + acp + afp + ajp - enp - gnp + nup + ncp + nfp + njp;
		namarea[0] = -eup - gup - aup - nup + ufp + ujp - ecp - gcp - acp - ncp + cfp + cjp;
		risarea[0] = -efp - gfp - afp - nfp - ufp - cfp - ejp - gjp - ajp - njp - ujp - cjp;
		//-
		asiarea[0] = -eap - gap + aup + acp + afp - anp - gnp + nup + ncp + nfp - ejp - gjp - ujp - cjp - fjp;
		feuarea[0] = +eap + enp + eup + ecp + ejp + gap + gnp + gup + gcp + gjp - afp - nfp - ufp - cfp + fjp;
		friarea[0] = -eup - gup - aup - nup + ucp - efp - gfp - afp - nfp - cfp - ejp - gjp - ajp - njp - cjp;
		comarea[0] = -eap - gap + aup + afp + ajp - anp - gnp + nup + nfp + njp - ecp - gcp - ucp + cfp + cjp;

		euroce[0] = CalculatePearsonCorrelation(eurarea, ocearea, 31);
		eurris[0] = CalculatePearsonCorrelation(eurarea, risarea, 31);
		eurnam[0] = CalculatePearsonCorrelation(eurarea, namarea, 31);
		oceris[0] = CalculatePearsonCorrelation(ocearea, risarea, 31);
		ocenam[0] = CalculatePearsonCorrelation(ocearea, namarea, 31);
		namris[0] = CalculatePearsonCorrelation(namarea, risarea, 31);
		//-
		feucom[0] = CalculatePearsonCorrelation(feuarea, comarea, 31);
		feunam[0] = CalculatePearsonCorrelation(feuarea, namarea, 31);
		feuasi[0] = CalculatePearsonCorrelation(feuarea, asiarea, 31);
		feufri[0] = CalculatePearsonCorrelation(feuarea, friarea, 31);
		comfri[0] = CalculatePearsonCorrelation(comarea, friarea, 31);
		namasi[0] = CalculatePearsonCorrelation(namarea, asiarea, 31);

		euroce_d[0] = CalculateDistanceCorrelation(eurarea, ocearea, 31);
		eurris_d[0] = CalculateDistanceCorrelation(eurarea, risarea, 31);
		eurnam_d[0] = CalculateDistanceCorrelation(eurarea, namarea, 31);
		oceris_d[0] = CalculateDistanceCorrelation(ocearea, risarea, 31);
		ocenam_d[0] = CalculateDistanceCorrelation(ocearea, namarea, 31);
		namris_d[0] = CalculateDistanceCorrelation(namarea, risarea, 31);
		//-
		feucom_d[0] = CalculateDistanceCorrelation(feuarea, comarea, 31);
		feunam_d[0] = CalculateDistanceCorrelation(feuarea, namarea, 31);
		feuasi_d[0] = CalculateDistanceCorrelation(feuarea, asiarea, 31);
		feufri_d[0] = CalculateDistanceCorrelation(feuarea, friarea, 31);
		comfri_d[0] = CalculateDistanceCorrelation(comarea, friarea, 31);
		namasi_d[0] = CalculateDistanceCorrelation(namarea, asiarea, 31);

		eurocecur[0] = +eap + enp + gap + gnp;
		eurriscur[0] = +efp + ejp + gfp + gjp;
		eurnamcur[0] = +eup + ecp + gup + gcp;
		oceriscur[0] = +afp + ajp + nfp + njp;
		ocenamcur[0] = +aup + acp + nup + ncp;
		namriscur[0] = +ufp + ujp + cfp + cjp;
		feucomcur[0] = +eap + enp + ecp + gap + gnp + gcp - afp - nfp - cfp;
		feunamcur[0] = +eup + ecp + gup + gcp - ufp - cfp;
		feuasicur[0] = +eap + enp + ejp + gap + gnp + gjp - afp - nfp + fjp;
		feufricur[0] = +eup + ejp + gup + gjp;
		comfricur[0] = +aup + afp + ajp + nup + nfp + njp - ucp + cfp + cjp;;
		namasicur[0] = -aup - nup + ujp - acp - ncp + cjp;

		fjancur[0] = -efp - gfp - ufp - cfp - ejp - gjp - ujp - cjp - eap - gap + aup + acp - enp - gnp + nup + ncp;
		egancur[0] = +eup + ecp + efp + ejp + gup + gcp + gfp + gjp + aup + acp + afp + ajp + nup + ncp + nfp + njp;
		egfjcur[0] = +eap + enp + eup + ecp + gap + gnp + gup + gcp - afp - nfp - ufp - cfp - ajp - njp - ujp - cjp;
		eguccur[0] = +eap + enp + efp + ejp + gap + gnp + gfp + gjp - aup - nup + ufp + ujp - acp - ncp + cfp + cjp;
		ucancur[0] = -eup - gup + ufp + ujp - ecp - gcp + cfp + cjp - eap - gap + afp + ajp - enp - gnp + nfp + njp;
		ucfjcur[0] = -eup - gup - aup - nup - ecp - gcp - acp - ncp - efp - gfp - afp - nfp - ejp - gjp - ajp - njp;

		egvfjancur[0] = +efp + ejp + eap + enp + gfp + gjp + gap + gnp;
		egvucancur[0] = +eup + ecp + eap + enp + gup + gcp + gap + gnp;
		egvucfjcur[0] = +efp + ejp + eup + ecp + gfp + gjp + gup + gcp;
		anvegfjcur[0] = -eap - gap + afp + ajp - enp - gnp + nfp + njp;
		anveguccur[0] = -eap - gap + aup + acp - enp - gnp + nup + ncp;
		anvucfjcur[0] = +aup + acp + afp + ajp + nup + ncp + nfp + njp;
		ucvfjancur[0] = +ufp + ujp - aup - nup + cfp + cjp - acp - ncp;
		ucvegancur[0] = -eup - gup - aup - nup - ecp - gcp - acp - ncp;
		ucvegfjcur[0] = +ufp + ujp - eup - gup + cfp + cjp - ecp - gcp;
		fjvegancur[0] = -ejp - gjp - ajp - njp - efp - gfp - afp - nfp;
		fjveguccur[0] = -ejp - gjp - ujp - cjp - efp - gfp - ufp - cfp;
		fjvucancur[0] = -ujp - cjp - ajp - njp - ufp - cfp - afp - nfp;

		//
		egvfjan[0] = CalculatePearsonCorrelation(eurarea, fjancur, 31);
		egvucan[0] = CalculatePearsonCorrelation(eurarea, ucancur, 31);
		egvucfj[0] = CalculatePearsonCorrelation(eurarea, ucfjcur, 31);
		anvegfj[0] = CalculatePearsonCorrelation(ocearea, egfjcur, 31);
		anveguc[0] = CalculatePearsonCorrelation(ocearea, eguccur, 31);
		anvucfj[0] = CalculatePearsonCorrelation(ocearea, ucfjcur, 31);
		ucvfjan[0] = CalculatePearsonCorrelation(namarea, fjancur, 31);
		ucvegan[0] = CalculatePearsonCorrelation(namarea, egancur, 31);
		ucvegfj[0] = CalculatePearsonCorrelation(namarea, egfjcur, 31);
		fjvegan[0] = CalculatePearsonCorrelation(risarea, egancur, 31);
		fjveguc[0] = CalculatePearsonCorrelation(risarea, eguccur, 31);
		fjvucan[0] = CalculatePearsonCorrelation(risarea, ucancur, 31);

		egvfjan_d[0] = CalculateDistanceCorrelation(eurarea, fjancur, 31);
		egvucan_d[0] = CalculateDistanceCorrelation(eurarea, ucancur, 31);
		egvucfj_d[0] = CalculateDistanceCorrelation(eurarea, ucfjcur, 31);
		anvegfj_d[0] = CalculateDistanceCorrelation(ocearea, egfjcur, 31);
		anveguc_d[0] = CalculateDistanceCorrelation(ocearea, eguccur, 31);
		anvucfj_d[0] = CalculateDistanceCorrelation(ocearea, ucfjcur, 31);
		ucvfjan_d[0] = CalculateDistanceCorrelation(namarea, fjancur, 31);
		ucvegan_d[0] = CalculateDistanceCorrelation(namarea, egancur, 31);
		ucvegfj_d[0] = CalculateDistanceCorrelation(namarea, egfjcur, 31);
		fjvegan_d[0] = CalculateDistanceCorrelation(risarea, egancur, 31);
		fjveguc_d[0] = CalculateDistanceCorrelation(risarea, eguccur, 31);
		fjvucan_d[0] = CalculateDistanceCorrelation(risarea, ucancur, 31);

		//RANDOS

		// eurcur[0] = [r - 1];
		anucjcur[0] = -eap - gap + afp - enp - gnp + nfp - eup - gup + ufp - ecp - gcp + cfp - ejp - gjp - fjp;
		// comcur[0] = [r - 1];
		egufjcur[0] = +eap + enp + ecp + gap + gnp + gcp - aup - nup + ucp - afp - nfp - cfp - ajp - njp - cjp;
		// riscur[0] = [r - 1];
		eganccur[0] = +eup + efp + ejp + gup + gfp + gjp + aup + afp + ajp + nup + nfp + njp - ucp + cfp + cjp;
		gancur[0] = -egp + gup + gcp + gfp + gjp - eap + nup + ncp + nfp + njp;
		eucfjcur[0] = +egp + eap + enp - gup - aup - nup - gcp - acp - ncp - gfp - afp - nfp - gjp - ajp - njp;
		//-
		gacur[0] = -egp + gnp + gup + gcp + gfp + gjp - eap + anp + aup + acp + afp + ajp; // ga no e
		nucfjcur[0] = -enp - gnp - anp - eup - gup - aup - ecp - gcp - acp - efp - gfp - afp - ejp - gjp - ajp; //ga no e
		eucur[0] = +egp + eap + enp + ecp + efp + ejp - gup - aup - nup + ucp + ufp + ujp; // eu no c
		ganfjcur[0] = -egp + gup + gcp - eap + aup + acp - enp + nup + ncp - efp - ufp - cfp - ejp - ujp - cjp; //eu no c
		gccur[0] = -egp + gap + gnp + gup + gfp + gjp - ecp - acp - ncp - ucp + cfp + cjp; // gc no u
		eanfjcur[0] = +egp + eup + ecp - gap + aup + acp - gnp + nup + ncp - gfp - ufp - cfp - gjp - ujp - cjp;
		efcur[0] = +egp + eap + enp + eup + ecp + ejp - gfp - afp - nfp - ufp - cfp + fjp; // ef no g
		// anucjcur[0] = [r - 1];
		nccur[0] = -enp - gnp - anp + nup + nfp + njp - ecp - gcp - acp - ucp + cfp + cjp; // nc no a
		// egufjcur[0] = [r - 1];
		ajcur[0] = -eap - gap + anp + aup + acp + afp - ejp - gjp - njp - ujp - cjp - fjp; // aj no n
		egucfcur[0] = +eap + enp + ejp + gap + gnp + gjp - aup - nup + ujp - acp - ncp + cjp - afp - nfp - cfp;
		ujcur[0] = -eup - gup - aup - nup + ucp + ufp - ejp - gjp - ajp - njp - cjp - fjp; // uj no f
		// eganccur[0] = [r - 1];

		eurvanucjcur[0] = +eap + enp + eup + ecp + ejp + gap + gnp + gup + gcp + gjp - afp - nfp - ufp - cfp + fjp; //eur
		comvegufjcur[0] = -eap - gap + aup + afp + ajp - enp - gnp + nup + nfp + njp - ecp - gcp - ucp + cfp + cjp; //com
		risveganccur[0] = -eup - gup - aup - nup + ucp - efp - gfp - afp - nfp - cfp - ejp - gjp - ajp - njp - cjp; //risk
		ganveucfjcur[0] = -egp + gup + gcp + gfp + gjp - eap + aup + acp + afp + ajp - enp + nup + ncp + nfp + njp; //gan
		asivegucfcur[0] = -eap - gap + aup + acp + afp - enp - gnp + nup + ncp + nfp - ejp - gjp - ujp - cjp - fjp; //asia
		//-
		gavnucfjcur[0] = +gnp + gup + gcp + gfp + gjp + anp + aup + acp + afp + ajp; //ga
		euvganfjcur[0] = +egp + eap + enp + efp + ejp - gup - aup - nup + ufp + ujp; //eu
		gcveanfjcur[0] = -egp + gap + gnp + gfp + gjp - ecp - acp - ncp + cfp + cjp; //gc
		efvanucjcur[0] = +eap + enp + eup + ecp + ejp - afp - nfp - ufp - cfp + cjp; //ef
		ajvegucfcur[0] = -eap - gap + aup + acp + afp - ejp - gjp - ujp - cjp - fjp; //aj
		ncvegufjcur[0] = -enp - gnp + nup + nfp + njp - ecp - gcp - ucp + cfp + cjp; //nc
		ujveganccur[0] = -eup - gup - aup - nup + ucp - ejp - gjp - ajp - njp - cjp; //uj

		eurvanucj[0] = CalculatePearsonCorrelation(feuarea, anucjcur, 31); //eur
		comvegufj[0] = CalculatePearsonCorrelation(comarea, egufjcur, 31); //com
		risveganc[0] = CalculatePearsonCorrelation(friarea, eganccur, 31); //risk
		ganveucfj[0] = CalculatePearsonCorrelation(gancur, eucfjcur, 31); //gan
		asivegucf[0] = CalculatePearsonCorrelation(asiarea, egucfcur, 31); //asia
		//-
		gavnucfj[0] = CalculatePearsonCorrelation(gacur, nucfjcur, 31); //ga
		euvganfj[0] = CalculatePearsonCorrelation(eucur, ganfjcur, 31); //eu
		gcveanfj[0] = CalculatePearsonCorrelation(gccur, eanfjcur, 31); //gc
		efvanucj[0] = CalculatePearsonCorrelation(efcur, anucjcur, 31); //ef
		ajvegucf[0] = CalculatePearsonCorrelation(ajcur, egucfcur, 31); //aj
		ncvegufj[0] = CalculatePearsonCorrelation(nccur, egufjcur, 31); //nc
		ujveganc[0] = CalculatePearsonCorrelation(ujcur, eganccur, 31); //uj

		eurvanucj_d[0] = CalculateDistanceCorrelation(feuarea, anucjcur, 31); //eur
		comvegufj_d[0] = CalculateDistanceCorrelation(comarea, egufjcur, 31); //com
		risveganc_d[0] = CalculateDistanceCorrelation(friarea, eganccur, 31); //risk
		ganveucfj_d[0] = CalculateDistanceCorrelation(gancur, eucfjcur, 31); //gan
		asivegucf_d[0] = CalculateDistanceCorrelation(asiarea, egucfcur, 31); //asia
		//-
		gavnucfj_d[0] = CalculateDistanceCorrelation(gacur, nucfjcur, 31); //ga
		euvganfj_d[0] = CalculateDistanceCorrelation(eucur, ganfjcur, 31); //eu
		gcveanfj_d[0] = CalculateDistanceCorrelation(gccur, eanfjcur, 31); //gc
		efvanucj_d[0] = CalculateDistanceCorrelation(efcur, anucjcur, 31); //ef
		ajvegucf_d[0] = CalculateDistanceCorrelation(ajcur, egucfcur, 31); //aj
		ncvegufj_d[0] = CalculateDistanceCorrelation(nccur, egufjcur, 31); //nc
		ujveganc_d[0] = CalculateDistanceCorrelation(ujcur, eganccur, 31); //uj

		//e vs ucj
		//g vs anj
		//a vs egf
		//n vs ucf
		//u vs ega
		//c vs efj
		//f vs gnu
		//j vs anc

		obname = Name + "area euroce";
		LabelMake(obname, 0, f + 485, 500, "EUR-OCE (" + DoubleToString(((eurocecur[0] - eurocecur[3]) + (eurocecur[0] - eurocecur[6]) + (eurocecur[0] - eurocecur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((eurocecur[0] - eurocecur[3]) + (eurocecur[0] - eurocecur[6]) + (eurocecur[0] - eurocecur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurocecur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (eurocecur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (eurocecur[0] > 2000 && euroce[0] <= -0.0001 && euroce_d[0] >= 0.0001 && eurocecur[0] < euroce5m[0] && eurocecur[0] < eurocecur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (eurocecur[0] < -2000 && euroce[0] <= -0.0001 && euroce_d[0] >= 0.0001 && eurocecur[0] > euroce5m[0] && eurocecur[0] > eurocecur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area eurris";
		LabelMake(obname, 0, f + 590, 500, "EUR-RIS (" + DoubleToString(((eurriscur[0] - eurriscur[3]) + (eurriscur[0] - eurriscur[6]) + (eurriscur[0] - eurriscur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((eurriscur[0] - eurriscur[3]) + (eurriscur[0] - eurriscur[6]) + (eurriscur[0] - eurriscur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurriscur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (eurriscur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (eurriscur[0] > 2000 && eurris[0] <= -0.0001 && eurris_d[0] >= 0.0001 && eurriscur[0] < eurris5m[0] && eurriscur[0] < eurriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (eurriscur[0] < -2000 && eurris[0] <= -0.0001 && eurris_d[0] >= 0.0001 && eurriscur[0] > eurris5m[0] && eurriscur[0] > eurriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area eurnam";
		LabelMake(obname, 0, f + 695, 500, "EUR-NAM (" + DoubleToString(((eurnamcur[0] - eurnamcur[3]) + (eurnamcur[0] - eurnamcur[6]) + (eurnamcur[0] - eurnamcur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((eurnamcur[0] - eurnamcur[3]) + (eurnamcur[0] - eurnamcur[6]) + (eurnamcur[0] - eurnamcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurnamcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (eurnamcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (eurnamcur[0] > 2000 && eurnam[0] <= -0.0001 && eurnam_d[0] >= 0.0001 && eurnamcur[0] < eurnam5m[0] && eurnamcur[0] < eurnamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (eurnamcur[0] < -2000 && eurnam[0] <= -0.0001 && eurnam_d[0] >= 0.0001 && eurnamcur[0] > eurnam5m[0] && eurnamcur[0] > eurnamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area oceris";
		LabelMake(obname, 0, f + 800, 500, "OCE-RIS (" + DoubleToString(((oceriscur[0] - oceriscur[3]) + (oceriscur[0] - oceriscur[6]) + (oceriscur[0] - oceriscur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((oceriscur[0] - oceriscur[3]) + (oceriscur[0] - oceriscur[6]) + (oceriscur[0] - oceriscur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (oceriscur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (oceriscur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (oceriscur[0] > 2000 && oceris[0] <= -0.0001 && oceris_d[0] >= 0.0001 && oceriscur[0] < oceris5m[0] && oceriscur[0] < oceriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (oceriscur[0] < -2000 && oceris[0] <= -0.0001 && oceris_d[0] >= 0.0001 && oceriscur[0] > oceris5m[0] && oceriscur[0] > oceriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area ocenam";
		LabelMake(obname, 0, f + 485, 635, "OCE-NAM (" + DoubleToString(((ocenamcur[0] - ocenamcur[3]) + (ocenamcur[0] - ocenamcur[6]) + (ocenamcur[0] - ocenamcur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((ocenamcur[0] - ocenamcur[3]) + (ocenamcur[0] - ocenamcur[6]) + (ocenamcur[0] - ocenamcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ocenamcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ocenamcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ocenamcur[0] > 2000 && ocenam[0] <= -0.0001 && ocenam_d[0] >= 0.0001 && ocenamcur[0] < ocenam5m[0] && ocenamcur[0] < ocenamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ocenamcur[0] < -2000 && ocenam[0] <= -0.0001 && ocenam_d[0] >= 0.0001 && ocenamcur[0] > ocenam5m[0] && ocenamcur[0] > ocenamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area namris";
		LabelMake(obname, 0, f + 590, 635, "NAM-RIS (" + DoubleToString(((namriscur[0] - namriscur[3]) + (namriscur[0] - namriscur[6]) + (namriscur[0] - namriscur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((namriscur[0] - namriscur[3]) + (namriscur[0] - namriscur[6]) + (namriscur[0] - namriscur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (namriscur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (namriscur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (namriscur[0] > 2000 && namris[0] <= -0.0001 && namris_d[0] >= 0.0001 && namriscur[0] < namris5m[0] && namriscur[0] < namriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (namriscur[0] < -2000 && namris[0] <= -0.0001 && namris_d[0] >= 0.0001 && namriscur[0] > namris5m[0] && namriscur[0] > namriscur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area feucom";
		LabelMake(obname, 0, f + 695, 635, "FEU-COM (" + DoubleToString(((feucomcur[0] - feucomcur[3]) + (feucomcur[0] - feucomcur[6]) + (feucomcur[0] - feucomcur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((feucomcur[0] - feucomcur[3]) + (feucomcur[0] - feucomcur[6]) + (feucomcur[0] - feucomcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feucomcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (feucomcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (feucomcur[0] > 2000 && feucom[0] <= -0.0001 && feucom_d[0] >= 0.0001 && feucomcur[0] < feucom5m[0] && feucomcur[0] < feucomcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (feucomcur[0] < -2000 && feucom[0] <= -0.0001 && feucom_d[0] >= 0.0001 && feucomcur[0] > feucom5m[0] && feucomcur[0] > feucomcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area feunam";
		LabelMake(obname, 0, f + 800, 635, "FEU-NAM (" + DoubleToString(((feunamcur[0] - feunamcur[3]) + (feunamcur[0] - feunamcur[6]) + (feunamcur[0] - feunamcur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((feunamcur[0] - feunamcur[3]) + (feunamcur[0] - feunamcur[6]) + (feunamcur[0] - feunamcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feunamcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (feunamcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (feunamcur[0] > 2000 && feunam[0] <= -0.0001 && feunam_d[0] >= 0.0001 && feunamcur[0] < feunam5m[0] && feunamcur[0] < feunamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (feunamcur[0] < -2000 && feunam[0] <= -0.0001 && feunam_d[0] >= 0.0001 && feunamcur[0] > feunam5m[0] && feunamcur[0] > feunamcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area feuasi";
		LabelMake(obname, 0, f + 485, 770, "FEU-ASI (" + DoubleToString(((feuasicur[0] - feuasicur[3]) + (feuasicur[0] - feuasicur[6]) + (feuasicur[0] - feuasicur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((feuasicur[0] - feuasicur[3]) + (feuasicur[0] - feuasicur[6]) + (feuasicur[0] - feuasicur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feuasicur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (feuasicur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (feuasicur[0] > 2000 && feuasi[0] <= -0.0001 && feuasi_d[0] >= 0.0001 && feuasicur[0] < feuasi5m[0] && feuasicur[0] < feuasicur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (feuasicur[0] < -2000 && feuasi[0] <= -0.0001 && feuasi_d[0] >= 0.0001 && feuasicur[0] > feuasi5m[0] && feuasicur[0] > feuasicur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area feufri";
		LabelMake(obname, 0, f + 590, 770, "FEU-FRI (" + DoubleToString(((feufricur[0] - feufricur[3]) + (feufricur[0] - feufricur[6]) + (feufricur[0] - feufricur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((feufricur[0] - feufricur[3]) + (feufricur[0] - feufricur[6]) + (feufricur[0] - feufricur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feufricur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (feufricur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (feufricur[0] > 2000 && feufri[0] <= -0.0001 && feufri_d[0] >= 0.0001 && feufricur[0] < feufri5m[0] && feufricur[0] < feufricur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (feufricur[0] < -2000 && feufri[0] <= -0.0001 && feufri_d[0] >= 0.0001 && feufricur[0] > feufri5m[0] && feufricur[0] > feufricur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area comfri";
		LabelMake(obname, 0, f + 695, 770, "COM-FRI (" + DoubleToString(((comfricur[0] - comfricur[3]) + (comfricur[0] - comfricur[6]) + (comfricur[0] - comfricur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((comfricur[0] - comfricur[3]) + (comfricur[0] - comfricur[6]) + (comfricur[0] - comfricur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);	ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (comfricur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (comfricur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (comfricur[0] > 2000 && comfri[0] <= -0.0001 && comfri_d[0] >= 0.0001 && comfricur[0] < comfri5m[0] && comfricur[0] < comfricur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (comfricur[0] < -2000 && comfri[0] <= -0.0001 && comfri_d[0] >= 0.0001 && comfricur[0] > comfri5m[0] && comfricur[0] > comfricur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "area namasi";
		LabelMake(obname, 0, f + 800, 770, "NAM-ASI (" + DoubleToString(((namasicur[0] - namasicur[3]) + (namasicur[0] - namasicur[6]) + (namasicur[0] - namasicur[9])) / 3, 0) + ")", 9, clrGray);
		{
			if (MathAbs(((namasicur[0] - namasicur[3]) + (namasicur[0] - namasicur[6]) + (namasicur[0] - namasicur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (namasicur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (namasicur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (namasicur[0] > 2000 && namasi[0] <= -0.0001 && namasi_d[0] >= 0.0001 && namasicur[0] < namasi5m[0] && namasicur[0] < namasicur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (namasicur[0] < -2000 && namasi[0] <= -0.0001 && namasi_d[0] >= 0.0001 && namasicur[0] > namasi5m[0] && namasicur[0] > namasicur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}

		for (int x = 10; x >= 0; x--)
		{
			obname = Name + "area euroce" + IntegerToString(x);
			LabelMake(obname, 0, f + 485, 515 + x * 10, DoubleToString(euroce[x], 2) + " / " + DoubleToString(eurocecur[x], 0) + " / " + DoubleToString(euroce_d[x], 2), 8, clrGray);
			if (euroce[x] > -0.0001 || euroce_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (euroce[x] <= -0.0001 && euroce_d[x] >= 0.0001 && eurocecur[x] < euroce5m[x] && eurocecur[x] < eurocecur[x + 1] && MathAbs(eurocecur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (euroce[x] <= -0.0001 && euroce_d[x] >= 0.0001 && eurocecur[x] > euroce5m[x] && eurocecur[x] > eurocecur[x + 1] && MathAbs(eurocecur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (euroce[x] <= -0.7 && euroce_d[x] >= 0.7 && MathAbs(eurocecur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area eurris" + IntegerToString(x);
			LabelMake(obname, 0, f + 590, 515 + x * 10, DoubleToString(eurris[x], 2) + " / " + DoubleToString(eurriscur[x], 0) + " / " + DoubleToString(eurris_d[x], 2), 8, clrGray);
			if (eurris[x] > -0.0001 || eurris_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurris[x] <= -0.0001 && eurris_d[x] >= 0.0001 && eurriscur[x] < eurris5m[x] && eurriscur[x] < eurriscur[x + 1] && MathAbs(eurriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (eurris[x] <= -0.0001 && eurris_d[x] >= 0.0001 && eurriscur[x] > eurris5m[x] && eurriscur[x] > eurriscur[x + 1] && MathAbs(eurriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (eurris[x] <= -0.7 && eurris_d[x] >= 0.7 && MathAbs(eurriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area eurnam" + IntegerToString(x);
			LabelMake(obname, 0, f + 695, 515 + x * 10, DoubleToString(eurnam[x], 2) + " / " + DoubleToString(eurnamcur[x], 0) + " / " + DoubleToString(eurnam_d[x], 2), 8, clrGray);
			if (eurnam[x] > -0.0001 || eurnam_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurnam[x] <= -0.0001 && eurnam_d[x] >= 0.0001 && eurnamcur[x] < eurnam5m[x] && eurnamcur[x] < eurnamcur[x + 1] && MathAbs(eurnamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (eurnam[x] <= -0.0001 && eurnam_d[x] >= 0.0001 && eurnamcur[x] > eurnam5m[x] && eurnamcur[x] > eurnamcur[x + 1] && MathAbs(eurnamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (eurnam[x] <= -0.7 && eurnam_d[x] >= 0.7 && MathAbs(eurnamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area oceris" + IntegerToString(x);
			LabelMake(obname, 0, f + 800, 515 + x * 10, DoubleToString(oceris[x], 2) + " / " + DoubleToString(oceriscur[x], 0) + " / " + DoubleToString(oceris_d[x], 2), 8, clrGray);
			if (oceris[x] > -0.0001 || oceris_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (oceris[x] <= -0.0001 && oceris_d[x] >= 0.0001 && oceriscur[x] < oceris5m[x] && oceriscur[x] < oceriscur[x + 1] && MathAbs(oceriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (oceris[x] <= -0.0001 && oceris_d[x] >= 0.0001 && oceriscur[x] > oceris5m[x] && oceriscur[x] > oceriscur[x + 1] && MathAbs(oceriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (oceris[x] <= -0.7 && oceris_d[x] >= 0.7 && MathAbs(oceriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area ocenam" + IntegerToString(x);
			LabelMake(obname, 0, f + 485, 650 + x * 10, DoubleToString(ocenam[x], 2) + " / " + DoubleToString(ocenamcur[x], 0) + " / " + DoubleToString(ocenam_d[x], 2), 8, clrGray);
			if (ocenam[x] > -0.0001 || ocenam_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ocenam[x] <= -0.0001 && ocenam_d[x] >= 0.0001 && ocenamcur[x] < ocenam5m[x] && ocenamcur[x] < ocenamcur[x + 1] && MathAbs(ocenamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ocenam[x] <= -0.0001 && ocenam_d[x] >= 0.0001 && ocenamcur[x] > ocenam5m[x] && ocenamcur[x] > ocenamcur[x + 1] && MathAbs(ocenamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ocenam[x] <= -0.7 && ocenam_d[x] >= 0.7 && MathAbs(ocenamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area namris" + IntegerToString(x);
			LabelMake(obname, 0, f + 590, 650 + x * 10, DoubleToString(namris[x], 2) + " / " + DoubleToString(namriscur[x], 0) + " / " + DoubleToString(namris_d[x], 2), 8, clrGray);
			if (namris[x] > -0.0001 || namris_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (namris[x] <= -0.0001 && namris_d[x] >= 0.0001 && namriscur[x] < namris5m[x] && namriscur[x] < namriscur[x + 1] && MathAbs(namriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (namris[x] <= -0.0001 && namris_d[x] >= 0.0001 && namriscur[x] > namris5m[x] && namriscur[x] > namriscur[x + 1] && MathAbs(namriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (namris[x] <= -0.7 && namris_d[x] >= 0.7 && MathAbs(namriscur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area feucom" + IntegerToString(x);
			LabelMake(obname, 0, f + 695, 650 + x * 10, DoubleToString(feucom[x], 2) + " / " + DoubleToString(feucomcur[x], 0) + " / " + DoubleToString(feucom_d[x], 2), 8, clrGray);
			if (feucom[x] > -0.0001 || feucom_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feucom[x] <= -0.0001 && feucom_d[x] >= 0.0001 && feucomcur[x] < feucom5m[x] && feucomcur[x] < feucomcur[x + 1] && MathAbs(feucomcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (feucom[x] <= -0.0001 && feucom_d[x] >= 0.0001 && feucomcur[x] > feucom5m[x] && feucomcur[x] > feucomcur[x + 1] && MathAbs(feucomcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (feucom[x] <= -0.7 && feucom_d[x] >= 0.7 && MathAbs(feucomcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area feunam" + IntegerToString(x);
			LabelMake(obname, 0, f + 800, 650 + x * 10, DoubleToString(feunam[x], 2) + " / " + DoubleToString(feunamcur[x], 0) + " / " + DoubleToString(feunam_d[x], 2), 8, clrGray);
			if (feunam[x] > -0.0001 || feunam_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feunam[x] <= -0.0001 && feunam_d[x] >= 0.0001 && feunamcur[x] < feunam5m[x] && feunamcur[x] < feunamcur[x + 1] && MathAbs(feunamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (feunam[x] <= -0.0001 && feunam_d[x] >= 0.0001 && feunamcur[x] > feunam5m[x] && feunamcur[x] > feunamcur[x + 1] && MathAbs(feunamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (feunam[x] <= -0.7 && feunam_d[x] >= 0.7 && MathAbs(feunamcur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area feuasi" + IntegerToString(x);
			LabelMake(obname, 0, f + 485, 785 + x * 10, DoubleToString(feuasi[x], 2) + " / " + DoubleToString(feuasicur[x], 0) + " / " + DoubleToString(feuasi_d[x], 2), 8, clrGray);
			if (feuasi[x] > -0.0001 || feuasi_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feuasi[x] <= -0.0001 && feuasi_d[x] >= 0.0001 && feuasicur[x] < feuasi5m[x] && feuasicur[x] < feuasicur[x + 1] && MathAbs(feuasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (feuasi[x] <= -0.0001 && feuasi_d[x] >= 0.0001 && feuasicur[x] > feuasi5m[x] && feuasicur[x] > feuasicur[x + 1] && MathAbs(feuasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (feuasi[x] <= -0.7 && feuasi_d[x] >= 0.7 && MathAbs(feuasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area feufri" + IntegerToString(x);
			LabelMake(obname, 0, f + 590, 785 + x * 10, DoubleToString(feufri[x], 2) + " / " + DoubleToString(feufricur[x], 0) + " / " + DoubleToString(feufri_d[x], 2), 8, clrGray);
			if (feufri[x] > -0.0001 || feufri_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (feufri[x] <= -0.0001 && feufri_d[x] >= 0.0001 && feufricur[x] < feufri5m[x] && feufricur[x] < feufricur[x + 1] && MathAbs(feufricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (feufri[x] <= -0.0001 && feufri_d[x] >= 0.0001 && feufricur[x] > feufri5m[x] && feufricur[x] > feufricur[x + 1] && MathAbs(feufricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (feufri[x] <= -0.7 && feufri_d[x] >= 0.7 && MathAbs(feufricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area comfri" + IntegerToString(x);
			LabelMake(obname, 0, f + 695, 785 + x * 10, DoubleToString(comfri[x], 2) + " / " + DoubleToString(comfricur[x], 0) + " / " + DoubleToString(comfri_d[x], 2), 8, clrGray);
			if (comfri[x] > -0.0001 || comfri_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (comfri[x] <= -0.0001 && comfri_d[x] >= 0.0001 && comfricur[x] < comfri5m[x] && comfricur[x] < comfricur[x + 1] && MathAbs(comfricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (comfri[x] <= -0.0001 && comfri_d[x] >= 0.0001 && comfricur[x] > comfri5m[x] && comfricur[x] > comfricur[x + 1] && MathAbs(comfricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (comfri[x] <= -0.7 && comfri_d[x] >= 0.7 && MathAbs(comfricur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			obname = Name + "area namasi" + IntegerToString(x);
			LabelMake(obname, 0, f + 800, 785 + x * 10, DoubleToString(namasi[x], 2) + " / " + DoubleToString(namasicur[x], 0) + " / " + DoubleToString(namasi_d[x], 2), 8, clrGray);
			if (namasi[x] > -0.0001 || namasi_d[x] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (namasi[x] <= -0.0001 && namasi_d[x] >= 0.0001 && namasicur[x] < namasi5m[x] && namasicur[x] < namasicur[x + 1] && MathAbs(namasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (namasi[x] <= -0.0001 && namasi_d[x] >= 0.0001 && namasicur[x] > namasi5m[x] && namasicur[x] > namasicur[x + 1] && MathAbs(namasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (namasi[x] <= -0.7 && namasi_d[x] >= 0.7 && MathAbs(namasicur[x]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
		}

		obname = Name + " cor1";
		LabelMake(obname, 0, f + 353, 60, "EU-OC: " + DoubleToString(euroce[0], 2), 8, clrGray);
		if (euroce[0] <= -0.5 && euroce_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (euroce[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor2";
		LabelMake(obname, 0, f + 423, 60, "EU-RI: " + DoubleToString(eurris[0], 2), 8, clrGray);
		if (eurris[0] <= -0.5 && eurris_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (eurris[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor3";
		LabelMake(obname, 0, f + 353, 70, "EU-NA: " + DoubleToString(eurnam[0], 2), 8, clrGray);
		if (eurnam[0] <= -0.5 && eurnam_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (eurnam[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor4";
		LabelMake(obname, 0, f + 423, 70, "OC-RI: " + DoubleToString(oceris[0], 2), 8, clrGray);
		if (oceris[0] <= -0.5 && oceris_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (oceris[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor5";
		LabelMake(obname, 0, f + 353, 80, "OC-NA: " + DoubleToString(ocenam[0], 2), 8, clrGray);
		if (ocenam[0] <= -0.5 && ocenam_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (ocenam[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor6";
		LabelMake(obname, 0, f + 423, 80, "NA-RI: " + DoubleToString(namris[0], 2), 8, clrGray);
		if (namris[0] <= -0.5 && namris_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (namris[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);

		obname = Name + " cor7";
		LabelMake(obname, 0, f + 353, 90, "FE-CO: " + DoubleToString(feucom[0], 2), 8, clrGray);
		if (feucom[0] <= -0.5 && feucom_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (feucom[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor8";
		LabelMake(obname, 0, f + 423, 90, "FE-NA: " + DoubleToString(feunam[0], 2), 8, clrGray);
		if (feunam[0] <= -0.5 && feunam_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (feunam[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor9";
		LabelMake(obname, 0, f + 353, 100, "FE-AS: " + DoubleToString(feuasi[0], 2), 8, clrGray);
		if (feuasi[0] <= -0.5 && feuasi_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (feuasi[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor10";
		LabelMake(obname, 0, f + 423, 100, "FE-FR: " + DoubleToString(feufri[0], 2), 8, clrGray);
		if (feufri[0] <= -0.5 && feufri_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (feufri[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor11";
		LabelMake(obname, 0, f + 353, 110, "CO-FR: " + DoubleToString(comfri[0], 2), 8, clrGray);
		if (comfri[0] <= -0.5 && comfri_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (comfri[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
		obname = Name + " cor12";
		LabelMake(obname, 0, f + 423, 110, "NA-AS: " + DoubleToString(namasi[0], 2), 8, clrGray);
		if (namasi[0] <= -0.5 && namasi_d[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		if (namasi[0] >= 0.5) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);

		int totrec = ArraySize(tokillb) + ArraySize(tokills);
		string totrecX[] = {};
		ArrayResize(totrecX, totrec);

		ArrayCopy(totrecX, tokillb, 0, 0, 0);
		ArrayCopy(totrecX, tokills, ArraySize(tokillb), 0, 0);

		ObjectsDeleteAll(0, Name + "recboyz");

		for (int x = 0; x < totrec; x++)
		{
			obname = Name + "recboyz" + IntegerToString(x);
			LabelMake(obname, 0, f + 395, 125 + 12 * x, totrecX[x], 9, clrRed);
			if (euroce[0] <= -0.5 && euroce_d[0] >= 0.5 && (totrecX[x] == ea_p || totrecX[x] == en_p || totrecX[x] == ga_p || totrecX[x] == gn_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			if (eurnam[0] <= -0.5 && eurnam_d[0] >= 0.5 && (totrecX[x] == eu_p || totrecX[x] == ec_p || totrecX[x] == gu_p || totrecX[x] == gc_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			if (eurris[0] <= -0.5 && eurris_d[0] >= 0.5 && (totrecX[x] == ef_p || totrecX[x] == ej_p || totrecX[x] == gf_p || totrecX[x] == gj_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			if (oceris[0] <= -0.5 && oceris_d[0] >= 0.5 && (totrecX[x] == af_p || totrecX[x] == aj_p || totrecX[x] == nf_p || totrecX[x] == nj_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			if (ocenam[0] <= -0.5 && ocenam_d[0] >= 0.5 && (totrecX[x] == au_p || totrecX[x] == ac_p || totrecX[x] == nu_p || totrecX[x] == nc_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			if (namris[0] <= -0.5 && namris_d[0] >= 0.5 && (totrecX[x] == uf_p || totrecX[x] == uj_p || totrecX[x] == cf_p || totrecX[x] == cj_p)) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
		}

		obname = Name + "eg-ucfj";
		LabelMake(obname, 0, f + 1060, 20, "NEU-UCFJ (" + DoubleToString(((egvucfjcur[0] - egvucfjcur[3]) + (egvucfjcur[0] - egvucfjcur[6]) + (egvucfjcur[0] - egvucfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((egvucfjcur[0] - egvucfjcur[3]) + (egvucfjcur[0] - egvucfjcur[6]) + (egvucfjcur[0] - egvucfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvucfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (egvucfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (egvucfjcur[0] > 2000 && egvucfj[0] <= -0.0001 && egvucfj_d[0] >= 0.0001 && egvucfjcur[0] < egvucfj5m[0] && egvucfjcur[0] < egvucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (egvucfjcur[0] < -2000 && egvucfj[0] <= -0.0001 && egvucfj_d[0] >= 0.0001 && egvucfjcur[0] > egvucfj5m[0] && egvucfjcur[0] > egvucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "eg-ucan";
		LabelMake(obname, 0, f + 1160, 20, "NEU-UCAN (" + DoubleToString(((egvucancur[0] - egvucancur[3]) + (egvucancur[0] - egvucancur[6]) + (egvucancur[0] - egvucancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((egvucancur[0] - egvucancur[3]) + (egvucancur[0] - egvucancur[6]) + (egvucancur[0] - egvucancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvucancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (egvucancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (egvucancur[0] > 2000 && egvucan[0] <= -0.0001 && egvucan_d[0] >= 0.0001 && egvucancur[0] < egvucan5m[0] && egvucancur[0] < egvucancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (egvucancur[0] < -2000 && egvucan[0] <= -0.0001 && egvucan_d[0] >= 0.0001 && egvucancur[0] > egvucan5m[0] && egvucancur[0] > egvucancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "eg-fjan";
		LabelMake(obname, 0, f + 1260, 20, "NEU-FJAN (" + DoubleToString(((egvfjancur[0] - egvfjancur[3]) + (egvfjancur[0] - egvfjancur[6]) + (egvfjancur[0] - egvfjancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((egvfjancur[0] - egvfjancur[3]) + (egvfjancur[0] - egvfjancur[6]) + (egvfjancur[0] - egvfjancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvfjancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (egvfjancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (egvfjancur[0] > 2000 && egvfjan[0] <= -0.0001 && egvfjan_d[0] >= 0.0001 && egvfjancur[0] < egvfjan5m[0] && egvfjancur[0] < egvfjancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (egvfjancur[0] < -2000 && egvfjan[0] <= -0.0001 && egvfjan_d[0] >= 0.0001 && egvfjancur[0] > egvfjan5m[0] && egvfjancur[0] > egvfjancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}

		obname = Name + "an-ucfj";
		LabelMake(obname, 0, f + 1060, 145, "OCE-UCFJ (" + DoubleToString(((anvucfjcur[0] - anvucfjcur[3]) + (anvucfjcur[0] - anvucfjcur[6]) + (anvucfjcur[0] - anvucfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((anvucfjcur[0] - anvucfjcur[3]) + (anvucfjcur[0] - anvucfjcur[6]) + (anvucfjcur[0] - anvucfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anvucfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (anvucfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (anvucfjcur[0] > 2000 && anvucfj[0] <= -0.0001 && anvucfj_d[0] >= 0.0001 && anvucfjcur[0] < anvucfj5m[0] && anvucfjcur[0] < anvucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (anvucfjcur[0] < -2000 && anvucfj[0] <= -0.0001 && anvucfj_d[0] >= 0.0001 && anvucfjcur[0] > anvucfj5m[0] && anvucfjcur[0] > anvucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "an-eguc";
		LabelMake(obname, 0, f + 1160, 145, "OCE-EGUC (" + DoubleToString(((anveguccur[0] - anveguccur[3]) + (anveguccur[0] - anveguccur[6]) + (anveguccur[0] - anveguccur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((anveguccur[0] - anveguccur[3]) + (anveguccur[0] - anveguccur[6]) + (anveguccur[0] - anveguccur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anveguccur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (anveguccur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (anveguccur[0] > 2000 && anveguc[0] <= -0.0001 && anveguc_d[0] >= 0.0001 && anveguccur[0] < anveguc5m[0] && anveguccur[0] < anveguccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (anveguccur[0] < -2000 && anveguc[0] <= -0.0001 && anveguc_d[0] >= 0.0001 && anveguccur[0] > anveguc5m[0] && anveguccur[0] > anveguccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "an-egfj";
		LabelMake(obname, 0, f + 1260, 145, "OCE-EGFJ (" + DoubleToString(((anvegfjcur[0] - anvegfjcur[3]) + (anvegfjcur[0] - anvegfjcur[6]) + (anvegfjcur[0] - anvegfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((anvegfjcur[0] - anvegfjcur[3]) + (anvegfjcur[0] - anvegfjcur[6]) + (anvegfjcur[0] - anvegfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anvegfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (anvegfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (anvegfjcur[0] > 2000 && anvegfj[0] <= -0.0001 && anvegfj_d[0] >= 0.0001 && anvegfjcur[0] < anvegfj5m[0] && anvegfjcur[0] < anvegfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (anvegfjcur[0] < -2000 && anvegfj[0] <= -0.0001 && anvegfj_d[0] >= 0.0001 && anvegfjcur[0] > anvegfj5m[0] && anvegfjcur[0] > anvegfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}

		obname = Name + "uc-egfj";
		LabelMake(obname, 0, f + 1060, 270, "NAM-EGFJ (" + DoubleToString(((ucvegfjcur[0] - ucvegfjcur[3]) + (ucvegfjcur[0] - ucvegfjcur[6]) + (ucvegfjcur[0] - ucvegfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ucvegfjcur[0] - ucvegfjcur[3]) + (ucvegfjcur[0] - ucvegfjcur[6]) + (ucvegfjcur[0] - ucvegfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvegfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ucvegfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ucvegfjcur[0] > 2000 && ucvegfj[0] <= -0.0001 && ucvegfj_d[0] >= 0.0001 && ucvegfjcur[0] < ucvegfj5m[0] && ucvegfjcur[0] < ucvegfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ucvegfjcur[0] < -2000 && ucvegfj[0] <= -0.0001 && ucvegfj_d[0] >= 0.0001 && ucvegfjcur[0] > ucvegfj5m[0] && ucvegfjcur[0] > ucvegfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "uc-egan";
		LabelMake(obname, 0, f + 1160, 270, "NAM-EGAN (" + DoubleToString(((ucvegancur[0] - ucvegancur[3]) + (ucvegancur[0] - ucvegancur[6]) + (ucvegancur[0] - ucvegancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ucvegancur[0] - ucvegancur[3]) + (ucvegancur[0] - ucvegancur[6]) + (ucvegancur[0] - ucvegancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvegancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ucvegancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ucvegancur[0] > 2000 && ucvegan[0] <= -0.0001 && ucvegan_d[0] >= 0.0001 && ucvegancur[0] < ucvegan5m[0] && ucvegancur[0] < ucvegancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ucvegancur[0] < -2000 && ucvegan[0] <= -0.0001 && ucvegan_d[0] >= 0.0001 && ucvegancur[0] > ucvegan5m[0] && ucvegancur[0] > ucvegancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "uc-fjan";
		LabelMake(obname, 0, f + 1260, 270, "NAM-FJAN (" + DoubleToString(((ucvfjancur[0] - ucvfjancur[3]) + (ucvfjancur[0] - ucvfjancur[6]) + (ucvfjancur[0] - ucvfjancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ucvfjancur[0] - ucvfjancur[3]) + (ucvfjancur[0] - ucvfjancur[6]) + (ucvfjancur[0] - ucvfjancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvfjancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ucvfjancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ucvfjancur[0] > 2000 && ucvfjan[0] <= -0.0001 && ucvfjan_d[0] >= 0.0001 && ucvfjancur[0] < ucvfjan5m[0] && ucvfjancur[0] < ucvfjancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ucvfjancur[0] < -2000 && ucvfjan[0] <= -0.0001 && ucvfjan_d[0] >= 0.0001 && ucvfjancur[0] > ucvfjan5m[0] && ucvfjancur[0] > ucvfjancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "fj-eguc";
		LabelMake(obname, 0, f + 1060, 395, "RIS-EGUC (" + DoubleToString(((fjveguccur[0] - fjveguccur[3]) + (fjveguccur[0] - fjveguccur[6]) + (fjveguccur[0] - fjveguccur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((fjveguccur[0] - fjveguccur[3]) + (fjveguccur[0] - fjveguccur[6]) + (fjveguccur[0] - fjveguccur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjveguccur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (fjveguccur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (fjveguccur[0] > 2000 && fjveguc[0] <= -0.0001 && fjveguc_d[0] >= 0.0001 && fjveguccur[0] < fjveguc5m[0] && fjveguccur[0] < fjveguccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (fjveguccur[0] < -2000 && fjveguc[0] <= -0.0001 && fjveguc_d[0] >= 0.0001 && fjveguccur[0] > fjveguc5m[0] && fjveguccur[0] > fjveguccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "fj-ucan";
		LabelMake(obname, 0, f + 1160, 395, "RIS-UCAN (" + DoubleToString(((fjvucancur[0] - fjvucancur[3]) + (fjvucancur[0] - fjvucancur[6]) + (fjvucancur[0] - fjvucancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((fjvucancur[0] - fjvucancur[3]) + (fjvucancur[0] - fjvucancur[6]) + (fjvucancur[0] - fjvucancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjvucancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (fjvucancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (fjvucancur[0] > 2000 && fjvucan[0] <= -0.0001 && fjvucan_d[0] >= 0.0001 && fjvucancur[0] < fjvucan5m[0] && fjvucancur[0] < fjvucancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (fjvucancur[0] < -2000 && fjvucan[0] <= -0.0001 && fjvucan_d[0] >= 0.0001 && fjvucancur[0] > fjvucan5m[0] && fjvucancur[0] > fjvucancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "fj-egan";
		LabelMake(obname, 0, f + 1260, 395, "RIS-EGAN (" + DoubleToString(((fjvegancur[0] - fjvegancur[3]) + (fjvegancur[0] - fjvegancur[6]) + (fjvegancur[0] - fjvegancur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((fjvegancur[0] - fjvegancur[3]) + (fjvegancur[0] - fjvegancur[6]) + (fjvegancur[0] - fjvegancur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjvegancur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (fjvegancur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (fjvegancur[0] > 2000 && fjvegan[0] <= -0.0001 && fjvegan_d[0] >= 0.0001 && fjvegancur[0] < fjvegan5m[0] && fjvegancur[0] < fjvegancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (fjvegancur[0] < -2000 && fjvegan[0] <= -0.0001 && fjvegan_d[0] >= 0.0001 && fjvegancur[0] > fjvegan5m[0] && fjvegancur[0] > fjvegancur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}

		obname = Name + "egf-all";
		LabelMake(obname, 0, f + 1460, 20, "FEU (" + DoubleToString(((eurvanucjcur[0] - eurvanucjcur[3]) + (eurvanucjcur[0] - eurvanucjcur[6]) + (eurvanucjcur[0] - eurvanucjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((eurvanucjcur[0] - eurvanucjcur[3]) + (eurvanucjcur[0] - eurvanucjcur[6]) + (eurvanucjcur[0] - eurvanucjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurvanucjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (eurvanucjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (eurvanucjcur[0] > 2000 && eurvanucj[0] <= -0.0001 && eurvanucj_d[0] >= 0.0001 && eurvanucjcur[0] < eurvanucj5m[0] && eurvanucjcur[0] < eurvanucjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (eurvanucjcur[0] < -2000 && eurvanucj[0] <= -0.0001 && eurvanucj_d[0] >= 0.0001 && eurvanucjcur[0] > eurvanucj5m[0] && eurvanucjcur[0] > eurvanucjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "anc-all";
		LabelMake(obname, 0, f + 1460, 145, "COM (" + DoubleToString(((comvegufjcur[0] - comvegufjcur[3]) + (comvegufjcur[0] - comvegufjcur[6]) + (comvegufjcur[0] - comvegufjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((comvegufjcur[0] - comvegufjcur[3]) + (comvegufjcur[0] - comvegufjcur[6]) + (comvegufjcur[0] - comvegufjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (comvegufjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (comvegufjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (comvegufjcur[0] > 2000 && comvegufj[0] <= -0.0001 && comvegufj_d[0] >= 0.0001 && comvegufjcur[0] < comvegufj5m[0] && comvegufjcur[0] < comvegufjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (comvegufjcur[0] < -2000 && comvegufj[0] <= -0.0001 && comvegufj_d[0] >= 0.0001 && comvegufjcur[0] > comvegufj5m[0] && comvegufjcur[0] > comvegufjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "ufj-all";
		LabelMake(obname, 0, f + 1460, 270, "RIS (" + DoubleToString(((risveganccur[0] - risveganccur[3]) + (risveganccur[0] - risveganccur[6]) + (risveganccur[0] - risveganccur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((risveganccur[0] - risveganccur[3]) + (risveganccur[0] - risveganccur[6]) + (risveganccur[0] - risveganccur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (risveganccur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (risveganccur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (risveganccur[0] > 2000 && risveganc[0] <= -0.0001 && risveganc_d[0] >= 0.0001 && risveganccur[0] < risveganc5m[0] && risveganccur[0] < risveganccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (risveganccur[0] < -2000 && risveganc[0] <= -0.0001 && risveganc_d[0] >= 0.0001 && risveganccur[0] > risveganc5m[0] && risveganccur[0] > risveganccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "gan-all";
		LabelMake(obname, 0, f + 1460, 395, "FUK (" + DoubleToString(((ganveucfjcur[0] - ganveucfjcur[3]) + (ganveucfjcur[0] - ganveucfjcur[6]) + (ganveucfjcur[0] - ganveucfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ganveucfjcur[0] - ganveucfjcur[3]) + (ganveucfjcur[0] - ganveucfjcur[6]) + (ganveucfjcur[0] - ganveucfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ganveucfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ganveucfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ganveucfjcur[0] > 2000 && ganveucfj[0] <= -0.0001 && ganveucfj_d[0] >= 0.0001 && ganveucfjcur[0] < ganveucfj5m[0] && ganveucfjcur[0] < ganveucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ganveucfjcur[0] < -2000 && ganveucfj[0] <= -0.0001 && ganveucfj_d[0] >= 0.0001 && ganveucfjcur[0] > ganveucfj5m[0] && ganveucfjcur[0] > ganveucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "asi-all";
		LabelMake(obname, 0, f + 1460, 520, "ASI (" + DoubleToString(((asivegucfcur[0] - asivegucfcur[3]) + (asivegucfcur[0] - asivegucfcur[6]) + (asivegucfcur[0] - asivegucfcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((asivegucfcur[0] - asivegucfcur[3]) + (asivegucfcur[0] - asivegucfcur[6]) + (asivegucfcur[0] - asivegucfcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (asivegucfcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (asivegucfcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (asivegucfcur[0] > 2000 && asivegucf[0] <= -0.0001 && asivegucf_d[0] >= 0.0001 && asivegucfcur[0] < asivegucf5m[0] && asivegucfcur[0] < asivegucfcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (asivegucfcur[0] < -2000 && asivegucf[0] <= -0.0001 && asivegucf_d[0] >= 0.0001 && asivegucfcur[0] > asivegucf5m[0] && asivegucfcur[0] > asivegucfcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "ga-all-e";
		LabelMake(obname, 0, f + 1060, 645, "GAv ( -E ) (" + DoubleToString(((gavnucfjcur[0] - gavnucfjcur[3]) + (gavnucfjcur[0] - gavnucfjcur[6]) + (gavnucfjcur[0] - gavnucfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((gavnucfjcur[0] - gavnucfjcur[3]) + (gavnucfjcur[0] - gavnucfjcur[6]) + (gavnucfjcur[0] - gavnucfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (gavnucfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (gavnucfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (gavnucfjcur[0] > 2000 && gavnucfj[0] <= -0.0001 && gavnucfj_d[0] >= 0.0001 && gavnucfjcur[0] < gavnucfj5m[0] && gavnucfjcur[0] < gavnucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (gavnucfjcur[0] < -2000 && gavnucfj[0] <= -0.0001 && gavnucfj_d[0] >= 0.0001 && gavnucfjcur[0] > gavnucfj5m[0] && gavnucfjcur[0] > gavnucfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "eu-all-c";
		LabelMake(obname, 0, f + 1160, 645, "EUv ( -C ) (" + DoubleToString(((euvganfjcur[0] - euvganfjcur[3]) + (euvganfjcur[0] - euvganfjcur[6]) + (euvganfjcur[0] - euvganfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((euvganfjcur[0] - euvganfjcur[3]) + (euvganfjcur[0] - euvganfjcur[6]) + (euvganfjcur[0] - euvganfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (euvganfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (euvganfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (euvganfjcur[0] > 2000 && euvganfj[0] <= -0.0001 && euvganfj_d[0] >= 0.0001 && euvganfjcur[0] < euvganfj5m[0] && euvganfjcur[0] < euvganfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (euvganfjcur[0] < -2000 && euvganfj[0] <= -0.0001 && euvganfj_d[0] >= 0.0001 && euvganfjcur[0] > euvganfj5m[0] && euvganfjcur[0] > euvganfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "gc-all-u";
		LabelMake(obname, 0, f + 1260, 645, "GCv ( -U ) (" + DoubleToString(((gcveanfjcur[0] - gcveanfjcur[3]) + (gcveanfjcur[0] - gcveanfjcur[6]) + (gcveanfjcur[0] - gcveanfjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((gcveanfjcur[0] - gcveanfjcur[3]) + (gcveanfjcur[0] - gcveanfjcur[6]) + (gcveanfjcur[0] - gcveanfjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (gcveanfjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (gcveanfjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (gcveanfjcur[0] > 2000 && gcveanfj[0] <= -0.0001 && gcveanfj_d[0] >= 0.0001 && gcveanfjcur[0] < gcveanfj5m[0] && gcveanfjcur[0] < gcveanfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (gcveanfjcur[0] < -2000 && gcveanfj[0] <= -0.0001 && gcveanfj_d[0] >= 0.0001 && gcveanfjcur[0] > gcveanfj5m[0] && gcveanfjcur[0] > gcveanfjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "ef-all-g";
		LabelMake(obname, 0, f + 1360, 645, "EFv ( -G ) (" + DoubleToString(((efvanucjcur[0] - efvanucjcur[3]) + (efvanucjcur[0] - efvanucjcur[6]) + (efvanucjcur[0] - efvanucjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((efvanucjcur[0] - efvanucjcur[3]) + (efvanucjcur[0] - efvanucjcur[6]) + (efvanucjcur[0] - efvanucjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (efvanucjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (efvanucjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (efvanucjcur[0] > 2000 && efvanucj[0] <= -0.0001 && efvanucj_d[0] >= 0.0001 && efvanucjcur[0] < efvanucj5m[0] && efvanucjcur[0] < efvanucjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (efvanucjcur[0] < -2000 && efvanucj[0] <= -0.0001 && efvanucj_d[0] >= 0.0001 && efvanucjcur[0] > efvanucj5m[0] && efvanucjcur[0] > efvanucjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "aj-all-n";
		LabelMake(obname, 0, f + 1060, 770, "AJv ( -N ) (" + DoubleToString(((ajvegucfcur[0] - ajvegucfcur[3]) + (ajvegucfcur[0] - ajvegucfcur[6]) + (ajvegucfcur[0] - ajvegucfcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ajvegucfcur[0] - ajvegucfcur[3]) + (ajvegucfcur[0] - ajvegucfcur[6]) + (ajvegucfcur[0] - ajvegucfcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ajvegucfcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ajvegucfcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ajvegucfcur[0] > 2000 && ajvegucf[0] <= -0.0001 && ajvegucf_d[0] >= 0.0001 && ajvegucfcur[0] < ajvegucf5m[0] && ajvegucfcur[0] < ajvegucfcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ajvegucfcur[0] < -2000 && ajvegucf[0] <= -0.0001 && ajvegucf_d[0] >= 0.0001 && ajvegucfcur[0] > ajvegucf5m[0] && ajvegucfcur[0] > ajvegucfcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "nc-all-a";
		LabelMake(obname, 0, f + 1160, 770, "NCv ( -A ) (" + DoubleToString(((ncvegufjcur[0] - ncvegufjcur[3]) + (ncvegufjcur[0] - ncvegufjcur[6]) + (ncvegufjcur[0] - ncvegufjcur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ncvegufjcur[0] - ncvegufjcur[3]) + (ncvegufjcur[0] - ncvegufjcur[6]) + (ncvegufjcur[0] - ncvegufjcur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ncvegufjcur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ncvegufjcur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ncvegufjcur[0] > 2000 && ncvegufj[0] <= -0.0001 && ncvegufj_d[0] >= 0.0001 && ncvegufjcur[0] < ncvegufj5m[0] && ncvegufjcur[0] < ncvegufjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ncvegufjcur[0] < -2000 && ncvegufj[0] <= -0.0001 && ncvegufj_d[0] >= 0.0001 && ncvegufjcur[0] > ncvegufj5m[0] && ncvegufjcur[0] > ncvegufjcur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}
		obname = Name + "uj-all-f";
		LabelMake(obname, 0, f + 1260, 770, "UJv ( -F ) (" + DoubleToString(((ujveganccur[0] - ujveganccur[3]) + (ujveganccur[0] - ujveganccur[6]) + (ujveganccur[0] - ujveganccur[9])) / 3, 0) + ")", 8, clrGray);
		{
			if (MathAbs(((ujveganccur[0] - ujveganccur[3]) + (ujveganccur[0] - ujveganccur[6]) + (ujveganccur[0] - ujveganccur[9]))) / 3 > 100) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow); ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black"); }
			else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ujveganccur[0] > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			else if (ujveganccur[0] < -2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrTomato);
			if (ujveganccur[0] > 2000 && ujveganc[0] <= -0.0001 && ujveganc_d[0] >= 0.0001 && ujveganccur[0] < ujveganc5m[0] && ujveganccur[0] < ujveganccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
			else if (ujveganccur[0] < -2000 && ujveganc[0] <= -0.0001 && ujveganc_d[0] >= 0.0001 && ujveganccur[0] > ujveganc5m[0] && ujveganccur[0] > ujveganccur[1]) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
		}

		for (int p = 10; p >= 0; p--)
		{
			obname = Name + "egvucfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 30 + p * 10, DoubleToString(egvucfj[p], 2) + " / " + DoubleToString(egvucfjcur[p], 0) + " / " + DoubleToString(egvucfj_d[p], 2), 8, clrGray);
			if (egvucfj[p] > -0.0001 || egvucfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvucfj[p] <= -0.0001 && egvucfj_d[p] >= 0.0001 && egvucfjcur[p] < egvucfj5m[p] && egvucfjcur[p] < egvucfjcur[p + 1] && MathAbs(egvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (egvucfj[p] <= -0.0001 && egvucfj_d[p] >= 0.0001 && egvucfjcur[p] > egvucfj5m[p] && egvucfjcur[p] > egvucfjcur[p + 1] && MathAbs(egvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (egvucfj[p] <= -0.7 && egvucfj_d[p] >= 0.7 && MathAbs(egvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (egvucfj[0] < egvucfj[1] && egvucfj[1] < egvucfj[2] && egvucfj[0] <= -0.0001 && egvucfj_d[0] > egvucfj_d[1] && egvucfj_d[1] > egvucfj_d[2] && egvucfj_d[0] >= 0.0001 && MathAbs(egvucfjcur[0]) >= 2000) { obname = Name + "egvucfj sign"; LabelMake(obname, 0, f + 1050, 20, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "egvucfj sign");

			obname = Name + "egvucan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 30 + p * 10, DoubleToString(egvucan[p], 2) + " / " + DoubleToString(egvucancur[p], 0) + " / " + DoubleToString(egvucan_d[p], 2), 8, clrGray);
			if (egvucan[p] > -0.0001 || egvucan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvucan[p] <= -0.0001 && egvucan_d[p] >= 0.0001 && egvucancur[p] < egvucan5m[p] && egvucancur[p] < egvucancur[p + 1] && MathAbs(egvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (egvucan[p] <= -0.0001 && egvucan_d[p] >= 0.0001 && egvucancur[p] > egvucan5m[p] && egvucancur[p] > egvucancur[p + 1] && MathAbs(egvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (egvucan[p] <= -0.7 && egvucan_d[p] >= 0.7 && MathAbs(egvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (egvucan[0] < egvucan[1] && egvucan[1] < egvucan[2] && egvucan[0] <= -0.0001 && egvucan_d[0] > egvucan_d[1] && egvucan_d[1] > egvucan_d[2] && egvucan_d[0] >= 0.0001 && MathAbs(egvucancur[0]) >= 2000) { obname = Name + "egvucan sign"; LabelMake(obname, 0, f + 1150, 20, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "egvucan sign");

			obname = Name + "egvfjan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 30 + p * 10, DoubleToString(egvfjan[p], 2) + " / " + DoubleToString(egvfjancur[p], 0) + " / " + DoubleToString(egvfjan_d[p], 2), 8, clrGray);
			if (egvfjan[p] > -0.0001 || egvfjan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (egvfjan[p] <= -0.0001 && egvfjan_d[p] >= 0.0001 && egvfjancur[p] < egvfjan5m[p] && egvfjancur[p] < egvfjancur[p + 1] && MathAbs(egvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (egvfjan[p] <= -0.0001 && egvfjan_d[p] >= 0.0001 && egvfjancur[p] > egvfjan5m[p] && egvfjancur[p] > egvfjancur[p + 1] && MathAbs(egvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (egvfjan[p] <= -0.7 && egvfjan_d[p] >= 0.7 && MathAbs(egvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (egvfjan[0] < egvfjan[1] && egvfjan[1] < egvfjan[2] && egvfjan[0] <= -0.0001 && egvfjan_d[0] > egvfjan_d[1] && egvfjan_d[1] > egvfjan_d[2] && egvfjan_d[0] >= 0.0001 && MathAbs(egvfjancur[0]) >= 2000) { obname = Name + "egvfjan sign"; LabelMake(obname, 0, f + 1250, 20, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "egvfjan sign");

			obname = Name + "anvucfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 155 + p * 10, DoubleToString(anvucfj[p], 2) + " / " + DoubleToString(anvucfjcur[p], 0) + " / " + DoubleToString(anvucfj_d[p], 2), 8, clrGray);
			if (anvucfj[p] > -0.0001 || anvucfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anvucfj[p] <= -0.0001 && anvucfj_d[p] >= 0.0001 && anvucfjcur[p] < anvucfj5m[p] && anvucfjcur[p] < anvucfjcur[p + 1] && MathAbs(anvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (anvucfj[p] <= -0.0001 && anvucfj_d[p] >= 0.0001 && anvucfjcur[p] > anvucfj5m[p] && anvucfjcur[p] > anvucfjcur[p + 1] && MathAbs(anvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (anvucfj[p] <= -0.7 && anvucfj_d[p] >= 0.7 && MathAbs(anvucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (anvucfj[0] < anvucfj[1] && anvucfj[1] < anvucfj[2] && anvucfj[0] <= -0.0001 && anvucfj_d[0] > anvucfj_d[1] && anvucfj_d[1] > anvucfj_d[2] && anvucfj_d[0] >= 0.0001 && MathAbs(anvucfjcur[0]) >= 2000) { obname = Name + "anvucfj sign"; LabelMake(obname, 0, f + 1050, 145, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "anvucfj sign");

			obname = Name + "anveguc" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 155 + p * 10, DoubleToString(anveguc[p], 2) + " / " + DoubleToString(anveguccur[p], 0) + " / " + DoubleToString(anveguc_d[p], 2), 8, clrGray);
			if (anveguc[p] > -0.0001 || anveguc_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anveguc[p] <= -0.0001 && anveguc_d[p] >= 0.0001 && anveguccur[p] < anveguc5m[p] && anveguccur[p] < anveguccur[p + 1] && MathAbs(anveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (anveguc[p] <= -0.0001 && anveguc_d[p] >= 0.0001 && anveguccur[p] > anveguc5m[p] && anveguccur[p] > anveguccur[p + 1] && MathAbs(anveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (anveguc[p] <= -0.7 && anveguc_d[p] >= 0.7 && MathAbs(anveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (anveguc[0] < anveguc[1] && anveguc[1] < anveguc[2] && anveguc[0] <= -0.0001 && anveguc_d[0] > anveguc_d[1] && anveguc_d[1] > anveguc_d[2] && anveguc_d[0] >= 0.0001 && MathAbs(anveguccur[0]) >= 2000) { obname = Name + "anveguc sign"; LabelMake(obname, 0, f + 1150, 145, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "anveguc sign");

			obname = Name + "anvegfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 155 + p * 10, DoubleToString(anvegfj[p], 2) + " / " + DoubleToString(anvegfjcur[p], 0) + " / " + DoubleToString(anvegfj_d[p], 2), 8, clrGray);
			if (anvegfj[p] > -0.0001 || anvegfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (anvegfj[p] <= -0.0001 && anvegfj_d[p] >= 0.0001 && anvegfjcur[p] < anvegfj5m[p] && anvegfjcur[p] < anvegfjcur[p + 1] && MathAbs(anvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (anvegfj[p] <= -0.0001 && anvegfj_d[p] >= 0.0001 && anvegfjcur[p] > anvegfj5m[p] && anvegfjcur[p] > anvegfjcur[p + 1] && MathAbs(anvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (anvegfj[p] <= -0.7 && anvegfj_d[p] >= 0.7 && MathAbs(anvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (anvegfj[0] < anvegfj[1] && anvegfj[1] < anvegfj[2] && anvegfj[0] <= -0.0001 && anvegfj_d[0] > anvegfj_d[1] && anvegfj_d[1] > anvegfj_d[2] && anvegfj_d[0] >= 0.0001 && MathAbs(anvegfjcur[0]) >= 2000) { obname = Name + "anvegfj sign"; LabelMake(obname, 0, f + 1250, 145, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "anvegfj sign");

			obname = Name + "ucvegfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 280 + p * 10, DoubleToString(ucvegfj[p], 2) + " / " + DoubleToString(ucvegfjcur[p], 0) + " / " + DoubleToString(ucvegfj_d[p], 2), 8, clrGray);
			if (ucvegfj[p] > -0.0001 || ucvegfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvegfj[p] <= -0.0001 && ucvegfj_d[p] >= 0.0001 && ucvegfjcur[p] < ucvegfj5m[p] && ucvegfjcur[p] < ucvegfjcur[p + 1] && MathAbs(ucvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ucvegfj[p] <= -0.0001 && ucvegfj_d[p] >= 0.0001 && ucvegfjcur[p] > ucvegfj5m[p] && ucvegfjcur[p] > ucvegfjcur[p + 1] && MathAbs(ucvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ucvegfj[p] <= -0.7 && ucvegfj_d[p] >= 0.7 && MathAbs(ucvegfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ucvegfj[0] < ucvegfj[1] && ucvegfj[1] < ucvegfj[2] && ucvegfj[0] <= -0.0001 && ucvegfj_d[0] > ucvegfj_d[1] && ucvegfj_d[1] > ucvegfj_d[2] && ucvegfj_d[0] >= 0.0001 && MathAbs(ucvegfjcur[0]) >= 2000) { obname = Name + "ucvegfj sign"; LabelMake(obname, 0, f + 1050, 270, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ucvegfj sign");

			obname = Name + "ucvegan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 280 + p * 10, DoubleToString(ucvegan[p], 2) + " / " + DoubleToString(ucvegancur[p], 0) + " / " + DoubleToString(ucvegan_d[p], 2), 8, clrGray);
			if (ucvegan[p] > -0.0001 || ucvegan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvegan[p] <= -0.0001 && ucvegan_d[p] >= 0.0001 && ucvegancur[p] < ucvegan5m[p] && ucvegancur[p] < ucvegancur[p + 1] && MathAbs(ucvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ucvegan[p] <= -0.0001 && ucvegan_d[p] >= 0.0001 && ucvegancur[p] > ucvegan5m[p] && ucvegancur[p] > ucvegancur[p + 1] && MathAbs(ucvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ucvegan[p] <= -0.7 && ucvegan_d[p] >= 0.7 && MathAbs(ucvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ucvegan[0] < ucvegan[1] && ucvegan[1] < ucvegan[2] && ucvegan[0] <= -0.0001 && ucvegan_d[0] > ucvegan_d[1] && ucvegan_d[1] > ucvegan_d[2] && ucvegan_d[0] >= 0.0001 && MathAbs(ucvegancur[0]) >= 2000) { obname = Name + "ucvegan sign"; LabelMake(obname, 0, f + 1150, 270, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ucvegan sign");

			obname = Name + "ucvfjan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 280 + p * 10, DoubleToString(ucvfjan[p], 2) + " / " + DoubleToString(ucvfjancur[p], 0) + " / " + DoubleToString(ucvfjan_d[p], 2), 8, clrGray);
			if (ucvfjan[p] > -0.0001 || ucvfjan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ucvfjan[p] <= -0.0001 && ucvfjan_d[p] >= 0.0001 && ucvfjancur[p] < ucvfjan5m[p] && ucvfjancur[p] < ucvfjancur[p + 1] && MathAbs(ucvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ucvfjan[p] <= -0.0001 && ucvfjan_d[p] >= 0.0001 && ucvfjancur[p] > ucvfjan5m[p] && ucvfjancur[p] > ucvfjancur[p + 1] && MathAbs(ucvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ucvfjan[p] <= -0.7 && ucvfjan_d[p] >= 0.7 && MathAbs(ucvfjancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ucvfjan[0] < ucvfjan[1] && ucvfjan[1] < ucvfjan[2] && ucvfjan[0] <= -0.0001 && ucvfjan_d[0] > ucvfjan_d[1] && ucvfjan_d[1] > ucvfjan_d[2] && ucvfjan_d[0] >= 0.0001 && MathAbs(ucvfjancur[0]) >= 2000) { obname = Name + "ucvfjan sign"; LabelMake(obname, 0, f + 1250, 270, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ucvfjan sign");

			obname = Name + "fjveguc" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 405 + p * 10, DoubleToString(fjveguc[p], 2) + " / " + DoubleToString(fjveguccur[p], 0) + " / " + DoubleToString(fjveguc_d[p], 2), 8, clrGray);
			if (fjveguc[p] > -0.0001 || fjveguc_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjveguc[p] <= -0.0001 && fjveguc_d[p] >= 0.0001 && fjveguccur[p] < fjveguc5m[p] && fjveguccur[p] < fjveguccur[p + 1] && MathAbs(fjveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (fjveguc[p] <= -0.0001 && fjveguc_d[p] >= 0.0001 && fjveguccur[p] > fjveguc5m[p] && fjveguccur[p] > fjveguccur[p + 1] && MathAbs(fjveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (fjveguc[p] <= -0.7 && fjveguc_d[p] >= 0.7 && MathAbs(fjveguccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (fjveguc[0] < fjveguc[1] && fjveguc[1] < fjveguc[2] && fjveguc[0] <= -0.0001 && fjveguc_d[0] > fjveguc_d[1] && fjveguc_d[1] > fjveguc_d[2] && fjveguc_d[0] >= 0.0001 && MathAbs(fjveguccur[0]) >= 2000) { obname = Name + "fjveguc sign"; LabelMake(obname, 0, f + 1050, 395, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "fjveguc sign");

			obname = Name + "fjvucan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 405 + p * 10, DoubleToString(fjvucan[p], 2) + " / " + DoubleToString(fjvucancur[p], 0) + " / " + DoubleToString(fjvucan_d[p], 2), 8, clrGray);
			if (fjvucan[p] > -0.0001 || fjvucan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjvucan[p] <= -0.0001 && fjvucan_d[p] >= 0.0001 && fjvucancur[p] < fjvucan5m[p] && fjvucancur[p] < fjvucancur[p + 1] && MathAbs(fjvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (fjvucan[p] <= -0.0001 && fjvucan_d[p] >= 0.0001 && fjvucancur[p] > fjvucan5m[p] && fjvucancur[p] > fjvucancur[p + 1] && MathAbs(fjvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (fjvucan[p] <= -0.7 && fjvucan_d[p] >= 0.7 && MathAbs(fjvucancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (fjvucan[0] < fjvucan[1] && fjvucan[1] < fjvucan[2] && fjvucan[0] <= -0.0001 && fjvucan_d[0] > fjvucan_d[1] && fjvucan_d[1] > fjvucan_d[2] && fjvucan_d[0] >= 0.0001 && MathAbs(fjvucancur[0]) >= 2000) { obname = Name + "fjvucan sign"; LabelMake(obname, 0, f + 1150, 395, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "fjvucan sign");

			obname = Name + "fjvegan" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 405 + p * 10, DoubleToString(fjvegan[p], 2) + " / " + DoubleToString(fjvegancur[p], 0) + " / " + DoubleToString(fjvegan_d[p], 2), 8, clrGray);
			if (fjvegan[p] > -0.0001 || fjvegan_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (fjvegan[p] <= -0.0001 && fjvegan_d[p] >= 0.0001 && fjvegancur[p] < fjvegan5m[p] && fjvegancur[p] < fjvegancur[p + 1] && MathAbs(fjvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (fjvegan[p] <= -0.0001 && fjvegan_d[p] >= 0.0001 && fjvegancur[p] > fjvegan5m[p] && fjvegancur[p] > fjvegancur[p + 1] && MathAbs(fjvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (fjvegan[p] <= -0.7 && fjvegan_d[p] >= 0.7 && MathAbs(fjvegancur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (fjvegan[0] < fjvegan[1] && fjvegan[1] < fjvegan[2] && fjvegan[0] <= -0.0001 && fjvegan_d[0] > fjvegan_d[1] && fjvegan_d[1] > fjvegan_d[2] && fjvegan_d[0] >= 0.0001 && MathAbs(fjvegancur[0]) >= 2000) { obname = Name + "fjvegan sign"; LabelMake(obname, 0, f + 1250, 395, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "fjvegan sign");

			obname = Name + "eurvanucj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1460, 30 + p * 10, DoubleToString(eurvanucj[p], 2) + " / " + DoubleToString(eurvanucjcur[p], 0) + " / " + DoubleToString(eurvanucj_d[p], 2), 8, clrGray);
			if (eurvanucj[p] > -0.0001 || eurvanucj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (eurvanucj[p] <= -0.0001 && eurvanucj_d[p] >= 0.0001 && eurvanucjcur[p] < eurvanucj5m[p] && eurvanucjcur[p] < eurvanucjcur[p + 1] && MathAbs(eurvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (eurvanucj[p] <= -0.0001 && eurvanucj_d[p] >= 0.0001 && eurvanucjcur[p] > eurvanucj5m[p] && eurvanucjcur[p] > eurvanucjcur[p + 1] && MathAbs(eurvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (eurvanucj[p] <= -0.7 && eurvanucj_d[p] >= 0.7 && MathAbs(eurvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (eurvanucj[0] < eurvanucj[1] && eurvanucj[1] < eurvanucj[2] && eurvanucj[0] <= -0.0001 && eurvanucj_d[0] > eurvanucj_d[1] && eurvanucj_d[1] > eurvanucj_d[2] && eurvanucj_d[0] >= 0.0001 && MathAbs(eurvanucjcur[0]) >= 2000) { obname = Name + "eurvanucj sign"; LabelMake(obname, 0, f + 1450, 20, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "eurvanucj sign");

			obname = Name + "comvegufj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1460, 155 + p * 10, DoubleToString(comvegufj[p], 2) + " / " + DoubleToString(comvegufjcur[p], 0) + " / " + DoubleToString(comvegufj_d[p], 2), 8, clrGray);
			if (comvegufj[p] > -0.0001 || comvegufj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (comvegufj[p] <= -0.0001 && comvegufj_d[p] >= 0.0001 && comvegufjcur[p] < comvegufj5m[p] && comvegufjcur[p] < comvegufjcur[p + 1] && MathAbs(comvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (comvegufj[p] <= -0.0001 && comvegufj_d[p] >= 0.0001 && comvegufjcur[p] > comvegufj5m[p] && comvegufjcur[p] > comvegufjcur[p + 1] && MathAbs(comvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (comvegufj[p] <= -0.7 && comvegufj_d[p] >= 0.7 && MathAbs(comvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (comvegufj[0] < comvegufj[1] && comvegufj[1] < comvegufj[2] && comvegufj[0] <= -0.0001 && comvegufj_d[0] > comvegufj_d[1] && comvegufj_d[1] > comvegufj_d[2] && comvegufj_d[0] >= 0.0001 && MathAbs(comvegufjcur[0]) >= 2000) { obname = Name + "comvegufj sign"; LabelMake(obname, 0, f + 1450, 145, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "comvegufj sign");

			obname = Name + "risveganc" + IntegerToString(p);
			LabelMake(obname, 0, f + 1460, 280 + p * 10, DoubleToString(risveganc[p], 2) + " / " + DoubleToString(risveganccur[p], 0) + " / " + DoubleToString(risveganc_d[p], 2), 8, clrGray);
			if (risveganc[p] > -0.0001 || risveganc_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (risveganc[p] <= -0.0001 && risveganc_d[p] >= 0.0001 && risveganccur[p] < risveganc5m[p] && risveganccur[p] < risveganccur[p + 1] && MathAbs(risveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (risveganc[p] <= -0.0001 && risveganc_d[p] >= 0.0001 && risveganccur[p] > risveganc5m[p] && risveganccur[p] > risveganccur[p + 1] && MathAbs(risveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (risveganc[p] <= -0.7 && risveganc_d[p] >= 0.7 && MathAbs(risveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (risveganc[0] < risveganc[1] && risveganc[1] < risveganc[2] && risveganc[0] <= -0.0001 && risveganc_d[0] > risveganc_d[1] && risveganc_d[1] > risveganc_d[2] && risveganc_d[0] >= 0.0001 && MathAbs(risveganccur[0]) >= 2000) { obname = Name + "risveganc sign"; LabelMake(obname, 0, f + 1450, 270, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "risveganc sign");

			obname = Name + "ganveucfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1460, 405 + p * 10, DoubleToString(ganveucfj[p], 2) + " / " + DoubleToString(ganveucfjcur[p], 0) + " / " + DoubleToString(ganveucfj_d[p], 2), 8, clrGray);
			if (ganveucfj[p] > -0.0001 || ganveucfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ganveucfj[p] <= -0.0001 && ganveucfj_d[p] >= 0.0001 && ganveucfjcur[p] < ganveucfj5m[p] && ganveucfjcur[p] < ganveucfjcur[p + 1] && MathAbs(ganveucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ganveucfj[p] <= -0.0001 && ganveucfj_d[p] >= 0.0001 && ganveucfjcur[p] > ganveucfj5m[p] && ganveucfjcur[p] > ganveucfjcur[p + 1] && MathAbs(ganveucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ganveucfj[p] <= -0.7 && ganveucfj_d[p] >= 0.7 && MathAbs(ganveucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ganveucfj[0] < ganveucfj[1] && ganveucfj[1] < ganveucfj[2] && ganveucfj[0] <= -0.0001 && ganveucfj_d[0] > ganveucfj_d[1] && ganveucfj_d[1] > ganveucfj_d[2] && ganveucfj_d[0] >= 0.0001 && MathAbs(ganveucfjcur[0]) >= 2000) { obname = Name + "ganveucfj sign"; LabelMake(obname, 0, f + 1450, 395, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ganveucfj sign");

			obname = Name + "asivegucf" + IntegerToString(p);
			LabelMake(obname, 0, f + 1460, 530 + p * 10, DoubleToString(asivegucf[p], 2) + " / " + DoubleToString(asivegucfcur[p], 0) + " / " + DoubleToString(asivegucf_d[p], 2), 8, clrGray);
			if (asivegucf[p] > -0.0001 || asivegucf_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (asivegucf[p] <= -0.0001 && asivegucf_d[p] >= 0.0001 && asivegucfcur[p] < asivegucf5m[p] && asivegucfcur[p] < asivegucfcur[p + 1] && MathAbs(asivegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (asivegucf[p] <= -0.0001 && asivegucf_d[p] >= 0.0001 && asivegucfcur[p] > asivegucf5m[p] && asivegucfcur[p] > asivegucfcur[p + 1] && MathAbs(asivegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (asivegucf[p] <= -0.7 && asivegucf_d[p] >= 0.7 && MathAbs(asivegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (asivegucf[0] < asivegucf[1] && asivegucf[1] < asivegucf[2] && asivegucf[0] <= -0.0001 && asivegucf_d[0] > asivegucf_d[1] && asivegucf_d[1] > asivegucf_d[2] && asivegucf_d[0] >= 0.0001 && MathAbs(asivegucfcur[0]) >= 2000) { obname = Name + "asivegucf sign"; LabelMake(obname, 0, f + 1450, 520, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "asivegucf sign");

			obname = Name + "gavnucfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 655 + p * 10, DoubleToString(gavnucfj[p], 2) + " / " + DoubleToString(gavnucfjcur[p], 0) + " / " + DoubleToString(gavnucfj_d[p], 2), 8, clrGray);
			if (gavnucfj[p] > -0.0001 || gavnucfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (gavnucfj[p] <= -0.0001 && gavnucfj_d[p] >= 0.0001 && gavnucfjcur[p] < gavnucfj5m[p] && gavnucfjcur[p] < gavnucfjcur[p + 1] && MathAbs(gavnucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (gavnucfj[p] <= -0.0001 && gavnucfj_d[p] >= 0.0001 && gavnucfjcur[p] > gavnucfj5m[p] && gavnucfjcur[p] > gavnucfjcur[p + 1] && MathAbs(gavnucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (gavnucfj[p] <= -0.7 && gavnucfj_d[p] >= 0.7 && MathAbs(gavnucfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (gavnucfj[0] < gavnucfj[1] && gavnucfj[1] < gavnucfj[2] && gavnucfj[0] <= -0.0001 && gavnucfj_d[0] > gavnucfj_d[1] && gavnucfj_d[1] > gavnucfj_d[2] && gavnucfj_d[0] >= 0.0001 && MathAbs(gavnucfjcur[0]) >= 2000) { obname = Name + "gavnucfj sign"; LabelMake(obname, 0, f + 1050, 645, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "gavnucfj sign");

			obname = Name + "euvganfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 655 + p * 10, DoubleToString(euvganfj[p], 2) + " / " + DoubleToString(euvganfjcur[p], 0) + " / " + DoubleToString(euvganfj_d[p], 2), 8, clrGray);
			if (euvganfj[p] > -0.0001 || euvganfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (euvganfj[p] <= -0.0001 && euvganfj_d[p] >= 0.0001 && euvganfjcur[p] < euvganfj5m[p] && euvganfjcur[p] < euvganfjcur[p + 1] && MathAbs(euvganfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (euvganfj[p] <= -0.0001 && euvganfj_d[p] >= 0.0001 && euvganfjcur[p] > euvganfj5m[p] && euvganfjcur[p] > euvganfjcur[p + 1] && MathAbs(euvganfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (euvganfj[p] <= -0.7 && euvganfj_d[p] >= 0.7 && MathAbs(euvganfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (euvganfj[0] < euvganfj[1] && euvganfj[1] < euvganfj[2] && euvganfj[0] <= -0.0001 && euvganfj_d[0] > euvganfj_d[1] && euvganfj_d[1] > euvganfj_d[2] && euvganfj_d[0] >= 0.0001 && MathAbs(euvganfjcur[0]) >= 2000) { obname = Name + "euvganfj sign"; LabelMake(obname, 0, f + 1150, 645, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "euvganfj sign");

			obname = Name + "gcveanfj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 655 + p * 10, DoubleToString(gcveanfj[p], 2) + " / " + DoubleToString(gcveanfjcur[p], 0) + " / " + DoubleToString(gcveanfj_d[p], 2), 8, clrGray);
			if (gcveanfj[p] > -0.0001 || gcveanfj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (gcveanfj[p] <= -0.0001 && gcveanfj_d[p] >= 0.0001 && gcveanfjcur[p] < gcveanfj5m[p] && gcveanfjcur[p] < gcveanfjcur[p + 1] && MathAbs(gcveanfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (gcveanfj[p] <= -0.0001 && gcveanfj_d[p] >= 0.0001 && gcveanfjcur[p] > gcveanfj5m[p] && gcveanfjcur[p] > gcveanfjcur[p + 1] && MathAbs(gcveanfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (gcveanfj[p] <= -0.7 && gcveanfj_d[p] >= 0.7 && MathAbs(gcveanfjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (gcveanfj[0] < gcveanfj[1] && gcveanfj[1] < gcveanfj[2] && gcveanfj[0] <= -0.0001 && gcveanfj_d[0] > gcveanfj_d[1] && gcveanfj_d[1] > gcveanfj_d[2] && gcveanfj_d[0] >= 0.0001 && MathAbs(gcveanfjcur[0]) >= 2000) { obname = Name + "gcveanfj sign"; LabelMake(obname, 0, f + 1250, 645, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "gcveanfj sign");

			obname = Name + "efvanucj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1360, 655 + p * 10, DoubleToString(efvanucj[p], 2) + " / " + DoubleToString(efvanucjcur[p], 0) + " / " + DoubleToString(efvanucj_d[p], 2), 8, clrGray);
			if (efvanucj[p] > -0.0001 || efvanucj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (efvanucj[p] <= -0.0001 && efvanucj_d[p] >= 0.0001 && efvanucjcur[p] < efvanucj5m[p] && efvanucjcur[p] < efvanucjcur[p + 1] && MathAbs(efvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (efvanucj[p] <= -0.0001 && efvanucj_d[p] >= 0.0001 && efvanucjcur[p] > efvanucj5m[p] && efvanucjcur[p] > efvanucjcur[p + 1] && MathAbs(efvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (efvanucj[p] <= -0.7 && efvanucj_d[p] >= 0.7 && MathAbs(efvanucjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (efvanucj[0] < efvanucj[1] && efvanucj[1] < efvanucj[2] && efvanucj[0] <= -0.0001 && efvanucj_d[0] > efvanucj_d[1] && efvanucj_d[1] > efvanucj_d[2] && efvanucj_d[0] >= 0.0001 && MathAbs(efvanucjcur[0]) >= 2000) { obname = Name + "efvanucj sign"; LabelMake(obname, 0, f + 1350, 645, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "efvanucj sign");

			obname = Name + "ajvegucf" + IntegerToString(p);
			LabelMake(obname, 0, f + 1060, 780 + p * 10, DoubleToString(ajvegucf[p], 2) + " / " + DoubleToString(ajvegucfcur[p], 0) + " / " + DoubleToString(ajvegucf_d[p], 2), 8, clrGray);
			if (ajvegucf[p] > -0.0001 || ajvegucf_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ajvegucf[p] <= -0.0001 && ajvegucf_d[p] >= 0.0001 && ajvegucfcur[p] < ajvegucf5m[p] && ajvegucfcur[p] < ajvegucfcur[p + 1] && MathAbs(ajvegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ajvegucf[p] <= -0.0001 && ajvegucf_d[p] >= 0.0001 && ajvegucfcur[p] > ajvegucf5m[p] && ajvegucfcur[p] > ajvegucfcur[p + 1] && MathAbs(ajvegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ajvegucf[p] <= -0.7 && ajvegucf_d[p] >= 0.7 && MathAbs(ajvegucfcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ajvegucf[0] < ajvegucf[1] && ajvegucf[1] < ajvegucf[2] && ajvegucf[0] <= -0.0001 && ajvegucf_d[0] > ajvegucf_d[1] && ajvegucf_d[1] > ajvegucf_d[2] && ajvegucf_d[0] >= 0.0001 && MathAbs(ajvegucfcur[0]) >= 2000) { obname = Name + "ajvegucf sign"; LabelMake(obname, 0, f + 1050, 770, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ajvegucf sign");

			obname = Name + "ncvegufj" + IntegerToString(p);
			LabelMake(obname, 0, f + 1160, 780 + p * 10, DoubleToString(ncvegufj[p], 2) + " / " + DoubleToString(ncvegufjcur[p], 0) + " / " + DoubleToString(ncvegufj_d[p], 2), 8, clrGray);
			if (ncvegufj[p] > -0.0001 || ncvegufj_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ncvegufj[p] <= -0.0001 && ncvegufj_d[p] >= 0.0001 && ncvegufjcur[p] < ncvegufj5m[p] && ncvegufjcur[p] < ncvegufjcur[p + 1] && MathAbs(ncvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ncvegufj[p] <= -0.0001 && ncvegufj_d[p] >= 0.0001 && ncvegufjcur[p] > ncvegufj5m[p] && ncvegufjcur[p] > ncvegufjcur[p + 1] && MathAbs(ncvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ncvegufj[p] <= -0.7 && ncvegufj_d[p] >= 0.7 && MathAbs(ncvegufjcur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ncvegufj[0] < ncvegufj[1] && ncvegufj[1] < ncvegufj[2] && ncvegufj[0] <= -0.0001 && ncvegufj_d[0] > ncvegufj_d[1] && ncvegufj_d[1] > ncvegufj_d[2] && ncvegufj_d[0] >= 0.0001 && MathAbs(ncvegufjcur[0]) >= 2000) { obname = Name + "ncvegufj sign"; LabelMake(obname, 0, f + 1150, 770, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ncvegufj sign");

			obname = Name + "ujveganc" + IntegerToString(p);
			LabelMake(obname, 0, f + 1260, 780 + p * 10, DoubleToString(ujveganc[p], 2) + " / " + DoubleToString(ujveganccur[p], 0) + " / " + DoubleToString(ujveganc_d[p], 2), 8, clrGray);
			if (ujveganc[p] > -0.0001 || ujveganc_d[p] < 0.0001) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGray);
			if (ujveganc[p] <= -0.0001 && ujveganc_d[p] >= 0.0001 && ujveganccur[p] < ujveganc5m[p] && ujveganccur[p] < ujveganccur[p + 1] && MathAbs(ujveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (ujveganc[p] <= -0.0001 && ujveganc_d[p] >= 0.0001 && ujveganccur[p] > ujveganc5m[p] && ujveganccur[p] > ujveganccur[p + 1] && MathAbs(ujveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrDodgerBlue);
			if (ujveganc[p] <= -0.7 && ujveganc_d[p] >= 0.7 && MathAbs(ujveganccur[p]) > 2000) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
			if (ujveganc[0] < ujveganc[1] && ujveganc[1] < ujveganc[2] && ujveganc[0] <= -0.0001 && ujveganc_d[0] > ujveganc_d[1] && ujveganc_d[1] > ujveganc_d[2] && ujveganc_d[0] >= 0.0001 && MathAbs(ujveganccur[0]) >= 2000) { obname = Name + "ujveganc sign"; LabelMake(obname, 0, f + 1250, 770, CharToString(204), 9, clrLime); ObjectSetString(0, obname, OBJPROP_FONT, "Wingdings"); }
			else ObjectDelete(0, Name + "ujveganc sign");
		}

		/*
		static datetime csvsave = 0;
		bool csvsave1 = false;
		if (csvsave < iTime(_Symbol, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 25) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 40)))
		{
		   csvsave = iTime(_Symbol, PERIOD_H1, 0);
		   csvsave1 = true;
		}
		if (csvsave1)
		{
		   for (int x = 60; x >= 0; x--)
		   {
			  mosquitoes["R"][x] = R1pre[x];
			  mosquitoes["R2"][x] = R2pre[x];
			  mosquitoes["R3"][x] = R3pre[x];
			  mosquitoes["rec"][x] = Recpre[x];
			  mosquitoes["total"][x] = Totalpre[x];
			  mosquitoes["eq"][x] = EQpre[x];
			  mosquitoes["R1std"][x] = R1stdpre[x];
		   }
		   files();
		   csvsave1 = false;
		}
		*/

		static datetime csvsave = 0;
		bool csvsave1 = false;
		if (csvsave < iTime(_Symbol, PERIOD_M1, 0))
		{
			csvsave = iTime(_Symbol, PERIOD_M1, 0);
			csvsave1 = true;
		}
		if (csvsave1)
		{
			filed(R, skunk, mainsort["d"][0].ToDbl(), rrr, R1TPCstdpre[0], R1TDCstdpre[0], R2TPCstdpre[0], R2TDCstdpre[0], R4, RC, R5, R5o, Totalpre[0], buy + sell, percdata["DayAll"][0].ToDbl(),
				percdata["DayAllS"][0].ToDbl(), percdata["DayAllA"][0].ToDbl(), fretr, frecretr, ofrecretr, eurbas, gbpbas, audbas, nzdbas, usdbas, cadbas, chfbas, jpybas);
			csvsave1 = false;
		}

		//FRIX FRIX2
		obname = Name + " FRIXa ";
		LabelMake(obname, 0, f + 10, 640, "FRIX: " + DoubleToString(count * cunt, 1) + " (" + IntegerToString(count) + ") / R: " + DoubleToString(R, 2), 11, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (R >= 1.00 && R < 1.10) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		else if (R >= 1.10 && R < 2.30) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		else if (R >= 2.30) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);

		obname = Name + " FRIXb ";
		LabelMake(obname, 0, f + 200, 630, "R4: " + DoubleToString(R4, 2) + " / RC: " + DoubleToString(RC, 2), 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (R4 >= 1.00 && R4 < 2) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		else if (R4 >= 2 && R4 < 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		else if (R4 >= 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);
		obname = Name + " FRIXc ";
		LabelMake(obname, 0, f + 225, 645, "R5: " + DoubleToString(R5, 2) + "(" + DoubleToString(R5o, 2) + ")", 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (R4 >= 1.00 && R4 < 2) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		else if (R4 >= 2 && R4 < 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		else if (R4 >= 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGold);

		obname = Name + " FRIX2 ";
		LabelMake(obname, 0, f + 10, 660, "FRIX2: " + DoubleToString(28 * cunt, 1) + " / R2: " + DoubleToString(skunk, 2) + " / R3: " + DoubleToString(mainsort["d"][0].ToDbl(), 2), 10, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		if (skunk >= 1.00 && skunk < 1.20) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrOrange);
		else if (skunk >= 1.20 && skunk < 2.01) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
		else if (skunk >= 2.01) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrPink);

		obname = Name + " FRIX3 ";
		LabelMake(obname, 0, f + 10, 675, "pR2: 1/" + DoubleToString(R2a1, 2) + " 2/" + DoubleToString(R2a2, 2) + " 3/" + DoubleToString(R2a3, 2) + " 4/" + DoubleToString(R2a4, 2) + " 5/" + DoubleToString(R2a5, 2) + " 6/" + DoubleToString(R2a6, 2) + " 7/" + DoubleToString(R2a7, 2) + " 8/" + DoubleToString(R2a8, 2), 8, clrYellow);

		obname = Name + " FRIX4 ";
		LabelMake(obname, 0, f + 10, 685, "aR2: 3x: " + DoubleToString(R2b1, 2) + " 5x: " + DoubleToString(R2b2, 2) + " 7x: " + DoubleToString(R2b3, 2) + " 10x: " + DoubleToString(R2b4, 2), 8, clrYellow);

		obname = Name + "Spread";
		LabelMake(obname, 0, f + 350, 730, "cost: " + DoubleToString(totspr * count + 7 * count, 1), 8, clrYellow);

		//RECOMMENDED PAIRS
		obname = Name + " topRecommendtop";
		LabelMake(obname, 0, f + 350, 740, "RECOMMEND:", 10, clrWhite);

		obname = Name + " topRecommend";
		LabelMake(obname, 0, f + 325, 755, "BUY: $" + DoubleToString(buy, 0), 10, clrWhite);

		for (int x = 8; x >= 0; x--)
		{
			obname = Name + " topRecommend" + IntegerToString(x);
			LabelMake(obname, 0, f + 325, 770 + x * 12, baskets2[a[x]].ToStr(), 10, C'0xc0,0xb0,0xff');
			if (StringLen(baskets2[a[x]].ToStr()) == 7) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xc0,0xb0,0xff' - C'0x00,0x60,0xff');
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[a[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		}
		obname = Name + " botRecommend";
		LabelMake(obname, 0, f + 400, 755, "SELL: $" + DoubleToString(sell, 0), 10, clrWhite);
		for (int x = 8; x >= 0; x--)
		{
			obname = Name + " botRecommend" + IntegerToString(x);
			LabelMake(obname, 0, f + 400, 770 + x * 12, baskets2[b[x]].ToStr(), 10, C'0xff,0xaa,0x8e');
			if (StringLen(baskets2[b[x]].ToStr()) == 7) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xff,0xaa,0x8e' - C'0xff,0x5a,0x00');
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][0].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][1].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][3].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][2].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 3) == 3 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][7].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][6].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
			if (StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][4].ToStr(), 4) == 4 && StringFind(baskets2[b[x]].ToStr(), basketdata["Day"][5].ToStr(), 1) == 1) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
		}

		obname = Name + " totalRecommend";
		LabelMake(obname, 0, f + 345, 885, "TOT: " + DoubleToString(buy + sell, 0) + " / r: " + DoubleToString((buy + sell) / maxbasket, 2), 10, clrWhite);

		obname = Name + "ENTRY";
		LabelMake(obname, 0, f + 355, 620, "ENTER", 24, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		obname = Name + "ENTRY2";
		LabelMake(obname, 0, f + 355, 650, "Ravg " + DoubleToString(r1avg, 2), 16, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		obname = Name + "ENTRY3";
		LabelMake(obname, 0, f + 355, 670, "RRR: " + DoubleToString(rrr, 2), 16, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		obname = Name + "ENTRY34";
		LabelMake(obname, 0, f + 355, 690, "REC: " + DoubleToString((buy + sell) / maxbasket, 2), 16, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");
		obname = Name + "ENTRY35";
		LabelMake(obname, 0, f + 355, 715, "Ds: " + percdata["DayAllS"][0].ToStr() + " As: " + percdata["DayAllA"][0].ToStr(), 8, clrRed);
		ObjectSetString(0, obname, OBJPROP_FONT, "Segoe UI Black");

		if (R >= 1.10 && rpre1 >= 1.10 && rpre2 >= 1.10)
			ObjectSetInteger(0, Name + "ENTRY", OBJPROP_COLOR, clrGreen);
		if (r1avg >= 1.10)
			ObjectSetInteger(0, Name + "ENTRY2", OBJPROP_COLOR, clrGreen);
		if (rrr >= 1)
			ObjectSetInteger(0, Name + "ENTRY3", OBJPROP_COLOR, clrGreen);
		if ((buy + sell) >= 2000 && (buy + sell) > maxbasket)
			ObjectSetInteger(0, Name + "ENTRY34", OBJPROP_COLOR, clrGreen);
		if (percdata["DayAllS"][0].ToDbl() >= 50 && percdata["DayAllA"][0].ToDbl() >= 20)
			ObjectSetInteger(0, Name + "ENTRY35", OBJPROP_COLOR, clrGreen);
		if (((buy + sell >= 2000) && R >= 1.10 && (buy + sell) > maxbasket && r1avg >= 1.10 && rrr >= 1 && rpre1 >= 1.10 && rpre2 >= 1.10) && percdata["DayAllS"][0].ToDbl() >= 50 && percdata["DayAllA"][0].ToDbl() >= 20 && frecretr >= 20 && fretr >= 30)
		{
			ObjectSetInteger(0, Name + "ENTRY", OBJPROP_COLOR, clrGold);
			ObjectSetInteger(0, Name + "ENTRY2", OBJPROP_COLOR, clrGold);
			ObjectSetInteger(0, Name + "ENTRY3", OBJPROP_COLOR, clrGold);
			ObjectSetInteger(0, Name + "ENTRY34", OBJPROP_COLOR, clrGold);
			ObjectSetInteger(0, Name + "ENTRY35", OBJPROP_COLOR, clrGold);
		}
	}

	basketdata.Clear();
	percdata.Clear();
	mainsort.Clear();
	baskets2.Clear();
}

//DE-INIT
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	mainsort.Clear();
	basketdata.Clear();
	baskets2.Clear();
	percdata.Clear();
	mosquitoes.Clear();
	pastdata.Clear();

	EventKillTimer();

	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

void filed(double r1, double r2, double r3, double rrr, double r1pc, double r1dc, double r2pc, double r2dc, double r4, double rc, double r5, double r5o, double total, double rec, double dvola, double svola, double act, double fretr, double frecretr, double orecretr, double eurbas, double gbpbas, double audbas, double nzdbas, double usdbas, double cadbas, double chfbas, double jpybas)
{
	string fileName = TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE) + " stats " + Name + ".csv";
	int fileHandle = FileOpen(fileName, FILE_CSV | FILE_WRITE | FILE_READ, ";");
	FileSeek(fileHandle, 0, SEEK_END);

	if (fileHandle != INVALID_HANDLE)
	{
		FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_M1, 0), TIME_DATE | TIME_MINUTES), DoubleToString(r1, 2), DoubleToString(r2, 2), DoubleToString(r3, 2), DoubleToString(rrr, 2),
			DoubleToString(r1pc, 2), DoubleToString(r1dc, 2), DoubleToString(r2pc, 2), DoubleToString(r2dc, 2), DoubleToString(r4, 2), DoubleToString(rc, 2), DoubleToString(r5, 2),
			DoubleToString(r5o, 2), DoubleToString(total, 0), DoubleToString(rec, 0), DoubleToString(dvola, 2), DoubleToString(svola, 2), DoubleToString(act, 2), DoubleToString(fretr, 2),
			DoubleToString(frecretr, 2), DoubleToString(orecretr, 2), DoubleToString(eurbas, 0), DoubleToString(gbpbas, 0), DoubleToString(audbas, 0), DoubleToString(nzdbas, 0), DoubleToString(usdbas, 0),
			DoubleToString(cadbas, 0), DoubleToString(chfbas, 0), DoubleToString(jpybas, 0));
	}

	FileClose(fileHandle);
}

void file(double& file1[], double& file2[], double& file3[], double& file4[], double& file5[], double& file6[], double& file7[])
{
	string fileName = TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE) + " " + Name + ".csv";
	int fileHandle = FileOpen(fileName, FILE_CSV | FILE_WRITE | FILE_READ, ";");

	FileSeek(fileHandle, 0, SEEK_END);

	FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE | TIME_MINUTES));

	if (fileHandle != INVALID_HANDLE)
	{
		for (int i = 0; i <= ArraySize(file1) - 1; i++)
		{
			FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_M1, ArraySize(file1) - i - 1), TIME_DATE | TIME_MINUTES), DoubleToString(file1[i], 2), DoubleToString(file2[i], 2), DoubleToString(file3[i], 2), DoubleToString(file4[i], 2), DoubleToString(file5[i], 0), DoubleToString(file6[i], 0), DoubleToString(file7[i], 2));
		}

		FileClose(fileHandle);
		Print("Data written to ", fileName);
	}
	else
	{
		Print("Failed to open the file!");
	}
	mosquitoes.Clear();
}

void files()
{
	double filee[], fileg[], filea[], filen[], filec[], fileu[];
	ArrayResize(filee, 61);
	ArrayResize(fileg, 61);
	ArrayResize(filea, 61);
	ArrayResize(filen, 61);
	ArrayResize(filec, 61);
	ArrayResize(fileu, 61);

	double stda[];
	ArrayResize(stda, 61);


	for (int i = 60; i >= 0; i--)
	{
		filee[60 - i] = mosquitoes["R"][i].ToDbl();
		fileg[60 - i] = mosquitoes["R2"][i].ToDbl();
		filea[60 - i] = mosquitoes["R3"][i].ToDbl();
		filen[60 - i] = mosquitoes["rec"][i].ToDbl();
		filec[60 - i] = mosquitoes["total"][i].ToDbl();
		fileu[60 - i] = mosquitoes["eq"][i].ToDbl();
		stda[60 - i] = mosquitoes["R1std"][i].ToDbl();
	}

	{
		file(filee, fileg, filea, filen, filec, fileu, stda);
	}
}

//+FILL PAST BUFFERS FOR AVG-----------------------------------------+
void BuildPastBuffer()
{
	double ohdayseur[73], ohdaysgbp[73], ohdaysaud[73], ohdaysnzd[73], ohdayscad[73], ohdaysusd[73], ohdayschf[73], ohdaysjpy[73];
	double ohwayseur[27], ohwaysgbp[27], ohwaysaud[27], ohwaysnzd[27], ohwayscad[27], ohwaysusd[27], ohwayschf[27], ohwaysjpy[27];
	double ohmayseur[7], ohmaysgbp[7], ohmaysaud[7], ohmaysnzd[7], ohmayscad[7], ohmaysusd[7], ohmayschf[7], ohmaysjpy[7];
	double oldayseur[73], oldaysgbp[73], oldaysaud[73], oldaysnzd[73], oldayscad[73], oldaysusd[73], oldayschf[73], oldaysjpy[73];
	double olwayseur[27], olwaysgbp[27], olwaysaud[27], olwaysnzd[27], olwayscad[27], olwaysusd[27], olwayschf[27], olwaysjpy[27];
	double olmayseur[7], olmaysgbp[7], olmaysaud[7], olmaysnzd[7], olmayscad[7], olmaysusd[7], olmayschf[7], olmaysjpy[7];

	ArrayInitialize(ohdayseur, 0); ArrayInitialize(ohdaysgbp, 0); ArrayInitialize(ohdaysaud, 0); ArrayInitialize(ohdaysnzd, 0); ArrayInitialize(ohdayscad, 0); ArrayInitialize(ohdaysusd, 0); ArrayInitialize(ohdayschf, 0); ArrayInitialize(ohdaysjpy, 0);
	ArrayInitialize(oldayseur, 0); ArrayInitialize(oldaysgbp, 0); ArrayInitialize(oldaysaud, 0); ArrayInitialize(oldaysnzd, 0); ArrayInitialize(oldayscad, 0); ArrayInitialize(oldaysusd, 0); ArrayInitialize(oldayschf, 0); ArrayInitialize(oldaysjpy, 0);
	ArrayInitialize(ohwayseur, 0); ArrayInitialize(ohwaysgbp, 0); ArrayInitialize(ohwaysaud, 0); ArrayInitialize(ohwaysnzd, 0); ArrayInitialize(ohwayscad, 0); ArrayInitialize(ohwaysusd, 0); ArrayInitialize(ohwayschf, 0); ArrayInitialize(ohwaysjpy, 0);
	ArrayInitialize(olwayseur, 0); ArrayInitialize(olwaysgbp, 0); ArrayInitialize(olwaysaud, 0); ArrayInitialize(olwaysnzd, 0); ArrayInitialize(olwayscad, 0); ArrayInitialize(olwaysusd, 0); ArrayInitialize(olwayschf, 0); ArrayInitialize(olwaysjpy, 0);
	ArrayInitialize(ohmayseur, 0); ArrayInitialize(ohmaysgbp, 0); ArrayInitialize(ohmaysaud, 0); ArrayInitialize(ohmaysnzd, 0); ArrayInitialize(ohmayscad, 0); ArrayInitialize(ohmaysusd, 0); ArrayInitialize(ohmayschf, 0); ArrayInitialize(ohmaysjpy, 0);
	ArrayInitialize(olmayseur, 0); ArrayInitialize(olmaysgbp, 0); ArrayInitialize(olmaysaud, 0); ArrayInitialize(olmaysnzd, 0); ArrayInitialize(olmayscad, 0); ArrayInitialize(olmaysusd, 0); ArrayInitialize(olmayschf, 0); ArrayInitialize(olmaysjpy, 0);

	ArraySetAsSeries(ohdayseur, true); ArraySetAsSeries(ohdaysgbp, true); ArraySetAsSeries(ohdaysaud, true); ArraySetAsSeries(ohdaysnzd, true); ArraySetAsSeries(ohdayscad, true); ArraySetAsSeries(ohdaysusd, true); ArraySetAsSeries(ohdayschf, true); ArraySetAsSeries(ohdaysjpy, true);
	ArraySetAsSeries(oldayseur, true); ArraySetAsSeries(oldaysgbp, true); ArraySetAsSeries(oldaysaud, true); ArraySetAsSeries(oldaysnzd, true); ArraySetAsSeries(oldayscad, true); ArraySetAsSeries(oldaysusd, true); ArraySetAsSeries(oldayschf, true); ArraySetAsSeries(oldaysjpy, true);
	ArraySetAsSeries(ohwayseur, true); ArraySetAsSeries(ohwaysgbp, true); ArraySetAsSeries(ohwaysaud, true); ArraySetAsSeries(ohwaysnzd, true); ArraySetAsSeries(ohwayscad, true); ArraySetAsSeries(ohwaysusd, true); ArraySetAsSeries(ohwayschf, true); ArraySetAsSeries(ohwaysjpy, true);
	ArraySetAsSeries(olwayseur, true); ArraySetAsSeries(olwaysgbp, true); ArraySetAsSeries(olwaysaud, true); ArraySetAsSeries(olwaysnzd, true); ArraySetAsSeries(olwayscad, true); ArraySetAsSeries(olwaysusd, true); ArraySetAsSeries(olwayschf, true); ArraySetAsSeries(olwaysjpy, true);
	ArraySetAsSeries(ohmayseur, true); ArraySetAsSeries(ohmaysgbp, true); ArraySetAsSeries(ohmaysaud, true); ArraySetAsSeries(ohmaysnzd, true); ArraySetAsSeries(ohmayscad, true); ArraySetAsSeries(ohmaysusd, true); ArraySetAsSeries(ohmayschf, true); ArraySetAsSeries(ohmaysjpy, true);
	ArraySetAsSeries(olmayseur, true); ArraySetAsSeries(olmaysgbp, true); ArraySetAsSeries(olmaysaud, true); ArraySetAsSeries(olmaysnzd, true); ArraySetAsSeries(olmayscad, true); ArraySetAsSeries(olmaysusd, true); ArraySetAsSeries(olmayschf, true); ArraySetAsSeries(olmaysjpy, true);

	int n1 = 10000;
	int n2 = 100;

	//PIP values
	double eupl = dblPipValue(eu_p);
	double egpl = dblPipValue(eg_p);
	double eapl = dblPipValue(ea_p);
	double enpl = dblPipValue(en_p);
	double ecpl = dblPipValue(ec_p);
	double efpl = dblPipValue(ef_p);
	double ejpl = dblPipValue(ej_p);
	double eurpl = (eupl + egpl + eapl + enpl + ecpl + efpl + ejpl) / 7;

	double gupl = dblPipValue(gu_p);
	double gapl = dblPipValue(ga_p);
	double gnpl = dblPipValue(gn_p);
	double gcpl = dblPipValue(gc_p);
	double gfpl = dblPipValue(gf_p);
	double gjpl = dblPipValue(gj_p);
	double gbppl = (gupl + egpl + gapl + gnpl + gcpl + gfpl + gjpl) / 7;

	double aupl = dblPipValue(au_p);
	double anpl = dblPipValue(an_p);
	double acpl = dblPipValue(ac_p);
	double afpl = dblPipValue(af_p);
	double ajpl = dblPipValue(aj_p);
	double audpl = (aupl + eapl + gapl + anpl + acpl + afpl + ajpl) / 7;

	double nupl = dblPipValue(nu_p);
	double ncpl = dblPipValue(nc_p);
	double nfpl = dblPipValue(nf_p);
	double njpl = dblPipValue(nj_p);
	double nzdpl = (nupl + enpl + gnpl + anpl + ncpl + nfpl + njpl) / 7;

	double ucpl = dblPipValue(uc_p);
	double cfpl = dblPipValue(cf_p);
	double cjpl = dblPipValue(cj_p);
	double cadpl = (ucpl + ecpl + gcpl + acpl + ncpl + cfpl + cjpl) / 7;

	double ufpl = dblPipValue(uf_p);
	double fjpl = dblPipValue(fj_p);
	double chfpl = (ufpl + efpl + gfpl + afpl + nfpl + cfpl + fjpl) / 7;

	double ujpl = dblPipValue(uj_p);
	double jpypl = (ujpl + ejpl + gjpl + ajpl + njpl + cjpl + fjpl) / 7;

	double usdpl = (eupl + gupl + aupl + nupl + ucpl + ufpl + ujpl) / 7;

	//FILL DAY 
	double ce1[], ce2[], ce3[], ce4[], ce5[], ce6[], ce7[];
	double he1[], he2[], he3[], he4[], he5[], he6[], he7[];
	double le1[], le2[], le3[], le4[], le5[], le6[], le7[];
	ArraySetAsSeries(ce1, true); ArraySetAsSeries(ce2, true); ArraySetAsSeries(ce3, true); ArraySetAsSeries(ce4, true); ArraySetAsSeries(ce5, true); ArraySetAsSeries(ce6, true); ArraySetAsSeries(ce7, true);
	ArraySetAsSeries(he1, true); ArraySetAsSeries(he2, true); ArraySetAsSeries(he3, true); ArraySetAsSeries(he4, true); ArraySetAsSeries(he5, true); ArraySetAsSeries(he6, true); ArraySetAsSeries(he7, true);
	ArraySetAsSeries(le1, true); ArraySetAsSeries(le2, true); ArraySetAsSeries(le3, true); ArraySetAsSeries(le4, true); ArraySetAsSeries(le5, true); ArraySetAsSeries(le6, true); ArraySetAsSeries(le7, true);
	ArrayResize(ce1, 74); ArrayResize(ce2, 74); ArrayResize(ce3, 74); ArrayResize(ce4, 74); ArrayResize(ce5, 74); ArrayResize(ce6, 74); ArrayResize(ce7, 74);
	ArrayResize(he1, 74); ArrayResize(he2, 74); ArrayResize(he3, 74); ArrayResize(he4, 74); ArrayResize(he5, 74); ArrayResize(he6, 74); ArrayResize(he7, 74);
	ArrayResize(le1, 74); ArrayResize(le2, 74); ArrayResize(le3, 74); ArrayResize(le4, 74); ArrayResize(le5, 74); ArrayResize(le6, 74); ArrayResize(le7, 74);

	if (CopyClose(eu_p, PERIOD_D1, 0, 74, ce1) < 0) Print(GetLastError());
	if (CopyClose(eg_p, PERIOD_D1, 0, 74, ce2) < 0) Print(GetLastError());
	if (CopyClose(ea_p, PERIOD_D1, 0, 74, ce3) < 0) Print(GetLastError());
	if (CopyClose(en_p, PERIOD_D1, 0, 74, ce4) < 0) Print(GetLastError());
	if (CopyClose(ec_p, PERIOD_D1, 0, 74, ce5) < 0) Print(GetLastError());
	if (CopyClose(ef_p, PERIOD_D1, 0, 74, ce6) < 0) Print(GetLastError());
	if (CopyClose(ej_p, PERIOD_D1, 0, 74, ce7) < 0) Print(GetLastError());

	if (CopyHigh(eu_p, PERIOD_D1, 0, 74, he1) < 0) Print(GetLastError());
	if (CopyHigh(eg_p, PERIOD_D1, 0, 74, he2) < 0) Print(GetLastError());
	if (CopyHigh(ea_p, PERIOD_D1, 0, 74, he3) < 0) Print(GetLastError());
	if (CopyHigh(en_p, PERIOD_D1, 0, 74, he4) < 0) Print(GetLastError());
	if (CopyHigh(ec_p, PERIOD_D1, 0, 74, he5) < 0) Print(GetLastError());
	if (CopyHigh(ef_p, PERIOD_D1, 0, 74, he6) < 0) Print(GetLastError());
	if (CopyHigh(ej_p, PERIOD_D1, 0, 74, he7) < 0) Print(GetLastError());

	if (CopyLow(eu_p, PERIOD_D1, 0, 74, le1) < 0) Print(GetLastError());
	if (CopyLow(eg_p, PERIOD_D1, 0, 74, le2) < 0) Print(GetLastError());
	if (CopyLow(ea_p, PERIOD_D1, 0, 74, le3) < 0) Print(GetLastError());
	if (CopyLow(en_p, PERIOD_D1, 0, 74, le4) < 0) Print(GetLastError());
	if (CopyLow(ec_p, PERIOD_D1, 0, 74, le5) < 0) Print(GetLastError());
	if (CopyLow(ef_p, PERIOD_D1, 0, 74, le6) < 0) Print(GetLastError());
	if (CopyLow(ej_p, PERIOD_D1, 0, 74, le7) < 0) Print(GetLastError());

	double cg1[], cg3[], cg4[], cg5[], cg6[], cg7[];
	double hg1[], hg3[], hg4[], hg5[], hg6[], hg7[];
	double lg1[], lg3[], lg4[], lg5[], lg6[], lg7[];
	ArraySetAsSeries(cg1, true); ArraySetAsSeries(cg3, true); ArraySetAsSeries(cg4, true); ArraySetAsSeries(cg5, true); ArraySetAsSeries(cg6, true); ArraySetAsSeries(cg7, true);
	ArraySetAsSeries(hg1, true); ArraySetAsSeries(hg3, true); ArraySetAsSeries(hg4, true); ArraySetAsSeries(hg5, true); ArraySetAsSeries(hg6, true); ArraySetAsSeries(hg7, true);
	ArraySetAsSeries(lg1, true); ArraySetAsSeries(lg3, true); ArraySetAsSeries(lg4, true); ArraySetAsSeries(lg5, true); ArraySetAsSeries(lg6, true); ArraySetAsSeries(lg7, true);
	ArrayResize(cg1, 74); ArrayResize(cg3, 74); ArrayResize(cg4, 74); ArrayResize(cg5, 74); ArrayResize(cg6, 74); ArrayResize(cg7, 74);
	ArrayResize(hg1, 74); ArrayResize(hg3, 74); ArrayResize(hg4, 74); ArrayResize(hg5, 74); ArrayResize(hg6, 74); ArrayResize(hg7, 74);
	ArrayResize(lg1, 74); ArrayResize(lg3, 74); ArrayResize(lg4, 74); ArrayResize(lg5, 74); ArrayResize(lg6, 74); ArrayResize(lg7, 74);

	if (CopyClose(gu_p, PERIOD_D1, 0, 74, cg1) < 0) Print(GetLastError());
	if (CopyClose(ga_p, PERIOD_D1, 0, 74, cg3) < 0) Print(GetLastError());
	if (CopyClose(gn_p, PERIOD_D1, 0, 74, cg4) < 0) Print(GetLastError());
	if (CopyClose(gc_p, PERIOD_D1, 0, 74, cg5) < 0) Print(GetLastError());
	if (CopyClose(gf_p, PERIOD_D1, 0, 74, cg6) < 0) Print(GetLastError());
	if (CopyClose(gj_p, PERIOD_D1, 0, 74, cg7) < 0) Print(GetLastError());

	if (CopyHigh(gu_p, PERIOD_D1, 0, 74, hg1) < 0) Print(GetLastError());
	if (CopyHigh(ga_p, PERIOD_D1, 0, 74, hg3) < 0) Print(GetLastError());
	if (CopyHigh(gn_p, PERIOD_D1, 0, 74, hg4) < 0) Print(GetLastError());
	if (CopyHigh(gc_p, PERIOD_D1, 0, 74, hg5) < 0) Print(GetLastError());
	if (CopyHigh(gf_p, PERIOD_D1, 0, 74, hg6) < 0) Print(GetLastError());
	if (CopyHigh(gj_p, PERIOD_D1, 0, 74, hg7) < 0) Print(GetLastError());

	if (CopyLow(gu_p, PERIOD_D1, 0, 74, lg1) < 0) Print(GetLastError());
	if (CopyLow(ga_p, PERIOD_D1, 0, 74, lg3) < 0) Print(GetLastError());
	if (CopyLow(gn_p, PERIOD_D1, 0, 74, lg4) < 0) Print(GetLastError());
	if (CopyLow(gc_p, PERIOD_D1, 0, 74, lg5) < 0) Print(GetLastError());
	if (CopyLow(gf_p, PERIOD_D1, 0, 74, lg6) < 0) Print(GetLastError());
	if (CopyLow(gj_p, PERIOD_D1, 0, 74, lg7) < 0) Print(GetLastError());

	double ca1[], ca4[], ca5[], ca6[], ca7[];
	double ha1[], ha4[], ha5[], ha6[], ha7[];
	double la1[], la4[], la5[], la6[], la7[];
	ArraySetAsSeries(ca1, true); ArraySetAsSeries(ca4, true); ArraySetAsSeries(ca5, true); ArraySetAsSeries(ca6, true); ArraySetAsSeries(ca7, true);
	ArraySetAsSeries(ha1, true); ArraySetAsSeries(ha4, true); ArraySetAsSeries(ha5, true); ArraySetAsSeries(ha6, true); ArraySetAsSeries(ha7, true);
	ArraySetAsSeries(la1, true); ArraySetAsSeries(la4, true); ArraySetAsSeries(la5, true); ArraySetAsSeries(la6, true); ArraySetAsSeries(la7, true);
	ArrayResize(ca1, 74); ArrayResize(ca4, 74); ArrayResize(ca5, 74); ArrayResize(ca6, 74); ArrayResize(ca7, 74);
	ArrayResize(ha1, 74); ArrayResize(ha4, 74); ArrayResize(ha5, 74); ArrayResize(ha6, 74); ArrayResize(ha7, 74);
	ArrayResize(la1, 74); ArrayResize(la4, 74); ArrayResize(la5, 74); ArrayResize(la6, 74); ArrayResize(la7, 74);

	if (CopyClose(au_p, PERIOD_D1, 0, 74, ca1) < 0) Print(GetLastError());
	if (CopyClose(an_p, PERIOD_D1, 0, 74, ca4) < 0) Print(GetLastError());
	if (CopyClose(ac_p, PERIOD_D1, 0, 74, ca5) < 0) Print(GetLastError());
	if (CopyClose(af_p, PERIOD_D1, 0, 74, ca6) < 0) Print(GetLastError());
	if (CopyClose(aj_p, PERIOD_D1, 0, 74, ca7) < 0) Print(GetLastError());

	if (CopyHigh(au_p, PERIOD_D1, 0, 74, ha1) < 0) Print(GetLastError());
	if (CopyHigh(an_p, PERIOD_D1, 0, 74, ha4) < 0) Print(GetLastError());
	if (CopyHigh(ac_p, PERIOD_D1, 0, 74, ha5) < 0) Print(GetLastError());
	if (CopyHigh(af_p, PERIOD_D1, 0, 74, ha6) < 0) Print(GetLastError());
	if (CopyHigh(aj_p, PERIOD_D1, 0, 74, ha7) < 0) Print(GetLastError());

	if (CopyLow(au_p, PERIOD_D1, 0, 74, la1) < 0) Print(GetLastError());
	if (CopyLow(an_p, PERIOD_D1, 0, 74, la4) < 0) Print(GetLastError());
	if (CopyLow(ac_p, PERIOD_D1, 0, 74, la5) < 0) Print(GetLastError());
	if (CopyLow(af_p, PERIOD_D1, 0, 74, la6) < 0) Print(GetLastError());
	if (CopyLow(aj_p, PERIOD_D1, 0, 74, la7) < 0) Print(GetLastError());

	double cn1[], cn5[], cn6[], cn7[];
	double hn1[], hn5[], hn6[], hn7[];
	double ln1[], ln5[], ln6[], ln7[];
	ArraySetAsSeries(cn1, true); ArraySetAsSeries(cn5, true); ArraySetAsSeries(cn6, true); ArraySetAsSeries(cn7, true);
	ArraySetAsSeries(hn1, true); ArraySetAsSeries(hn5, true); ArraySetAsSeries(hn6, true); ArraySetAsSeries(hn7, true);
	ArraySetAsSeries(ln1, true); ArraySetAsSeries(ln5, true); ArraySetAsSeries(ln6, true); ArraySetAsSeries(ln7, true);
	ArrayResize(cn1, 74); ArrayResize(cn5, 74); ArrayResize(cn6, 74); ArrayResize(cn7, 74);
	ArrayResize(hn1, 74); ArrayResize(hn5, 74); ArrayResize(hn6, 74); ArrayResize(hn7, 74);
	ArrayResize(ln1, 74); ArrayResize(ln5, 74); ArrayResize(ln6, 74); ArrayResize(ln7, 74);

	if (CopyClose(nu_p, PERIOD_D1, 0, 74, cn1) < 0) Print(GetLastError());
	if (CopyClose(nc_p, PERIOD_D1, 0, 74, cn5) < 0) Print(GetLastError());
	if (CopyClose(nf_p, PERIOD_D1, 0, 74, cn6) < 0) Print(GetLastError());
	if (CopyClose(nj_p, PERIOD_D1, 0, 74, cn7) < 0) Print(GetLastError());

	if (CopyHigh(nu_p, PERIOD_D1, 0, 74, hn1) < 0) Print(GetLastError());
	if (CopyHigh(nc_p, PERIOD_D1, 0, 74, hn5) < 0) Print(GetLastError());
	if (CopyHigh(nf_p, PERIOD_D1, 0, 74, hn6) < 0) Print(GetLastError());
	if (CopyHigh(nj_p, PERIOD_D1, 0, 74, hn7) < 0) Print(GetLastError());

	if (CopyLow(nu_p, PERIOD_D1, 0, 74, ln1) < 0) Print(GetLastError());
	if (CopyLow(nc_p, PERIOD_D1, 0, 74, ln5) < 0) Print(GetLastError());
	if (CopyLow(nf_p, PERIOD_D1, 0, 74, ln6) < 0) Print(GetLastError());
	if (CopyLow(nj_p, PERIOD_D1, 0, 74, ln7) < 0) Print(GetLastError());

	double cc1[], cc6[], cc7[];
	double hc1[], hc6[], hc7[];
	double lc1[], lc6[], lc7[];
	ArraySetAsSeries(cc1, true); ArraySetAsSeries(cc6, true); ArraySetAsSeries(cc7, true);
	ArraySetAsSeries(hc1, true); ArraySetAsSeries(hc6, true); ArraySetAsSeries(hc7, true);
	ArraySetAsSeries(lc1, true); ArraySetAsSeries(lc6, true); ArraySetAsSeries(lc7, true);
	ArrayResize(cc1, 74); ArrayResize(cc6, 74); ArrayResize(cc7, 74);
	ArrayResize(hc1, 74); ArrayResize(hc6, 74); ArrayResize(hc7, 74);
	ArrayResize(lc1, 74); ArrayResize(lc6, 74); ArrayResize(lc7, 74);

	if (CopyClose(uc_p, PERIOD_D1, 0, 74, cc1) < 0) Print(GetLastError());
	if (CopyClose(cf_p, PERIOD_D1, 0, 74, cc6) < 0) Print(GetLastError());
	if (CopyClose(cj_p, PERIOD_D1, 0, 74, cc7) < 0) Print(GetLastError());

	if (CopyHigh(uc_p, PERIOD_D1, 0, 74, hc1) < 0) Print(GetLastError());
	if (CopyHigh(cf_p, PERIOD_D1, 0, 74, hc6) < 0) Print(GetLastError());
	if (CopyHigh(cj_p, PERIOD_D1, 0, 74, hc7) < 0) Print(GetLastError());

	if (CopyLow(uc_p, PERIOD_D1, 0, 74, lc1) < 0) Print(GetLastError());
	if (CopyLow(cf_p, PERIOD_D1, 0, 74, lc6) < 0) Print(GetLastError());
	if (CopyLow(cj_p, PERIOD_D1, 0, 74, lc7) < 0) Print(GetLastError());

	double cf1[], cf7[];
	double hf1[], hf7[];
	double lf1[], lf7[];
	ArraySetAsSeries(cf1, true); ArraySetAsSeries(cf7, true);
	ArraySetAsSeries(hf1, true); ArraySetAsSeries(hf7, true);
	ArraySetAsSeries(lf1, true); ArraySetAsSeries(lf7, true);
	ArrayResize(cf1, 74); ArrayResize(cf7, 74);
	ArrayResize(hf1, 74); ArrayResize(hf7, 74);
	ArrayResize(lf1, 74); ArrayResize(lf7, 74);

	if (CopyClose(uf_p, PERIOD_D1, 0, 74, cf1) < 0) Print(GetLastError());
	if (CopyClose(fj_p, PERIOD_D1, 0, 74, cf7) < 0) Print(GetLastError());

	if (CopyHigh(uf_p, PERIOD_D1, 0, 74, hf1) < 0) Print(GetLastError());
	if (CopyHigh(fj_p, PERIOD_D1, 0, 74, hf7) < 0) Print(GetLastError());

	if (CopyLow(uf_p, PERIOD_D1, 0, 74, lf1) < 0) Print(GetLastError());
	if (CopyLow(fj_p, PERIOD_D1, 0, 74, lf7) < 0) Print(GetLastError());

	double cj1[];
	double hj1[];
	double lj1[];
	ArraySetAsSeries(cj1, true);
	ArraySetAsSeries(hj1, true);
	ArraySetAsSeries(lj1, true);
	ArrayResize(cj1, 74);
	ArrayResize(hj1, 74);
	ArrayResize(lj1, 74);

	if (CopyClose(uj_p, PERIOD_D1, 0, 74, cj1) < 0) Print(GetLastError());

	if (CopyHigh(uj_p, PERIOD_D1, 0, 74, hj1) < 0) Print(GetLastError());

	if (CopyLow(uj_p, PERIOD_D1, 0, 74, lj1) < 0) Print(GetLastError());


	{	//BufferFill for past prices
		//Day
		for (int i = 1; i <= 72; i++)
		{
			ohdayseur[i] = hdod(eu_p, he1, ce1, eupl, i) + hdod(eg_p, he2, ce2, egpl, i) + hdod(ea_p, he3, ce3, eapl, i) + hdod(en_p, he4, ce4, enpl, i) + hdod(ec_p, he5, ce5, ecpl, i) + hdod(ef_p, he6, ce6, efpl, i) + hdod(ej_p, he7, ce7, ejpl, i);
			ohdaysgbp[i] = -ldod(eg_p, le2, ce2, egpl, i) + hdod(gu_p, hg1, cg1, gupl, i) + hdod(ga_p, hg3, cg3, gapl, i) + hdod(gn_p, hg4, cg4, gnpl, i) + hdod(gc_p, hg5, cg5, gcpl, i) + hdod(gf_p, hg6, cg6, gfpl, i) + hdod(gj_p, hg7, cg7, gjpl, i);
			ohdaysaud[i] = -ldod(ea_p, le3, ce3, eapl, i) - ldod(ga_p, lg3, cg3, gapl, i) + hdod(au_p, ha1, ca1, aupl, i) + hdod(an_p, ha4, ca4, anpl, i) + hdod(ac_p, ha5, ca5, acpl, i) + hdod(af_p, ha6, ca6, afpl, i) + hdod(aj_p, ha7, ca7, ajpl, i);
			ohdaysnzd[i] = -ldod(en_p, le4, ce4, enpl, i) - ldod(gn_p, lg4, cg4, gnpl, i) - ldod(an_p, la4, ca4, anpl, i) + hdod(nu_p, hn1, cn1, nupl, i) + hdod(nc_p, hn5, cn5, ncpl, i) + hdod(nf_p, hn6, cn6, nfpl, i) + hdod(nj_p, hn7, cn7, njpl, i);
			ohdayscad[i] = -ldod(ec_p, le5, ce5, ecpl, i) - ldod(gc_p, lg5, cg5, gcpl, i) - ldod(ac_p, la5, ca5, acpl, i) - ldod(nc_p, ln5, cn5, ncpl, i) - ldod(uc_p, lc1, cc1, ucpl, i) + hdod(cf_p, hc6, cc6, cfpl, i) + hdod(cj_p, hc7, cc7, cjpl, i);
			ohdaysusd[i] = -ldod(eu_p, le1, ce1, eupl, i) - ldod(gu_p, lg1, cg1, gupl, i) - ldod(au_p, la1, ca1, aupl, i) - ldod(nu_p, ln1, cn1, nupl, i) + hdod(uc_p, hc1, cc1, ucpl, i) + hdod(uf_p, hf1, cf1, ufpl, i) + hdod(uj_p, hj1, cj1, ujpl, i);
			ohdayschf[i] = -ldod(ef_p, le6, ce6, efpl, i) - ldod(gf_p, lg6, cg6, gfpl, i) - ldod(af_p, la6, ca6, afpl, i) - ldod(nf_p, ln6, cn6, nfpl, i) - ldod(uf_p, lf1, cf1, ufpl, i) - ldod(cf_p, lc6, cc6, cfpl, i) + hdod(fj_p, hf7, cf7, fjpl, i);
			ohdaysjpy[i] = -ldod(ej_p, le7, ce7, ejpl, i) - ldod(gj_p, lg7, cg7, gjpl, i) - ldod(aj_p, la7, ca7, ajpl, i) - ldod(nj_p, ln7, cn7, njpl, i) - ldod(uj_p, lj1, cj1, ujpl, i) - ldod(cj_p, lc7, cc7, cjpl, i) - ldod(fj_p, lf7, cf7, fjpl, i);

			oldayseur[i] = ldod(eu_p, le1, ce1, eupl, i) + ldod(eg_p, le2, ce2, egpl, i) + ldod(ea_p, le3, ce3, eapl, i) + ldod(en_p, le4, ce4, enpl, i) + ldod(ec_p, le5, ce5, ecpl, i) + ldod(ef_p, le6, ce6, efpl, i) + ldod(ej_p, le7, ce7, ejpl, i);
			oldaysgbp[i] = -hdod(eg_p, he2, ce2, egpl, i) + ldod(gu_p, lg1, cg1, gupl, i) + ldod(ga_p, lg3, cg3, gapl, i) + ldod(gn_p, lg4, cg4, gnpl, i) + ldod(gc_p, lg5, cg5, gcpl, i) + ldod(gf_p, lg6, cg6, gfpl, i) + ldod(gj_p, lg7, cg7, gjpl, i);
			oldaysaud[i] = -hdod(ea_p, he3, ce3, eapl, i) - hdod(ga_p, hg3, cg3, gapl, i) + ldod(au_p, la1, ca1, aupl, i) + ldod(an_p, la4, ca4, anpl, i) + ldod(ac_p, la5, ca5, acpl, i) + ldod(af_p, la6, ca6, afpl, i) + ldod(aj_p, la7, ca7, ajpl, i);
			oldaysnzd[i] = -hdod(en_p, he4, ce4, enpl, i) - hdod(gn_p, hg4, cg4, gnpl, i) - hdod(an_p, ha4, ca4, anpl, i) + ldod(nu_p, ln1, cn1, nupl, i) + ldod(nc_p, ln5, cn5, ncpl, i) + ldod(nf_p, ln6, cn6, nfpl, i) + ldod(nj_p, ln7, cn7, njpl, i);
			oldayscad[i] = -hdod(ec_p, he5, ce5, ecpl, i) - hdod(gc_p, hg5, cg5, gcpl, i) - hdod(ac_p, ha5, ca5, acpl, i) - hdod(nc_p, hn5, cn5, ncpl, i) - hdod(uc_p, hc1, cc1, ucpl, i) + ldod(cf_p, lc6, cc6, cfpl, i) + ldod(cj_p, lc7, cc7, cjpl, i);
			oldaysusd[i] = -hdod(eu_p, he1, ce1, eupl, i) - hdod(gu_p, hg1, cg1, gupl, i) - hdod(au_p, ha1, ca1, aupl, i) - hdod(nu_p, hn1, cn1, nupl, i) + ldod(uc_p, lc1, cc1, ucpl, i) + ldod(uf_p, lf1, cf1, ufpl, i) + ldod(uj_p, lj1, cj1, ujpl, i);
			oldayschf[i] = -hdod(ef_p, he6, ce6, efpl, i) - hdod(gf_p, hg6, cg6, gfpl, i) - hdod(af_p, ha6, ca6, afpl, i) - hdod(nf_p, hn6, cn6, nfpl, i) - hdod(uf_p, hf1, cf1, ufpl, i) - hdod(cf_p, hc6, cc6, cfpl, i) + ldod(fj_p, lf7, cf7, fjpl, i);
			oldaysjpy[i] = -hdod(ej_p, he7, ce7, ejpl, i) - hdod(gj_p, hg7, cg7, gjpl, i) - hdod(aj_p, ha7, ca7, ajpl, i) - hdod(nj_p, hn7, cn7, njpl, i) - hdod(uj_p, hj1, cj1, ujpl, i) - hdod(cj_p, hc7, cc7, cjpl, i) - hdod(fj_p, hf7, cf7, fjpl, i);
		}
	}

	//FILL WEEK 
	double wce1[], wce2[], wce3[], wce4[], wce5[], wce6[], wce7[];
	double whe1[], whe2[], whe3[], whe4[], whe5[], whe6[], whe7[];
	double wle1[], wle2[], wle3[], wle4[], wle5[], wle6[], wle7[];
	ArraySetAsSeries(wce1, true); ArraySetAsSeries(wce2, true); ArraySetAsSeries(wce3, true); ArraySetAsSeries(wce4, true); ArraySetAsSeries(wce5, true); ArraySetAsSeries(wce6, true); ArraySetAsSeries(wce7, true);
	ArraySetAsSeries(whe1, true); ArraySetAsSeries(whe2, true); ArraySetAsSeries(whe3, true); ArraySetAsSeries(whe4, true); ArraySetAsSeries(whe5, true); ArraySetAsSeries(whe6, true); ArraySetAsSeries(whe7, true);
	ArraySetAsSeries(wle1, true); ArraySetAsSeries(wle2, true); ArraySetAsSeries(wle3, true); ArraySetAsSeries(wle4, true); ArraySetAsSeries(wle5, true); ArraySetAsSeries(wle6, true); ArraySetAsSeries(wle7, true);
	ArrayResize(wce1, 28); ArrayResize(wce2, 28); ArrayResize(wce3, 28); ArrayResize(wce4, 28); ArrayResize(wce5, 28); ArrayResize(wce6, 28); ArrayResize(wce7, 28);
	ArrayResize(whe1, 28); ArrayResize(whe2, 28); ArrayResize(whe3, 28); ArrayResize(whe4, 28); ArrayResize(whe5, 28); ArrayResize(whe6, 28); ArrayResize(whe7, 28);
	ArrayResize(wle1, 28); ArrayResize(wle2, 28); ArrayResize(wle3, 28); ArrayResize(wle4, 28); ArrayResize(wle5, 28); ArrayResize(wle6, 28); ArrayResize(wle7, 28);

	if (CopyClose(eu_p, PERIOD_W1, 0, 28, wce1) < 0) Print(GetLastError());
	if (CopyClose(eg_p, PERIOD_W1, 0, 28, wce2) < 0) Print(GetLastError());
	if (CopyClose(ea_p, PERIOD_W1, 0, 28, wce3) < 0) Print(GetLastError());
	if (CopyClose(en_p, PERIOD_W1, 0, 28, wce4) < 0) Print(GetLastError());
	if (CopyClose(ec_p, PERIOD_W1, 0, 28, wce5) < 0) Print(GetLastError());
	if (CopyClose(ef_p, PERIOD_W1, 0, 28, wce6) < 0) Print(GetLastError());
	if (CopyClose(ej_p, PERIOD_W1, 0, 28, wce7) < 0) Print(GetLastError());

	if (CopyHigh(eu_p, PERIOD_W1, 0, 28, whe1) < 0) Print(GetLastError());
	if (CopyHigh(eg_p, PERIOD_W1, 0, 28, whe2) < 0) Print(GetLastError());
	if (CopyHigh(ea_p, PERIOD_W1, 0, 28, whe3) < 0) Print(GetLastError());
	if (CopyHigh(en_p, PERIOD_W1, 0, 28, whe4) < 0) Print(GetLastError());
	if (CopyHigh(ec_p, PERIOD_W1, 0, 28, whe5) < 0) Print(GetLastError());
	if (CopyHigh(ef_p, PERIOD_W1, 0, 28, whe6) < 0) Print(GetLastError());
	if (CopyHigh(ej_p, PERIOD_W1, 0, 28, whe7) < 0) Print(GetLastError());

	if (CopyLow(eu_p, PERIOD_W1, 0, 28, wle1) < 0) Print(GetLastError());
	if (CopyLow(eg_p, PERIOD_W1, 0, 28, wle2) < 0) Print(GetLastError());
	if (CopyLow(ea_p, PERIOD_W1, 0, 28, wle3) < 0) Print(GetLastError());
	if (CopyLow(en_p, PERIOD_W1, 0, 28, wle4) < 0) Print(GetLastError());
	if (CopyLow(ec_p, PERIOD_W1, 0, 28, wle5) < 0) Print(GetLastError());
	if (CopyLow(ef_p, PERIOD_W1, 0, 28, wle6) < 0) Print(GetLastError());
	if (CopyLow(ej_p, PERIOD_W1, 0, 28, wle7) < 0) Print(GetLastError());

	double wcg1[], wcg3[], wcg4[], wcg5[], wcg6[], wcg7[];
	double whg1[], whg3[], whg4[], whg5[], whg6[], whg7[];
	double wlg1[], wlg3[], wlg4[], wlg5[], wlg6[], wlg7[];
	ArraySetAsSeries(wcg1, true); ArraySetAsSeries(wcg3, true); ArraySetAsSeries(wcg4, true); ArraySetAsSeries(wcg5, true); ArraySetAsSeries(wcg6, true); ArraySetAsSeries(wcg7, true);
	ArraySetAsSeries(whg1, true); ArraySetAsSeries(whg3, true); ArraySetAsSeries(whg4, true); ArraySetAsSeries(whg5, true); ArraySetAsSeries(whg6, true); ArraySetAsSeries(whg7, true);
	ArraySetAsSeries(wlg1, true); ArraySetAsSeries(wlg3, true); ArraySetAsSeries(wlg4, true); ArraySetAsSeries(wlg5, true); ArraySetAsSeries(wlg6, true); ArraySetAsSeries(wlg7, true);
	ArrayResize(wcg1, 28); ArrayResize(wcg3, 28); ArrayResize(wcg4, 28); ArrayResize(wcg5, 28); ArrayResize(wcg6, 28); ArrayResize(wcg7, 28);
	ArrayResize(whg1, 28); ArrayResize(whg3, 28); ArrayResize(whg4, 28); ArrayResize(whg5, 28); ArrayResize(whg6, 28); ArrayResize(whg7, 28);
	ArrayResize(wlg1, 28); ArrayResize(wlg3, 28); ArrayResize(wlg4, 28); ArrayResize(wlg5, 28); ArrayResize(wlg6, 28); ArrayResize(wlg7, 28);

	if (CopyClose(gu_p, PERIOD_W1, 0, 28, wcg1) < 0) Print(GetLastError());
	if (CopyClose(ga_p, PERIOD_W1, 0, 28, wcg3) < 0) Print(GetLastError());
	if (CopyClose(gn_p, PERIOD_W1, 0, 28, wcg4) < 0) Print(GetLastError());
	if (CopyClose(gc_p, PERIOD_W1, 0, 28, wcg5) < 0) Print(GetLastError());
	if (CopyClose(gf_p, PERIOD_W1, 0, 28, wcg6) < 0) Print(GetLastError());
	if (CopyClose(gj_p, PERIOD_W1, 0, 28, wcg7) < 0) Print(GetLastError());

	if (CopyHigh(gu_p, PERIOD_W1, 0, 28, whg1) < 0) Print(GetLastError());
	if (CopyHigh(ga_p, PERIOD_W1, 0, 28, whg3) < 0) Print(GetLastError());
	if (CopyHigh(gn_p, PERIOD_W1, 0, 28, whg4) < 0) Print(GetLastError());
	if (CopyHigh(gc_p, PERIOD_W1, 0, 28, whg5) < 0) Print(GetLastError());
	if (CopyHigh(gf_p, PERIOD_W1, 0, 28, whg6) < 0) Print(GetLastError());
	if (CopyHigh(gj_p, PERIOD_W1, 0, 28, whg7) < 0) Print(GetLastError());

	if (CopyLow(gu_p, PERIOD_W1, 0, 28, wlg1) < 0) Print(GetLastError());
	if (CopyLow(ga_p, PERIOD_W1, 0, 28, wlg3) < 0) Print(GetLastError());
	if (CopyLow(gn_p, PERIOD_W1, 0, 28, wlg4) < 0) Print(GetLastError());
	if (CopyLow(gc_p, PERIOD_W1, 0, 28, wlg5) < 0) Print(GetLastError());
	if (CopyLow(gf_p, PERIOD_W1, 0, 28, wlg6) < 0) Print(GetLastError());
	if (CopyLow(gj_p, PERIOD_W1, 0, 28, wlg7) < 0) Print(GetLastError());

	double wca1[], wca4[], wca5[], wca6[], wca7[];
	double wha1[], wha4[], wha5[], wha6[], wha7[];
	double wla1[], wla4[], wla5[], wla6[], wla7[];
	ArraySetAsSeries(wca1, true); ArraySetAsSeries(wca4, true); ArraySetAsSeries(wca5, true); ArraySetAsSeries(wca6, true); ArraySetAsSeries(wca7, true);
	ArraySetAsSeries(wha1, true); ArraySetAsSeries(wha4, true); ArraySetAsSeries(wha5, true); ArraySetAsSeries(wha6, true); ArraySetAsSeries(wha7, true);
	ArraySetAsSeries(wla1, true); ArraySetAsSeries(wla4, true); ArraySetAsSeries(wla5, true); ArraySetAsSeries(wla6, true); ArraySetAsSeries(wla7, true);
	ArrayResize(wca1, 28); ArrayResize(wca4, 28); ArrayResize(wca5, 28); ArrayResize(wca6, 28); ArrayResize(wca7, 28);
	ArrayResize(wha1, 28); ArrayResize(wha4, 28); ArrayResize(wha5, 28); ArrayResize(wha6, 28); ArrayResize(wha7, 28);
	ArrayResize(wla1, 28); ArrayResize(wla4, 28); ArrayResize(wla5, 28); ArrayResize(wla6, 28); ArrayResize(wla7, 28);

	if (CopyClose(au_p, PERIOD_W1, 0, 28, wca1) < 0) Print(GetLastError());
	if (CopyClose(an_p, PERIOD_W1, 0, 28, wca4) < 0) Print(GetLastError());
	if (CopyClose(ac_p, PERIOD_W1, 0, 28, wca5) < 0) Print(GetLastError());
	if (CopyClose(af_p, PERIOD_W1, 0, 28, wca6) < 0) Print(GetLastError());
	if (CopyClose(aj_p, PERIOD_W1, 0, 28, wca7) < 0) Print(GetLastError());

	if (CopyHigh(au_p, PERIOD_W1, 0, 28, wha1) < 0) Print(GetLastError());
	if (CopyHigh(an_p, PERIOD_W1, 0, 28, wha4) < 0) Print(GetLastError());
	if (CopyHigh(ac_p, PERIOD_W1, 0, 28, wha5) < 0) Print(GetLastError());
	if (CopyHigh(af_p, PERIOD_W1, 0, 28, wha6) < 0) Print(GetLastError());
	if (CopyHigh(aj_p, PERIOD_W1, 0, 28, wha7) < 0) Print(GetLastError());

	if (CopyLow(au_p, PERIOD_W1, 0, 28, wla1) < 0) Print(GetLastError());
	if (CopyLow(an_p, PERIOD_W1, 0, 28, wla4) < 0) Print(GetLastError());
	if (CopyLow(ac_p, PERIOD_W1, 0, 28, wla5) < 0) Print(GetLastError());
	if (CopyLow(af_p, PERIOD_W1, 0, 28, wla6) < 0) Print(GetLastError());
	if (CopyLow(aj_p, PERIOD_W1, 0, 28, wla7) < 0) Print(GetLastError());

	double wcn1[], wcn5[], wcn6[], wcn7[];
	double whn1[], whn5[], whn6[], whn7[];
	double wln1[], wln5[], wln6[], wln7[];
	ArraySetAsSeries(wcn1, true); ArraySetAsSeries(wcn5, true); ArraySetAsSeries(wcn6, true); ArraySetAsSeries(wcn7, true);
	ArraySetAsSeries(whn1, true); ArraySetAsSeries(whn5, true); ArraySetAsSeries(whn6, true); ArraySetAsSeries(whn7, true);
	ArraySetAsSeries(wln1, true); ArraySetAsSeries(wln5, true); ArraySetAsSeries(wln6, true); ArraySetAsSeries(wln7, true);
	ArrayResize(wcn1, 28); ArrayResize(wcn5, 28); ArrayResize(wcn6, 28); ArrayResize(wcn7, 28);
	ArrayResize(whn1, 28); ArrayResize(whn5, 28); ArrayResize(whn6, 28); ArrayResize(whn7, 28);
	ArrayResize(wln1, 28); ArrayResize(wln5, 28); ArrayResize(wln6, 28); ArrayResize(wln7, 28);

	if (CopyClose(nu_p, PERIOD_W1, 0, 28, wcn1) < 0) Print(GetLastError());
	if (CopyClose(nc_p, PERIOD_W1, 0, 28, wcn5) < 0) Print(GetLastError());
	if (CopyClose(nf_p, PERIOD_W1, 0, 28, wcn6) < 0) Print(GetLastError());
	if (CopyClose(nj_p, PERIOD_W1, 0, 28, wcn7) < 0) Print(GetLastError());

	if (CopyHigh(nu_p, PERIOD_W1, 0, 28, whn1) < 0) Print(GetLastError());
	if (CopyHigh(nc_p, PERIOD_W1, 0, 28, whn5) < 0) Print(GetLastError());
	if (CopyHigh(nf_p, PERIOD_W1, 0, 28, whn6) < 0) Print(GetLastError());
	if (CopyHigh(nj_p, PERIOD_W1, 0, 28, whn7) < 0) Print(GetLastError());

	if (CopyLow(nu_p, PERIOD_W1, 0, 28, wln1) < 0) Print(GetLastError());
	if (CopyLow(nc_p, PERIOD_W1, 0, 28, wln5) < 0) Print(GetLastError());
	if (CopyLow(nf_p, PERIOD_W1, 0, 28, wln6) < 0) Print(GetLastError());
	if (CopyLow(nj_p, PERIOD_W1, 0, 28, wln7) < 0) Print(GetLastError());

	double wcc1[], wcc6[], wcc7[];
	double whc1[], whc6[], whc7[];
	double wlc1[], wlc6[], wlc7[];
	ArraySetAsSeries(wcc1, true); ArraySetAsSeries(wcc6, true); ArraySetAsSeries(wcc7, true);
	ArraySetAsSeries(whc1, true); ArraySetAsSeries(whc6, true); ArraySetAsSeries(whc7, true);
	ArraySetAsSeries(wlc1, true); ArraySetAsSeries(wlc6, true); ArraySetAsSeries(wlc7, true);
	ArrayResize(wcc1, 28); ArrayResize(wcc6, 28); ArrayResize(wcc7, 28);
	ArrayResize(whc1, 28); ArrayResize(whc6, 28); ArrayResize(whc7, 28);
	ArrayResize(wlc1, 28); ArrayResize(wlc6, 28); ArrayResize(wlc7, 28);

	if (CopyClose(uc_p, PERIOD_W1, 0, 28, wcc1) < 0) Print(GetLastError());
	if (CopyClose(cf_p, PERIOD_W1, 0, 28, wcc6) < 0) Print(GetLastError());
	if (CopyClose(cj_p, PERIOD_W1, 0, 28, wcc7) < 0) Print(GetLastError());

	if (CopyHigh(uc_p, PERIOD_W1, 0, 28, whc1) < 0) Print(GetLastError());
	if (CopyHigh(cf_p, PERIOD_W1, 0, 28, whc6) < 0) Print(GetLastError());
	if (CopyHigh(cj_p, PERIOD_W1, 0, 28, whc7) < 0) Print(GetLastError());

	if (CopyLow(uc_p, PERIOD_W1, 0, 28, wlc1) < 0) Print(GetLastError());
	if (CopyLow(cf_p, PERIOD_W1, 0, 28, wlc6) < 0) Print(GetLastError());
	if (CopyLow(cj_p, PERIOD_W1, 0, 28, wlc7) < 0) Print(GetLastError());

	double wcf1[], wcf7[];
	double whf1[], whf7[];
	double wlf1[], wlf7[];
	ArraySetAsSeries(wcf1, true); ArraySetAsSeries(wcf7, true);
	ArraySetAsSeries(whf1, true); ArraySetAsSeries(whf7, true);
	ArraySetAsSeries(wlf1, true); ArraySetAsSeries(wlf7, true);
	ArrayResize(wcf1, 28); ArrayResize(wcf7, 28);
	ArrayResize(whf1, 28); ArrayResize(whf7, 28);
	ArrayResize(wlf1, 28); ArrayResize(wlf7, 28);

	if (CopyClose(uf_p, PERIOD_W1, 0, 28, wcf1) < 0) Print(GetLastError());
	if (CopyClose(fj_p, PERIOD_W1, 0, 28, wcf7) < 0) Print(GetLastError());

	if (CopyHigh(uf_p, PERIOD_W1, 0, 28, whf1) < 0) Print(GetLastError());
	if (CopyHigh(fj_p, PERIOD_W1, 0, 28, whf7) < 0) Print(GetLastError());

	if (CopyLow(uf_p, PERIOD_W1, 0, 28, wlf1) < 0) Print(GetLastError());
	if (CopyLow(fj_p, PERIOD_W1, 0, 28, wlf7) < 0) Print(GetLastError());

	double wcj1[];
	double whj1[];
	double wlj1[];
	ArraySetAsSeries(wcj1, true);
	ArraySetAsSeries(whj1, true);
	ArraySetAsSeries(wlj1, true);
	ArrayResize(wcj1, 28);
	ArrayResize(whj1, 28);
	ArrayResize(wlj1, 28);

	if (CopyClose(uj_p, PERIOD_W1, 0, 28, wcj1) < 0) Print(GetLastError());

	if (CopyHigh(uj_p, PERIOD_W1, 0, 28, whj1) < 0) Print(GetLastError());

	if (CopyLow(uj_p, PERIOD_W1, 0, 28, wlj1) < 0) Print(GetLastError());


	{	//BufferFill for past prices
		//Week
		for (int i = 1; i <= 26; i++)
		{
			ohwayseur[i] = hdod(eu_p, whe1, wce1, eupl, i) + hdod(eg_p, whe2, wce2, egpl, i) + hdod(ea_p, whe3, wce3, eapl, i) + hdod(en_p, whe4, wce4, enpl, i) + hdod(ec_p, whe5, wce5, ecpl, i) + hdod(ef_p, whe6, wce6, efpl, i) + hdod(ej_p, whe7, wce7, ejpl, i);
			ohwaysgbp[i] = -ldod(eg_p, wle2, wce2, egpl, i) + hdod(gu_p, whg1, wcg1, gupl, i) + hdod(ga_p, whg3, wcg3, gapl, i) + hdod(gn_p, whg4, wcg4, gnpl, i) + hdod(gc_p, whg5, wcg5, gcpl, i) + hdod(gf_p, whg6, wcg6, gfpl, i) + hdod(gj_p, whg7, wcg7, gjpl, i);
			ohwaysaud[i] = -ldod(ea_p, wle3, wce3, eapl, i) - ldod(ga_p, wlg3, wcg3, gapl, i) + hdod(au_p, wha1, wca1, aupl, i) + hdod(an_p, wha4, wca4, anpl, i) + hdod(ac_p, wha5, wca5, acpl, i) + hdod(af_p, wha6, wca6, afpl, i) + hdod(aj_p, wha7, wca7, ajpl, i);
			ohwaysnzd[i] = -ldod(en_p, wle4, wce4, enpl, i) - ldod(gn_p, wlg4, wcg4, gnpl, i) - ldod(an_p, wla4, wca4, anpl, i) + hdod(nu_p, whn1, wcn1, nupl, i) + hdod(nc_p, whn5, wcn5, ncpl, i) + hdod(nf_p, whn6, wcn6, nfpl, i) + hdod(nj_p, whn7, wcn7, njpl, i);
			ohwayscad[i] = -ldod(ec_p, wle5, wce5, ecpl, i) - ldod(gc_p, wlg5, wcg5, gcpl, i) - ldod(ac_p, wla5, wca5, acpl, i) - ldod(nc_p, wln5, wcn5, ncpl, i) - ldod(uc_p, wlc1, wcc1, ucpl, i) + hdod(cf_p, whc6, wcc6, cfpl, i) + hdod(cj_p, whc7, wcc7, cjpl, i);
			ohwaysusd[i] = -ldod(eu_p, wle1, wce1, eupl, i) - ldod(gu_p, wlg1, wcg1, gupl, i) - ldod(au_p, wla1, wca1, aupl, i) - ldod(nu_p, wln1, wcn1, nupl, i) + hdod(uc_p, whc1, wcc1, ucpl, i) + hdod(uf_p, whf1, wcf1, ufpl, i) + hdod(uj_p, whj1, wcj1, ujpl, i);
			ohwayschf[i] = -ldod(ef_p, wle6, wce6, efpl, i) - ldod(gf_p, wlg6, wcg6, gfpl, i) - ldod(af_p, wla6, wca6, afpl, i) - ldod(nf_p, wln6, wcn6, nfpl, i) - ldod(uf_p, wlf1, wcf1, ufpl, i) - ldod(cf_p, wlc6, wcc6, cfpl, i) + hdod(fj_p, whf7, wcf7, fjpl, i);
			ohwaysjpy[i] = -ldod(ej_p, wle7, wce7, ejpl, i) - ldod(gj_p, wlg7, wcg7, gjpl, i) - ldod(aj_p, wla7, wca7, ajpl, i) - ldod(nj_p, wln7, wcn7, njpl, i) - ldod(uj_p, wlj1, wcj1, ujpl, i) - ldod(cj_p, wlc7, wcc7, cjpl, i) - ldod(fj_p, wlf7, wcf7, fjpl, i);

			olwayseur[i] = ldod(eu_p, wle1, wce1, eupl, i) + ldod(eg_p, wle2, wce2, egpl, i) + ldod(ea_p, wle3, wce3, eapl, i) + ldod(en_p, wle4, wce4, enpl, i) + ldod(ec_p, wle5, wce5, ecpl, i) + ldod(ef_p, wle6, wce6, efpl, i) + ldod(ej_p, wle7, wce7, ejpl, i);
			olwaysgbp[i] = -hdod(eg_p, whe2, wce2, egpl, i) + ldod(gu_p, wlg1, wcg1, gupl, i) + ldod(ga_p, wlg3, wcg3, gapl, i) + ldod(gn_p, wlg4, wcg4, gnpl, i) + ldod(gc_p, wlg5, wcg5, gcpl, i) + ldod(gf_p, wlg6, wcg6, gfpl, i) + ldod(gj_p, wlg7, wcg7, gjpl, i);
			olwaysaud[i] = -hdod(ea_p, whe3, wce3, eapl, i) - hdod(ga_p, whg3, wcg3, gapl, i) + ldod(au_p, wla1, wca1, aupl, i) + ldod(an_p, wla4, wca4, anpl, i) + ldod(ac_p, wla5, wca5, acpl, i) + ldod(af_p, wla6, wca6, afpl, i) + ldod(aj_p, wla7, wca7, ajpl, i);
			olwaysnzd[i] = -hdod(en_p, whe4, wce4, enpl, i) - hdod(gn_p, whg4, wcg4, gnpl, i) - hdod(an_p, wha4, wca4, anpl, i) + ldod(nu_p, wln1, wcn1, nupl, i) + ldod(nc_p, wln5, wcn5, ncpl, i) + ldod(nf_p, wln6, wcn6, nfpl, i) + ldod(nj_p, wln7, wcn7, njpl, i);
			olwayscad[i] = -hdod(ec_p, whe5, wce5, ecpl, i) - hdod(gc_p, whg5, wcg5, gcpl, i) - hdod(ac_p, wha5, wca5, acpl, i) - hdod(nc_p, whn5, wcn5, ncpl, i) - hdod(uc_p, whc1, wcc1, ucpl, i) + ldod(cf_p, wlc6, wcc6, cfpl, i) + ldod(cj_p, wlc7, wcc7, cjpl, i);
			olwaysusd[i] = -hdod(eu_p, whe1, wce1, eupl, i) - hdod(gu_p, whg1, wcg1, gupl, i) - hdod(au_p, wha1, wca1, aupl, i) - hdod(nu_p, whn1, wcn1, nupl, i) + ldod(uc_p, wlc1, wcc1, ucpl, i) + ldod(uf_p, wlf1, wcf1, ufpl, i) + ldod(uj_p, wlj1, wcj1, ujpl, i);
			olwayschf[i] = -hdod(ef_p, whe6, wce6, efpl, i) - hdod(gf_p, whg6, wcg6, gfpl, i) - hdod(af_p, wha6, wca6, afpl, i) - hdod(nf_p, whn6, wcn6, nfpl, i) - hdod(uf_p, whf1, wcf1, ufpl, i) - hdod(cf_p, whc6, wcc6, cfpl, i) + ldod(fj_p, wlf7, wcf7, fjpl, i);
			olwaysjpy[i] = -hdod(ej_p, whe7, wce7, ejpl, i) - hdod(gj_p, whg7, wcg7, gjpl, i) - hdod(aj_p, wha7, wca7, ajpl, i) - hdod(nj_p, whn7, wcn7, njpl, i) - hdod(uj_p, whj1, wcj1, ujpl, i) - hdod(cj_p, whc7, wcc7, cjpl, i) - hdod(fj_p, whf7, wcf7, fjpl, i);
		}
	}

	//FILL WEEK 
	double mce1[], mce2[], mce3[], mce4[], mce5[], mce6[], mce7[];
	double mhe1[], mhe2[], mhe3[], mhe4[], mhe5[], mhe6[], mhe7[];
	double mle1[], mle2[], mle3[], mle4[], mle5[], mle6[], mle7[];
	ArraySetAsSeries(mce1, true); ArraySetAsSeries(mce2, true); ArraySetAsSeries(mce3, true); ArraySetAsSeries(mce4, true); ArraySetAsSeries(mce5, true); ArraySetAsSeries(mce6, true); ArraySetAsSeries(mce7, true);
	ArraySetAsSeries(mhe1, true); ArraySetAsSeries(mhe2, true); ArraySetAsSeries(mhe3, true); ArraySetAsSeries(mhe4, true); ArraySetAsSeries(mhe5, true); ArraySetAsSeries(mhe6, true); ArraySetAsSeries(mhe7, true);
	ArraySetAsSeries(mle1, true); ArraySetAsSeries(mle2, true); ArraySetAsSeries(mle3, true); ArraySetAsSeries(mle4, true); ArraySetAsSeries(mle5, true); ArraySetAsSeries(mle6, true); ArraySetAsSeries(mle7, true);
	ArrayResize(mce1, 8); ArrayResize(mce2, 8); ArrayResize(mce3, 8); ArrayResize(mce4, 8); ArrayResize(mce5, 8); ArrayResize(mce6, 8); ArrayResize(mce7, 8);
	ArrayResize(mhe1, 8); ArrayResize(mhe2, 8); ArrayResize(mhe3, 8); ArrayResize(mhe4, 8); ArrayResize(mhe5, 8); ArrayResize(mhe6, 8); ArrayResize(mhe7, 8);
	ArrayResize(mle1, 8); ArrayResize(mle2, 8); ArrayResize(mle3, 8); ArrayResize(mle4, 8); ArrayResize(mle5, 8); ArrayResize(mle6, 8); ArrayResize(mle7, 8);

	if (CopyClose(eu_p, PERIOD_MN1, 0, 8, mce1) < 0) Print(GetLastError());
	if (CopyClose(eg_p, PERIOD_MN1, 0, 8, mce2) < 0) Print(GetLastError());
	if (CopyClose(ea_p, PERIOD_MN1, 0, 8, mce3) < 0) Print(GetLastError());
	if (CopyClose(en_p, PERIOD_MN1, 0, 8, mce4) < 0) Print(GetLastError());
	if (CopyClose(ec_p, PERIOD_MN1, 0, 8, mce5) < 0) Print(GetLastError());
	if (CopyClose(ef_p, PERIOD_MN1, 0, 8, mce6) < 0) Print(GetLastError());
	if (CopyClose(ej_p, PERIOD_MN1, 0, 8, mce7) < 0) Print(GetLastError());

	if (CopyHigh(eu_p, PERIOD_MN1, 0, 8, mhe1) < 0) Print(GetLastError());
	if (CopyHigh(eg_p, PERIOD_MN1, 0, 8, mhe2) < 0) Print(GetLastError());
	if (CopyHigh(ea_p, PERIOD_MN1, 0, 8, mhe3) < 0) Print(GetLastError());
	if (CopyHigh(en_p, PERIOD_MN1, 0, 8, mhe4) < 0) Print(GetLastError());
	if (CopyHigh(ec_p, PERIOD_MN1, 0, 8, mhe5) < 0) Print(GetLastError());
	if (CopyHigh(ef_p, PERIOD_MN1, 0, 8, mhe6) < 0) Print(GetLastError());
	if (CopyHigh(ej_p, PERIOD_MN1, 0, 8, mhe7) < 0) Print(GetLastError());

	if (CopyLow(eu_p, PERIOD_MN1, 0, 8, mle1) < 0) Print(GetLastError());
	if (CopyLow(eg_p, PERIOD_MN1, 0, 8, mle2) < 0) Print(GetLastError());
	if (CopyLow(ea_p, PERIOD_MN1, 0, 8, mle3) < 0) Print(GetLastError());
	if (CopyLow(en_p, PERIOD_MN1, 0, 8, mle4) < 0) Print(GetLastError());
	if (CopyLow(ec_p, PERIOD_MN1, 0, 8, mle5) < 0) Print(GetLastError());
	if (CopyLow(ef_p, PERIOD_MN1, 0, 8, mle6) < 0) Print(GetLastError());
	if (CopyLow(ej_p, PERIOD_MN1, 0, 8, mle7) < 0) Print(GetLastError());

	double mcg1[], mcg3[], mcg4[], mcg5[], mcg6[], mcg7[];
	double mhg1[], mhg3[], mhg4[], mhg5[], mhg6[], mhg7[];
	double mlg1[], mlg3[], mlg4[], mlg5[], mlg6[], mlg7[];
	ArraySetAsSeries(mcg1, true); ArraySetAsSeries(mcg3, true); ArraySetAsSeries(mcg4, true); ArraySetAsSeries(mcg5, true); ArraySetAsSeries(mcg6, true); ArraySetAsSeries(mcg7, true);
	ArraySetAsSeries(mhg1, true); ArraySetAsSeries(mhg3, true); ArraySetAsSeries(mhg4, true); ArraySetAsSeries(mhg5, true); ArraySetAsSeries(mhg6, true); ArraySetAsSeries(mhg7, true);
	ArraySetAsSeries(mlg1, true); ArraySetAsSeries(mlg3, true); ArraySetAsSeries(mlg4, true); ArraySetAsSeries(mlg5, true); ArraySetAsSeries(mlg6, true); ArraySetAsSeries(mlg7, true);
	ArrayResize(mcg1, 8); ArrayResize(mcg3, 8); ArrayResize(mcg4, 8); ArrayResize(mcg5, 8); ArrayResize(mcg6, 8); ArrayResize(mcg7, 8);
	ArrayResize(mhg1, 8); ArrayResize(mhg3, 8); ArrayResize(mhg4, 8); ArrayResize(mhg5, 8); ArrayResize(mhg6, 8); ArrayResize(mhg7, 8);
	ArrayResize(mlg1, 8); ArrayResize(mlg3, 8); ArrayResize(mlg4, 8); ArrayResize(mlg5, 8); ArrayResize(mlg6, 8); ArrayResize(mlg7, 8);

	if (CopyClose(gu_p, PERIOD_MN1, 0, 8, mcg1) < 0) Print(GetLastError());
	if (CopyClose(ga_p, PERIOD_MN1, 0, 8, mcg3) < 0) Print(GetLastError());
	if (CopyClose(gn_p, PERIOD_MN1, 0, 8, mcg4) < 0) Print(GetLastError());
	if (CopyClose(gc_p, PERIOD_MN1, 0, 8, mcg5) < 0) Print(GetLastError());
	if (CopyClose(gf_p, PERIOD_MN1, 0, 8, mcg6) < 0) Print(GetLastError());
	if (CopyClose(gj_p, PERIOD_MN1, 0, 8, mcg7) < 0) Print(GetLastError());

	if (CopyHigh(gu_p, PERIOD_MN1, 0, 8, mhg1) < 0) Print(GetLastError());
	if (CopyHigh(ga_p, PERIOD_MN1, 0, 8, mhg3) < 0) Print(GetLastError());
	if (CopyHigh(gn_p, PERIOD_MN1, 0, 8, mhg4) < 0) Print(GetLastError());
	if (CopyHigh(gc_p, PERIOD_MN1, 0, 8, mhg5) < 0) Print(GetLastError());
	if (CopyHigh(gf_p, PERIOD_MN1, 0, 8, mhg6) < 0) Print(GetLastError());
	if (CopyHigh(gj_p, PERIOD_MN1, 0, 8, mhg7) < 0) Print(GetLastError());

	if (CopyLow(gu_p, PERIOD_MN1, 0, 8, mlg1) < 0) Print(GetLastError());
	if (CopyLow(ga_p, PERIOD_MN1, 0, 8, mlg3) < 0) Print(GetLastError());
	if (CopyLow(gn_p, PERIOD_MN1, 0, 8, mlg4) < 0) Print(GetLastError());
	if (CopyLow(gc_p, PERIOD_MN1, 0, 8, mlg5) < 0) Print(GetLastError());
	if (CopyLow(gf_p, PERIOD_MN1, 0, 8, mlg6) < 0) Print(GetLastError());
	if (CopyLow(gj_p, PERIOD_MN1, 0, 8, mlg7) < 0) Print(GetLastError());

	double mca1[], mca4[], mca5[], mca6[], mca7[];
	double mha1[], mha4[], mha5[], mha6[], mha7[];
	double mla1[], mla4[], mla5[], mla6[], mla7[];
	ArraySetAsSeries(mca1, true); ArraySetAsSeries(mca4, true); ArraySetAsSeries(mca5, true); ArraySetAsSeries(mca6, true); ArraySetAsSeries(mca7, true);
	ArraySetAsSeries(mha1, true); ArraySetAsSeries(mha4, true); ArraySetAsSeries(mha5, true); ArraySetAsSeries(mha6, true); ArraySetAsSeries(mha7, true);
	ArraySetAsSeries(mla1, true); ArraySetAsSeries(mla4, true); ArraySetAsSeries(mla5, true); ArraySetAsSeries(mla6, true); ArraySetAsSeries(mla7, true);
	ArrayResize(mca1, 8); ArrayResize(mca4, 8); ArrayResize(mca5, 8); ArrayResize(mca6, 8); ArrayResize(mca7, 8);
	ArrayResize(mha1, 8); ArrayResize(mha4, 8); ArrayResize(mha5, 8); ArrayResize(mha6, 8); ArrayResize(mha7, 8);
	ArrayResize(mla1, 8); ArrayResize(mla4, 8); ArrayResize(mla5, 8); ArrayResize(mla6, 8); ArrayResize(mla7, 8);

	if (CopyClose(au_p, PERIOD_MN1, 0, 8, mca1) < 0) Print(GetLastError());
	if (CopyClose(an_p, PERIOD_MN1, 0, 8, mca4) < 0) Print(GetLastError());
	if (CopyClose(ac_p, PERIOD_MN1, 0, 8, mca5) < 0) Print(GetLastError());
	if (CopyClose(af_p, PERIOD_MN1, 0, 8, mca6) < 0) Print(GetLastError());
	if (CopyClose(aj_p, PERIOD_MN1, 0, 8, mca7) < 0) Print(GetLastError());

	if (CopyHigh(au_p, PERIOD_MN1, 0, 8, mha1) < 0) Print(GetLastError());
	if (CopyHigh(an_p, PERIOD_MN1, 0, 8, mha4) < 0) Print(GetLastError());
	if (CopyHigh(ac_p, PERIOD_MN1, 0, 8, mha5) < 0) Print(GetLastError());
	if (CopyHigh(af_p, PERIOD_MN1, 0, 8, mha6) < 0) Print(GetLastError());
	if (CopyHigh(aj_p, PERIOD_MN1, 0, 8, mha7) < 0) Print(GetLastError());

	if (CopyLow(au_p, PERIOD_MN1, 0, 8, mla1) < 0) Print(GetLastError());
	if (CopyLow(an_p, PERIOD_MN1, 0, 8, mla4) < 0) Print(GetLastError());
	if (CopyLow(ac_p, PERIOD_MN1, 0, 8, mla5) < 0) Print(GetLastError());
	if (CopyLow(af_p, PERIOD_MN1, 0, 8, mla6) < 0) Print(GetLastError());
	if (CopyLow(aj_p, PERIOD_MN1, 0, 8, mla7) < 0) Print(GetLastError());

	double mcn1[], mcn5[], mcn6[], mcn7[];
	double mhn1[], mhn5[], mhn6[], mhn7[];
	double mln1[], mln5[], mln6[], mln7[];
	ArraySetAsSeries(mcn1, true); ArraySetAsSeries(mcn5, true); ArraySetAsSeries(mcn6, true); ArraySetAsSeries(mcn7, true);
	ArraySetAsSeries(mhn1, true); ArraySetAsSeries(mhn5, true); ArraySetAsSeries(mhn6, true); ArraySetAsSeries(mhn7, true);
	ArraySetAsSeries(mln1, true); ArraySetAsSeries(mln5, true); ArraySetAsSeries(mln6, true); ArraySetAsSeries(mln7, true);
	ArrayResize(mcn1, 8); ArrayResize(mcn5, 8); ArrayResize(mcn6, 8); ArrayResize(mcn7, 8);
	ArrayResize(mhn1, 8); ArrayResize(mhn5, 8); ArrayResize(mhn6, 8); ArrayResize(mhn7, 8);
	ArrayResize(mln1, 8); ArrayResize(mln5, 8); ArrayResize(mln6, 8); ArrayResize(mln7, 8);

	if (CopyClose(nu_p, PERIOD_MN1, 0, 8, mcn1) < 0) Print(GetLastError());
	if (CopyClose(nc_p, PERIOD_MN1, 0, 8, mcn5) < 0) Print(GetLastError());
	if (CopyClose(nf_p, PERIOD_MN1, 0, 8, mcn6) < 0) Print(GetLastError());
	if (CopyClose(nj_p, PERIOD_MN1, 0, 8, mcn7) < 0) Print(GetLastError());

	if (CopyHigh(nu_p, PERIOD_MN1, 0, 8, mhn1) < 0) Print(GetLastError());
	if (CopyHigh(nc_p, PERIOD_MN1, 0, 8, mhn5) < 0) Print(GetLastError());
	if (CopyHigh(nf_p, PERIOD_MN1, 0, 8, mhn6) < 0) Print(GetLastError());
	if (CopyHigh(nj_p, PERIOD_MN1, 0, 8, mhn7) < 0) Print(GetLastError());

	if (CopyLow(nu_p, PERIOD_MN1, 0, 8, mln1) < 0) Print(GetLastError());
	if (CopyLow(nc_p, PERIOD_MN1, 0, 8, mln5) < 0) Print(GetLastError());
	if (CopyLow(nf_p, PERIOD_MN1, 0, 8, mln6) < 0) Print(GetLastError());
	if (CopyLow(nj_p, PERIOD_MN1, 0, 8, mln7) < 0) Print(GetLastError());

	double mcc1[], mcc6[], mcc7[];
	double mhc1[], mhc6[], mhc7[];
	double mlc1[], mlc6[], mlc7[];
	ArraySetAsSeries(mcc1, true); ArraySetAsSeries(mcc6, true); ArraySetAsSeries(mcc7, true);
	ArraySetAsSeries(mhc1, true); ArraySetAsSeries(mhc6, true); ArraySetAsSeries(mhc7, true);
	ArraySetAsSeries(mlc1, true); ArraySetAsSeries(mlc6, true); ArraySetAsSeries(mlc7, true);
	ArrayResize(mcc1, 8); ArrayResize(mcc6, 8); ArrayResize(mcc7, 8);
	ArrayResize(mhc1, 8); ArrayResize(mhc6, 8); ArrayResize(mhc7, 8);
	ArrayResize(mlc1, 8); ArrayResize(mlc6, 8); ArrayResize(mlc7, 8);

	if (CopyClose(uc_p, PERIOD_MN1, 0, 8, mcc1) < 0) Print(GetLastError());
	if (CopyClose(cf_p, PERIOD_MN1, 0, 8, mcc6) < 0) Print(GetLastError());
	if (CopyClose(cj_p, PERIOD_MN1, 0, 8, mcc7) < 0) Print(GetLastError());

	if (CopyHigh(uc_p, PERIOD_MN1, 0, 8, mhc1) < 0) Print(GetLastError());
	if (CopyHigh(cf_p, PERIOD_MN1, 0, 8, mhc6) < 0) Print(GetLastError());
	if (CopyHigh(cj_p, PERIOD_MN1, 0, 8, mhc7) < 0) Print(GetLastError());

	if (CopyLow(uc_p, PERIOD_MN1, 0, 8, mlc1) < 0) Print(GetLastError());
	if (CopyLow(cf_p, PERIOD_MN1, 0, 8, mlc6) < 0) Print(GetLastError());
	if (CopyLow(cj_p, PERIOD_MN1, 0, 8, mlc7) < 0) Print(GetLastError());

	double mcf1[], mcf7[];
	double mhf1[], mhf7[];
	double mlf1[], mlf7[];
	ArraySetAsSeries(mcf1, true); ArraySetAsSeries(mcf7, true);
	ArraySetAsSeries(mhf1, true); ArraySetAsSeries(mhf7, true);
	ArraySetAsSeries(mlf1, true); ArraySetAsSeries(mlf7, true);
	ArrayResize(mcf1, 8); ArrayResize(mcf7, 8);
	ArrayResize(mhf1, 8); ArrayResize(mhf7, 8);
	ArrayResize(mlf1, 8); ArrayResize(mlf7, 8);

	if (CopyClose(uf_p, PERIOD_MN1, 0, 8, mcf1) < 0) Print(GetLastError());
	if (CopyClose(fj_p, PERIOD_MN1, 0, 8, mcf7) < 0) Print(GetLastError());

	if (CopyHigh(uf_p, PERIOD_MN1, 0, 8, mhf1) < 0) Print(GetLastError());
	if (CopyHigh(fj_p, PERIOD_MN1, 0, 8, mhf7) < 0) Print(GetLastError());

	if (CopyLow(uf_p, PERIOD_MN1, 0, 8, mlf1) < 0) Print(GetLastError());
	if (CopyLow(fj_p, PERIOD_MN1, 0, 8, mlf7) < 0) Print(GetLastError());

	double mcj1[];
	double mhj1[];
	double mlj1[];
	ArraySetAsSeries(mcj1, true);
	ArraySetAsSeries(mhj1, true);
	ArraySetAsSeries(mlj1, true);
	ArrayResize(mcj1, 8);
	ArrayResize(mhj1, 8);
	ArrayResize(mlj1, 8);

	if (CopyClose(uj_p, PERIOD_MN1, 0, 8, mcj1) < 0) Print(GetLastError());

	if (CopyHigh(uj_p, PERIOD_MN1, 0, 8, mhj1) < 0) Print(GetLastError());

	if (CopyLow(uj_p, PERIOD_MN1, 0, 8, mlj1) < 0) Print(GetLastError());


	{	//BufferFill for past prices
		//Week
		for (int i = 1; i <= 6; i++)
		{
			ohmayseur[i] = hdod(eu_p, mhe1, mce1, eupl, i) + hdod(eg_p, mhe2, mce2, egpl, i) + hdod(ea_p, mhe3, mce3, eapl, i) + hdod(en_p, mhe4, mce4, enpl, i) + hdod(ec_p, mhe5, mce5, ecpl, i) + hdod(ef_p, mhe6, mce6, efpl, i) + hdod(ej_p, mhe7, mce7, ejpl, i);
			ohmaysgbp[i] = -ldod(eg_p, mle2, mce2, egpl, i) + hdod(gu_p, mhg1, mcg1, gupl, i) + hdod(ga_p, mhg3, mcg3, gapl, i) + hdod(gn_p, mhg4, mcg4, gnpl, i) + hdod(gc_p, mhg5, mcg5, gcpl, i) + hdod(gf_p, mhg6, mcg6, gfpl, i) + hdod(gj_p, mhg7, mcg7, gjpl, i);
			ohmaysaud[i] = -ldod(ea_p, mle3, mce3, eapl, i) - ldod(ga_p, mlg3, mcg3, gapl, i) + hdod(au_p, mha1, mca1, aupl, i) + hdod(an_p, mha4, mca4, anpl, i) + hdod(ac_p, mha5, mca5, acpl, i) + hdod(af_p, mha6, mca6, afpl, i) + hdod(aj_p, mha7, mca7, ajpl, i);
			ohmaysnzd[i] = -ldod(en_p, mle4, mce4, enpl, i) - ldod(gn_p, mlg4, mcg4, gnpl, i) - ldod(an_p, mla4, mca4, anpl, i) + hdod(nu_p, mhn1, mcn1, nupl, i) + hdod(nc_p, mhn5, mcn5, ncpl, i) + hdod(nf_p, mhn6, mcn6, nfpl, i) + hdod(nj_p, mhn7, mcn7, njpl, i);
			ohmayscad[i] = -ldod(ec_p, mle5, mce5, ecpl, i) - ldod(gc_p, mlg5, mcg5, gcpl, i) - ldod(ac_p, mla5, mca5, acpl, i) - ldod(nc_p, mln5, mcn5, ncpl, i) - ldod(uc_p, mlc1, mcc1, ucpl, i) + hdod(cf_p, mhc6, mcc6, cfpl, i) + hdod(cj_p, mhc7, mcc7, cjpl, i);
			ohmaysusd[i] = -ldod(eu_p, mle1, mce1, eupl, i) - ldod(gu_p, mlg1, mcg1, gupl, i) - ldod(au_p, mla1, mca1, aupl, i) - ldod(nu_p, mln1, mcn1, nupl, i) + hdod(uc_p, mhc1, mcc1, ucpl, i) + hdod(uf_p, mhf1, mcf1, ufpl, i) + hdod(uj_p, mhj1, mcj1, ujpl, i);
			ohmayschf[i] = -ldod(ef_p, mle6, mce6, efpl, i) - ldod(gf_p, mlg6, mcg6, gfpl, i) - ldod(af_p, mla6, mca6, afpl, i) - ldod(nf_p, mln6, mcn6, nfpl, i) - ldod(uf_p, mlf1, mcf1, ufpl, i) - ldod(cf_p, mlc6, mcc6, cfpl, i) + hdod(fj_p, mhf7, mcf7, fjpl, i);
			ohmaysjpy[i] = -ldod(ej_p, mle7, mce7, ejpl, i) - ldod(gj_p, mlg7, mcg7, gjpl, i) - ldod(aj_p, mla7, mca7, ajpl, i) - ldod(nj_p, mln7, mcn7, njpl, i) - ldod(uj_p, mlj1, mcj1, ujpl, i) - ldod(cj_p, mlc7, mcc7, cjpl, i) - ldod(fj_p, mlf7, mcf7, fjpl, i);

			olmayseur[i] = ldod(eu_p, mle1, mce1, eupl, i) + ldod(eg_p, mle2, mce2, egpl, i) + ldod(ea_p, mle3, mce3, eapl, i) + ldod(en_p, mle4, mce4, enpl, i) + ldod(ec_p, mle5, mce5, ecpl, i) + ldod(ef_p, mle6, mce6, efpl, i) + ldod(ej_p, mle7, mce7, ejpl, i);
			olmaysgbp[i] = -hdod(eg_p, mhe2, mce2, egpl, i) + ldod(gu_p, mlg1, mcg1, gupl, i) + ldod(ga_p, mlg3, mcg3, gapl, i) + ldod(gn_p, mlg4, mcg4, gnpl, i) + ldod(gc_p, mlg5, mcg5, gcpl, i) + ldod(gf_p, mlg6, mcg6, gfpl, i) + ldod(gj_p, mlg7, mcg7, gjpl, i);
			olmaysaud[i] = -hdod(ea_p, mhe3, mce3, eapl, i) - hdod(ga_p, mhg3, mcg3, gapl, i) + ldod(au_p, mla1, mca1, aupl, i) + ldod(an_p, mla4, mca4, anpl, i) + ldod(ac_p, mla5, mca5, acpl, i) + ldod(af_p, mla6, mca6, afpl, i) + ldod(aj_p, mla7, mca7, ajpl, i);
			olmaysnzd[i] = -hdod(en_p, mhe4, mce4, enpl, i) - hdod(gn_p, mhg4, mcg4, gnpl, i) - hdod(an_p, mha4, mca4, anpl, i) + ldod(nu_p, mln1, mcn1, nupl, i) + ldod(nc_p, mln5, mcn5, ncpl, i) + ldod(nf_p, mln6, mcn6, nfpl, i) + ldod(nj_p, mln7, mcn7, njpl, i);
			olmayscad[i] = -hdod(ec_p, mhe5, mce5, ecpl, i) - hdod(gc_p, mhg5, mcg5, gcpl, i) - hdod(ac_p, mha5, mca5, acpl, i) - hdod(nc_p, mhn5, mcn5, ncpl, i) - hdod(uc_p, mhc1, mcc1, ucpl, i) + ldod(cf_p, mlc6, mcc6, cfpl, i) + ldod(cj_p, mlc7, mcc7, cjpl, i);
			olmaysusd[i] = -hdod(eu_p, mhe1, mce1, eupl, i) - hdod(gu_p, mhg1, mcg1, gupl, i) - hdod(au_p, mha1, mca1, aupl, i) - hdod(nu_p, mhn1, mcn1, nupl, i) + ldod(uc_p, mlc1, mcc1, ucpl, i) + ldod(uf_p, mlf1, mcf1, ufpl, i) + ldod(uj_p, mlj1, mcj1, ujpl, i);
			olmayschf[i] = -hdod(ef_p, mhe6, mce6, efpl, i) - hdod(gf_p, mhg6, mcg6, gfpl, i) - hdod(af_p, mha6, mca6, afpl, i) - hdod(nf_p, mhn6, mcn6, nfpl, i) - hdod(uf_p, mhf1, mcf1, ufpl, i) - hdod(cf_p, mhc6, mcc6, cfpl, i) + ldod(fj_p, mlf7, mcf7, fjpl, i);
			olmaysjpy[i] = -hdod(ej_p, mhe7, mce7, ejpl, i) - hdod(gj_p, mhg7, mcg7, gjpl, i) - hdod(aj_p, mha7, mca7, ajpl, i) - hdod(nj_p, mhn7, mcn7, njpl, i) - hdod(uj_p, mhj1, mcj1, ujpl, i) - hdod(cj_p, mhc7, mcc7, cjpl, i) - hdod(fj_p, mhf7, mcf7, fjpl, i);
		}
	}

	//EUR
	double euravdl = 0, euravdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		euravdl += oldayseur[i] / 72;
		euravdh += ohdayseur[i] / 72;
	}
	double euravwl = 0, euravwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		euravwl += olwayseur[i] / 26;
		euravwh += ohwayseur[i] / 26;
	}
	double euravml = 0, euravmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		euravml += olmayseur[i] / 6;
		euravmh += ohmayseur[i] / 6;
	}

	//GBP
	double gbpavdl = 0, gbpavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		gbpavdl += oldaysgbp[i] / 72;
		gbpavdh += ohdaysgbp[i] / 72;
	}
	double gbpavwl = 0, gbpavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		gbpavwl += olwaysgbp[i] / 26;
		gbpavwh += ohwaysgbp[i] / 26;
	}
	double gbpavml = 0, gbpavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		gbpavml += olmaysgbp[i] / 6;
		gbpavmh += ohmaysgbp[i] / 6;
	}

	//AUD
	double audavdl = 0, audavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		audavdl += oldaysaud[i] / 72;
		audavdh += ohdaysaud[i] / 72;
	}
	double audavwl = 0, audavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		audavwl += olwaysaud[i] / 26;
		audavwh += ohwaysaud[i] / 26;
	}
	double audavml = 0, audavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		audavml += olmaysaud[i] / 6;
		audavmh += ohmaysaud[i] / 6;
	}

	//NZD
	double nzdavdl = 0, nzdavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		nzdavdl += oldaysnzd[i] / 72;
		nzdavdh += ohdaysnzd[i] / 72;
	}
	double nzdavwl = 0, nzdavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		nzdavwl += olwaysnzd[i] / 26;
		nzdavwh += ohwaysnzd[i] / 26;
	}
	double nzdavml = 0, nzdavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		nzdavml += olmaysnzd[i] / 6;
		nzdavmh += ohmaysnzd[i] / 6;
	}

	//CAD
	double cadavdl = 0, cadavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		cadavdl += oldayscad[i] / 72;
		cadavdh += ohdayscad[i] / 72;
	}
	double cadavwl = 0, cadavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		cadavwl += olwayscad[i] / 26;
		cadavwh += ohwayscad[i] / 26;
	}
	double cadavml = 0, cadavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		cadavml += olmayscad[i] / 6;
		cadavmh += ohmayscad[i] / 6;
	}

	//USD
	double usdavdl = 0, usdavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		usdavdl += oldaysusd[i] / 72;
		usdavdh += ohdaysusd[i] / 72;
	}
	double usdavwl = 0, usdavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		usdavwl += olwaysusd[i] / 26;
		usdavwh += ohwaysusd[i] / 26;
	}
	double usdavml = 0, usdavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		usdavml += olmaysusd[i] / 6;
		usdavmh += ohmaysusd[i] / 6;
	}

	//CHF
	double chfavdl = 0, chfavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		chfavdl += oldayschf[i] / 72;
		chfavdh += ohdayschf[i] / 72;
	}
	double chfavwl = 0, chfavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		chfavwl += olwayschf[i] / 26;
		chfavwh += ohwayschf[i] / 26;
	}
	double chfavml = 0, chfavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		chfavml += olmayschf[i] / 6;
		chfavmh += ohmayschf[i] / 6;
	}

	//JPY
	double jpyavdl = 0, jpyavdh = 0;
	for (int i = 72; i >= 1; i--)
	{
		jpyavdl += oldaysjpy[i] / 72;
		jpyavdh += ohdaysjpy[i] / 72;
	}
	double jpyavwl = 0, jpyavwh = 0;
	for (int i = 26; i >= 1; i--)
	{
		jpyavwl += olwaysjpy[i] / 26;
		jpyavwh += ohwaysjpy[i] / 26;
	}
	double jpyavml = 0, jpyavmh = 0;
	for (int i = 6; i >= 1; i--)
	{
		jpyavml += olmaysjpy[i] / 6;
		jpyavmh += ohmaysjpy[i] / 6;
	}

	double avghd = MathAbs(euravdh + gbpavdh + audavdh + nzdavdh + cadavdh + usdavdh + chfavdh + jpyavdh);
	double avgld = MathAbs(euravdl + gbpavdl + audavdl + nzdavdl + cadavdl + usdavdl + chfavdl + jpyavdl);

	double avghw = MathAbs(euravwh + gbpavwh + audavwh + nzdavwh + cadavwh + usdavwh + chfavwh + jpyavwh);
	double avglw = MathAbs(euravwl + gbpavwl + audavwl + nzdavwl + cadavwl + usdavwl + chfavwl + jpyavwl);

	double avghm = MathAbs(euravmh + gbpavmh + audavmh + nzdavmh + cadavmh + usdavmh + chfavmh + jpyavmh);
	double avglm = MathAbs(euravml + gbpavml + audavml + nzdavml + cadavml + usdavml + chfavml + jpyavml);

	//Day
	double eustdh = iStdDevOnArray(ohdayseur, 72, 24, 0, MODE_SMA, 1);
	double eustdl = iStdDevOnArray(oldayseur, 72, 24, 0, MODE_SMA, 1);
	double gbstdh = iStdDevOnArray(ohdaysgbp, 72, 24, 0, MODE_SMA, 1);
	double gbstdl = iStdDevOnArray(oldaysgbp, 72, 24, 0, MODE_SMA, 1);
	double austdh = iStdDevOnArray(ohdaysaud, 72, 24, 0, MODE_SMA, 1);
	double austdl = iStdDevOnArray(oldaysaud, 72, 24, 0, MODE_SMA, 1);
	double nzstdh = iStdDevOnArray(ohdaysnzd, 72, 24, 0, MODE_SMA, 1);
	double nzstdl = iStdDevOnArray(oldaysnzd, 72, 24, 0, MODE_SMA, 1);
	double castdh = iStdDevOnArray(ohdayscad, 72, 24, 0, MODE_SMA, 1);
	double castdl = iStdDevOnArray(oldayscad, 72, 24, 0, MODE_SMA, 1);
	double usstdh = iStdDevOnArray(ohdaysusd, 72, 24, 0, MODE_SMA, 1);
	double usstdl = iStdDevOnArray(oldaysusd, 72, 24, 0, MODE_SMA, 1);
	double chstdh = iStdDevOnArray(ohdayschf, 72, 24, 0, MODE_SMA, 1);
	double chstdl = iStdDevOnArray(oldayschf, 72, 24, 0, MODE_SMA, 1);
	double jpstdh = iStdDevOnArray(ohdaysjpy, 72, 24, 0, MODE_SMA, 1);
	double jpstdl = iStdDevOnArray(oldaysjpy, 72, 24, 0, MODE_SMA, 1);
	//Week
	double eustwh = iStdDevOnArray(ohwayseur, 26, 9, 0, MODE_SMA, 1);
	double eustwl = iStdDevOnArray(olwayseur, 26, 9, 0, MODE_SMA, 1);
	double gbstwh = iStdDevOnArray(ohwaysgbp, 26, 9, 0, MODE_SMA, 1);
	double gbstwl = iStdDevOnArray(olwaysgbp, 26, 9, 0, MODE_SMA, 1);
	double austwh = iStdDevOnArray(ohwaysaud, 26, 9, 0, MODE_SMA, 1);
	double austwl = iStdDevOnArray(olwaysaud, 26, 9, 0, MODE_SMA, 1);
	double nzstwh = iStdDevOnArray(ohwaysnzd, 26, 9, 0, MODE_SMA, 1);
	double nzstwl = iStdDevOnArray(olwaysnzd, 26, 9, 0, MODE_SMA, 1);
	double castwh = iStdDevOnArray(ohwayscad, 26, 9, 0, MODE_SMA, 1);
	double castwl = iStdDevOnArray(olwayscad, 26, 9, 0, MODE_SMA, 1);
	double usstwh = iStdDevOnArray(ohwaysusd, 26, 9, 0, MODE_SMA, 1);
	double usstwl = iStdDevOnArray(olwaysusd, 26, 9, 0, MODE_SMA, 1);
	double chstwh = iStdDevOnArray(ohwayschf, 26, 9, 0, MODE_SMA, 1);
	double chstwl = iStdDevOnArray(olwayschf, 26, 9, 0, MODE_SMA, 1);
	double jpstwh = iStdDevOnArray(ohwaysjpy, 26, 9, 0, MODE_SMA, 1);
	double jpstwl = iStdDevOnArray(olwaysjpy, 26, 9, 0, MODE_SMA, 1);
	//Month
	double eustmh = iStdDevOnArray(ohmayseur, 6, 2, 0, MODE_SMA, 1);
	double eustml = iStdDevOnArray(olmayseur, 6, 2, 0, MODE_SMA, 1);
	double gbstmh = iStdDevOnArray(ohmaysgbp, 6, 2, 0, MODE_SMA, 1);
	double gbstml = iStdDevOnArray(olmaysgbp, 6, 2, 0, MODE_SMA, 1);
	double austmh = iStdDevOnArray(ohmaysaud, 6, 2, 0, MODE_SMA, 1);
	double austml = iStdDevOnArray(olmaysaud, 6, 2, 0, MODE_SMA, 1);
	double nzstmh = iStdDevOnArray(ohmaysnzd, 6, 2, 0, MODE_SMA, 1);
	double nzstml = iStdDevOnArray(olmaysnzd, 6, 2, 0, MODE_SMA, 1);
	double castmh = iStdDevOnArray(ohmayscad, 6, 2, 0, MODE_SMA, 1);
	double castml = iStdDevOnArray(olmayscad, 6, 2, 0, MODE_SMA, 1);
	double usstmh = iStdDevOnArray(ohmaysusd, 6, 2, 0, MODE_SMA, 1);
	double usstml = iStdDevOnArray(olmaysusd, 6, 2, 0, MODE_SMA, 1);
	double chstmh = iStdDevOnArray(ohmayschf, 6, 2, 0, MODE_SMA, 1);
	double chstml = iStdDevOnArray(olmayschf, 6, 2, 0, MODE_SMA, 1);
	double jpstmh = iStdDevOnArray(ohmaysjpy, 6, 2, 0, MODE_SMA, 1);
	double jpstml = iStdDevOnArray(olmaysjpy, 6, 2, 0, MODE_SMA, 1);

	double daystd = eustdh + eustdl + gbstdh + gbstdl + austdh + austdl + nzstdh + nzstdl + castdh + castdl + usstdh + usstdl + chstdh + chstdl + jpstdh + jpstdl;
	double weekstd = eustwh + eustwl + gbstwh + gbstwl + austwh + austwl + nzstwh + nzstwl + castwh + castwl + usstwh + usstwl + chstwh + chstwl + jpstwh + jpstwl;
	double monthstd = eustmh + eustml + gbstmh + gbstml + austmh + austml + nzstmh + nzstml + castmh + castml + usstmh + usstml + chstmh + chstml + jpstmh + jpstml;

	pastdata["DayAVG"].Add(avghd + avgld, 1);
	pastdata["WeekAVG"].Add(avghw + avglw, 1);
	pastdata["MonthAVG"].Add(avghm + avglm, 1);
	pastdata["STD"][0].Add("EUR");
	pastdata["STD"][0].Add(iStdDevOnArray(ohdayseur, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldayseur, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][1].Add("GBP");
	pastdata["STD"][1].Add(iStdDevOnArray(ohdaysgbp, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldaysgbp, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][2].Add("AUD");
	pastdata["STD"][2].Add(iStdDevOnArray(ohdaysaud, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldaysaud, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][3].Add("NZD");
	pastdata["STD"][3].Add(iStdDevOnArray(ohdaysnzd, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldaysnzd, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][4].Add("CAD");
	pastdata["STD"][4].Add(iStdDevOnArray(ohdayscad, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldayscad, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][5].Add("USD");
	pastdata["STD"][5].Add(iStdDevOnArray(ohdaysusd, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldaysusd, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][6].Add("CHF");
	pastdata["STD"][6].Add(iStdDevOnArray(ohdayschf, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldayschf, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][7].Add("JPY");
	pastdata["STD"][7].Add(iStdDevOnArray(ohdaysjpy, 72, 24, 0, MODE_SMA, 1) + MathAbs(iStdDevOnArray(oldaysjpy, 72, 24, 0, MODE_SMA, 1)), 1);
	pastdata["STD"][8].Add("ALLDay");
	pastdata["STD"][8].Add(daystd, 1);
	pastdata["STD"][9].Add("ALLWeek");
	pastdata["STD"][9].Add(weekstd, 1);
	pastdata["STD"][10].Add("ALLMonth");
	pastdata["STD"][10].Add(monthstd, 1);
	pastdata["AVGd"][0].Add("EUR");
	pastdata["AVGd"][0].Add(MathAbs(euravdh), 1);
	pastdata["AVGd"][0].Add(MathAbs(euravdl), 1);
	pastdata["AVGd"][1].Add("GBP");
	pastdata["AVGd"][1].Add(MathAbs(gbpavdh), 1);
	pastdata["AVGd"][1].Add(MathAbs(gbpavdl), 1);
	pastdata["AVGd"][2].Add("AUD");
	pastdata["AVGd"][2].Add(MathAbs(audavdh), 1);
	pastdata["AVGd"][2].Add(MathAbs(audavdl), 1);
	pastdata["AVGd"][3].Add("NZD");
	pastdata["AVGd"][3].Add(MathAbs(nzdavdh), 1);
	pastdata["AVGd"][3].Add(MathAbs(nzdavdl), 1);
	pastdata["AVGd"][4].Add("CAD");
	pastdata["AVGd"][4].Add(MathAbs(cadavdh), 1);
	pastdata["AVGd"][4].Add(MathAbs(cadavdl), 1);
	pastdata["AVGd"][5].Add("USD");
	pastdata["AVGd"][5].Add(MathAbs(usdavdh), 1);
	pastdata["AVGd"][5].Add(MathAbs(usdavdl), 1);
	pastdata["AVGd"][6].Add("CHF");
	pastdata["AVGd"][6].Add(MathAbs(chfavdh), 1);
	pastdata["AVGd"][6].Add(MathAbs(chfavdl), 1);
	pastdata["AVGd"][7].Add("JPY");
	pastdata["AVGd"][7].Add(MathAbs(jpyavdh), 1);
	pastdata["AVGd"][7].Add(MathAbs(jpyavdl), 1);

	double eu5C[], eg5C[], ea5C[], en5C[], ec5C[], ef5C[], ej5C[];
	double gu5C[], ga5C[], gn5C[], gc5C[], gf5C[], gj5C[];
	double au5C[], an5C[], ac5C[], af5C[], aj5C[];
	double nu5C[], nc5C[], nf5C[], nj5C[];
	double uc5C[], cf5C[], cj5C[];
	double uf5C[], fj5C[];
	double uj5C[];

	//Print(Bars(_Symbol, PERIOD_D1));

	ArrayResize(eu5C, 33);
	CopyClose(eu_p, PERIOD_D1, 0, 33, eu5C);
	ArraySetAsSeries(eu5C, true);

	ArrayResize(eg5C, 33);
	CopyClose(eg_p, PERIOD_D1, 0, 33, eg5C);
	ArraySetAsSeries(eg5C, true);

	ArrayResize(ea5C, 33);
	CopyClose(ea_p, PERIOD_D1, 0, 33, ea5C);
	ArraySetAsSeries(ea5C, true);

	ArrayResize(en5C, 33);
	CopyClose(en_p, PERIOD_D1, 0, 33, en5C);
	ArraySetAsSeries(en5C, true);

	ArrayResize(ec5C, 33);
	CopyClose(ec_p, PERIOD_D1, 0, 33, ec5C);
	ArraySetAsSeries(ec5C, true);

	ArrayResize(ef5C, 33);
	CopyClose(ef_p, PERIOD_D1, 0, 33, ef5C);
	ArraySetAsSeries(ef5C, true);

	ArrayResize(ej5C, 33);
	CopyClose(ej_p, PERIOD_D1, 0, 33, ej5C);
	ArraySetAsSeries(ej5C, true);

	ArrayResize(gu5C, 33);
	CopyClose(gu_p, PERIOD_D1, 0, 33, gu5C);
	ArraySetAsSeries(gu5C, true);

	ArrayResize(ga5C, 33);
	CopyClose(ga_p, PERIOD_D1, 0, 33, ga5C);
	ArraySetAsSeries(ga5C, true);

	ArrayResize(gn5C, 33);
	CopyClose(gn_p, PERIOD_D1, 0, 33, gn5C);
	ArraySetAsSeries(gn5C, true);

	ArrayResize(gc5C, 33);
	CopyClose(gc_p, PERIOD_D1, 0, 33, gc5C);
	ArraySetAsSeries(gc5C, true);

	ArrayResize(gf5C, 33);
	CopyClose(gf_p, PERIOD_D1, 0, 33, gf5C);
	ArraySetAsSeries(gf5C, true);

	ArrayResize(gj5C, 33);
	CopyClose(gj_p, PERIOD_D1, 0, 33, gj5C);
	ArraySetAsSeries(gj5C, true);

	ArrayResize(au5C, 33);
	CopyClose(au_p, PERIOD_D1, 0, 33, au5C);
	ArraySetAsSeries(au5C, true);

	ArrayResize(an5C, 33);
	CopyClose(an_p, PERIOD_D1, 0, 33, an5C);
	ArraySetAsSeries(an5C, true);

	ArrayResize(ac5C, 33);
	CopyClose(ac_p, PERIOD_D1, 0, 33, ac5C);
	ArraySetAsSeries(ac5C, true);

	ArrayResize(af5C, 33);
	CopyClose(af_p, PERIOD_D1, 0, 33, af5C);
	ArraySetAsSeries(af5C, true);

	ArrayResize(aj5C, 33);
	CopyClose(aj_p, PERIOD_D1, 0, 33, aj5C);
	ArraySetAsSeries(aj5C, true);

	ArrayResize(nu5C, 33);
	CopyClose(nu_p, PERIOD_D1, 0, 33, nu5C);
	ArraySetAsSeries(nu5C, true);

	ArrayResize(nc5C, 33);
	CopyClose(nc_p, PERIOD_D1, 0, 33, nc5C);
	ArraySetAsSeries(nc5C, true);

	ArrayResize(nf5C, 33);
	CopyClose(nf_p, PERIOD_D1, 0, 33, nf5C);
	ArraySetAsSeries(nf5C, true);

	ArrayResize(nj5C, 33);
	CopyClose(nj_p, PERIOD_D1, 0, 33, nj5C);
	ArraySetAsSeries(nj5C, true);

	ArrayResize(uc5C, 33);
	CopyClose(uc_p, PERIOD_D1, 0, 33, uc5C);
	ArraySetAsSeries(uc5C, true);

	ArrayResize(cf5C, 33);
	CopyClose(cf_p, PERIOD_D1, 0, 33, cf5C);
	ArraySetAsSeries(cf5C, true);

	ArrayResize(cj5C, 33);
	CopyClose(cj_p, PERIOD_D1, 0, 33, cj5C);
	ArraySetAsSeries(cj5C, true);

	ArrayResize(uf5C, 33);
	CopyClose(uf_p, PERIOD_D1, 0, 33, uf5C);
	ArraySetAsSeries(uf5C, true);

	ArrayResize(fj5C, 33);
	CopyClose(fj_p, PERIOD_D1, 0, 33, fj5C);
	ArraySetAsSeries(fj5C, true);

	ArrayResize(uj5C, 33);
	CopyClose(uj_p, PERIOD_D1, 0, 33, uj5C);
	ArraySetAsSeries(uj5C, true);

	//Daily / Weekly / Monthly % moves from current close 5C[1] = current day close, WC[1] = previous week close, M.GetCloseValue(1) = previous week close - FOR PAIRS
	DWM dwmoa[];

	double dwmo[];
	ArrayResize(dwmo, 33);
	ArrayInitialize(dwmo, 0);

	for (int x = 30; x >= 1; x--)
	{
		//0. EU
		AddToDWM(dwmoa, ((eu5C[x] - eu5C[x + 1]) / eu5C[x + 1]) * 100, eu_p);
		//1. EG
		AddToDWM(dwmoa, ((eg5C[x] - eg5C[x + 1]) / eg5C[x + 1]) * 100, eg_p);
		//2. EA
		AddToDWM(dwmoa, ((ea5C[x] - ea5C[x + 1]) / ea5C[x + 1]) * 100, ea_p);
		//3. EN
		AddToDWM(dwmoa, ((en5C[x] - en5C[x + 1]) / en5C[x + 1]) * 100, en_p);
		//4. EC
		AddToDWM(dwmoa, ((ec5C[x] - ec5C[x + 1]) / ec5C[x + 1]) * 100, ec_p);
		//5. EF
		AddToDWM(dwmoa, ((ef5C[x] - ef5C[x + 1]) / ef5C[x + 1]) * 100, ef_p);
		//6. EJ
		AddToDWM(dwmoa, ((ej5C[x] - ej5C[x + 1]) / ej5C[x + 1]) * 100, ej_p);
		//7. GU
		AddToDWM(dwmoa, ((gu5C[x] - gu5C[x + 1]) / gu5C[x + 1]) * 100, gu_p);
		//8. GA
		AddToDWM(dwmoa, ((ga5C[x] - ga5C[x + 1]) / ga5C[x + 1]) * 100, ga_p);
		//9. GN
		AddToDWM(dwmoa, ((gn5C[x] - gn5C[x + 1]) / gn5C[x + 1]) * 100, gn_p);
		//10. GC
		AddToDWM(dwmoa, ((gc5C[x] - gc5C[x + 1]) / gc5C[x + 1]) * 100, gc_p);
		//11. GF
		AddToDWM(dwmoa, ((gf5C[x] - gf5C[x + 1]) / gf5C[x + 1]) * 100, gf_p);
		//12. GJ
		AddToDWM(dwmoa, ((gj5C[x] - gj5C[x + 1]) / gj5C[x + 1]) * 100, gj_p);
		//13. AU
		AddToDWM(dwmoa, ((au5C[x] - au5C[x + 1]) / au5C[x + 1]) * 100, au_p);
		//14. AN
		AddToDWM(dwmoa, ((an5C[x] - an5C[x + 1]) / an5C[x + 1]) * 100, an_p);
		//15. AC
		AddToDWM(dwmoa, ((ac5C[x] - ac5C[x + 1]) / ac5C[x + 1]) * 100, ac_p);
		//16. AF
		AddToDWM(dwmoa, ((af5C[x] - af5C[x + 1]) / af5C[x + 1]) * 100, af_p);
		//17. AJ
		AddToDWM(dwmoa, ((aj5C[x] - aj5C[x + 1]) / aj5C[x + 1]) * 100, aj_p);
		//18. NU
		AddToDWM(dwmoa, ((nu5C[x] - nu5C[x + 1]) / nu5C[x + 1]) * 100, nu_p);
		//19. NC
		AddToDWM(dwmoa, ((nc5C[x] - nc5C[x + 1]) / nc5C[x + 1]) * 100, nc_p);
		//20. NF
		AddToDWM(dwmoa, ((nf5C[x] - nf5C[x + 1]) / nf5C[x + 1]) * 100, nf_p);
		//21. NJ
		AddToDWM(dwmoa, ((nj5C[x] - nj5C[x + 1]) / nj5C[x + 1]) * 100, nj_p);
		//22. UC
		AddToDWM(dwmoa, ((uc5C[x] - uc5C[x + 1]) / uc5C[x + 1]) * 100, uc_p);
		//23. CF
		AddToDWM(dwmoa, ((cf5C[x] - cf5C[x + 1]) / cf5C[x + 1]) * 100, cf_p);
		//24. CJ
		AddToDWM(dwmoa, ((cj5C[x] - cj5C[x + 1]) / cj5C[x + 1]) * 100, cj_p);
		//25. UF
		AddToDWM(dwmoa, ((uf5C[x] - uf5C[x + 1]) / uf5C[x + 1]) * 100, uf_p);
		//26. FJ
		AddToDWM(dwmoa, ((fj5C[x] - fj5C[x + 1]) / fj5C[x + 1]) * 100, fj_p);
		//27. UJ
		AddToDWM(dwmoa, ((uj5C[x] - uj5C[x + 1]) / uj5C[x + 1]) * 100, uj_p);

		for (int y = 27; y >= 0; y--)
		{
			dwmo[x] += MathAbs(dwmoa[y].daily);
		}
		ArrayFree(dwmoa);
	}

	Rstr = totR(0);

	double R1str[];
	ArrayResize(R1str, 33);
	for (int x = 30; x >= 1; x--)
	{
		R1str[x] = totR(x);
	}

	double R2a[];
	ArrayResize(R2a, 33);

	for (int x = 30; x >= 1; x--)
	{
		R2a[x] = (28 * dwmo[x]) / (28 * R1str[x] * 28);
	}

	/*
	for (int x = 371; x >= 1; x--)
	{
	   if (x == 141) Print("150 - 141: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 131) Print("140 - 131: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 121) Print("130 - 121: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 111) Print("120 - 111: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 101) Print("110 - 101: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 91) Print("100 - 91: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 81) Print("90 - 81: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 71) Print("80 - 71: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 61) Print("70 - 61: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 51) Print("60 - 51: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 41) Print("50 - 41: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 31) Print("40 - 31: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 21) Print("30 - 21: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 11) Print("20 - 11: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	   if (x == 1) Print("10 - 1: " + DoubleToString(R2a[x + 9], 2) + "   "  + DoubleToString(R2a[x + 8], 2) + "   " + DoubleToString(R2a[x + 7], 2) + "   " + DoubleToString(R2a[x + 6], 2) + "   " + DoubleToString(R2a[x + 5], 2) + "   " + DoubleToString(R2a[x + 4], 2) + "   " + DoubleToString(R2a[x + 3], 2) + "   " + DoubleToString(R2a[x + 2], 2) + "   " + DoubleToString(R2a[x + 1], 2) + "   " + DoubleToString(R2a[x], 2));
	}
	*/

	files2(R2a);

	R2a1 = R2a[1];
	R2a2 = R2a[2];
	R2a3 = R2a[3];
	R2a4 = R2a[4];
	R2a5 = R2a[5];
	R2a6 = R2a[6];
	R2a7 = R2a[7];
	R2a8 = R2a[8];
	R2a9 = R2a[9];
	R2a10 = R2a[10];
	R2b1 = (R2a1 + R2a2 + R2a3) / 3;
	R2b2 = (R2a1 + R2a2 + R2a3 + R2a4 + R2a5) / 5;
	R2b3 = (R2a1 + R2a2 + R2a3 + R2a4 + R2a5 + R2a6 + R2a7) / 7;
	R2b4 = (R2a1 + R2a2 + R2a3 + R2a4 + R2a5 + R2a6 + R2a7 + R2a8 + R2a9 + R2a10) / 10;
}
//+------------------------------------------------------------------+

void files2(double& passarray[])
{
	int count = ArraySize(passarray);
	//Print (count);

	double filee[];
	ArrayResize(filee, count + 1);

	for (int i = count - 1; i >= 0; i--)
	{
		filee[count - i] = passarray[i];
	}

	{
		file2(filee);
	}
}
void file2(double& file1[])
{
	string fileName = TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE) + " HIST " + IntegerToString(Hour()) + " " + Name + ".csv";
	int fileHandle = FileOpen(fileName, FILE_WRITE | FILE_CSV);

	FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE | TIME_MINUTES));

	if (fileHandle != INVALID_HANDLE)
	{
		for (int i = 0; i <= ArraySize(file1) - 1; i++)
		{
			FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_D1, ArraySize(file1) - i - 1), TIME_DATE), DoubleToString(file1[i], 2));
		}

		FileClose(fileHandle);
		Print("Data written to ", fileName);
	}
	else
	{
		Print("Failed to open the file!");
	}
}