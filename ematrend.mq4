//+------------------------------------------------------------------+
//|                                                   EMA Score.mq4  |
//|                        Generated by ChatGPT                       |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.03"
#property strict
#property indicator_separate_window
#property indicator_buffers 9
#property indicator_color1 DodgerBlue    // Positive histogram
#property indicator_color2 OrangeRed     // Negative histogram
#property indicator_color3 Green    // Positive histogram
#property indicator_color4 Magenta     // Negative histogram
#property indicator_width1 2       // Bar width for positive
#property indicator_width2 2       // Bar width for negative
#property indicator_width3 2       // Bar width for positive
#property indicator_width4 2       // Bar width for negative

// Indicator buffers
double PosBuffer[];
double NegBuffer[];
double OppBuffera[];
double OppBufferb[];
double black[], blue[], green[], orange[], red[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int init()
{
   IndicatorBuffers(9);
   IndicatorShortName("ET");
   // Positive score buffer
   SetIndexStyle(0, DRAW_HISTOGRAM, STYLE_SOLID, 2);
   SetIndexBuffer(0, PosBuffer);
   SetIndexLabel(0, "EMA+ Score");
   
   // Negative score buffer
   SetIndexStyle(1, DRAW_HISTOGRAM, STYLE_SOLID, 2);
   SetIndexBuffer(1, NegBuffer);
   SetIndexLabel(1, "EMA- Score");
   
   // Negative score buffer
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexBuffer(2, OppBuffera);
   SetIndexLabel(2, "EMA- Score");
   
   // Negative score buffer
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexBuffer(3, OppBufferb);
   SetIndexLabel(3, "EMA- Score");
   
   SetIndexStyle(4, DRAW_NONE);
   SetIndexBuffer(4, black);
   SetIndexStyle(5, DRAW_NONE);
   SetIndexBuffer(5, blue);
   SetIndexEmptyValue(5, 0.0);
   SetIndexStyle(6, DRAW_NONE);
   SetIndexBuffer(6, green);
   SetIndexEmptyValue(6, 0.0);
   SetIndexStyle(7, DRAW_NONE);
   SetIndexBuffer(7, orange);
   SetIndexEmptyValue(7, 0.0);
   SetIndexStyle(8, DRAW_NONE);
   SetIndexBuffer(8, red);
   SetIndexEmptyValue(8, 0.0);
   
   return(0);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
int deinit()
{
   ObjectsDeleteAll(0, "ETArr");
   return(0);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate (const int rates_total,
                 const int prev_calculated,
                 const datetime& time[],
                 const double& open[],
                 const double& high[],
                 const double& low[],
                 const double& close[],
                 const long& tick_volume[],
                 const long& volume[],
                 const int& spread[])
  {
   int totalBars = Bars;

   double hup[];
   ArrayResize(hup, Bars);
   double ldn[];
   ArrayResize(ldn, Bars);
   double up[];
   ArrayResize(up, Bars);
   double dn[];
   ArrayResize(dn, Bars);
   double md[];
   ArrayResize(md, Bars);
   
   static datetime shift = 0;
   bool checkshift = false;
   if (shift < iTime(_Symbol, PERIOD_CURRENT, 0))
   {
      shift = iTime(_Symbol, PERIOD_CURRENT, 0);
      checkshift = true;
   }
   if (checkshift)
   {
      // Loop through bars from oldest to newest
      for(int i = totalBars - 1; i >= 0; i--)
      {
         double ema200 = iMA(NULL, 0, 200, 0, MODE_EMA, PRICE_CLOSE, i);
         double ema20  = iMA(NULL, 0, 20,  0, MODE_EMA, PRICE_CLOSE, i);
         double ema50  = iMA(NULL, 0, 50,  0, MODE_EMA, PRICE_CLOSE, i);
   
         // condition 1: uptrend alignment
         if(ema20 > ema50 && ema50 > ema200)
           {
            OppBuffera[i] = 0;
           }
         // condition 2: downtrend alignment
         else if(ema20 < ema50 && ema50 < ema200)
           {
            OppBufferb[i] = 0;
           }
         
         int prevPos = 0;
         int prevNeg = 0;
         if(i < totalBars - 1)
         {
            prevPos = (int)PosBuffer[i+1];
            prevNeg = -(int)NegBuffer[i+1]; // convert negative buffer back to positive count
         }
   
         int posScore = 0;
         int negScore = 0;
         
         // Positive side: price above EMA200
         if(open[i] > ema200)
         {
            posScore = prevPos;
            if(open[i] > ema20)
               posScore++;
            else if(low[i] < ema20)
               posScore = 0;
         }
         
         // Negative side: price below EMA200
         if(open[i] < ema200)
         {
            negScore = prevNeg;
            if(open[i] < ema20)
               negScore++;
            else if(high[i] > ema20)
               negScore = 0;
         }
   
         // Assign buffers: positive always >=0, negative <=0
         PosBuffer[i] = posScore;
         NegBuffer[i] = -negScore;
      }
      
      // iterate through bars
      for(int i = 521; i >= 0; i--)
      {
         up[i] = high[iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, i + 6)];
         dn[i] = low[iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, i + 6)];
         double dist = up[i] - dn[i];
         hup[i] = up[i] - 0.236 * dist;
         ldn[i] = dn[i] + 0.236 * dist; 
         md[i] = dn[i] + dist/2;
         //black[1000] = 0.0;
         if (open[i] > md[i]) black[i] = black[i + 1] + 0.001;
         else if (open[i] < md[i]) black[i] = black[i + 1] - 0.001;
      }
      
      for(int i = 500; i >= 0; i--)
      {
         //squawk[i] = iMAOnArray(black, 0, 6, 0, MODE_LWMA, i);// + iStochastic(_Symbol, PERIOD_CURRENT, 8, 5, 3, MODE_EMA, 0, MODE_MAIN, i)/1000;
         if (open[i] > md[i]) blue[i] = black[i];
         else if (open[i] > hup[i]) green[i] = black[i];
         else if (open[i] < md[i]) red[i] = black[i];
         else orange[i] = black[i];
               
         if ((red[i + 2] != 0 || red[i + 1] != 0 || orange[i + 2] != 0 || orange[i + 1] != 0) && (blue[i] != 0 || green[i] != 0) && NegBuffer[i] == 0)
         {
            string obname = "ETArrD" + IntegerToString(i);
            burnarr(obname, low[i] - 50 * _Point, 108, i, clrAliceBlue);
            if (i == 0) 
            {
               Alert("Shift up in " + _Symbol);
               SendNotification("Shift up in " + _Symbol);
            }
         }  
         if ((blue[i + 2] != 0 || blue[i + 1] != 0 || green[i + 2] != 0 || green[i + 1] != 0) && (red[i] != 0 || orange[i] != 0) && PosBuffer[i] == 0)
         {
            string obname = "ETArrU" + IntegerToString(i);
            burnarr(obname, high[i] + 50 * _Point, 108, i, clrPink);
            if (i == 0) 
            {
               Alert("Shift down in " + _Symbol);
               SendNotification("Shift down in " + _Symbol);
            }
         }
      }
      checkshift = false;
   }
   
   return(0);
}

//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 0);
}
//+------------------------------------------------------------------+