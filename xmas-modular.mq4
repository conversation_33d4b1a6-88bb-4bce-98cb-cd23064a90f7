//+------------------------------------------------------------------+
//|                                                  xmas-modular.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
//--- input parameters
input int      DayStart = 10;

#define Name WindowExpertName()

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M5, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M5, 0);
		}
		if (new_1m_check)
		{
			//if (ChartPeriod() <= 5) 
			{ // || ChartPeriod() <= 5) { // Only run from M1 to H4
            trap(DayStart);
			}
			new_1m_check = false;
		}
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Main trap function - now modular for any day count              |
//+------------------------------------------------------------------+
void trap(int pico){
   int startt = pico;
   if (pico < 1) startt = 1;
   
   double begin = iOpen(_Symbol, PERIOD_D1, startt);
   double startadr = adr(21, startt + 21);
   
   // Clear existing objects first
   DeleteObjects();
   
   // Draw using modular function based on day 10 pattern
   DrawDayPattern(startt, begin, startadr);
}

//+------------------------------------------------------------------+
//| Modular function to draw day patterns based on day 10 structure |
//+------------------------------------------------------------------+
void DrawDayPattern(int startt, double begin, double startadr)
{
   // Calculate S3 and S4 like in day 10
   double S3 = MathSqrt(startt * 0.5 * 0.5) * startadr;
   double S4 = 2 * MathSqrt(startt * 0.5 * 0.5) * startadr;
   
   // Base prices and levels
   double P1 = begin;
   double H1 = begin + startadr;
   double L1 = begin - startadr;
   
   // Draw main base lines (P1, H1, L1) - these are the core lines from day 10
   DrawBasicLines(P1, H1, L1, S3, S4, startt);
   
   // Draw expanding pattern levels based on startt
   DrawExpandingLevels(begin, startadr, startt);
}

//+------------------------------------------------------------------+
//| Draw basic lines - main center line and first high/low levels   |
//+------------------------------------------------------------------+
void DrawBasicLines(double P1, double H1, double L1, double S3, double S4, int startt)
{
   string obname;
   
   // Main center line (P1) - like day 10 "f11"
   obname = Name + "f11";
   objtrend3(obname, P1, P1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 2, 0, clrMagenta, "");
   
   // Diagonal from H1 to L1 - like day 10 "f12"
   obname = Name + "f12";
   objtrend3(obname, H1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1, 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1, 2, 0, clrIndigo, "");
   
   // High level horizontal line - like day 10 "fh11"
   obname = Name + "fh11";
   objtrend3(obname, H1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrCyan, "");
   
   // Low level horizontal line - like day 10 "fl11"
   obname = Name + "fl11";
   objtrend3(obname, L1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrCyan, "");
   
   // Diagonal from P1 to H1 - like day 10 "fh1"
   obname = Name + "fh1";
   objtrend3(obname, P1, H1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
   
   // Diagonal from P1 to L1 - like day 10 "fl1"
   obname = Name + "fl1";
   objtrend3(obname, P1, L1, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - 1), false), 1, 0, clrYellow, "");
   
   // S3 and S4 levels (like day 10 structure)
   obname = Name + "fhs1";
   objtrend3(obname, P1 + S3, P1 + S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 3, 0, clrWhite, "");
   
   obname = Name + "fls1";
   objtrend3(obname, P1 - S3, P1 - S3, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 3, 0, clrWhite, "");
   
   obname = Name + "fhs2";
   objtrend3(obname, P1 + S4, P1 + S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 3, 0, clrWhite, "");
   
   obname = Name + "fls2";
   objtrend3(obname, P1 - S4, P1 - S4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt), false), 
             iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 3, 0, clrWhite, "");
}

//+------------------------------------------------------------------+
//| Draw expanding levels pattern based on day count                |
//+------------------------------------------------------------------+
void DrawExpandingLevels(double begin, double startadr, int startt)
{
   // Draw level groups following day 10's exact pattern
   for(int levelGroup = 2; levelGroup <= startt + 1; levelGroup++) // P2 through P11 for day 10
   {
      DrawComplexLevelGroup(begin, startadr, startt, levelGroup);
   }
}

//+------------------------------------------------------------------+
//| Draw complete level group following day 10's exact pattern      |
//+------------------------------------------------------------------+
void DrawComplexLevelGroup(double begin, double startadr, int startt, int levelGroup)
{
   string obname;
   double P_value;
   
   // Calculate P value exactly like day 10
   if(levelGroup % 2 == 0) // P2, P4, P6, P8, P10 = startadr/2
      P_value = startadr / 2;
   else // P3, P5, P7, P9, P11 = startadr
      P_value = startadr;
   
   // Draw horizontal mid-level lines (like fh21, fl21, fh31, fl31)
   if(levelGroup >= 2)
   {
      int timeOffset = (levelGroup == 2) ? startt - 1 : startt - levelGroup + 1;
      int timeOffset2 = (levelGroup <= 3) ? startt - levelGroup : startt - levelGroup + 1;
      
      obname = Name + "fh" + IntegerToString(levelGroup) + "1";
      objtrend3(obname, begin + P_value, begin + P_value, 
                iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, timeOffset), false), 
                iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrLime, "");
      
      obname = Name + "fl" + IntegerToString(levelGroup) + "1";
      objtrend3(obname, begin - P_value, begin - P_value, 
                iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, timeOffset), false), 
                iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 1, 0, clrLime, "");
   }
   
   // Draw all the detailed sub-levels based on levelGroup
   DrawDetailedSubLevels(begin, startadr, startt, levelGroup, P_value);
   
   // Draw top/bottom horizontal levels for the highest levels
   if(levelGroup >= startt)
   {
      DrawTopBottomLevels(begin, startadr, startt, levelGroup, P_value);
   }
}

//+------------------------------------------------------------------+
//| Draw detailed sub-levels matching day 10's exact pattern       |
//+------------------------------------------------------------------+
void DrawDetailedSubLevels(double begin, double startadr, int startt, int levelGroup, double P_value)
{
   string obname;
   
   // Calculate time indices exactly like day 10
   int timeFrom = startt - levelGroup + 1;
   int timeTo = startt - levelGroup;
   if(levelGroup == 2) {
      timeFrom = startt - 1;
      timeTo = startt - 2;
   }
   
   // Convert time indices to bar shifts
   int barFrom = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, timeFrom), false);
   int barTo = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, timeTo), false);
   if(levelGroup == 2) // P2 pattern
   {      // fh2a: begin + P2
      obname = Name + "fh2a";
      objtrend3(obname, begin + P_value, begin + P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl2a";
      objtrend3(obname, begin + P_value, begin + P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh2b: begin - P2
      obname = Name + "fh2b";
      objtrend3(obname, begin - P_value, begin - P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl2b";
      objtrend3(obname, begin - P_value, begin - P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
   }   else if(levelGroup == 3) // P3 pattern
   {
      // fh3a: begin + P3
      obname = Name + "fh3a";
      objtrend3(obname, begin + P_value, begin + P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl3a";
      objtrend3(obname, begin + P_value, begin + P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh3b: begin
      obname = Name + "fh3b";
      objtrend3(obname, begin, begin + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl3b";
      objtrend3(obname, begin, begin - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh3c: begin - P3
      obname = Name + "fh3c";
      objtrend3(obname, begin - P_value, begin - P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl3c";
      objtrend3(obname, begin - P_value, begin - P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
   }   else if(levelGroup == 4) // P4 pattern
   {
      // fh4a: begin + P4 + startadr
      obname = Name + "fh4a";
      objtrend3(obname, begin + P_value + startadr, begin + P_value + startadr + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl4a";
      objtrend3(obname, begin + P_value + startadr, begin + P_value + startadr - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh4b: begin + P4
      obname = Name + "fh4b";
      objtrend3(obname, begin + P_value, begin + P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl4b";
      objtrend3(obname, begin + P_value, begin + P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh4c: begin - P4
      obname = Name + "fh4c";
      objtrend3(obname, begin - P_value, begin - P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl4c";
      objtrend3(obname, begin - P_value, begin - P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh4d: begin - P4 - startadr
      obname = Name + "fh4d";
      objtrend3(obname, begin - P_value - startadr, begin - P_value - startadr + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl4d";
      objtrend3(obname, begin - P_value - startadr, begin - P_value - startadr - startadr, barFrom, barTo, 1, 0, 0x003333, "");
   }   else if(levelGroup == 5) // P5 pattern  
   {
      // fh5a: begin + P5 + startadr
      obname = Name + "fh5a";
      objtrend3(obname, begin + P_value + startadr, begin + P_value + startadr + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl5a";
      objtrend3(obname, begin + P_value + startadr, begin + P_value + startadr - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh5b: begin + P5
      obname = Name + "fh5b";
      objtrend3(obname, begin + P_value, begin + P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl5b";
      objtrend3(obname, begin + P_value, begin + P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh5c: begin
      obname = Name + "fh5c";
      objtrend3(obname, begin, begin + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl5c";
      objtrend3(obname, begin, begin - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh5d: begin - P5
      obname = Name + "fh5d";
      objtrend3(obname, begin - P_value, begin - P_value + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl5d";
      objtrend3(obname, begin - P_value, begin - P_value - startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      // fh5e: begin - P5 - startadr
      obname = Name + "fh5e";
      objtrend3(obname, begin - P_value - startadr, begin - P_value - startadr + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      obname = Name + "fl5e";
      objtrend3(obname, begin - P_value - startadr, begin - P_value - startadr - startadr, barFrom, barTo, 1, 0, 0x003333, "");
   }
   else // For levels 6 and above, use the expanding pattern
   {
      DrawExpandingSubLevels(begin, startadr, levelGroup, P_value, barFrom, barTo);
   }
}

//+------------------------------------------------------------------+
//| Draw expanding sub-levels for levels 6 and above               |
//+------------------------------------------------------------------+
void DrawExpandingSubLevels(double begin, double startadr, int levelGroup, double P_value, int barFrom, int barTo)
{
   string obname;
   string subLetters[] = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k"};
   
   // Based on day 10 pattern for levels 6-11
   int numSubLevels = levelGroup;
   
   for(int sub = 0; sub < numSubLevels; sub++)
   {
      double offset = 0;
      
      // Calculate offset based on the expanding pattern from day 10
      if(sub == 0) {
         offset = P_value + (levelGroup - 3) * startadr;
      }
      else if(sub < numSubLevels/2) {
         offset = P_value + (levelGroup - 3 - sub) * startadr;
      }
      else if(sub == numSubLevels/2 && levelGroup % 2 == 1) {
         offset = 0; // Center level for odd numbered groups
      }
      else {
         int mirrorSub = (levelGroup % 2 == 0) ? sub - numSubLevels/2 : sub - numSubLevels/2 - 1;
         offset = -P_value - mirrorSub * startadr;
      }
      
      // Draw high and low levels
      obname = Name + "fh" + IntegerToString(levelGroup) + subLetters[sub];
      objtrend3(obname, begin + offset, begin + offset + startadr, barFrom, barTo, 1, 0, 0x003333, "");
      
      obname = Name + "fl" + IntegerToString(levelGroup) + subLetters[sub];
      objtrend3(obname, begin + offset, begin + offset - startadr, barFrom, barTo, 1, 0, 0x003333, "");
   }
}

//+------------------------------------------------------------------+
//| Custom indicator de-init function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}

//+------------------------------------------------------------------+
//| Delete all objects created by this indicator                    |
//+------------------------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}

//+------------------------------------------------------------------+
//| Create trend line objects                                       |
//+------------------------------------------------------------------+
void objtrend3(string name, double pr1, double pr2, int t1, int t2, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2]);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}

//+------------------------------------------------------------------+
//| Get uninit reason text                                          |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode)
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}

//+------------------------------------------------------------------+
//| Calculate Average Daily Range                                   |
//+------------------------------------------------------------------+
double adr(const int X, const int Y){
   
   double HD[], LD[];
   ArrayResize(HD, Y + 1);
   ArrayResize(LD, Y + 1);
   CopyHigh(_Symbol, PERIOD_D1, 1, Y + 1, HD);
   CopyLow(_Symbol, PERIOD_D1, 1, Y + 1, LD);
   double adra = 0;
   
   for (int x = Y; x > Y - X; x--){
      adra += (HD[x] - LD[x]);
   }
   
   double adri = 0;
   adri = adra / X;
   
   ArrayFree(HD);
   ArrayFree(LD);
   return(adri);
}

//+------------------------------------------------------------------+
//| Draw top/bottom horizontal projection levels for highest groups |
//+------------------------------------------------------------------+
void DrawTopBottomLevels(double begin, double startadr, int startt, int levelGroup, double P_value)
{
   string obname;
   string Name = _Symbol + "xmas";
   
   // For the highest level groups (9+), draw projection lines extending to current time
   if(levelGroup >= 9)
   {
      // Draw the expanding pattern for the top level group
      int numLevels = 2 * levelGroup - 1;
        for(int i = 0; i < numLevels; i++)
      {
         double levelOffset;
         
         // Calculate level offset based on position in the expanding pattern
         if(i == 0) 
         {
            levelOffset = P_value + (levelGroup - 6) * startadr;
         }
         else if(i == numLevels - 1)
         {
            levelOffset = -P_value - (levelGroup - 6) * startadr;
         }
         else if(i < numLevels / 2)
         {
            levelOffset = P_value + (levelGroup - 6 - i) * startadr;
         }
         else if(i == numLevels / 2)
         {
            levelOffset = 0;
         }
         else
         {
            levelOffset = -P_value - (levelGroup - 6 - (numLevels - 1 - i)) * startadr;
         }
         
         // Calculate the corresponding high and low values dynamically
         double highValue = begin + levelOffset + startadr;
         double lowValue = begin + levelOffset - startadr;
           // Draw high projection line
         obname = Name + "fh" + IntegerToString(levelGroup) + CharToString('a' + i);
         objtrend3(obname, begin + levelOffset, 
                  highValue, 
                  iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - (levelGroup - 1)), false), 
                  iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 
                  1, 0, 0x003333, "");
         
         // Draw low projection line
         obname = Name + "fl" + IntegerToString(levelGroup) + CharToString('a' + i);
         objtrend3(obname, begin + levelOffset, 
                  lowValue, 
                  iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, startt - (levelGroup - 1)), false), 
                  iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false), 
                  1, 0, 0x003333, "");
      }
   }
}
//+------------------------------------------------------------------+
