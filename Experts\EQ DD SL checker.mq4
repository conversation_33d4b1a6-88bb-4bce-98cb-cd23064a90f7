//+------------------------------------------------------------------+
//|                                                         test.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2019, sakisf."
#property link      "https://www.forexfactory.com/sakisf"
#property version   "1.03"
#property strict
#define Name WindowExpertName()

input int CheckSeconds = 1; //Check every X seconds
input double maxddr = 5; //Maximum DD allowed in %
input bool tradeenable = false; //Allow automatic position closing

double equityhigh;
double equitylow;
double openequity;
double meqh, meql, meqb;
bool alerts = false;

bool addnt;

int p = 1600;
int o = 200;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
	//--- set GVs	
	if (GlobalVariableCheck("eqhigh") == false) GlobalVariableSet("eqhigh", AccountEquity());
	equityhigh = GlobalVariableGet("eqhigh");
	if (GlobalVariableCheck("eqlow") == false) GlobalVariableSet("eqlow", AccountEquity());
	equitylow = GlobalVariableGet("eqlow");
	if (GlobalVariableCheck("openeq") == false) GlobalVariableSet("openeq", AccountBalance());
	openequity = GlobalVariableGet("openeq");
	if (GlobalVariableCheck("meqh") == false) GlobalVariableSet("meqh", equityhigh);
	meqh = GlobalVariableGet("meqh");
	if (GlobalVariableCheck("meql") == false) GlobalVariableSet("meql", equitylow);
	meql = GlobalVariableGet("meql");
	if (GlobalVariableCheck("meqb") == false) GlobalVariableSet("meqb", openequity);
	meqb = GlobalVariableGet("meqb");
	if (GlobalVariableCheck("EQp" + _Symbol) == false) GlobalVariableSet("EQp" + _Symbol, p); else p = (int)GlobalVariableGet("EQp" + _Symbol);
	if (GlobalVariableCheck("EQo" + _Symbol) == false) GlobalVariableSet("EQo" + _Symbol, o); else o = (int)GlobalVariableGet("EQo" + _Symbol);
	if (GlobalVariableCheck("EQal" + _Symbol) == false) GlobalVariableSet("EQal" + _Symbol, 1);
	alerts = GlobalVariableGet("EQal" + _Symbol);
	
	reclabloc();
	staticbuilds();

	//--- create timer
	EventSetTimer(60);
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	//--- destroy timer
	EventKillTimer();
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
	bool new_check = false;
	static datetime recheck_time = 0;
	if (TimeCurrent() >= recheck_time + (CheckSeconds))
	{
		new_check = true;
		recheck_time = TimeCurrent();
	}
	if (new_check)
	{
		if (OrdersTotal() == 0) { equityhigh = AccountBalance(); GlobalVariableSet("eqlow", equityhigh); equitylow = AccountBalance(); GlobalVariableSet("eqhigh", equitylow); openequity = AccountBalance(); GlobalVariableSet("openeq", openequity); }
		else if (OrdersTotal() > 0) {
			if (AccountEquity() > equityhigh)
			{
				equityhigh = AccountEquity();
				GlobalVariableSet("eqhigh", equityhigh);
			}
			if (AccountEquity() < equitylow)
			{
				equitylow = AccountEquity();
				GlobalVariableSet("eqlow", equitylow);
			}
			if (AccountEquity() > meqh)
			{
				meqh = AccountEquity();
				GlobalVariableSet("meqh", meqh);
			}
			if (AccountEquity() < meql)
			{
				meql = AccountEquity();
				GlobalVariableSet("meql", meql);
			}
			GlobalVariablesFlush();
		}
		eqshow();
		new_check = false;
	}
	
	bool new1_check = false;
	static datetime recheck1_time = 0;
	if (recheck1_time < iTime(_Symbol, PERIOD_M1, 0))
	{
		new1_check = true;
		recheck1_time = iTime(_Symbol, PERIOD_M1, 0);
	}
	if (new1_check)
	{
		tradecheck();
		new1_check = false;
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
	//---
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	{//Reset
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetChar("R", 0)) { equityhigh = AccountBalance(); GlobalVariableDel("eqhigh"); GlobalVariableSet("eqhigh", equityhigh); equitylow = AccountBalance(); GlobalVariableDel("eqlow"); GlobalVariableSet("eqlow", equitylow); GlobalVariablesFlush(); }
			if (lparam == StringGetChar("M", 0))
			{
				additions();
			}
			if (lparam == StringGetChar("N", 0)) { ObjectsDeleteAll(0, Name + "meq"); ObjectsDeleteAll(0, Name + "max"); addnt = false; }
		}
	}
	{//Move rectangle
		if (id == CHARTEVENT_OBJECT_DRAG)
		{
			if (sparam == StringConcatenate(Name + " MovRec"))
			{
				GlobalVariableSet("EQp" + _Symbol, (int)ObjectGet(Name + " MovRec", OBJPROP_XDISTANCE));
				GlobalVariableSet("EQo" + _Symbol, (int)ObjectGet(Name + " MovRec", OBJPROP_YDISTANCE));
				GlobalVariablesFlush();
				reclabloc(); staticbuilds(); if (addnt) additions();
			}
		}
	}
	{//Alerts on/off
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "numlabel0") && ObjectGet(Name + "numlabel0", OBJPROP_COLOR) == clrGreen)
			{
				alerts = false; GlobalVariableSet("EQal" + _Symbol, 0); ObjectSet(Name + "numlabel0", OBJPROP_COLOR, clrRed);
			}
			else if (sparam == StringConcatenate(Name + "numlabel0") && ObjectGet(Name + "numlabel0", OBJPROP_COLOR) == clrRed)
			{
				alerts = true; GlobalVariableSet("EQal" + _Symbol, 1); ObjectSet(Name + "numlabel0", OBJPROP_COLOR, clrGreen);
			}
		}
	}
	//---
}
//+------------------------------------------------------------------+

//+Additonal labels--------------------------------------------------+
void additions() {
	string obname;
	obname = Name + "meqh"; LabelMake(obname, 0, p + 10, o + 275, "Max Equity: " + DoubleToStr(GlobalVariableGet("meqh"), 2), 9, clrGreen);
	obname = Name + "meqb"; LabelMake(obname, 0, p + 10, o + 295, "Begin Balance: " + DoubleToStr(GlobalVariableGet("meqb"), 2), 9, clrWhite);
	obname = Name + "meql"; LabelMake(obname, 0, p + 10, o + 315, "Min Equity: " + DoubleToStr(GlobalVariableGet("meql"), 2), 9, clrDarkOrchid);

	obname = Name + "maxup"; LabelMake(obname, 0, p + 10, o + 345, "Max run-up: " + DoubleToStr(GlobalVariableGet("meqh") - GlobalVariableGet("meqb"), 2), 8, clrGreen);
	obname = Name + "maxdn"; LabelMake(obname, 0, p + 10, o + 360, "Max run-dn: " + DoubleToStr(GlobalVariableGet("meqb") - GlobalVariableGet("meql"), 2), 8, clrDarkOrchid);
	addnt = true;
}
//+------------------------------------------------------------------+

//+TEST SHOW LABELS--------------------------------------------------+
void staticbuilds() {
	string obname;
	obname = Name + "eqh"; LabelMake(obname, 0, p + 10, o + 10, "Open Equity max:", 9, clrBlue);
	obname = Name + "eqh1"; LabelMake(obname, 0, p + 115, o + 10, "", 9, clrBlue);
	obname = Name + "bal"; LabelMake(obname, 0, p + 10, o + 25, "Current Balance:", 9, clrBlack);
	obname = Name + "bal1"; LabelMake(obname, 0, p + 115, o + 25, "", 9, clrBlack);
	obname = Name + "eql"; LabelMake(obname, 0, p + 10, o + 40, "Open Equity min:", 9, clrRed);
	obname = Name + "eql1"; LabelMake(obname, 0, p + 115, o + 40, "", 9, clrRed);
	obname = Name + "eqm"; LabelMake(obname, 0, p + 10, o + 55, "Open balance:", 9, clrWhite);
	obname = Name + "eqm1"; LabelMake(obname, 0, p + 115, o + 55, "", 9, clrWhite);
	obname = Name + "mxp"; LabelMake(obname, 0, p + 10, o + 85, "Max Profit:", 9, clrBlue);
	obname = Name + "mxp1"; LabelMake(obname, 0, p + 115, o + 85, "", 9, clrBlue);
	obname = Name + "mxl"; LabelMake(obname, 0, p + 10, o + 100, "Max Loss:", 9, clrRed);
	obname = Name + "mxl1"; LabelMake(obname, 0, p + 115, o + 100, "", 9, clrRed);
	obname = Name + "mdd"; LabelMake(obname, 0, p + 10, o + 130, "Max DD all:", 9, clrRed);
	obname = Name + "mdd1"; LabelMake(obname, 0, p + 115, o + 130, "", 9, clrRed);
	obname = Name + "mnl"; LabelMake(obname, 0, p + 10, o + 145, "Max DD equity:", 9, clrRed);
	obname = Name + "mnl1"; LabelMake(obname, 0, p + 115, o + 145, "", 9, clrRed);
	obname = Name + "tpl"; LabelMake(obname, 0, p + 10, o + 175, "Total P/L:", 9, clrBlue); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); if (totalpl() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "tpl1"; LabelMake(obname, 0, p + 115, o + 175, "", 9, clrBlue); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); if (totalpl() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "dpl"; LabelMake(obname, 0, p + 10, o + 190, "Total closed:", 9, clrBlue); if (PipsCollected() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "dpl1"; LabelMake(obname, 0, p + 115, o + 190, "", 9, clrBlue); if (PipsCollected() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "dpp"; LabelMake(obname, 0, p + 10, o + 205, "Total Day:", 9, clrBlue); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); if (totalpl() + PipsCollected() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "dpp1"; LabelMake(obname, 0, p + 115, o + 205, "", 9, clrBlue); ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black"); if (totalpl() + PipsCollected() < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "pos"; LabelMake(obname, 0, p + 10, o + 225, "Positive:", 8, clrBlue);
	obname = Name + "pos1"; LabelMake(obname, 0, p + 115, o + 225, "", 8, clrBlue);
	obname = Name + "neg"; LabelMake(obname, 0, p + 10, o + 240, "Negative:", 8, clrRed);
	obname = Name + "neg1"; LabelMake(obname, 0, p + 115, o + 240, "", 8, clrRed);
	obname = Name + "bas"; LabelMake(obname, 0, p + 10, o + 255, "BasketPL:", 8, clrDarkViolet);
	obname = Name + "bas1"; LabelMake(obname, 0, p + 115, o + 255, "", 8, clrDarkViolet);
	obname = Name + "numlabel0"; LabelMake(obname, 0, p + 10, o + 70, "AL", 9, clrBlack); 
	if (alerts) ObjectSetInteger(0, Name + "numlabel0", OBJPROP_COLOR, clrGreen); else ObjectSetInteger(0, Name + "numlabel0", OBJPROP_COLOR, clrRed);
}
//+------------------------------------------------------------------+

//+TEST SHOW LABELS--------------------------------------------------+
void eqshow()
{
	ObjectSetString(0, Name + "eqh1", OBJPROP_TEXT, DoubleToStr(GlobalVariableGet("eqhigh"), 2));
	ObjectSetString(0, Name + "bal1", OBJPROP_TEXT, DoubleToStr(AccountBalance(), 2));
	ObjectSetString(0, Name + "eql1", OBJPROP_TEXT, DoubleToStr(GlobalVariableGet("eqlow"), 2));
	ObjectSetString(0, Name + "eqm1", OBJPROP_TEXT, DoubleToStr(GlobalVariableGet("openeq"), 2));
	if (OrdersTotal() > 0) ObjectSetString(0, Name + "mxp1", OBJPROP_TEXT, DoubleToStr(GlobalVariableGet("eqhigh") - GlobalVariableGet("openeq"), 2)); else ObjectSetString(0, Name + "mxp1", OBJPROP_TEXT, "");
	if (OrdersTotal() > 0) ObjectSetString(0, Name + "mxl1", OBJPROP_TEXT, DoubleToStr(GlobalVariableGet("openeq") - GlobalVariableGet("eqlow"), 2)); else ObjectSetString(0, Name + "mxl1", OBJPROP_TEXT, "");
	ObjectSetString(0, Name + "mdd1", OBJPROP_TEXT, DoubleToStr(maxdd(), 2) + " (" + DoubleToStr(maxddr, 1) + "%)");
	ObjectSetString(0, Name + "mnl1", OBJPROP_TEXT, DoubleToStr(maxeqdd(), 2) + " (" + DoubleToStr(AccountEquity(), 2) + ")"); if (AccountEquity() >= maxeqdd()) { ObjectSetInteger(0, Name + "mnl1", OBJPROP_COLOR, clrBlue); ObjectSetInteger(0, Name + "mnl", OBJPROP_COLOR, clrBlue); } else { ObjectSetInteger(0, Name + "mnl1", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "mnl", OBJPROP_COLOR, clrRed); }
	ObjectSetString(0, Name + "tpl1", OBJPROP_TEXT, DoubleToStr(totalpl(), 2) + "(" + DoubleToStr(totalpl()/AccountBalance()*100, 2) + "%)"); if (totalpl() < 0) { ObjectSetInteger(0, Name + "tpl1", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "tpl", OBJPROP_COLOR, clrRed); } else { ObjectSetInteger(0, Name + "tpl1", OBJPROP_COLOR, clrBlue); ObjectSetInteger(0, Name + "tpl", OBJPROP_COLOR, clrBlue); }
	ObjectSetString(0, Name + "dpl1", OBJPROP_TEXT, DoubleToStr(PipsCollected(), 2)); if (PipsCollected() < 0) { ObjectSetInteger(0, Name + "dpl1", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "dpl", OBJPROP_COLOR, clrRed); } else { ObjectSetInteger(0, Name + "dpl1", OBJPROP_COLOR, clrBlue); ObjectSetInteger(0, Name + "dpl", OBJPROP_COLOR, clrBlue); }
	ObjectSetString(0, Name + "dpp1", OBJPROP_TEXT, DoubleToStr(totalpl() + PipsCollected(), 2)); if (totalpl() + PipsCollected() < 0) { ObjectSetInteger(0, Name + "dpp1", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "dpp", OBJPROP_COLOR, clrRed); } else { ObjectSetInteger(0, Name + "dpp1", OBJPROP_COLOR, clrBlue); ObjectSetInteger(0, Name + "dpp", OBJPROP_COLOR, clrBlue); }
	ObjectSetString(0, Name + "pos1", OBJPROP_TEXT, DoubleToStr(totalpos(), 2));
	ObjectSetString(0, Name + "neg1", OBJPROP_TEXT, DoubleToStr(totalneg(), 2));
	int numb = 0;
	for (int i = OrdersTotal(); i >= 0; i--)
		if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
		{
			if(OrderMagicNumber() >= 999 && OrderMagicNumber() <= 2000) numb++;
		}
		
	if (numb > 0) {
			ObjectSetString(0, Name + "bas1", OBJPROP_TEXT, DoubleToStr(basketpos(), 2));
	}
	else ObjectSetString(0, Name + "bas1", OBJPROP_TEXT, "No basket");
	if (alerts) ObjectSetInteger(0, Name + "numlabel0", OBJPROP_COLOR, clrGreen); else ObjectSetInteger(0, Name + "numlabel0", OBJPROP_COLOR, clrRed);
}
//+------------------------------------------------------------------+

//+SL WATCHDOG & ALERT-----------------------------------------------+
void tradecheck()
{
	if (totalpl() <= maxdd() && alerts) Alert("MAX DD LOSS REACHED!!!");
	if (AccountEquity() <= maxeqdd() && alerts) Alert("MAX EQUITY LOSS REACHED!!!");
}
//+------------------------------------------------------------------+

//+MAX DD CALC-------------------------------------------------------+
double maxdd() {
	double maxloss = 0;
		if (OrdersTotal() == 0) maxloss = -((openequity * maxddr) / 100);
		else if (OrdersTotal() > 0) maxloss = -((equityhigh * maxddr) / 100);
	
	return (maxloss);
}
//+------------------------------------------------------------------+

//+MAX EQUITY DD CALC FROM HIGH--------------------------------------+
double maxeqdd() {
	double maxeqloss = equityhigh + maxdd();
	
	return (maxeqloss);
}
//+------------------------------------------------------------------+

//+P/L CALC----------------------------------------------------------+
double totalpl()
{
	double total = 0;
	for (int i = OrdersTotal(); i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (total);
}
//+------------------------------------------------------------------+

//+POSITIVE TRADES---------------------------------------------------+
double totalpos(){
	double total = 0;
	for (int i = OrdersTotal(); i >= 0; i--)
		if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
		{
			if(OrderProfit()>0) total+= OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (total);
}
//+------------------------------------------------------------------+

//+NEGATIVE TRADES---------------------------------------------------+
double totalneg(){
	double total = 0;
	for (int i = OrdersTotal(); i >= 0; i--)
		if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
		{
			if(OrderProfit()<=0) total+= OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (total);
}
//+------------------------------------------------------------------+

//+BASKET TRADES-----------------------------------------------------+
double basketpos(){
	double total = 0;
	for (int i = OrdersTotal(); i >= 0; i--)
		if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
		{
			if(OrderMagicNumber() >= 999) total+= OrderProfit() + OrderSwap() + OrderCommission();
		}
	return (total);
}
//+------------------------------------------------------------------+

/*
//+AUTO CLOSE TRADES-------------------------------------------------+
void tradeclose() {
	if (tradeenable){
		for (int i = OrdersTotal(); i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && (totalpl() < maxdd() || AccountEquity() <= maxeqdd()))
		{
			int ret;
			//ret = OrderClose(0, OrderLots(), )
		}
	}
}
//+------------------------------------------------------------------+
*/

//+------------------------------------------------------------------+
void reclabloc() {
	p = (int)GlobalVariableGet("EQp" + _Symbol); o = (int)GlobalVariableGet("EQo" + _Symbol);
	string obname;
	obname = Name + " LabRec";
	//RecMake(obname, p - 5, o - 5, 480, 55, clrWhite, clrBlack);
	RecMake(obname, p + 5, o + 5, 230, 380, C'223,199,178', clrBlack);

	obname = Name + " MovRec";
	RecMake(obname, p + 4, o + 4, 0, 0, clrWhite, clrBlack);
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, true);
	ObjectSetInteger(0, obname, OBJPROP_SELECTED, true);
}
//+------------------------------------------------------------------+

//+MONEY COLLECTED FUNC----------------------------------------------+
double PipsCollected()
{
	double Pips = 0;
	double GetPips = 0;
	for (int cnt = 0; cnt < OrdersHistoryTotal(); cnt++)
		if (OrderSelect(cnt, SELECT_BY_POS, MODE_HISTORY))
		{
			if ((OrderType() == OP_BUY || OrderType() == OP_SELL) && TimeToStr(OrderCloseTime(), TIME_DATE) == TimeToStr(TimeCurrent(), TIME_DATE))
				Pips += (OrderProfit() + OrderSwap() + OrderCommission());
		}
	return (Pips);
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name, const int corner, const int x, const int y, const string label, const int FSize, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const int x, const int y, const int xs, const int ys, const color FCol, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, Name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+