/*
   Generated by ex4-to-mq4 decompiler FREEWARE 4.0.451.7
   Website: H TT p : // w wW .M ET aqu o t E s. NeT
   E-mail : S UP po R T@ M e tA Q u O tES. NE t
*/
#property copyright "Oechel 2008"
#property link      "http://www.metaquotes.net"

#property indicator_chart_window
#define Name WindowExpertName()
#property strict

input color DownColor = clrOrangeRed;
input color UpColor = clrCyan;
extern bool alerter = false;
input bool lines = true;
input string aa = "";

int UpDownCheckArray[9][2];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	string TFLabel[9] = { "1", "5", "15", "30", "H1", "H4", "D", "W", "M" };
	int YDistance1 = 30;
	int YDistance2 = 43;
	int XDistance1[9] = { 260, 289, 318, 348, 378, 406, 440, 472, 504 };
	int XDistance2[9] = { 250, 279, 311, 340, 371, 399, 431, 463, 495 };
	string PeriodNamesText[9] = { "M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1" };
	string PeriodNamesCircle[9] = { "M1B", "M5B", "M15B", "M30B", "H1B", "H4B", "D1B", "W1B", "MN1B" };
	string obname;

	for (int i = 0; i <= 8; i++) {
		obname = Name + PeriodNamesText[i];
		LabelMake(obname, 2, XDistance1[i] - 230, YDistance1, TFLabel[i], 7, "Arial Bold", clrGreen);

		obname = Name + PeriodNamesCircle[i];
		LabelMake(obname, 2, XDistance2[i] - 230, YDistance2, "O", 25, "Arial Bold", clrGreen);
	}
	LabelMake(Name + "EX1", 2, 292, 30, "O = 20, # = 200", 7, "Arial Black", clrGreen);
	if (alerter) ObjectSetInteger(0, Name + "EX1", OBJPROP_COLOR, clrCyan);
	else ObjectSetInteger(0, Name + "EX1", OBJPROP_COLOR, clrOrangeRed);
	
	

   /*
	obname = Name + "dirm1";
	LabelMake(obname, 2, 15, 65, "not M1", 10, "Arial", clrGreen);

	obname = Name + "dirm5";
	LabelMake(obname, 2, 15, 50, "not M5", 10, "Arial", clrGreen);

	obname = Name + "dirm15";
	LabelMake(obname, 2, 15, 35, "not M15", 10, "Arial", clrGreen);

	obname = Name + "dirm30";
	LabelMake(obname, 2, 15, 20, "not M30", 10, "Arial", clrGreen);
   */
   
	//---
	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DE-INITIALIZATION PROCEDURE---------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
      ObjectsDeleteAll(0, Name);
		if (!IsTesting())
		{
			DeleteObjects();
		}
}
//+------------------------------------------------------------------+

//+OBJECTS DELETE FUNCTION-------------------------------------------+
void DeleteObjects() {
	for (int i = ObjectsTotal() - 1; i >= 0; i--) {
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1) {
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
   //uint start1 = GetTickCount();
	//5M and up alert - M30 & H1 filters
	if (alerter)
	{
		bool alert_reset = false;
		static datetime alert_check = 0;
		if (alert_check < iTime(NULL, PERIOD_M5, 0))
		{
			alert_reset = true;
			alert_check = iTime(NULL, PERIOD_M5, 0);
		}
		if (alert_reset) {
			AlertsLTF();
			alert_reset = false;
		}
	}
	
	//M1 and up alert - M30 & H1 & previous bar filters
	if (alerter)
	{
		bool alert_resetM = false;
		static datetime alert_checkM = 0;
		if (alert_checkM < iTime(NULL, PERIOD_M1, 0))
		{
			alert_resetM = true;
			alert_checkM = iTime(NULL, PERIOD_M1, 0);
		}
		if (alert_resetM) {
			//AlertsTLTF();
			//AlertsFLTF();
			alert_resetM = false;
		}
	}
	
	//H1 and up alert - W1 & MN1 filters
	if (alerter)
	{
		bool alert_resetH = false;
		static datetime alert_checkH = 0;
		if (alert_checkH < iTime(NULL, PERIOD_H1, 0))
		{
			alert_resetH = true;
			alert_checkH = iTime(NULL, PERIOD_H1, 0);
		}
		if (alert_resetH) {
			AlertsHTF();
			AlertsTHTF();
			AlertsFHTF();
			alert_resetH = false;
		}
	}
   
   MainCalcs();
	if (lines) horizlines();
	
	bool delayed = false;
	static datetime delay_check = 0;
	if (delay_check < iTime(NULL, PERIOD_M1, 0))
	{
		delayed = true;
		delay_check = iTime(NULL, PERIOD_M1, 0);
	}
	if (delayed) {
	   alrt20();
		delayed = false;
	}
	
	bool de2layed = false;
	static datetime de2lay_check = 0;
	if (de2lay_check < iTime(NULL, PERIOD_M5, 0))
	{
		de2layed = true;
		de2lay_check = iTime(NULL, PERIOD_M5, 0);
	}
	if (de2layed) {
	   alrt21();
		de2layed = false;
	}
	
	/*
	bool delayed = false;
	static datetime delay_check = 0;
	if (delay_check < iTime(NULL, PERIOD_M1, 0))
	{
		delayed = true;
		delay_check = iTime(NULL, PERIOD_M1, 0);
	}
	if (delayed) {
		//VisualAlert();
		//ATRPipCalcs();
		delayed = false;
	}
	*/
   //uint end1 = GetTickCount(); Print(end1 - start1);
	return (rates_total);
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{ //Enable/Disable Alerter
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "EX1") && ObjectGetInteger(0, StringConcatenate(Name + "EX1"), OBJPROP_COLOR, clrCyan) && alerter)
			{
				ObjectSetInteger(0, StringConcatenate(Name + "EX1"), OBJPROP_COLOR, clrOrangeRed);
				alerter = false;
				Print("catch! red");
			}
			else if (sparam == StringConcatenate(Name + "EX1") && ObjectGetInteger(0, StringConcatenate(Name + "EX1"), OBJPROP_COLOR, clrOrangeRed) && !alerter)
			{
				ObjectSetInteger(0, StringConcatenate(Name + "EX1"), OBJPROP_COLOR, clrCyan);
				alerter = true;
				Print("catch! blue");
			}
		}
	}
	{//Open M1
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "M1B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M1);
				//long x = ChartOpen(_Symbol, PERIOD_M1);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open M5
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "M5B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M5);
				//long x = ChartOpen(_Symbol, PERIOD_M5);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open M15
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "M15B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M15);
				//long x = ChartOpen(_Symbol, PERIOD_M15);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open M30
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "M30B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_M30);
				//long x = ChartOpen(_Symbol, PERIOD_M30);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open H1
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "H1B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H1);
				//long x = ChartOpen(_Symbol, PERIOD_H1);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open H4
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "H4B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_H4);
				//long x = ChartOpen(_Symbol, PERIOD_H4);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open D1
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "D1B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_D1);
				//long x = ChartOpen(_Symbol, PERIOD_D1);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open W1
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "W1B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_W1);
				//long x = ChartOpen(_Symbol, PERIOD_W1);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
	{//Open MN1
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "MN1B"))
			{
				ChartSetSymbolPeriod(0, _Symbol, PERIOD_MN1);
				//long x = ChartOpen(_Symbol, PERIOD_MN1);
				//ChartApplyTemplate(x, "defnew.tpl");
			}
		}
	}
}
//+------------------------------------------------------------------+

//+MAIN PROCEDURE----------------------------------------------------+
void MainCalcs() {
	static int TFMinutesArr[9] = { 1, 5, 15, 30, 60, 240, 1440, 10080, 43200 };
	static string PeriodNamesText[9] = { "M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1" };
	static string PeriodNamesCircle[9] = { "M1B", "M5B", "M15B", "M30B", "H1B", "H4B", "D1B", "W1B", "MN1B" };

	for (int i = 0; i <= 8; i++) {
		UpDownCheckArray[i][0] = 0;
		if (Bid - iMA(NULL, TFMinutesArr[i], 20, 0, MODE_EMA, PRICE_CLOSE, 0) > 0.0) {
			UpDownCheckArray[i][0] = 1;
		}
		if (iMA(NULL, TFMinutesArr[i], 20, 0, MODE_EMA, PRICE_CLOSE, 0) - Bid > 0.0) {
			UpDownCheckArray[i][0] = -1;
		}
      
      if (UpDownCheckArray[i][0] == 1) { ObjectSetInteger(0, Name + PeriodNamesText[i], OBJPROP_COLOR, UpColor); ObjectSetInteger(0, Name + PeriodNamesCircle[i], OBJPROP_COLOR, UpColor); }
		if (UpDownCheckArray[i][0] == 1 && iMA(NULL, TFMinutesArr[i], 200, 0, MODE_EMA, PRICE_CLOSE, 0) - Bid > 0.0) { ObjectSetInteger(0, Name + PeriodNamesText[i], OBJPROP_COLOR, DownColor); }
		if (UpDownCheckArray[i][0] == -1) { ObjectSetInteger(0, Name + PeriodNamesText[i], OBJPROP_COLOR, DownColor); ObjectSetInteger(0, Name + PeriodNamesCircle[i], OBJPROP_COLOR, DownColor); }
      if (UpDownCheckArray[i][0] == -1 && Bid - iMA(NULL, TFMinutesArr[i], 200, 0, MODE_EMA, PRICE_CLOSE, 0) > 0.0) { ObjectSetInteger(0, Name + PeriodNamesText[i], OBJPROP_COLOR, UpColor); }
      
      ObjectSetString(0, Name + PeriodNamesText[i], OBJPROP_TOOLTIP, DoubleToString(iMA(_Symbol, TFMinutesArr[i], 200, 0, MODE_EMA, PRICE_CLOSE, 0), _Digits));
      ObjectSetString(0, Name + PeriodNamesCircle[i], OBJPROP_TOOLTIP, DoubleToString(iMA(_Symbol, TFMinutesArr[i], 20, 0, MODE_EMA, PRICE_CLOSE, 0), _Digits));
   }
	for (int i = 0; i <= 8; i++) {
		UpDownCheckArray[i][1] = 0;
		if (iClose(_Symbol, TFMinutesArr[i], 1) - iMA(NULL, TFMinutesArr[i], 20, 0, MODE_EMA, PRICE_CLOSE, 1) > 0.0) {
			UpDownCheckArray[i][1] = 1;
		}
		if (iMA(NULL, TFMinutesArr[i], 20, 0, MODE_EMA, PRICE_CLOSE, 1) - iClose(_Symbol, TFMinutesArr[i], 1) > 0.0) {
			UpDownCheckArray[i][1] = -1;
		}
   }
}       
//+------------------------------------------------------------------+

void alrt20()
{
   int x = 0, y = 0;
   for (int i = 10; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M5, i) > iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE, i))
         x++;
      if (iHigh(_Symbol, PERIOD_M5, i) < iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE, i))
         y++;
   }
   if (x == 10 && iLow(_Symbol, PERIOD_M5, 0) < iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (long hook)");
   if (y == 10 && iHigh(_Symbol, PERIOD_M5, 0) > iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (short hook)");
   int z = 0, w = 0;
   for (int i = 10; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M5, i) > iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, i))
         z++;
      if (iHigh(_Symbol, PERIOD_M5, i) < iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, i))
         w++;
   }
   if (z == 10 && iLow(_Symbol, PERIOD_M5, 0) < iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (steep long hook)");
   if (w == 10 && iHigh(_Symbol, PERIOD_M5, 0) > iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (steep short hook)");
   int k = 0, l = 0;
   for (int i = 20; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M5, i) > iMA(_Symbol, PERIOD_M5, 34, 0, MODE_EMA, PRICE_CLOSE, i))
         k++;
      if (iHigh(_Symbol, PERIOD_M5, i) < iMA(_Symbol, PERIOD_M5, 34, 0, MODE_EMA, PRICE_CLOSE, i))
         l++;
   }
   if (k == 20 && iLow(_Symbol, PERIOD_M5, 0) < iMA(_Symbol, PERIOD_M5, 34, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (deep retrace long hook)");
   if (l == 20 && iHigh(_Symbol, PERIOD_M5, 0) > iMA(_Symbol, PERIOD_M5, 34, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M5 (deep retrace short hook)");
   int m = 0, n = 0;
   for (int i = 30; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M5, i) > iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_LOW, i))
         m++;
      if (iHigh(_Symbol, PERIOD_M5, i) < iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_HIGH, i))
         n++;
   }
   if (m == 30 && iLow(_Symbol, PERIOD_M5, 0) < iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_LOW, 0))
      Alert("Watch " + _Symbol + " on M5 (long bb jump)");
   if (n == 30 && iHigh(_Symbol, PERIOD_M5, 0) > iBands(_Symbol, PERIOD_M5, 20, 2, 0, PRICE_CLOSE, MODE_HIGH, 0))
      Alert("Watch " + _Symbol + " on M5 (short bb drop)");
}
void alrt21()
{
   int x = 0, y = 0;
   for (int i = 10; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M15, i) > iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE, i))
         x++;
      if (iHigh(_Symbol, PERIOD_M15, i) < iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE, i))
         y++;
   }
   if (x == 10 && iLow(_Symbol, PERIOD_M15, 0) < iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (long hook)");
   if (y == 10 && iHigh(_Symbol, PERIOD_M15, 0) > iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (short hook)");
   int z = 0, w = 0;
   for (int i = 10; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M15, i) > iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, i))
         z++;
      if (iHigh(_Symbol, PERIOD_M15, i) < iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, i))
         w++;
   }
   if (z == 10 && iLow(_Symbol, PERIOD_M15, 0) < iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (steep long hook)");
   if (w == 10 && iHigh(_Symbol, PERIOD_M15, 0) > iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (steep short hook)");
   int k = 0, l = 0;
   for (int i = 20; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M15, i) > iMA(_Symbol, PERIOD_M15, 34, 0, MODE_EMA, PRICE_CLOSE, i))
         k++;
      if (iHigh(_Symbol, PERIOD_M15, i) < iMA(_Symbol, PERIOD_M15, 34, 0, MODE_EMA, PRICE_CLOSE, i))
         l++;
   }
   if (k == 20 && iLow(_Symbol, PERIOD_M15, 0) < iMA(_Symbol, PERIOD_M15, 34, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (deep retrace long hook)");
   if (l == 20 && iHigh(_Symbol, PERIOD_M15, 0) > iMA(_Symbol, PERIOD_M15, 34, 0, MODE_EMA, PRICE_CLOSE, 0))
      Alert("Watch " + _Symbol + " on M15 (deep retrace short hook)");
   int m = 0, n = 0;
   for (int i = 30; i >= 1; i--)
   {
      if (iLow(_Symbol, PERIOD_M15, i) > iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_LOW, i))
         m++;
      if (iHigh(_Symbol, PERIOD_M15, i) < iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_HIGH, i))
         n++;
   }
   if (m == 30 && iLow(_Symbol, PERIOD_M15, 0) < iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_LOW, 0))
      Alert("Watch " + _Symbol + " on M15 (long bb jump)");
   if (n == 30 && iHigh(_Symbol, PERIOD_M15, 0) > iBands(_Symbol, PERIOD_M15, 20, 2, 0, PRICE_CLOSE, MODE_HIGH, 0))
      Alert("Watch " + _Symbol + " on M15 (short bb drop)");
}
         

//+ALERTS MAIN LOW TF------------------------------------------------+
void AlertsLTF() {
	if (UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == 1) Alert("WSS *STRONG UP* LowTF " + Symbol());
	if (UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == 1) Alert("WSS REVERSING UP LowTF " + Symbol());
	if (UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == -1) Alert("WSS HOOK DOWN? LowTF " + Symbol());
	if (UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == -1) Alert("WSS *STRONG DN* LowTF " + Symbol());
	if (UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == -1) Alert("WSS REVERSING DOWN LowTF " + Symbol());
	if (UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == 1) Alert("WSS HOOK UP? LowTF " + Symbol());
	if ((iClose(_Symbol, PERIOD_M5, 1) < iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, 1) && iClose(_Symbol, PERIOD_M5, 1) > iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, 1)) || (iClose(_Symbol, PERIOD_M5, 1) > iMA(_Symbol, PERIOD_M5, 200, 0, MODE_EMA, PRICE_CLOSE, 1) && iClose(_Symbol, PERIOD_M5, 1) < iMA(_Symbol, PERIOD_M5, 300, 0, MODE_EMA, PRICE_CLOSE, 1))) Alert("Between 200 & 300 EMA M5 " + _Symbol);
}
//+------------------------------------------------------------------+

//+ALERTS TEST LOW TF------------------------------------------------+
void AlertsTLTF() {
	if ((UpDownCheckArray[0][1] == -1 || UpDownCheckArray[1][1] == -1) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == 1) Alert("WSS *STRONG UP* LowTF + *TST* " + Symbol());
	if ((UpDownCheckArray[0][1] == -1 || UpDownCheckArray[1][1] == -1) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == 1) Alert("WSS REVERSING UP LowTF + *TST* " + Symbol());
	if ((UpDownCheckArray[0][1] == -1 || UpDownCheckArray[1][1] == -1) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == -1) Alert("WSS HOOK DOWN? LowTF + *TST* " + Symbol());
	if ((UpDownCheckArray[0][1] == 1 || UpDownCheckArray[1][1] == 1) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == -1) Alert("WSS *STRONG DN* LowTF + *TST* " + Symbol());
	if ((UpDownCheckArray[0][1] == 1 || UpDownCheckArray[1][1] == 1) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == -1) Alert("WSS REVERSING DOWN LowTF + *TST* " + Symbol());
	if ((UpDownCheckArray[0][1] == 1 || UpDownCheckArray[1][1] == 1) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == 1) Alert("WSS HOOK UP? LowTF + *TST* " + Symbol());
}
//+------------------------------------------------------------------+

//+ALERTS TEST LOW TF DAY FILTERED-----------------------------------+
void AlertsFLTF() {
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == 1) Alert("WSS *STRONG UP* LowTF + *DayF* " + Symbol());
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == 1) Alert("WSS REVERSING UP LowTF + *DayF* " + Symbol());
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[3][0] == -1) Alert("WSS HOOK DOWN? LowTF + *DayF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == -1) Alert("WSS *STRONG DN* LowTF + *DayF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == -1) Alert("WSS REVERSING DOWN LowTF + *DayF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[3][0] == 1) Alert("WSS HOOK UP? LowTF + *DayF* " + Symbol());
}
//+------------------------------------------------------------------+

//+ALERTS MAIN HIGH TF-----------------------------------------------+
void AlertsHTF() {
	if (UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == 1) Alert("WSS *STRONG UP* HighTF " + Symbol());
	if (UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == 1) Alert("WSS REVERSING UP HighTF " + Symbol());
	if (UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == -1) Alert("WSS HOOK DOWN? HighTF " + Symbol());
	if (UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == -1) Alert("WSS *STRONG DN* HighTF " + Symbol());
	if (UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == -1) Alert("WSS REVERSING DOWN HighTF " + Symbol());
	if (UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == 1) Alert("WSS HOOK UP? HighTF " + Symbol());
}
//+------------------------------------------------------------------+

//+ALERTS TEST HIGH TF-----------------------------------------------+
void AlertsTHTF() {
	if ((UpDownCheckArray[4][1] == -1 || UpDownCheckArray[5][1] == -1) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == 1) Alert("WSS *STRONG UP* HighTF + *TST* " + Symbol());
	if ((UpDownCheckArray[4][1] == -1 || UpDownCheckArray[5][1] == -1) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == 1) Alert("WSS REVERSING UP HighTF + *TST* " + Symbol());
	if ((UpDownCheckArray[4][1] == -1 || UpDownCheckArray[5][1] == -1) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == -1) Alert("WSS HOOK DOWN? HighTF + *TST* " + Symbol());
	if ((UpDownCheckArray[4][1] == 1 || UpDownCheckArray[5][1] == 1) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == -1) Alert("WSS *STRONG DN* HighTF + *TST* " + Symbol());
	if ((UpDownCheckArray[4][1] == 1 || UpDownCheckArray[5][1] == 1) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == -1) Alert("WSS REVERSING DOWN HighTF + *TST* " + Symbol());
	if ((UpDownCheckArray[4][1] == 1 || UpDownCheckArray[5][1] == 1) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == 1) Alert("WSS HOOK UP? HighTF + *TST* " + Symbol());
}
//+------------------------------------------------------------------+

//+ALERTS TEST HIGH TF-----------------------------------------------+
void AlertsFHTF() {
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == 1) Alert("WSS *STRONG UP* HighTF + *DF* " + Symbol());
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == 1) Alert("WSS REVERSING UP HighTF + *DF* " + Symbol());
	if (Bid > iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1 && UpDownCheckArray[6][0] == 1 && UpDownCheckArray[8][0] == -1 && UpDownCheckArray[7][0] == -1) Alert("WSS HOOK DOWN? HighTF + *DF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == -1) Alert("WSS *STRONG DN* HighTF + *DF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == -1) Alert("WSS REVERSING DOWN HighTF + *DF* " + Symbol());
	if (Bid < iOpen(_Symbol, PERIOD_D1, 0) && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1 && UpDownCheckArray[6][0] == -1 && UpDownCheckArray[8][0] == 1 && UpDownCheckArray[7][0] == 1) Alert("WSS HOOK UP? HighTF + *DF* " + Symbol());
}
//+------------------------------------------------------------------+

//+HorizLines-----------------------------------------------------------+
void horizlines()
{
	string obname;
	double sma5 = iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma15 = iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma30 = iMA(_Symbol, PERIOD_M30, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma60 = iMA(_Symbol, PERIOD_H1, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma240 = iMA(_Symbol, PERIOD_H4, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma1440 = iMA(_Symbol, PERIOD_D1, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
	double sma10080 = iMA(_Symbol, PERIOD_W1, 20, 0, MODE_EMA, PRICE_CLOSE, 0);
   
   color smallLines = clrWhite;;
   
   if (ChartPeriod() <= 60){
   obname = Name + "5mMAL";
   objhoriz(obname, sma5, smallLines);
   ObjectSetText(obname, "M5: " + DoubleToStr(sma5, _Digits));

   obname = Name + "15mMAL";
   objhoriz(obname, sma15, smallLines);
   ObjectSetText(obname, "M15: " + DoubleToStr(sma15, _Digits));

   obname = Name + "30mMAL";
   objhoriz(obname, sma30, smallLines);
   ObjectSetText(obname, "M30: " + DoubleToStr(sma30, _Digits));
   }
   
   obname = Name + "60mMAL";
   objhoriz(obname, sma60, clrBlack);
   ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet(obname, OBJPROP_WIDTH, 1);
   ObjectSetText(obname, "H1: " + DoubleToStr(sma60, _Digits));
   if (Bid > sma60)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (Bid < sma60)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

   obname = Name + "240mMAL";
   objhoriz(obname, sma240, clrBlack);
   ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet(obname, OBJPROP_WIDTH, 2);
   ObjectSetText(obname, "H4: " + DoubleToStr(sma240, _Digits));
   if (Bid > sma240)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (Bid < sma240)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

   obname = Name + "1440mMAL";
   objhoriz(obname, sma1440, clrBlack);
   ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet(obname, OBJPROP_WIDTH, 2);
   ObjectSetText(obname, "D1: " + DoubleToStr(sma1440, _Digits));
   if (Bid > sma1440)
      ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (Bid < sma1440)
      ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   
   obname = Name + "10080mMAL";
   objhoriz(obname, sma10080, clrBlack);
   ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet(obname, OBJPROP_WIDTH, 2);
   ObjectSetText(obname, "W1: " + DoubleToStr(sma10080, _Digits));
   if (Bid > sma10080)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if (Bid < sma10080)
   	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
}
//-------------------------------------------------------------------+

//+ATR CALCULATOR----------------------------------------------------+
void ATRPipCalcs() {
	double Atr21Range, DailyPips;

	double Atr21Day = iATR(NULL, PERIOD_D1, 21, 1);
	if (Atr21Day < 0.1) Atr21Range = 10000.0 * Atr21Day;
	else Atr21Range = 100.0 * Atr21Day;
	if (MarketInfo(_Symbol, MODE_PROFITCALCMODE) == 1) Atr21Range = Atr21Day;

	double DailyPoints = iATR(NULL, PERIOD_D1, 1, 0);
	if (DailyPoints < 0.1) DailyPips = 10000.0 * DailyPoints;
	else DailyPips = 100.0 * DailyPoints;
	if (MarketInfo(_Symbol, MODE_PROFITCALCMODE) == 1) DailyPips = DailyPoints;

	string obname = Name + "ADR";
	LabelMake(obname, 2, 15, 80, "RT: " + DoubleToStr(DailyPips, 2) + " R21: " + DoubleToStr(Atr21Range, 2) + " (" + DoubleToStr(DailyPips / Atr21Range * 100, 2) + "%)", 7, "Arial", clrBlack);
	if (Atr21Range > 1 && (Atr21Range - DailyPips < 10 || DailyPips > Atr21Range)) { ObjectSetInteger(0, Name + "ADR", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "ADR", OBJPROP_FONTSIZE, 8); }
	else if (Atr21Range > 500 && (Atr21Range - DailyPips < 100 || DailyPips > Atr21Range)) { ObjectSetInteger(0, Name + "ADR", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "ADR", OBJPROP_FONTSIZE, 8); }
	else if ((MarketInfo(_Symbol, MODE_PROFITCALCMODE) == 1) && Atr21Range < 1 && (Atr21Range - DailyPips < 0.1 || DailyPips > Atr21Range)) { ObjectSetInteger(0, Name + "ADR", OBJPROP_COLOR, clrRed); ObjectSetInteger(0, Name + "ADR", OBJPROP_FONTSIZE, 8); }
}
//+------------------------------------------------------------------+

//+VISUAL ALERTS-----------------------------------------------------+
void VisualAlert() {
	//M1
	if (UpDownCheckArray[0][0] == 1 && UpDownCheckArray[1][0] == -1 && UpDownCheckArray[3][0] == -1) {
		ObjectSetString(0, Name + "dirm1", OBJPROP_TEXT, "M1 Short"); ObjectSetInteger(0, Name + "dirm1", OBJPROP_COLOR, DownColor); ObjectSetString(0, Name + "dirm1", OBJPROP_FONT, "Arial Black");
	}
	else if (UpDownCheckArray[0][0] == -1 && UpDownCheckArray[1][0] == 1 && UpDownCheckArray[3][0] == 1) {
		ObjectSetString(0, Name + "dirm1", OBJPROP_TEXT, "M1 Long"); ObjectSetInteger(0, Name + "dirm1", OBJPROP_COLOR, UpColor); ObjectSetString(0, Name + "dirm1", OBJPROP_FONT, "Arial Black");
	}
	else { ObjectSetString(0, Name + "dirm1", OBJPROP_TEXT, "not M1"); ObjectSetInteger(0, Name + "dirm1", OBJPROP_COLOR, clrGreen); ObjectSetString(0, Name + "dirm1", OBJPROP_FONT, "Arial"); }

	//M5
	if (UpDownCheckArray[1][0] == 1 && UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == -1) {
		ObjectSetString(0, Name + "dirm5", OBJPROP_TEXT, "M5 Short"); ObjectSetInteger(0, Name + "dirm5", OBJPROP_COLOR, DownColor); ObjectSetString(0, Name + "dirm5", OBJPROP_FONT, "Arial Black");
	}
	else if (UpDownCheckArray[1][0] == -1 && UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == 1) {
		ObjectSetString(0, Name + "dirm5", OBJPROP_TEXT, "M5 Long"); ObjectSetInteger(0, Name + "dirm5", OBJPROP_COLOR, UpColor); ObjectSetString(0, Name + "dirm5", OBJPROP_FONT, "Arial Black");
	}
	else { ObjectSetString(0, Name + "dirm5", OBJPROP_TEXT, "not M5"); ObjectSetInteger(0, Name + "dirm5", OBJPROP_COLOR, clrGreen); ObjectSetString(0, Name + "dirm5", OBJPROP_FONT, "Arial"); }

	//M15		
	if (UpDownCheckArray[2][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1) {
		ObjectSetString(0, Name + "dirm15", OBJPROP_TEXT, "M15 Short"); ObjectSetInteger(0, Name + "dirm15", OBJPROP_COLOR, DownColor); ObjectSetString(0, Name + "dirm15", OBJPROP_FONT, "Arial Black");
	}
	else if (UpDownCheckArray[2][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1) {
		ObjectSetString(0, Name + "dirm15", OBJPROP_TEXT, "M15 Long"); ObjectSetInteger(0, Name + "dirm15", OBJPROP_COLOR, UpColor); ObjectSetString(0, Name + "dirm15", OBJPROP_FONT, "Arial Black");
	}
	else { ObjectSetString(0, Name + "dirm15", OBJPROP_TEXT, "not M15"); ObjectSetInteger(0, Name + "dirm15", OBJPROP_COLOR, clrGreen); ObjectSetString(0, Name + "dirm15", OBJPROP_FONT, "Arial"); }

	//M30
	if (UpDownCheckArray[3][0] == 1 && UpDownCheckArray[4][0] == -1 && UpDownCheckArray[5][0] == -1) {
		ObjectSetString(0, Name + "dirm30", OBJPROP_TEXT, "M30 Short"); ObjectSetInteger(0, Name + "dirm30", OBJPROP_COLOR, DownColor); ObjectSetString(0, Name + "dirm30", OBJPROP_FONT, "Arial Black");
	}
	else if (UpDownCheckArray[3][0] == -1 && UpDownCheckArray[4][0] == 1 && UpDownCheckArray[5][0] == 1) {
		ObjectSetString(0, Name + "dirm30", OBJPROP_TEXT, "M30 Long"); ObjectSetInteger(0, Name + "dirm30", OBJPROP_COLOR, UpColor); ObjectSetString(0, Name + "dirm30", OBJPROP_FONT, "Arial Black");
	}
	else { ObjectSetString(0, Name + "dirm30", OBJPROP_TEXT, "not M30"); ObjectSetInteger(0, Name + "dirm30", OBJPROP_COLOR, clrGreen);  ObjectSetString(0, Name + "dirm30", OBJPROP_FONT, "Arial"); }
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION (+Font name)-----------------------------------+
void LabelMake(const string name, const int corner, const int x, const int y, const string label, const int FSize, const string FFont, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, FFont, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objhoriz(string oname, double pr1, color col)
{
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_HLINE, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_DOT);
	ObjectSet(oname, OBJPROP_WIDTH, 0);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_SELECTABLE, false);
}
//+------------------------------------------------------------------+