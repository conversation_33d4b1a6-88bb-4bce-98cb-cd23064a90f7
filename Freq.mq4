#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#define Name WindowExpertName()

extern int periods = 750; // Previous bars to calculate (0 = all)
input int P = 5; //Days to draw
extern int nod = 100; //Number of days to draw (mid max 510)
input string a = "";

double shownd[13][31], showpd[13][31];
double pasthfinder[], pastlfinder[];

bool showpast = false;

double HD1[], LD1[], CD1[], OD1[];
datetime TD1[];

bool firstrun;

//+INIT FUNCTION-----------------------------------------------------+
int OnInit()
{
	//Static builds
	
	/*
	{
		string obname;
		string PercsP[13] = { "-50/-20", "-20/0", "0/20", "20/40", "40/60", "60/80", "80/100", "100/120", "120/140", "140/160", "160/180", "180/200", "200+" };
		string PercsN[13] = { "50/20", "20/0", "0/-20", "-20/-40", "-40/-60", "-60/-80", "-80/-100", "-100/-120", "-120/-140", "-140/-160", "-160/-180", "-180/-200", "-200-" };
		for (int x = 0; x <= 12; x++) {
			obname = Name + "PercsP" + IntegerToString(x); LabelMake(obname, 0, 600 + 50 * x, 30, PercsP[x], 7, "Arial", clrLightBlue);
			obname = Name + "PercSP" + IntegerToString(x); LabelMake(obname, 0, 600 + 50 * x, 45, "", 7, "Arial", clrLightBlue);
			obname = Name + "PercsN" + IntegerToString(x); LabelMake(obname, 0, 600 + 50 * x, 60, PercsN[x], 7, "Arial", clrRed);
			obname = Name + "PercSN" + IntegerToString(x); LabelMake(obname, 0, 600 + 50 * x, 75, "", 7, "Arial", clrRed);
		}
   }
   */
	
	if (nod > 510)
		nod = 510;
	if (ChartPeriod() <= 240)
	{
      ArraySetAsSeries(HD1, true);
      ArraySetAsSeries(LD1, true);
      ArraySetAsSeries(CD1, true);
      ArraySetAsSeries(OD1, true);
      ArraySetAsSeries(TD1, true);
      ArrayResize(HD1, periods + 2);
      ArrayResize(LD1, periods + 2);
      ArrayResize(CD1, periods + 2);
      ArrayResize(OD1, periods + 2);
      ArrayResize(TD1, periods + 2);
      CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
      CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
      CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
      CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
      CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);

		freq();
		for (int x = nod; x >= 1; x--)
		{
			buildmids(x);
		}
		for (int x = P; x >= 0; x--)
		{
		   freqd(x);
		   buildd(x);
		}
	}
	EventSetTimer(10);
	firstrun = true;
	
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+CALCULATE---------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2025.06.30 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("Freq expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true && ChartPeriod() <= 240)
	{		
      bool day_reset = false;
      static datetime day_reset_time = 0;
      if (day_reset_time < iTime(_Symbol, PERIOD_D1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 30) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 420)))
      {
         day_reset = true;
         day_reset_time = iTime(_Symbol, PERIOD_D1, 0);
      }
      if (day_reset)
      {
         ArrayInitialize(HD1, 0);
         ArrayInitialize(LD1, 0);
         ArrayInitialize(CD1, 0);
         ArrayInitialize(OD1, 0);
         ArrayInitialize(TD1, 0);
         ArraySetAsSeries(HD1, true);
         ArraySetAsSeries(LD1, true);
         ArraySetAsSeries(CD1, true);
         ArraySetAsSeries(OD1, true);
         ArraySetAsSeries(TD1, true);
         ArrayResize(HD1, periods + 2);
         ArrayResize(LD1, periods + 2);
         ArrayResize(CD1, periods + 2);
         ArrayResize(OD1, periods + 2);
         ArrayResize(TD1, periods + 2);
         CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
         CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
         CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
         CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
         CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);
         day_reset = false;
      }

	   bool new_2m_check = false;
		static datetime start_2m_time = 0;
		if (start_2m_time < iTime(NULL, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 420)))
		{
			new_2m_check = true;
			start_2m_time = iTime(NULL, PERIOD_H1, 0);
		}
		if (new_2m_check)
		{
         ArrayInitialize(HD1, 0);
         ArrayInitialize(LD1, 0);
         ArrayInitialize(CD1, 0);
         ArrayInitialize(OD1, 0);
         ArrayInitialize(TD1, 0);
         ArraySetAsSeries(HD1, true);
         ArraySetAsSeries(LD1, true);
         ArraySetAsSeries(CD1, true);
         ArraySetAsSeries(OD1, true);
         ArraySetAsSeries(TD1, true);
         ArrayResize(HD1, periods + 2);
         ArrayResize(LD1, periods + 2);
         ArrayResize(CD1, periods + 2);
         ArrayResize(OD1, periods + 2);
         ArrayResize(TD1, periods + 2);
         CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
         CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
         CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
         CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
         CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);
			freq();
   		for (int x = nod; x >= 1; x--)
   		{
   		   int xp = 0;
   		   //HLC[x] = (HD1[x+1] + LD1[x+1] + CD1[x+1]) / 3;
   		   if (iBars(_Symbol, PERIOD_D1) > nod)
               buildmids(x);
   			else if (iBars(_Symbol, PERIOD_D1) <= nod) { xp = iBars(_Symbol, PERIOD_D1); buildmids(xp); }
   		}
   		
   		for (int x = P; x >= 1; x--)
   		{
   		   freqd(x);
   		   buildd(x);
   		}
   		
         ChartRedraw();
			new_2m_check = false;
		}
      
		bool new_3m_check = false;
		static datetime start_3m_time = 0;
		if (start_3m_time < iTime(NULL, PERIOD_M5, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M5, 0) + 10) && TimeCurrent() <= (iTime(NULL, PERIOD_M5, 0) + 30)))
		{
			new_3m_check = true;
			start_3m_time = iTime(NULL, PERIOD_M5, 0);
		}
		if (new_3m_check)
		{
         ArrayInitialize(HD1, 0);
         ArrayInitialize(LD1, 0);
         ArrayInitialize(CD1, 0);
         ArrayInitialize(OD1, 0);
         ArrayInitialize(TD1, 0);
         ArraySetAsSeries(HD1, true);
         ArraySetAsSeries(LD1, true);
         ArraySetAsSeries(CD1, true);
         ArraySetAsSeries(OD1, true);
         ArraySetAsSeries(TD1, true);
         ArrayResize(HD1, periods + 2);
         ArrayResize(LD1, periods + 2);
         ArrayResize(CD1, periods + 2);
         ArrayResize(OD1, periods + 2);
         ArrayResize(TD1, periods + 2);
         CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
         CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
         CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
         CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
         CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);
			freq();
			freqd(0);
   		buildd(0);
         ChartRedraw();
			new_3m_check = false;
		}
		
		/*
		bool new_4m_check = false;
		static datetime start_4m_time = 0;
		if (start_4m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_4m_check = true;
			start_4m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_4m_check)
		{
			checker();
         new_4m_check = false;
		}
		*/
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+EVENT TIMER-------------------------------------------------------+
void OnTimer(){
//FirstRun
   {
		if (firstrun && ChartPeriod() <= 240)
		{
         ArraySetAsSeries(HD1, true);
         ArraySetAsSeries(LD1, true);
         ArraySetAsSeries(CD1, true);
         ArraySetAsSeries(OD1, true);
         ArraySetAsSeries(TD1, true);
         ArrayResize(HD1, periods + 2);
         ArrayResize(LD1, periods + 2);
         ArrayResize(CD1, periods + 2);
         ArrayResize(OD1, periods + 2);
         ArrayResize(TD1, periods + 2);
         CopyHigh(_Symbol, PERIOD_D1, 0, periods + 2, HD1);
         CopyLow(_Symbol, PERIOD_D1, 0, periods + 2, LD1);
         CopyClose(_Symbol, PERIOD_D1, 0, periods + 2, CD1);
         CopyOpen(_Symbol, PERIOD_D1, 0, periods + 2, OD1);
         CopyTime(_Symbol, PERIOD_D1, 0, periods + 2, TD1);
			//int start1 = GetTickCount();
			freq();
			//int end1 = GetTickCount() - start1; Print (end1);
			//int start2 = GetTickCount();
   		for (int x = nod; x >= 1; x--)
   		{
            //HLC[x] = (HD1[x+1] + LD1[x+1] + CD1[x+1]) / 3;
   			buildmids(x);
   		}
   		
   		for (int x = P; x >= 1; x--)
   		{
   		   freqd(x);
   		   buildd(x);
   		}
   		
         ChartRedraw();
			//int end2 = GetTickCount() - start2; Print (end2);
			firstrun = false;
		}
	}
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Switch TF lines on/off
		if (id == CHARTEVENT_KEYDOWN)
		{
			if (lparam == StringGetChar("S", 0) && showpast == false)
			{
				showpast = true;
			   freq();
      		for (int x = nod; x >= 1; x--)
      		{
      			buildmids(x);
      		}
            ChartRedraw();
   		}
			else if (lparam == StringGetChar("S", 0) && showpast == true)
			{
				showpast = false;
			   freq();
      		for (int x = nod; x >= 1; x--)
      		{
      			buildmids(x);
      		}
            ChartRedraw();
			}
		}
	}
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	EventKillTimer();
	return;
}
//+------------------------------------------------------------------+

//+MAIN CALCS--------------------------------------------------------+
void freq()
{
	if (iBars(_Symbol, PERIOD_D1) < periods || periods == 0)
		periods = iBars(_Symbol, PERIOD_D1) - 1;

	double pasth[], pastl[];
	ArrayResize(pasth, periods + 1);
	ArrayResize(pastl, periods + 1);

	ArrayResize(pastlfinder, periods + 2);
	ArrayInitialize(pastlfinder, 0);
	ArrayResize(pasthfinder, periods + 2);
	ArrayInitialize(pasthfinder, 0);

	for (int x = periods - 3; x > 0; x--)
	{
		if ((HD1[x + 2] - LD1[x + 2]) != 0)
			pasth[x] = ((HD1[x + 1] - LD1[x + 2]) / (HD1[x + 2] - LD1[x + 2])) - 0.5;
		if ((HD1[x + 2] - LD1[x + 2]) != 0)
			pastl[x] = ((LD1[x + 1] - LD1[x + 2]) / (HD1[x + 2] - LD1[x + 2])) - 0.5;
	}
	//Categorize past lows
	for (int x = periods; x > 0; x--)
	{
		if (pastl[x] <= -2)
			pastlfinder[x] = 1;
		if (pastl[x] > -2 && pastl[x] <= -1.8)
			pastlfinder[x] = 2;
		if (pastl[x] > -1.8 && pastl[x] <= -1.6)
			pastlfinder[x] = 3;
		if (pastl[x] > -1.6 && pastl[x] <= -1.4)
			pastlfinder[x] = 4;
		if (pastl[x] > -1.4 && pastl[x] <= -1.2)
			pastlfinder[x] = 5;
		if (pastl[x] > -1.2 && pastl[x] <= -1.0)
			pastlfinder[x] = 6;
		if (pastl[x] > -1.0 && pastl[x] <= -0.8)
			pastlfinder[x] = 7;
		if (pastl[x] > -0.8 && pastl[x] <= -0.6)
			pastlfinder[x] = 8;
		if (pastl[x] > -0.6 && pastl[x] <= -0.4)
			pastlfinder[x] = 9;
		if (pastl[x] > -0.4 && pastl[x] <= -0.2)
			pastlfinder[x] = 10;
		if (pastl[x] > -0.2 && pastl[x] <= 0.0)
			pastlfinder[x] = 11;
		if (pastl[x] > 0.0 && pastl[x] <= 0.2)
			pastlfinder[x] = 12;
		if (pastl[x] > 0.2 && pastl[x] <= 0.5)
			pastlfinder[x] = 13;
	}

	//Categorize past highs
	for (int x = periods; x > 0; x--)
	{
		//if (pasth[x] < -0.2) pasthfinder[x] = 14;
		if (pasth[x] >= -0.5 && pasth[x] < -0.2)
			pasthfinder[x] = 14;
		if (pasth[x] >= -0.2 && pasth[x] < 0.0)
			pasthfinder[x] = 15;
		if (pasth[x] >= 0.0 && pasth[x] < 0.2)
			pasthfinder[x] = 16;
		if (pasth[x] >= 0.2 && pasth[x] < 0.4)
			pasthfinder[x] = 17;
		if (pasth[x] >= 0.4 && pasth[x] < 0.6)
			pasthfinder[x] = 18;
		if (pasth[x] >= 0.6 && pasth[x] < 0.8)
			pasthfinder[x] = 19;
		if (pasth[x] >= 0.8 && pasth[x] < 1)
			pasthfinder[x] = 20;
		if (pasth[x] >= 1.0 && pasth[x] < 1.2)
			pasthfinder[x] = 21;
		if (pasth[x] >= 1.2 && pasth[x] < 1.4)
			pasthfinder[x] = 22;
		if (pasth[x] >= 1.4 && pasth[x] < 1.6)
			pasthfinder[x] = 23;
		if (pasth[x] >= 1.6 && pasth[x] < 1.8)
			pasthfinder[x] = 24;
		if (pasth[x] >= 1.8 && pasth[x] < 2)
			pasthfinder[x] = 25;
		if (pasth[x] >= 2)
			pasthfinder[x] = 26;
	}
}
//+------------------------------------------------------------------+

//+MAIN CALC FOR D-DAYS----------------------------------------------+
void freqd(const int D)
{ 
	if (iBars(_Symbol, PERIOD_D1) < periods || periods == 0)
		periods = iBars(_Symbol, PERIOD_D1) - 1;

	double closep[], closen[];
	ArrayResize(closep, periods);
	ArrayResize(closen, periods);
	double ranger[];
	ArrayResize(ranger, periods + 1);

	double Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));

	int both = 0, botl = 0;

	double pastchd1 = 0, pastcld1 = 0;

	pastchd1 = ((HD1[D] - LD1[D + 1]) / (HD1[D + 1] - LD1[D + 1])) - 0.5; 
	pastcld1 = ((LD1[D] - LD1[D + 1]) / (HD1[D + 1] - LD1[D + 1])) - 0.5; 

	//Categorize low
	if (pastcld1 <= -2)
		botl = 1;
	if (pastcld1 > -2 && pastcld1 <= -1.8)
		botl = 2;
	if (pastcld1 > -1.8 && pastcld1 <= -1.6)
		botl = 3;
	if (pastcld1 > -1.6 && pastcld1 <= -1.4)
		botl = 4;
	if (pastcld1 > -1.4 && pastcld1 <= -1.2)
		botl = 5;
	if (pastcld1 > -1.2 && pastcld1 <= -1.0)
		botl = 6;
	if (pastcld1 > -1.0 && pastcld1 <= -0.8)
		botl = 7;
	if (pastcld1 > -0.8 && pastcld1 <= -0.6)
		botl = 8;
	if (pastcld1 > -0.6 && pastcld1 <= -0.4)
		botl = 9;
	if (pastcld1 > -0.4 && pastcld1 <= -0.2)
		botl = 10;
	if (pastcld1 > -0.2 && pastcld1 <= 0.0)
		botl = 11;
	if (pastcld1 > 0.0 && pastcld1 <= 0.2)
		botl = 12;
	if (pastcld1 > 0.2 && pastcld1 <= 0.5)
		botl = 13;

	//Categorize high
	//if (pastchd1 < -0.2) both = 14;
	if (pastchd1 >= -0.5 && pastchd1 < -0.2)
		both = 14;
	if (pastchd1 >= -0.2 && pastchd1 < 0.0)
		both = 15;
	if (pastchd1 >= 0.0 && pastchd1 < 0.2)
		both = 16;
	if (pastchd1 >= 0.2 && pastchd1 < 0.4)
		both = 17;
	if (pastchd1 >= 0.4 && pastchd1 < 0.6)
		both = 18;
	if (pastchd1 >= 0.6 && pastchd1 < 0.8)
		both = 19;
	if (pastchd1 >= 0.8 && pastchd1 < 1)
		both = 20;
	if (pastchd1 >= 1.0 && pastchd1 < 1.2)
		both = 21;
	if (pastchd1 >= 1.2 && pastchd1 < 1.4)
		both = 22;
	if (pastchd1 >= 1.4 && pastchd1 < 1.6)
		both = 23;
	if (pastchd1 >= 1.6 && pastchd1 < 1.8)
		both = 24;
	if (pastchd1 >= 1.8 && pastchd1 < 2)
		both = 25;
	if (pastchd1 >= 2)
		both = 26;

	double medR = 0, R1 = 0, R2 = 0, RP = 0;	//, R3 = 0, R4 = 0, RP = 0;
	bool Rb1 = false, Rb2 = false, Rb3 = false; //, Rb4 = false, Rb5 = false;

	for (int x = periods; x > D; x--)
	{ 
		ranger[x] = (HD1[x] - LD1[x]) / Pip;
	}
	medR = ranger[ArrayMaximum(ranger, 0, 0)] / 3;
	R1 = medR;
	R2 = 2 * medR;				  // R3 = 3 * medR; R4 = 4 * medR;
	RP = (HD1[D] - LD1[D]) / Pip; 
	if (RP > 0 && RP < R1)
		Rb1 = true;
	if (RP > R1 && RP < R2)
		Rb2 = true;
	if (RP > R2)
		Rb3 = true;

	bool mode1 = false;
	if (CD1[D] > OD1[D])
		mode1 = true;
	else
		mode1 = false; 

	double upl = 0, lol = 0;
	if (Rb1)
	{
		lol = 0;
		upl = R1;
	}
	if (Rb2)
	{
		lol = R1;
		upl = R2;
	}
	if (Rb3)
	{
		lol = R2;
		upl = 3 * medR;
	}

	for (int x = periods - 1; x > D; x--)
	{ 
		if (mode1)
		{
			if ((CD1[x] > OD1[x]) && (CD1[x + 1] > OD1[x + 1]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pasthfinder[x] == both)
				closep[x] = ((HD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closep[x] = -1;
		}

		if (!mode1)
		{
			if ((CD1[x] > OD1[x]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pasthfinder[x] == both)
				closep[x] = ((HD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closep[x] = -1;
		}

		if (mode1)
		{
			if ((CD1[x] < OD1[x]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pastlfinder[x] == botl)
				closen[x] = ((LD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closen[x] = 1;
		}

		if (!mode1)
		{
			if ((CD1[x] < OD1[x]) && (CD1[x + 1] < OD1[x + 1]) && (ranger[x + 1] > lol && ranger[x + 1] < upl) && (HD1[x + 1] - LD1[x + 1]) != 0 && pastlfinder[x] == botl)
				closen[x] = ((LD1[x] - LD1[x + 1]) / (HD1[x + 1] - LD1[x + 1])) - 0.5;
			else
				closen[x] = 1;
		}
	}

	double postot = 0, negtot = 0;

	for (int x = periods - 1; x > D; x--)
	{ 
		if (closep[x] > -0.5)
			postot++;
		if (closen[x] < 0.5)
			negtot++;
	}

	double countp[13];
	ArrayInitialize(countp, 0);
	double countn[13];
	ArrayInitialize(countn, 0);

	for (int x = periods - 1; x > D; x--)
	{ 
		//if (closep[x] < -0.2) countp[0]++;
		if (closep[x] > -0.5 && closep[x] <= -0.2)
			countp[0]++;
		if (closep[x] >= -0.2 && closep[x] < 0.0)
			countp[1]++;
		if (closep[x] >= 0.0 && closep[x] < 0.2)
			countp[2]++;
		if (closep[x] >= 0.2 && closep[x] < 0.4)
			countp[3]++;
		if (closep[x] >= 0.4 && closep[x] < 0.6)
			countp[4]++;
		if (closep[x] >= 0.6 && closep[x] < 0.8)
			countp[5]++;
		if (closep[x] >= 0.8 && closep[x] < 1.0)
			countp[6]++;
		if (closep[x] >= 1.0 && closep[x] < 1.2)
			countp[7]++;
		if (closep[x] >= 1.2 && closep[x] < 1.4)
			countp[8]++;
		if (closep[x] >= 1.4 && closep[x] < 1.6)
			countp[9]++;
		if (closep[x] >= 1.6 && closep[x] < 1.8)
			countp[10]++;
		if (closep[x] >= 1.8 && closep[x] < 2.0)
			countp[11]++;
		if (closep[x] >= 2)
			countp[12]++;

		//if (closen[x] > 0.2) countn[0]++;
		if (closen[x] < 0.5 && closen[x] >= 0.2)
			countn[0]++;
		if (closen[x] <= 0.2 && closen[x] > 0.0)
			countn[1]++;
		if (closen[x] <= 0.0 && closen[x] > -0.2)
			countn[2]++;
		if (closen[x] <= -0.2 && closen[x] > -0.4)
			countn[3]++;
		if (closen[x] <= -0.4 && closen[x] > -0.6)
			countn[4]++;
		if (closen[x] <= -0.6 && closen[x] > -0.8)
			countn[5]++;
		if (closen[x] <= -0.8 && closen[x] > -1.0)
			countn[6]++;
		if (closen[x] <= -1.0 && closen[x] > -1.2)
			countn[7]++;
		if (closen[x] <= -1.2 && closen[x] > -1.4)
			countn[8]++;
		if (closen[x] <= -1.4 && closen[x] > -1.6)
			countn[9]++;
		if (closen[x] <= -1.6 && closen[x] > -1.8)
			countn[10]++;
		if (closen[x] <= -1.8 && closen[x] > -2.0)
			countn[11]++;
		if (closen[x] <= -2)
			countn[12]++;
	}

	double statp[13];
	ArrayInitialize(statp, 0);
	double statn[13];
	ArrayInitialize(statn, 0);

	if (postot != 0 && negtot != 0)
	{
		for (int x = 12; x >= 0; x--)
		{
			statp[x] = (countp[x] / postot) * 100;
			statn[x] = (countn[x] / negtot) * 100;
		}
	}

	for (int x = 12; x >= 0; x--)
	{
		showpd[x][D] = statp[x];
		shownd[x][D] = statn[x];
	}
}
//+------------------------------------------------------------------+

//+CREATE PAST & CURRENT SHAPES--------------------------------------+
void buildd(const int D)
{ 
	
	string obname;

	double shownc[13], showpc[13];
	ArrayInitialize(shownc, 0);
	ArrayInitialize(showpc, 0);

	shownc[12] = shownd[12][D];
	for (int x = 11; x >= 0; x--)
	{
		shownc[x] = shownc[x + 1] + shownd[x][D];
	}
	showpc[12] = showpd[12][D];
	for (int x = 11; x >= 0; x--)
	{
		showpc[x] = showpc[x + 1] + showpd[x][D]; 
	}

	double levels[23] = {-2, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.5, -0.4, -0.2, 0, 0.2, 0.4, 0.5, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0};

	double level0[23];
	ArrayInitialize(level0, 0);

	for (int x = 22; x >= 0; x--)
	{
		level0[x] = ((HD1[D] + LD1[D]) / 2) + (levels[x] * (HD1[D] - LD1[D])); 
	}

	int p = 0;
	while (p >= 0 && p <= 12)
	{
		//if (showpc[p] <= 33.33)
		if (showpc[p] <= 49.99)
			break;
		p++;
	}
	int o = 0;
	while (o >= 0 && o <= 12)
	{
		//if (shownc[o] <= 33.33)
		if (shownc[o] <= 49.99)
			break;
		o++;
	}

	int r = 0;
	while (r >= 0 && r <= 12)
	{
		if (showpc[r] <= 66.67)
		//if (showpc[r] <= 69.99)
			break;
		r++;
	}
	int s = 0;
	while (s >= 0 && s <= 12)
	{
		if (shownc[s] <= 66.67)
		//if (shownc[s] <= 69.99)
			break;
		s++;
	}

	int levelslow1[14] = {14, 12, 11, 10, 9, 7, 6, 5, 4, 3, 2, 1, 0, 0};
	int levelshigh1[14] = {8, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 22};

	if (ChartPeriod() > 5 && D >= 1)
	{
		if (D <= P)
		{
			if (p > 1)
			{
				obname = Name + "UpExtLine" + IntegerToString(D);
				RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrBlue, "Extreme High Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone High End D-" + IntegerToString(D - 1) + ": ");
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (D == 1)
				{
					ObjectSetInteger(0, obname, OBJPROP_BACK, true);
					//ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0);
				}
				if (HD1[D - 1] >= level0[levelshigh1[p - 1]])
				{
					ObjectSetInteger(0, Name + "UpExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
					ObjectSetInteger(0, obname, OBJPROP_BACK, true);
					
					if (D == 1)
					{
						ObjectSetInteger(0, Name + "ArrUpExtreme", OBJPROP_COLOR, clrYellow);
						ObjectSetInteger(0, Name + "ArrUpTZE", OBJPROP_COLOR, clrYellow);
					}
					
				}
			}
			else
			{
				ObjectDelete(obname);
			}

			if (o > 1)
			{
				obname = Name + "DnExtLine" + IntegerToString(D);
				RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrRed, "Extreme Low Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone Low End D-" + IntegerToString(D - 1) + ": ");
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (D == 1)
				{
					ObjectSetInteger(0, obname, OBJPROP_BACK, true);
					//ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0);
				}
				if (LD1[D - 1] <= level0[levelslow1[o - 1]])
				{
					ObjectSetInteger(0, Name + "DnExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
					ObjectSetInteger(0, obname, OBJPROP_BACK, true);
					
					if (D == 1)
					{
						ObjectSetInteger(0, Name + "ArrDnExtreme", OBJPROP_COLOR, clrYellow);
						ObjectSetInteger(0, Name + "ArrDnTZE", OBJPROP_COLOR, clrYellow);
					}
					
				}
			}
			else
			{
				ObjectDelete(obname);
			}
/*
			if (r > 1)
			{
				obname = Name + "UnNewLine" + IntegerToString(D);
				objtrend(obname, level0[levelshigh1[r - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrGreen, "<66.67% UpZnStart " + DoubleToStr(level0[levelshigh1[r - 1]], _Digits));
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (HD1[D - 1] >= level0[levelshigh1[r - 1]])
				{
					ObjectSetInteger(0, Name + "UnNewLine" + IntegerToString(D), OBJPROP_COLOR, clrGold);
				}
			}
			else
			{
				ObjectDelete(obname);
			}

			if (s > 1)
			{
				obname = Name + "DnNewLine" + IntegerToString(D);
				objtrend(obname, level0[levelslow1[s - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrDeepPink, "<66.67% DnZnStart " + DoubleToStr(level0[levelslow1[s - 1]], _Digits));
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				if (D > 1)
					ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
				if (LD1[D - 1] <= level0[levelslow1[s - 1]])
				{
					ObjectSetInteger(0, Name + "DnNewLine" + IntegerToString(D), OBJPROP_COLOR, clrGold);
				}
			}
			else
			{
				ObjectDelete(obname);
			}
*/
			obname = Name + "MidLineN" + IntegerToString(D);
			RecFill(obname, (HD1[D] - LD1[D]) * 0.5625 + LD1[D], (HD1[D] - LD1[D]) * 0.4375 + LD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrAquamarine, "TZ 50% upper D-" + IntegerToString(D - 1) + ": ", "TZ 50% lower D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
			if (CD1[D - 1] < (HD1[D] + LD1[D]) / 2)
				ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
			//ObjectSetInteger(0, obname, OBJPROP_BACK, false);
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_WIDTH, 0);
/*
			obname = Name + "MidLineN-" + IntegerToString(D);
			if (D <= 2)
				objtrend(obname, (HD1[D] + LD1[D]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrWhiteSmoke, "Mid" + IntegerToString(D) + ": " + DoubleToStr((HD1[D] + LD1[D]) / 2, _Digits));
			ObjectSet(obname, OBJPROP_WIDTH, 1);
			ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
			if (D > 1)
				ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
*/	
			/*	
			obname = Name + "Balance-" + IntegerToString(D);
			if (D <= P)
				objtrend(obname, (HD1[D] + LD1[D] + CD1[D]) / 3, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrBlack, "Pivot" + IntegerToString(D) + ": " + DoubleToStr((HD1[D] + LD1[D] + CD1[D]) / 3, _Digits));
			ObjectSet(obname, OBJPROP_WIDTH, 2);
			ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
			
			if (D > 1)
				ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
			for (int rs = D; rs >= 0; rs--)
			   {
			      if ((HD1[D] + LD1[D] + CD1[D]) > (HD1[D+1] + LD1[D+1] + CD1[D+1]))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_COLOR, clrBlue);
			      if (((HD1[D] + LD1[D] + CD1[D]) > (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) > (HD1[D+2] + LD1[D+2] + CD1[D+2])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 3);
			      if (((HD1[D] + LD1[D] + CD1[D]) > (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) > (HD1[D+2] + LD1[D+2] + CD1[D+2])) && ((HD1[D+2] + LD1[D+2] + CD1[D+2]) > (HD1[D+3] + LD1[D+3] + CD1[D+3])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 4);
			      if (((HD1[D] + LD1[D] + CD1[D]) > (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) > (HD1[D+2] + LD1[D+2] + CD1[D+2])) && ((HD1[D+2] + LD1[D+2] + CD1[D+2]) > (HD1[D+3] + LD1[D+3] + CD1[D+3])) && ((HD1[D+3] + LD1[D+3] + CD1[D+3]) > (HD1[D+4] + LD1[D+4] + CD1[D+4])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 5);
			      if ((HD1[D] + LD1[D] + CD1[D]) < (HD1[D+1] + LD1[D+1] + CD1[D+1]))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_COLOR, clrRed);
			      if (((HD1[D] + LD1[D] + CD1[D]) < (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) < (HD1[D+2] + LD1[D+2] + CD1[D+2])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 3);
			      if (((HD1[D] + LD1[D] + CD1[D]) < (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) < (HD1[D+2] + LD1[D+2] + CD1[D+2])) && ((HD1[D+2] + LD1[D+2] + CD1[D+2]) < (HD1[D+3] + LD1[D+3] + CD1[D+3])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 4);
			      if (((HD1[D] + LD1[D] + CD1[D]) < (HD1[D+1] + LD1[D+1] + CD1[D+1])) && ((HD1[D+1] + LD1[D+1] + CD1[D+1]) < (HD1[D+2] + LD1[D+2] + CD1[D+2])) && ((HD1[D+2] + LD1[D+2] + CD1[D+2]) < (HD1[D+3] + LD1[D+3] + CD1[D+3])) && ((HD1[D+3] + LD1[D+3] + CD1[D+3]) < (HD1[D+4] + LD1[D+4] + CD1[D+4])))
			      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(D), OBJPROP_WIDTH, 5);
			   }
			*/
		}
	}

	if (ChartPeriod() <= 5 && D == 1)
	{
		if (p > 1)
		{
			obname = Name + "UpExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrBlue, "Extreme High Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone High End D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
			if (HD1[D - 1] >= level0[levelshigh1[p - 1]])
				ObjectSetInteger(0, Name + "UpExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
		}
		else
		{
			ObjectDelete(obname);
		}

		if (o > 1)
		{
			obname = Name + "DnExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrRed, "Extreme Low Start D-" + IntegerToString(D - 1) + ": ", "Trade Zone Low End D-" + IntegerToString(D - 1) + ": ");
			if (D > 1)
				ObjectSetInteger(0, obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false)] - 1);
			if (LD1[D - 1] <= level0[levelslow1[o - 1]])
				ObjectSetInteger(0, Name + "DnExtLine" + IntegerToString(D), OBJPROP_COLOR, clrYellow);
		}
		else
		{
			ObjectDelete(obname);
		}

		obname = Name + "MidLineN" + IntegerToString(D);
		RecFill(obname, (HD1[D] - LD1[D]) * 0.5625 + LD1[D], (HD1[D] - LD1[D]) * 0.4375 + LD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrAquamarine, "TZ 50% upper D-" + IntegerToString(D - 1) + ": ", "TZ 50% lower D-" + IntegerToString(D - 1) + ": ");
		if (D > 1)
			ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
		if (CD1[D - 1] < (HD1[D] + LD1[D]) / 2)
			ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
		//ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 3);

		obname = Name + "MidLineN-" + IntegerToString(D);
		objtrend(obname, (HD1[D] + LD1[D]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrWhiteSmoke, "Mid" + IntegerToString(D) + ": " + DoubleToStr((HD1[D] + LD1[D]) / 2, _Digits));
		ObjectSet(obname, OBJPROP_WIDTH, 1);
		ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
		if (D > 1)
			ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 2], false) + 1]);
	}

	if (D == 0)
	{
		if (p > 1)
		{
			obname = Name + "UpExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelshigh1[p]], level0[levelshigh1[p - 1]], 0, 10, clrBlue, "Extreme High Start D-New: ", "Trade Zone High End D-New: ");
			ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
			//ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		}
		else
		{
			ObjectDelete(obname);
		}

		if (o > 1)
		{
			obname = Name + "DnExtLine" + IntegerToString(D);
			RecFill(obname, level0[levelslow1[o]], level0[levelslow1[o - 1]], 0, 10, clrRed, "Extreme Low Start D-New: ", "Trade Zone Low End D-New: ");
			ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
			//ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		}
		else
		{
			ObjectDelete(obname);
		}

		if (r > 1)
		{
			obname = Name + "UnNewLine" + IntegerToString(D);
			objtrend(obname, level0[levelshigh1[r - 1]], 0, 10, clrGreen, "<66.67% UpZnStart " + DoubleToStr(level0[levelshigh1[r - 1]], _Digits));
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
		}
		else
		{
			ObjectDelete(obname);
		}

		if (s > 1)
		{
			obname = Name + "DnNewLine" + IntegerToString(D);
			objtrend(obname, level0[levelslow1[s - 1]], 0, 10, clrDeepPink, "<66.67% DnZnStart " + DoubleToStr(level0[levelslow1[s - 1]], _Digits));
			ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
		}
		else
		{
			ObjectDelete(obname);
		}

		obname = Name + "MidLineN" + IntegerToString(D);
		RecFill(obname, (HD1[D] - LD1[D]) * 0.5625 + LD1[D], (HD1[D] - LD1[D]) * 0.4375 + LD1[D], 0, 10, clrAquamarine, "TZ 50% upper D-New: ", "TZ 50% lower D-New: ");
		if (CD1[0] < (HD1[D] + LD1[D]) / 2)
			ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
		ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
		//ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);

		obname = Name + "MidLineN-" + IntegerToString(D);
		objtrend(obname, (HD1[D] + LD1[D]) / 2, 0, 10, clrWhiteSmoke, "Mid: " + DoubleToStr((HD1[D] + LD1[D]) / 2, _Digits));
		ObjectSet(obname, OBJPROP_WIDTH, 1);
		ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
		ObjectSet(obname, OBJPROP_TIME1, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_CURRENT, 0), false)] + 5 * Period() * 60);
	}
}
//+------------------------------------------------------------------+

//+COLOR CHECKER-----------------------------------------------------+
void checker() {
	double levels[23] = { -2, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.5, -0.4, -0.2, 0, 0.2, 0.4, 0.5, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0 };

	double shownc[13], showpc[13];
	ArrayInitialize(shownc, 0);
	ArrayInitialize(showpc, 0);

	shownc[12] = shownd[12][1];
	for (int x = 11; x >= 0; x--)
	{
		shownc[x] = shownc[x + 1] + shownd[x][1];
	}
	showpc[12] = showpd[12][1];
	for (int x = 11; x >= 0; x--)
	{
		showpc[x] = showpc[x + 1] + showpd[x][1]; 
	}
	
	for (int i = 12; i >= 0; i--) {
		ObjectSetString(0, Name + "PercSP" + IntegerToString(i), OBJPROP_TEXT, DoubleToStr(showpc[i], 2));
	}
	for (int i = 12; i >= 0; i--) {
		ObjectSetString(0, Name + "PercSN" + IntegerToString(i), OBJPROP_TEXT, DoubleToStr(shownc[i], 2));
	}
	
	double level0[23];
	ArrayInitialize(level0, 0);

	for (int x = 22; x >= 0; x--) {
		level0[x] = ((iHigh(_Symbol, PERIOD_D1, 1) + iLow(_Symbol, PERIOD_D1, 1)) / 2) + (levels[x] * (iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)));
	}

	int levelslow[13] = { 0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14 };
	int levelshigh[13] = { 22, 21, 20, 19, 18, 17, 16, 15, 13, 12, 11, 10, 8 };

	for (int x = 0, i = 11; x <= 11; x++, i--) {
		ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_TOOLTIP, DoubleToStr(level0[levelslow[x + 1]], _Digits) + " - " + DoubleToStr(level0[levelslow[x]], _Digits));
	}
	ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_TOOLTIP, DoubleToStr(level0[levelslow[0]], _Digits) + "-");

	for (int x = 0, i = 11; x <= 11; x++, i--) {
		ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_TOOLTIP, DoubleToStr(level0[levelshigh[x + 1]], _Digits) + " - " + DoubleToStr(level0[levelshigh[x]], _Digits));
	}
	ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_TOOLTIP, DoubleToStr(level0[levelshigh[0]], _Digits) + "+");
	
	//CurL & LL
	if (Bid < level0[0]) { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrWhite); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	else { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrRed); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (Bid > level0[levelslow[x]] && Bid < level0[levelslow[x + 1]]) { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrWhite); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
		else { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrRed); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial"); }
	}

	if (iLow(_Symbol, PERIOD_D1, 0) < level0[0]) { ObjectSet(Name + "PercsN" + IntegerToString(12), OBJPROP_COLOR, clrDarkViolet); ObjectSetString(0, Name + "PercsN" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (iLow(_Symbol, PERIOD_D1, 0) > level0[levelslow[x]] && iLow(_Symbol, PERIOD_D1, 0) < level0[levelslow[x + 1]]) { ObjectSet(Name + "PercsN" + IntegerToString(i), OBJPROP_COLOR, clrDarkViolet); ObjectSetString(0, Name + "PercsN" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
	}

	//CurH & HH
	if (Bid > level0[22]) { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrWhite); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	else { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrLightBlue); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (Bid > level0[levelshigh[x + 1]] && Bid < level0[levelshigh[x]]) { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrWhite); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
		else { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrLightBlue); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial"); }
	}

	if (iHigh(_Symbol, PERIOD_D1, 0) > level0[22]) { ObjectSet(Name + "PercsP" + IntegerToString(12), OBJPROP_COLOR, clrDarkGreen); ObjectSetString(0, Name + "PercsP" + IntegerToString(12), OBJPROP_FONT, "Arial Black"); }
	for (int x = 0, i = 11; x <= 11; x++, i--) {
		if (iHigh(_Symbol, PERIOD_D1, 0) > level0[levelshigh[x + 1]] && iHigh(_Symbol, PERIOD_D1, 0) < level0[levelshigh[x]]) { ObjectSet(Name + "PercsP" + IntegerToString(i), OBJPROP_COLOR, clrDarkGreen); ObjectSetString(0, Name + "PercsP" + IntegerToString(i), OBJPROP_FONT, "Arial Black"); }
	}
}
//+------------------------------------------------------------------+

//+BUILDMIDS---------------------------------------------------------+
void buildmids(const int D)
{
   string obname;

	ObjectsDeleteAll(0, Name + "OlineCon-");
	ObjectsDeleteAll(0, Name + "Oline-");

	obname = Name + "OLine-" + IntegerToString(D);
	objtrend(obname, OD1[D], iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false), 4, clrNONE, DoubleToStr(OD1[D], _Digits));
	ObjectSet(obname, OBJPROP_PRICE2, (HD1[D + 1] + LD1[D + 1]) / 2);
	ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D - 1], false)]);
	ObjectSet(obname, OBJPROP_WIDTH, 2);
	if (HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2)
	{
		//ObjectDelete(obname);
		ObjectSet(obname, OBJPROP_COLOR, clrOlive);
		ObjectSet(obname, OBJPROP_PRICE1, HD1[D]);
	}
	else if (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2)
	{
		//ObjectDelete(obname);
		ObjectSet(obname, OBJPROP_COLOR, clrOlive);
		ObjectSet(obname, OBJPROP_PRICE1, LD1[D]);
	}

	int t = 0, y = 0;

	if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) || (LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2))
	{
		obname = Name + "OLineCon-" + IntegerToString(D);
		objtrend(obname, (HD1[D + 1] + LD1[D + 1]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false), 4, clrLime, DoubleToStr((HD1[D + 1] + LD1[D + 1]) / 2, _Digits) + " @ " + TimeToStr(TD1[D], TIME_DATE));
		if ((HD1[D] < (HD1[D + 1] + LD1[D + 1]) / 2) && (HD1[iHighest(_Symbol, PERIOD_D1, MODE_HIGH, D, 1)] > ObjectGet(obname, OBJPROP_PRICE2)))
		{
			for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
			{
				if (iHigh(_Symbol, PERIOD_CURRENT, x) < ObjectGet(obname, OBJPROP_PRICE2))
					t++;
				if (iHigh(_Symbol, PERIOD_CURRENT, x) > ObjectGet(obname, OBJPROP_PRICE2))
					break;
			}
			ObjectDelete(Name + "OLineCon-" + IntegerToString(D - 1));
			if (!showpast)
				ObjectDelete(obname);
			else if (showpast)
			{
				ObjectSet(obname, OBJPROP_WIDTH, 1);
				ObjectSet(obname, OBJPROP_COLOR, clrOlive);
				ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - t], false)]);
			}
		}
		if ((LD1[D] > (HD1[D + 1] + LD1[D + 1]) / 2) && (LD1[iLowest(_Symbol, PERIOD_D1, MODE_LOW, D, 1)] < ObjectGet(obname, OBJPROP_PRICE2)))
		{
			for (int x = iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false); x >= 1; x--)
			{
				if (iLow(_Symbol, PERIOD_CURRENT, x) > ObjectGet(obname, OBJPROP_PRICE2))
					y++;
				if (iLow(_Symbol, PERIOD_CURRENT, x) < ObjectGet(obname, OBJPROP_PRICE2))
					break;
			}
			ObjectDelete(Name + "OLineCon-" + IntegerToString(D - 1));
			if (!showpast)
				ObjectDelete(obname);
			else if (showpast)
			{
				ObjectSet(obname, OBJPROP_WIDTH, 1);
				ObjectSet(obname, OBJPROP_COLOR, clrOlive);
				ObjectSet(obname, OBJPROP_TIME2, Time[iBarShift(_Symbol, PERIOD_CURRENT, Time[iBarShift(_Symbol, PERIOD_CURRENT, TD1[D], false) - y], false)]);
			}
		}
	}

	if (ObjectGet(Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) == clrLime)
		ObjectSet(Name + "OLine-" + IntegerToString(D), OBJPROP_COLOR, clrLime);
	if (!showpast && ObjectGet(Name + "OLineCon-" + IntegerToString(D), OBJPROP_COLOR) != clrLime)
		ObjectDelete(Name + "OLine-" + IntegerToString(D));
		
	obname = Name + "MidLineN" + IntegerToString(1);
	RecFill(obname, (HD1[1] - LD1[1]) * 0.5625 + LD1[1], (HD1[1] - LD1[1]) * 0.4375 + LD1[1], iBarShift(_Symbol, PERIOD_CURRENT, TD1[0], false), 4, clrAquamarine, "TZ 50% upper D-" + IntegerToString(0) + ": ", "TZ 50% lower D-" + IntegerToString(0) + ": ");
	if (CD1[0] < (HD1[1] + LD1[1]) / 2)
	ObjectSet(obname, OBJPROP_COLOR, clrMediumPurple);
	ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);

	obname = Name + "MidLineN-" + IntegerToString(1);
	objtrend(obname, (HD1[1] + LD1[1]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, TD1[0], false), 4, clrWhiteSmoke, "Mid" + IntegerToString(1) + ": " + DoubleToStr((HD1[1] + LD1[1]) / 2, _Digits));
	ObjectSet(obname, OBJPROP_WIDTH, 1);
	ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
	
	/*
	obname = Name + "Balance-" + IntegerToString(1);
	objtrend(obname, (HD1[1] + LD1[1] + CD1[1]) / 3, iBarShift(_Symbol, PERIOD_CURRENT, TD1[0], false), 4, clrBlack, "Pivot" + IntegerToString(1) + ": " + DoubleToStr((HD1[1] + LD1[1] + CD1[1]) / 3, _Digits));
	ObjectSet(obname, OBJPROP_WIDTH, 2);
	ObjectSet(obname, OBJPROP_STYLE, STYLE_SOLID);
	   {
	      if ((HD1[1] + LD1[1] + CD1[1]) > (HD1[2] + LD1[2] + CD1[2]))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_COLOR, clrBlue);
	      if (((HD1[1] + LD1[1] + CD1[1]) > (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) > (HD1[3] + LD1[3] + CD1[3])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 3);
	      if (((HD1[1] + LD1[1] + CD1[1]) > (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) > (HD1[3] + LD1[3] + CD1[3])) && ((HD1[3] + LD1[3] + CD1[3]) > (HD1[4] + LD1[4] + CD1[4])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 4);
	      if (((HD1[1] + LD1[1] + CD1[1]) > (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) > (HD1[3] + LD1[3] + CD1[3])) && ((HD1[3] + LD1[3] + CD1[3]) > (HD1[4] + LD1[4] + CD1[4])) && ((HD1[4] + LD1[4] + CD1[4]) > (HD1[5] + LD1[5] + CD1[5])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 5);
	      if ((HD1[1] + LD1[1] + CD1[1]) < (HD1[2] + LD1[2] + CD1[2]))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_COLOR, clrRed);
	      if (((HD1[1] + LD1[1] + CD1[1]) < (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) < (HD1[3] + LD1[3] + CD1[3])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 3);
	      if (((HD1[1] + LD1[1] + CD1[1]) < (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) < (HD1[3] + LD1[3] + CD1[3])) && ((HD1[3] + LD1[3] + CD1[3]) < (HD1[4] + LD1[4] + CD1[4])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 4);
	      if (((HD1[1] + LD1[1] + CD1[1]) < (HD1[2] + LD1[2] + CD1[2])) && ((HD1[2] + LD1[2] + CD1[2]) < (HD1[3] + LD1[3] + CD1[3])) && ((HD1[3] + LD1[3] + CD1[3]) < (HD1[4] + LD1[4] + CD1[4])) && ((HD1[4] + LD1[4] + CD1[4]) < (HD1[5] + LD1[5] + CD1[5])))
	      ObjectSetInteger(0, Name + "Balance-" + IntegerToString(1), OBJPROP_WIDTH, 5);
	   }
   */
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const string Font,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//+------------------------------------------------------------------+

/*
//+FIBMAKE-----------------------------------------------------------+
bool FibMake(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
		{
			Print(__FUNCTION__,
				": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
			return (false);
		}
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 23);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	double levels[23] = { -1.5, -1.3, -1.1, -0.9, -0.7, -0.5, -0.3, -0.1, 0, 0.1, 0.3, 0.5, 0.7, 0.9, 1, 1.1, 1.3, 1.5, 1.7, 1.9, 2.1, 2.3, 2.5 };
	string levell[23] = { "-2 - %$", "-1.8 - %$", "-1.6 - %$", "-1.4 - %$", "-1.2 - %$", "-1 - %$", "-0.8 - %$", "-0.6 - %$", "-50", "-0.4 - %$", "-0.2 - %$", "0 - %$", "0.2 - %$", "0.4 - %$", "50", "0.6 - %$", "0.8 - %$", "1 - %$", "1.2 - %$", "1.4 - %$", "1.6 - %$", "1.8 - %$", "2 - %$" };

	for (int x = 0; x <= 22; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	return(true);
}
//+------------------------------------------------------------------+
*/
/*
//+ARROWPRICE--------------------------------------------------------+
void ArrowPrice(const string name, const double x, const int y, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW_LEFT_PRICE, 0, 0, 0))
		{
			Print("error: can't create arrow_right_price! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[y]);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "FreqPrice " + StringSubstr(name, StringLen(Name), 0) + ": " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+
*/
//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, int t, int pir, color col, string buls)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_TIME2, Time[0] + pir * Period() * 60);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr1);
	ObjectSet(name, OBJPROP_STYLE, 0);
	ObjectSet(name, OBJPROP_WIDTH, 2);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, false);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls);
}
//+------------------------------------------------------------------+

void RecFill(const string name, const double x, const double xs, const int t, const int pir, const color FCol, string buls, string bers)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, xs);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[0] + pir * Period() * 60);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, buls + DoubleToStr(x, _Digits) + " - " + bers + DoubleToStr(xs, _Digits) + " " + " M: " + DoubleToStr((x + xs) / 2, _Digits));
}
//Create rectangle backgrounds - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+