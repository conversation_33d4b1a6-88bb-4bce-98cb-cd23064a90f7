//+------------------------------------------------------------------+
//|                                                   AutoboxFib.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#define Name WindowExpertName()

input color TriggerColor = C'0,40,0'; //Trigger color
double midline;
input int days = 20;

// Fibonacci constants for optimization
const double FIB_236 = 0.236;
const double FIB_382 = 0.382;
const double FIB_50 = 0.5;

// Global variables
int hr; // Add this line to declare hr globally

// Market data cache structure
struct DayData {
    double high;
    double low;
    double dist;
    double u236, u382;
    double d236, d382;
    bool calculated;
};

// Static arrays and cache variables for optimization
static DayData dayCache[21];
static datetime lastCacheUpdate = 0;
static double rectPrices1[20];
static double rectPrices2[20];
static double fiboPrices1[20];
static double fiboPrices2[20];
static bool arraysInitialized = false;

// Object management for Phase 2 optimization
struct ObjectInfo {
    string name;
    bool exists;
    int type;
};

static ObjectInfo managedObjects[100];
static int objectCount = 0;
static bool objectPoolInitialized = false;

// REMOVED: Phase 3 optimization structures - restored original calculation logic

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   InitializeArrays();
   InvalidateCache();
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
	datetime expiry = D'2025.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("AutoBoxFib expired on " + TimeToStr(expiry, TIME_DATE));
		YesStop = true;
	}
	
	if (YesStop != true)
	{
      static datetime checkt1 = 0;
      bool check11 = false;
      if (checkt1 < iTime(_Symbol, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 240) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 290)))
      {
         checkt1 = iTime(_Symbol, PERIOD_H1, 0);
         check11 = true;
      }
      if (check11)
      {
         ObjectsDeleteAll(0, Name);
         ResetManagedObjects(); // Reset object management after mass deletion
         check11 = false;
      }
      
      static datetime checkt = 0;
      bool check1 = false;
      if (checkt < iTime(_Symbol, PERIOD_M5, 0))
      {
         checkt = iTime(_Symbol, PERIOD_M5, 0);
         check1 = true;
      }
      if (check1)
      {
         // REMOVED: Phase 3 optimization - restored original logic
         
         Rec();
         // Cleanup managed objects periodically
         CleanupManagedObjects();
         // Reset arrays for reuse
         ArrayInitialize(rectPrices1, 0);
         ArrayInitialize(rectPrices2, 0);
         ArrayInitialize(fiboPrices1, 0);
         ArrayInitialize(fiboPrices2, 0);
         int count = 0, counta = 0;
         string name;
         for (int x = ObjectsTotal(); x >= 0; x--)
         {
            name = ObjectName(x);
            if (ObjectType(name) == OBJ_RECTANGLE && ObjectGetInteger(0, name, OBJPROP_COLOR) == TriggerColor)
            { 
               if (count < 19) {
                  count++;
                  rectPrices1[count] = ObjectGet(name, OBJPROP_PRICE1);
                  rectPrices2[count] = ObjectGet(name, OBJPROP_PRICE2);
               }
            }
            if (ObjectType(name) == OBJ_FIBO)
            {
               if (counta < 19) {
                  counta++;
                  fiboPrices1[counta] = ObjectGet(name, OBJPROP_PRICE1);
                  fiboPrices2[counta] = ObjectGet(name, OBJPROP_PRICE2);
               }
            }
         }
         
         bool check = false;
         for (int x = 19; x >= 0; x--)
         {
            if (rectPrices1[x] != fiboPrices1[x] || rectPrices2[x] != fiboPrices2[x])
            {
               check = true;
               break;
            }
         }
         
         if (count != counta || check)
         {      
            Objectfind();
            check = false;
         }
      check1 = false;
      }
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	ResetManagedObjects(); // Reset object management on deinit
	EventKillTimer();
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void Rec(){
   string obname;
   hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10; // Update global hr instead of declaring local
   double high = 0, low = 0;
   midline = 0;
   
   // Invalidate cache if new day
   static datetime lastUpdate = 0;
   datetime currentDay = iTime(_Symbol, PERIOD_D1, 0);
   if (lastUpdate != currentDay) {
      InvalidateCache();
      lastUpdate = currentDay;
   }
   
   // Cache common datetime values
   datetime d0Time = iTime(_Symbol, PERIOD_D1, 0);
   datetime d1Time = iTime(_Symbol, PERIOD_D1, 1);
   int d0Shift = iBarShift(_Symbol, PERIOD_CURRENT, d0Time, false);
   int d1Shift = iBarShift(_Symbol, PERIOD_CURRENT, d1Time, false);
   
   for (int x = days; x >= 1; x--)
   {
      // Use cached data to avoid redundant market calls
      DayData data = GetDayData(x, hr);
      
      obname = Name + "xbox0" + IntegerToString(x);
      RecMake(obname, data.high, data.low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24), false), 0, C'40,40,40', "xbox0" + IntegerToString(x));
      obname = Name + "switch0" + IntegerToString(x);
      RecMake(obname, data.u236, data.u382, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 38), false), 0, C'79,79,0', "switch0" + IntegerToString(x));
      if (x > 2) ObjectSetInteger(0, obname, OBJPROP_BACK, false);
      obname = Name + "switch1" + IntegerToString(x);
      RecMake(obname, data.d236, data.d382, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 38), false), 0, C'100,0,17', "switch1" + IntegerToString(x));
      if (x > 2) ObjectSetInteger(0, obname, OBJPROP_BACK, false);
      if (x < 2)
      {
      obname = Name + "switch0" + IntegerToString(x);
      RecMake(obname, data.u236, data.u382, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 24), false), 0, C'79,79,0', "switch0" + IntegerToString(x));
      obname = Name + "switch1" + IntegerToString(x);
      RecMake(obname, data.d236, data.d382, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 24), false), 0, C'100,0,17', "switch1" + IntegerToString(x));
      }
   }

   if (hr >= 0)
   {
      high = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
      low = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
   }
   midline = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1))) / 2;
   
   obname = Name + "tbox";
   RecMake(obname, high, low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, TriggerColor, "tbox");
   obname = Name + "tboxlow";
   RecMake(obname, low, low - (high - low) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, C'79,21,0', "tboxlow");
   obname = Name + "tboxhigh";
   RecMake(obname, high, high + (high - low) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, C'9,51,47', "tboxhigh");
   obname = Name + "aFib";
   if (ObjectFind(0, Name + "aFib") <= 0) FibMake(obname, iTime(_Symbol, PERIOD_D1, 0), midline + (high - low), iTime(_Symbol, PERIOD_H1, 4), midline - (high - low), clrAqua);
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > midline && iLow(_Symbol, PERIOD_H1, 0) < midline)) { Alert("AutoFib " + _Symbol + " crossing midline. *WATCH*"); SendNotification("AutoFib " + _Symbol + " crossing midline. *WATCH*"); }
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > high && iLow(_Symbol, PERIOD_H1, 0) < midline)) { Alert("AutoFib " + _Symbol + " possible HIGH grab/trend. *WATCH*"); SendNotification("AutoFib " + _Symbol + " possible HIGH grab/trend. *WATCH*"); }
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > low && iLow(_Symbol, PERIOD_H1, 0) < low)) { Alert("AutoFib " + _Symbol + " possible LOW grab/trend. *WATCH*"); SendNotification("AutoFib " + _Symbol + " possible LOW grab/trend. *WATCH*"); }
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void Objectfind(){
   string obname;
   double price1 = 0;
   double price2 = 0;
   datetime time1 = 0;
   datetime time2 = 0;
   double midline2 = 0;
   hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10;

   string name;
   double pricemid = 0;

   for (int x = ObjectsTotal(); x >= 0; x--)
   {
      name = StringConcatenate(ObjectName(x));
      if (ObjectType(name) == OBJ_RECTANGLE && ObjectGetInteger(0, name, OBJPROP_COLOR) == TriggerColor && ObjectFind(0, Name + "Fib" + IntegerToString(x)) < 0)
         {
            double pricea = 0;
            double priceb = 0;

            // Cache repeated calculations for D-1 midpoint
            int lowestIdx = iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 24 + 1);
            int highestIdx = iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 24 + 1);
            double lowestPrice = iLow(_Symbol, PERIOD_H1, lowestIdx);
            double highestPrice = iHigh(_Symbol, PERIOD_H1, highestIdx);
            
            // Calculate D-1 midpoint
            pricea = lowestPrice + (highestPrice - lowestPrice) / 2;
            pricemid = pricea;
            
            // Calculate current day direction price - CORRECTED CALCULATION
            if (iClose(_Symbol, PERIOD_D1, 1) > iClose(_Symbol, PERIOD_D1, 2))
            {
               priceb = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
            }
            else if (iClose(_Symbol, PERIOD_D1, 1) < iClose(_Symbol, PERIOD_D1, 2))
            {
               priceb = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
            }

            time1 = iTime(_Symbol, PERIOD_D1, 1);
            time2 = iTime(_Symbol, PERIOD_D1, 0);

            // CORRECTED: Restore original conditional price assignment logic
            if (pricea > priceb) { price1 = pricea; price2 = priceb; }
            else { price1 = priceb; price2 = pricea; }

            obname = Name + "Fib" + IntegerToString(x);
            ObjectSetString(0, obname, OBJPROP_TEXT, "D-1");
            midline2 = (pricea + priceb) / 2;
         }
      else DeleteManagedObject(Name + "Fib" + IntegerToString(x));
   }
   
   obname = Name + " mds";
   RecMake(obname, midline, midline2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, C'40,40,40', "");
   
   obname = Name + " ymid";
   objtrend2(obname, pricemid, pricemid, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false), 0, 0, 3, STYLE_SOLID, C'100,100,100', "");   
   // CORRECTED: Restore original D-2, D-3, D-4 calculations
   double price3 = 0, price4 = 0;

   double pricen = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 48 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 48 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 48 + 1))) / 2;
   double priceo = 0;

   if (iClose(_Symbol, PERIOD_D1, 2) > iClose(_Symbol, PERIOD_D1, 3))
   priceo = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 24 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 2) < iClose(_Symbol, PERIOD_D1, 3))
   priceo = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 24 + hr + 1));

   if (pricen > priceo) { price3 = pricen; price4 = priceo; }
   else { price3 = priceo; price4 = pricen; }

   obname = Name + "Fibo";
   datetime time11 = iTime(_Symbol, PERIOD_D1, 2);
   datetime time22 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 48 + 1), false));
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-2");

   double highd2 = MathMax(priceo, pricen) + 2 * (price3 - price4);
   double lowd2 = MathMin(priceo, pricen) - 2 * MathAbs(price3 - price4);

   double price5 = 0, price6 = 0;

   double pricen1 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 72 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1))) / 2;
   double priceo1 = 0;

   if (iClose(_Symbol, PERIOD_D1, 3) > iClose(_Symbol, PERIOD_D1, 4))
   priceo1 = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 48 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 3) < iClose(_Symbol, PERIOD_D1, 4))
   priceo1 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 48 + hr + 1));

   if (pricen1 > priceo1) { price5 = pricen1; price6 = priceo1; }
   else { price5 = priceo1; price6 = pricen1; }

   obname = Name + "Fiboa";
   datetime time111 = iTime(_Symbol, PERIOD_D1, 3);
   datetime time222 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 72 + 1), false));
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-3");

   double highd3 = MathMax(pricen1, priceo1) + 2 * (price5 - price6);
   double lowd3 = MathMin(priceo1, pricen1) - 2 * MathAbs(price5 - price6);

   double price7 = 0, price8 = 0;

   double pricen2 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 96 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1))) / 2;
   double priceo2 = 0;

   if (iClose(_Symbol, PERIOD_D1, 4) > iClose(_Symbol, PERIOD_D1, 5))
   priceo2 = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 72 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 4) < iClose(_Symbol, PERIOD_D1, 5))
   priceo2 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 72 + hr + 1));

   if (pricen2 > priceo2) { price7 = pricen2; price8 = priceo2; }
   else { price7 = priceo2; price8 = pricen2; }

   obname = Name + "Fibob";
   datetime time1111 = iTime(_Symbol, PERIOD_D1, 4);
   datetime time2222 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 96 + 1), false));
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-4");

   double highd4 = MathMax(pricen2, priceo2) + 2 * (price7 - price8);
   double lowd4 = MathMin(priceo2, pricen2) - 2 * MathAbs(price7 - price8);

   double midd2 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 24 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 24 + 1))) / 2;
   obname = Name + "Bibo";
   //RecMake(obname, midd3, midd2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 34), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 24), false), 0, clrWhite, "");

   double midd4 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 72 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1))) / 2;
   obname = Name + "Biboa";
   //RecMake(obname, midd3, midd4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 58), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 48), false), 0, clrWhite, "");

   double midd5 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 96 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1))) / 2;
   obname = Name + "Bibob";
   //RecMake(obname, midd4, midd5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 82), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 72), false), 0, clrWhite, "");

   double midd6 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 120 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 120 + 1))) / 2;
   obname = Name + "Biboc";
   //RecMake(obname, midd6, midd5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 106), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 96), false), 0, clrWhite, "");
   
   obname = Name + "midline";
   objtrend2(obname, midline, midline, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   double high = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
   obname = Name + "highline";
   objtrend2(obname, high, high, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   double low = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
   obname = Name + "lowline";
   objtrend2(obname, low, low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   
   double ph[3];
   ArrayInitialize(ph, 0);
   double pl[3];
   ArrayInitialize(pl, 0);
   ph[0] = highd2; ph[1] = highd3; ph[2] = highd4;
   pl[0] = lowd2; pl[1] = lowd3; pl[2] = lowd4;
   
   bool phl[6];
   ArrayInitialize(phl, false);
   
   /*
   if (iClose(_Symbol, PERIOD_D1, 1) > highd4) { phl[2] = true; ph[2] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) > highd3) { phl[1] = true; ph[1] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) > highd2) { phl[0] = true; ph[0] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd4) { phl[5] = true; pl[2] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd3) { phl[4] = true; pl[1] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd2) { phl[3] = true; pl[0] = 0; }
   */
    
   int countph = 0;
   
   for (int x = 2; x >= 0; x--)
   {
      if (phl[x] == false) countph++;
   }
   
   double highprice1 = 0, lowprice1 = 0;
   double highprice2 = 0, lowprice2 = 0;
   double highprice3 = 0, lowprice3 = 0;
   
   if (countph == 3)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice1 = ph[0];
      lowprice1 = ph[2];
   }
   if (countph == 2)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice1 = ph[0];
      lowprice1 = ph[1];
   }
   if (countph == 1)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice3 = ph[0];
   }
   
   //countph = 0;
   int countpl = 0;
   
   for (int x = 2; x >= 0; x--)
   {
      if (phl[x + 3] == false) countpl++;
   }
   
   if (countpl == 3)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice2 = pl[0];
      lowprice2 = pl[2];
   }
   if (countpl == 2)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice2 = pl[0];
      lowprice2 = pl[1];
   }
   if (countpl == 1)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice3 = pl[0];
   }

   if (countph > 1)
   {
      obname = Name + "highbox";
      RecMake(obname, highprice1, lowprice1, 3, 0, 0, clrBlue, "");
   }
   if (countpl > 1)
   {
      obname = Name + "lowbox";
      RecMake(obname, highprice2, lowprice2, 3, 0, 0, clrRed, "");
   }
   if (countph == 1)
   {
      obname = Name + "highbox";
      objtrend2(obname, highprice3, highprice3, 3, 0, 0, 4, 0, clrBlue, "");
   }
   if (countpl == 1)
   {
      obname = Name + "lowbox";
      objtrend2(obname, lowprice3, lowprice3, 3, 0, 0, 4, 0, clrRed, "");
   }
   //if (_Symbol == "USDCHF") Print(ph[2] + " " + ph[1] + " " + ph[0] + " - " + highprice1 + " " + lowprice1 + " / " + highprice2 + " " + lowprice2);    
}

//+------------------------------------------------------------------+
//+CACHE FUNCTIONS FOR OPTIMIZATION----------------------------------+
DayData GetDayData(int dayOffset, int hri) {
    // Check if cache is valid and data already calculated
    if (dayCache[dayOffset].calculated && lastCacheUpdate == iTime(_Symbol, PERIOD_D1, 0)) {
        return dayCache[dayOffset];
    }
    
    // Calculate once and cache for reuse
    int shift = hri + dayOffset * 24 + 1;
    
    // Cache repeated calculations
    int highestBar = iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, shift);
    int lowestBar = iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, shift);
    
    double highPrice = iHigh(_Symbol, PERIOD_H1, highestBar);
    double lowPrice = iLow(_Symbol, PERIOD_H1, lowestBar);
    double distPrice = highPrice - lowPrice;
    
    // Store in cache
    dayCache[dayOffset].high = highPrice;
    dayCache[dayOffset].low = lowPrice;
    dayCache[dayOffset].dist = distPrice;
    dayCache[dayOffset].u236 = highPrice - FIB_236 * distPrice;
    dayCache[dayOffset].u382 = highPrice - FIB_382 * distPrice;
    dayCache[dayOffset].d236 = lowPrice + FIB_236 * distPrice;
    dayCache[dayOffset].d382 = lowPrice + FIB_382 * distPrice;
    dayCache[dayOffset].calculated = true;
    
    return dayCache[dayOffset];
}

void InitializeArrays() {
    if (!arraysInitialized) {
        ArrayInitialize(rectPrices1, 0);
        ArrayInitialize(rectPrices2, 0);
        ArrayInitialize(fiboPrices1, 0);
        ArrayInitialize(fiboPrices2, 0);
        
        // Initialize struct array manually
        for (int i = 0; i < 21; i++) {
            dayCache[i].high = 0;
            dayCache[i].low = 0;
            dayCache[i].dist = 0;
            dayCache[i].u236 = 0;
            dayCache[i].u382 = 0;
            dayCache[i].d236 = 0;
            dayCache[i].d382 = 0;
            dayCache[i].calculated = false;
        }
        arraysInitialized = true;
    }
    
    // Initialize object pool
    if (!objectPoolInitialized) {
        for (int i = 0; i < 100; i++) {
            managedObjects[i].name = "";
            managedObjects[i].exists = false;
            managedObjects[i].type = 0;
        }
        objectCount = 0;
        objectPoolInitialized = true;
    }
    
    // REMOVED: Phase 3 optimization - restored original logic
}

void InvalidateCache() {
    for (int i = 0; i < 21; i++) {
        dayCache[i].calculated = false;
    }
    lastCacheUpdate = iTime(_Symbol, PERIOD_D1, 0);
}

//+------------------------------------------------------------------+

//+OPTIMIZED FIBMAKE FUNCTIONS---------------------------------------+
bool FibMake(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
    // Try to reuse existing object first
    if (ObjectFind(0, name) >= 0) {
        // Object exists, just update properties
        ObjectSetDouble(0, name, OBJPROP_PRICE1, price1);
        ObjectSetDouble(0, name, OBJPROP_PRICE2, price2);
        ObjectSetInteger(0, name, OBJPROP_TIME1, time1);
        ObjectSetInteger(0, name, OBJPROP_TIME2, time2);
        ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
        return true;
    }
    
    // Create new object
	if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
	{
		Print(__FUNCTION__,
			": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
		return (false);
	}
		
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 27);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	double levels[17] = { -3, -2.5, -2, -1.5, -1, -0.5, 0, 0.25, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 3.5, 4 };
	string levell[17] = { "-4 - %$", "-3.5 - %$", "-3 - %$", "-2.5 - %$","-2 - %$", "-1.5 - %$", "0 - %$", "0.25 - %$", "0.5 - %$", "0.75 - %$", "1 - %$", "1.5 - %$", "2 - %$", "2.5 - %$", "3 - %$", "3.5 - %$", "4 - %$" };

	for (int x = 0; x <= 16; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	
	RegisterManagedObject(name, OBJ_FIBO);
	return(true);
}
//+------------------------------------------------------------------+

//+OPTIMIZED SUP/RES FUNCTION----------------------------------------+
void objtrend2(string name,
               double pr1,
               double pr2,
               int t1,
               int t2,
               int t3,
               int wi,
               int st,
               color col,
               string tett)
{
    // FIXED: Simplified to avoid circular calls - directly create/update trend line
    if (ObjectFind(0, name) < 0) {
        if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
        {
            Print("error: can't create label_object! code #", GetLastError());
            return;
        }
    }

    datetime Trime[];
    ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
    CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
    ArraySetAsSeries(Trime, true);

    ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
    ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
    ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
    ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
    ObjectSetInteger(0, name, OBJPROP_STYLE, st);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
    ObjectSetInteger(0, name, OBJPROP_RAY, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetInteger(0, name, OBJPROP_COLOR, col);
    ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, name, OBJPROP_READONLY, true);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);

    RegisterManagedObject(name, OBJ_TREND);
}
//+------------------------------------------------------------------+

//+FIBMAKE-----------------------------------------------------------+
bool FibMake2(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
    // Try to reuse existing object first
    if (ObjectFind(0, name) >= 0) {
        // Object exists, just update properties
        ObjectSetDouble(0, name, OBJPROP_PRICE1, price1);
        ObjectSetDouble(0, name, OBJPROP_PRICE2, price2);
        ObjectSetInteger(0, name, OBJPROP_TIME1, time1);
        ObjectSetInteger(0, name, OBJPROP_TIME2, time2);
        ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
        return true;
    }
    
    // Create new object
	if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
	{
		Print(__FUNCTION__,
			": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
		return (false);
	}
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 6);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	//if (ChartPeriod() <= 15) ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	double levels[8] = { -3, -2, -1, 2, 3, 4 };
	string levell[8] = { "-4 - %$", "-3 - %$", "-2 - %$", "2 - %$", "3 - %$", "4 - %$" };
   //double levels[8] = { -3, -2, -1.5, -1, 2, 2.5, 3, 4 };
	//string levell[8] = { "-4 - %$", "-3 - %$", "-2.5 - %$", "-2 - %$", "2 - %$", "2.5 - %$", "3 - %$", "4 - %$" };

	for (int x = 0; x <= 7; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	
	RegisterManagedObject(name, OBJ_FIBO);
	return(true);
}
//+------------------------------------------------------------------+

//+SIMPLIFIED RECMAKE FUNCTION (FIXED)----------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
    // FIXED: Simplified to avoid complex optimization logic that might cause issues
    if (ObjectFind(0, name) < 0) {
        if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
        {
            Print("error: can't create rectangle_object! code #", GetLastError());
            return;
        }
    }

    ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
    ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
    ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
    ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
    ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, name, OBJPROP_BACK, true);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, name, OBJPROP_READONLY, true);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);

    // Register object for management
    RegisterManagedObject(name, OBJ_RECTANGLE);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+
//+OBJECT MANAGEMENT FOR PHASE 2 OPTIMIZATION-----------------------+
bool UpdateOrCreateRectangle(string name, double price1, double price2, int t1, int t2, int t3, color col, string tooltip) {
    if (ObjectFind(0, name) >= 0) {
        // Object exists, just update properties
        ObjectSetDouble(0, name, OBJPROP_PRICE1, price1);
        ObjectSetDouble(0, name, OBJPROP_PRICE2, price2);
        ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
        ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
        ObjectSetInteger(0, name, OBJPROP_COLOR, col);
        ObjectSetString(0, name, OBJPROP_TOOLTIP, tooltip);
        // Ensure all rectangle properties are set correctly
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, name, OBJPROP_BACK, true);
        ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(0, name, OBJPROP_READONLY, true);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
        return true;
    } else {
        // Create new object and set all properties like the original RecMake
        if (ObjectCreate(0, name, OBJ_RECTANGLE, 0, Time[t1], price1, Time[t2] + t3, price2)) {
            ObjectSetInteger(0, name, OBJPROP_COLOR, col);
            ObjectSetDouble(0, name, OBJPROP_PRICE1, price1);
            ObjectSetDouble(0, name, OBJPROP_PRICE2, price2);
            ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
            ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
            ObjectSetInteger(0, name, OBJPROP_BACK, true);
            ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
            ObjectSetInteger(0, name, OBJPROP_READONLY, true);
            ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
            ObjectSetString(0, name, OBJPROP_TOOLTIP, tooltip);
            return true;
        }
        return false;
    }
}

bool UpdateOrCreateTrendLine(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett) {
    if (ObjectFind(0, name) >= 0) {
        // Object exists, just update properties
        datetime Trime[];
        ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
        CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
        ArraySetAsSeries(Trime, true);

        ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
        ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
        ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
        ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
        ObjectSetInteger(0, name, OBJPROP_COLOR, col);
        ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
        return true;
    } else {
        // FIXED: Create new object directly instead of calling objtrend2 (prevents infinite loop)
        if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0)) {
            Print("error: can't create trend_object! code #", GetLastError());
            return false;
        }

        datetime Trime[];
        ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
        CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
        ArraySetAsSeries(Trime, true);

        ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
        ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
        ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
        ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
        ObjectSetInteger(0, name, OBJPROP_STYLE, st);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
        ObjectSetInteger(0, name, OBJPROP_RAY, false);
        ObjectSetInteger(0, name, OBJPROP_BACK, false);
        ObjectSetInteger(0, name, OBJPROP_COLOR, col);
        ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
        ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(0, name, OBJPROP_READONLY, true);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);

        RegisterManagedObject(name, OBJ_TREND);
        return true;
    }
}

void RegisterManagedObject(string name, int objType) {
    // Check if object already exists in management array
    for (int i = 0; i < objectCount; i++) {
        if (managedObjects[i].name == name) {
            managedObjects[i].exists = true;
            managedObjects[i].type = objType;
            return; // Object already registered
        }
    }
    
    // Add new object if not found
    if (objectCount < 99) {
        managedObjects[objectCount].name = name;
        managedObjects[objectCount].exists = true;
        managedObjects[objectCount].type = objType;
        objectCount++;
    }
}

void CleanupManagedObjects() {
    // More efficient cleanup - only check objects marked as existing
    int validCount = 0;
    
    for (int i = 0; i < objectCount; i++) {
        if (managedObjects[i].exists) {
            if (ObjectFind(0, managedObjects[i].name) < 0) {
                managedObjects[i].exists = false;
            } else {
                // Compact the array by moving valid objects to the front
                if (i != validCount) {
                    managedObjects[validCount] = managedObjects[i];
                }
                validCount++;
            }
        }
    }
    
    // Update object count to reflect only valid objects
    objectCount = validCount;
}

void DeleteManagedObject(string name) {
    // Remove from object management array
    for (int i = 0; i < objectCount; i++) {
        if (managedObjects[i].name == name) {
            managedObjects[i].exists = false;
            break;
        }
    }
    
    // Delete the actual object
    ObjectDelete(0, name);
}

void ResetManagedObjects() {
    // Reset object management when ObjectsDeleteAll is called
    for (int i = 0; i < objectCount; i++) {
        managedObjects[i].exists = false;
    }
    objectCount = 0;
}
// REMOVED: CalculateDayPrices() function - restored original calculation logic in Objectfind()

// REMOVED: Phase 3 optimization functions - restored original calculation logic

//+------------------------------------------------------------------+
//+PHASE 3: BATCH OBJECT OPTIMIZATION-----------------------------------+
// Batch update function to reduce individual ObjectSet calls
void BatchUpdateObjects() {
    // This function can be called to update multiple objects efficiently
    // Currently integrated into the existing functions, but could be expanded
    // for further optimization if needed
}

// Phase 3: Optimized rectangle creation with pre-allocated pools
bool CreateOptimizedRectangle(string name, double price1, double price2, int t1, int t2, int t3, color col, string tooltip, bool setBack = true) {
    if (ObjectFind(0, name) >= 0) {
        // Object exists, batch update all properties at once
        ObjectSetDouble(0, name, OBJPROP_PRICE1, price1);
        ObjectSetDouble(0, name, OBJPROP_PRICE2, price2);
        ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
        ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
        ObjectSetInteger(0, name, OBJPROP_COLOR, col);
        ObjectSetString(0, name, OBJPROP_TOOLTIP, tooltip);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, name, OBJPROP_BACK, setBack);
        ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(0, name, OBJPROP_READONLY, true);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
        return true;
    } else if (ObjectCreate(0, name, OBJ_RECTANGLE, 0, Time[t1], price1, Time[t2] + t3, price2)) {
        // New object created, set all properties efficiently
        ObjectSetInteger(0, name, OBJPROP_COLOR, col);
        ObjectSetString(0, name, OBJPROP_TOOLTIP, tooltip);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, name, OBJPROP_BACK, setBack);
        ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(0, name, OBJPROP_READONLY, true);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
        RegisterManagedObject(name, OBJ_RECTANGLE);
        return true;
    }
    return false;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Helper function for day level calculations                        |
//+------------------------------------------------------------------+
void CalculateDayLevels(int dayOffset, string objName, string label, double &highLevel, double &lowLevel) {
   int hourOffset = hr + (24 * dayOffset) + 1;
   int prevHourOffset = hr + (24 * (dayOffset-1)) + 1;
   
   // Cache repeated calculations
   int lowestIdx = iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hourOffset);
   int highestIdx = iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hourOffset);
   double lowestPrice = iLow(_Symbol, PERIOD_H1, lowestIdx);
   double highestPrice = iHigh(_Symbol, PERIOD_H1, highestIdx);
   
   double midPrice = lowestPrice + (highestPrice - lowestPrice) / 2;
   double dirPrice = 0;
   
   if (iClose(_Symbol, PERIOD_D1, dayOffset-1) > iClose(_Symbol, PERIOD_D1, dayOffset))
      dirPrice = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, prevHourOffset));
   else if (iClose(_Symbol, PERIOD_D1, dayOffset-1) < iClose(_Symbol, PERIOD_D1, dayOffset))
      dirPrice = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, prevHourOffset));
   
   double price1, price2;
   if (midPrice > dirPrice) { price1 = midPrice; price2 = dirPrice; }
   else { price1 = dirPrice; price2 = midPrice; }
   
   // Set object properties
   ObjectSetString(0, objName, OBJPROP_TEXT, label);
   
   // Calculate high and low levels
   highLevel = MathMax(midPrice, dirPrice) + 2 * (price1 - price2);
   lowLevel = MathMin(dirPrice, midPrice) - 2 * MathAbs(price1 - price2);
   
   // Register the object for management
   RegisterManagedObject(objName, OBJ_FIBO);
}
//+------------------------------------------------------------------+