//+------------------------------------------------------------------+
//|                                           Gapfinder.mq4          |
//|                                           Copyright 2018, Sakis  |
//|                                                                  |
//+------------------------------------------------------------------+

// Version History
// ---------------
// v1.0 Made initial finder
// v1.1 Multiple changes, fixes etc.

#property indicator_chart_window
#property link "https://www.forexfactory.com/sakisf"

#define Name WindowExpertName()
#property strict

extern int periods = 7200;					//How many bars to check
input int mingap = 2; //Minimum gap * Point
extern ENUM_TIMEFRAMES DROP_TF = PERIOD_M1; //Which TF to check
input string a = "";

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
	//---
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &HD1h[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	bool new_1m_check = false;
	static datetime start_1m_time = 0;
	if (start_1m_time < iTime(NULL, DROP_TF, 0))
	{
		new_1m_check = true;
		start_1m_time = iTime(NULL, DROP_TF, 0);
	}
	if (new_1m_check && ChartPeriod() <= 60)
	{
		Gapfinder();
		new_1m_check = false;
	}
	return (rates_total);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN ROUTINE------------------------------------------------------+
void Gapfinder()
{
	if (Bars(_Symbol, PERIOD_M1) < periods)
		periods = Bars(_Symbol, PERIOD_M1) - 2;

	double Pip;
	if (MarketInfo(_Symbol, MODE_MARGINCALCMODE) == 0 && _Digits >= 3 && (StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == -1) && (StringFind(StringSubstr(_Symbol, 0, 1), "D", 0) == -1))
		Pip = (_Point * MathPow(10, MathMod(_Digits, 2)));
	else if (_Digits == 3 && StringFind(StringSubstr(_Symbol, 0, 1), "X", 0) == 0)
		Pip = 0.1;
	//else if ((MarketInfo(_Symbol, MODE_MARGINCALCMODE) == 0 && _Digits == 2)) Pip = 1;
	else
		Pip = 1;

	string obname;

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 1);
	ArrayResize(OD1, periods + 1);
	ArrayResize(HD1, periods + 1);
	ArrayResize(LD1, periods + 1);
	ArrayResize(TD1, periods + 1);
	CopyClose(_Symbol, DROP_TF, 0, periods + 1, CD1);
	CopyOpen(_Symbol, DROP_TF, 0, periods + 1, OD1);
	CopyHigh(_Symbol, DROP_TF, 0, periods + 1, HD1);
	CopyLow(_Symbol, DROP_TF, 0, periods + 1, LD1);
	CopyTime(_Symbol, DROP_TF, 0, periods + 1, TD1);

	double upper[], lower[];
	double zup[], zdn[];
	ArrayResize(upper, periods + 1);
	ArrayResize(lower, periods + 1);
	ArrayResize(zup, periods + 1);
	ArrayResize(zdn, periods + 1);
	
	ObjectsDeleteAll(0, Name);

	for (int i = periods - 1; i >= 1; i--)
	{
		if ((OD1[i] - CD1[i + 1] >= mingap * _Point) && LD1[i] > CD1[i + 1] && OD1[i] > CD1[i + 1])
		{
			obname = Name + "ArrD" + IntegerToString(i);
			burnarr(obname, OD1[i], 234, iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false), clrYellow);
			obname = Name + "LinD" + IntegerToString(i);
			objtrend(obname, CD1[i + 1], iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false), clrYellow, DoubleToStr((CD1[i] - OD1[i + 1]) / Pip, 2));
			obname = Name + "LabD" + IntegerToString(i);
			objtext(obname, CD1[i + 1], (OD1[i] - CD1[i + 1]) / Pip, clrYellow, DoubleToStr((OD1[i + 1] - CD1[i]) / Pip, 2), iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false));
			lower[i] = CD1[i + 1];
		}
		else
		{
			//ObjectDelete(Name + "ArrD" + IntegerToString(i));
			//ObjectDelete(Name + "LinD" + IntegerToString(i));
			//ObjectDelete(Name + "LabD" + IntegerToString(i));
			lower[i] = EMPTY_VALUE;
		}
		if ((CD1[i + 1] - OD1[i] >= mingap * _Point) && HD1[i] < CD1[i + 1] && CD1[i + 1] > OD1[i])
		{
			obname = Name + "ArrU" + IntegerToString(i);
			burnarr(obname, OD1[i], 233, iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false), clrLime);
			obname = Name + "LinU" + IntegerToString(i);
			objtrend(obname, CD1[i + 1], iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false), clrLime, DoubleToStr((CD1[i + 1] - OD1[i]) / Pip, 2));
			obname = Name + "LabU" + IntegerToString(i);
			objtext(obname, CD1[i + 1], (CD1[i + 1] - OD1[i]) / Pip, clrLime, DoubleToStr((CD1[i + 1] - OD1[i]) / Pip, 2), iBarShift(_Symbol, PERIOD_CURRENT, TD1[i], false));
			upper[i] = CD1[i + 1];
		}
		else
		{
			//ObjectDelete(Name + "ArrU" + IntegerToString(i));
			//ObjectDelete(Name + "LinU" + IntegerToString(i));
			//ObjectDelete(Name + "LabU" + IntegerToString(i));
			upper[i] = EMPTY_VALUE;
		}
	}

	// For gaps below
	// If ( open of candle > close of previous candle by point and low of candle - close previous candle by point ) THEN draw arrow at open of candle with gap, draw line at close of previous candle, put text of difference between low of candle and close of previous candle THEN add the value of previous close to buffer lower OTHERWISE leave buffer lower empty - ELSE will delete ghosts due to obname, do not remove

	// For gaps above
	// If ( open of candle < close of previous candle by point and close of previous candle - HD1h of candle by point ) THEN draw arrow at open of candle with gap, draw line at close of previous candle, put text of difference between HD1h of candle and close of previous candle THEN add the value of previous close to buffer upper OTHERWISE leave buffer lower empty - ELSE will delete ghosts due to obname, do not remove

	for (int x = periods - 1; x >= 1; x--)
	{
		zdn[x] = LD1[ArrayMinimum(LD1, x, 1)];
      zup[x] = HD1[ArrayMaximum(HD1, x, 1)];
		
		if (lower[x + 1] >= zdn[x])
		{
			ObjectDelete(Name + "ArrD" + IntegerToString(x + 1));
			ObjectDelete(Name + "LinD" + IntegerToString(x + 1));
			ObjectDelete(Name + "LabD" + IntegerToString(x + 1));
		}
		if (upper[x + 1] <= zup[x])
		{
			ObjectDelete(Name + "ArrU" + IntegerToString(x + 1));
			ObjectDelete(Name + "LinU" + IntegerToString(x + 1));
			ObjectDelete(Name + "LabU" + IntegerToString(x + 1));
		}
	}

	// if ( previous lower buffer[close price that has a gap up] price is bigger than a newer lower low (i.e. was covered), then delete all corresponding arrows and lines and labels)
	// GAP UP PRICE < LOWER LOW, THE GAP STAYS

	// if ( previous upper buffer[close price that has a gap down] price is lower than a newer HD1her HD1h (i.e. was covered), then delete all corresponding arrows and lines and labels)
	// GAP DOWN PRICE > HD1HER HD1H, THE GAP STAYS

	//Backup (mistake 2nd)
	//if((lower[x+1]<zup[x] && lower[x+1]>zdn[x])) { ObjectDelete(Name+"ArrD"+IntegerToString(x+1)); ObjectDelete(Name+"LinD"+IntegerToString(x+1)); ObjectDelete(Name+"LabD"+IntegerToString(x+1)); }
	//if((upper[x+1]<zup[x] && lower[x+1]>zdn[x])) { ObjectDelete(Name+"ArrU"+IntegerToString(x+1)); ObjectDelete(Name+"LinU"+IntegerToString(x+1)); ObjectDelete(Name+"LabU"+IntegerToString(x+1)); }
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+

//+TL CREATE---------------------------------------------------------+
void objtrend(string name, double pr1, int t, color col, string buls)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_TIME2, Time[0] + 4 * Period() * 60);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr1);
	ObjectSet(name, OBJPROP_STYLE, 0);
	ObjectSet(name, OBJPROP_WIDTH, 2);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t], TIME_DATE | TIME_MINUTES) + " " + buls);
}
//+------------------------------------------------------------------+

//+LABELMAKE (AT PRICE)----------------------------------------------+
void objtext(string name, double pr1, double diff, color col, string buls, int t)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetString(0, name, OBJPROP_TEXT, DoubleToStr(diff, 2));
	//ObjectSetString(0, name, OBJPROP_PRICE1, pr1);
	//ObjectSetString(0, name, OBJPROP_TIME1, Time[0]+60*65);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetDouble(0, name, OBJPROP_ANGLE, 0);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectMove(0, name, 0, Time[0] + 4 * Period() * 65, pr1);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t], TIME_DATE | TIME_MINUTES) + " " + buls);
}
//+------------------------------------------------------------------+