//+------------------------------------------------------------------+
//|                                                     deezcrap.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window

#property indicator_buffers 6

double black[], blue[], red[], squawk[], green[], orange[];
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
  IndicatorBuffers(4);
//--- indicator buffers mapping
   IndicatorShortName("DN");
   SetIndexBuffer(0, black);
   SetIndexStyle(0, DRAW_NONE, STYLE_SOLID, 3, clrBlack);
   SetIndexLabel(0, "");
   SetIndexBuffer(1, squawk);
   SetIndexStyle(1, DRAW_NONE, STYLE_SOLID, 3, clrBlack);
   SetIndexLabel(1, "");
   SetIndexBuffer(2, blue);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 3, clrGreen);
   SetIndexLabel(2, "");
   SetIndexBuffer(3, red);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 3, clrOrange);
   SetIndexLabel(3, "");
   SetIndexBuffer(4, green);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 3, C'0,70,0');
   SetIndexLabel(4, "");
   SetIndexBuffer(5, orange);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 3, C'113,73,0');
   SetIndexLabel(5, "");
   //SetIndexEmptyValue(0, 0.0);
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   int start = prev_calculated;
   if(start < 1)
      start = 1;  // start from the first bar
   double hup[];
   ArrayResize(hup, Bars);
   double ldn[];
   ArrayResize(ldn, Bars);
   double up[];
   ArrayResize(up, Bars);
   double dn[];
   ArrayResize(dn, Bars);
   double md[];
   ArrayResize(md, Bars);
   // iterate through bars
   for(int i = rates_total - 27; i >= 0; i--)
   {
      up[i] = high[iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, i + 6)];
      dn[i] = low[iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, i + 6)];
      double dist = up[i] - dn[i];
      hup[i] = up[i] - 0.236 * dist;
      ldn[i] = dn[i] + 0.236 * dist; 
      md[i] = dn[i] + dist/2;
      //black[1000] = 0.0;
      if (close[i] > md[i]) black[i] = black[i + 1] + 0.001;
      else if (close[i] < md[i]) black[i] = black[i + 1] - 0.001;
   }
   
   for(int i = 0; i <= rates_total - 127; i++)
   {
      squawk[i] = iMAOnArray(black, 0, 6, 0, MODE_LWMA, i);// + iStochastic(_Symbol, PERIOD_CURRENT, 8, 5, 3, MODE_EMA, 0, MODE_MAIN, i)/1000;
      if (close[i] > md[i]) blue[i] = squawk[i];
      if (close[i] > hup[i]) green[i] = squawk[i];
      if (close[i] < md[i]) red[i] = squawk[i];
      if (close[i] < ldn[i]) orange[i] = squawk[i];
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
