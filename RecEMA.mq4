//+------------------------------------------------------------------+
//|                                                            R.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_separate_window
#property indicator_buffers 17

#resource "\\Files\\explosion.wav"

double rec[];
double emarec1[], emarec2[], emarec3[], emarec4[], emarec5[], emarec6[], emarec7[], emarec8[], emarec9[], emarec0[], emarec10[], emarec11[], emarec12[], emarec13[];
double redrec[];
double greenrec[];

input int ema1 = 3; //EMA
input int ema2 = 5; //EMA
input int ema3 = 8; //EMA
input int ema4 = 13; //EMA
input int ema5 = 21; //EMA
input int ema6 = 34; //EMA
input int ema7 = 55; //EMA
input int ema8 = 89; //EMA
input int ema9 = 144; //EMA
input int ema0 = 233; //EMA
input int ema10 = 377; //EMA
input int ema11 = 610; //EMA
input int ema12 = 987; //EMA
input int ema13 = 1597; //EMA
input double threshold = 200; //$$ amount to check above for rec
input double multiplier = 1.8; //multiplier ema

#define Name MQLInfoString(MQL_PROGRAM_NAME)
input string prefix = ""; //Broker prefix
input string suffix = ""; //Broker suffix

static const string eu_p = prefix + "EURUSD" + suffix;
static const string eg_p = prefix + "EURGBP" + suffix;
static const string ea_p = prefix + "EURAUD" + suffix;
static const string en_p = prefix + "EURNZD" + suffix;
static const string ec_p = prefix + "EURCAD" + suffix;
static const string ef_p = prefix + "EURCHF" + suffix;
static const string ej_p = prefix + "EURJPY" + suffix;
static const string gu_p = prefix + "GBPUSD" + suffix;
static const string ga_p = prefix + "GBPAUD" + suffix;
static const string gn_p = prefix + "GBPNZD" + suffix;
static const string gc_p = prefix + "GBPCAD" + suffix;
static const string gf_p = prefix + "GBPCHF" + suffix;
static const string gj_p = prefix + "GBPJPY" + suffix;
static const string au_p = prefix + "AUDUSD" + suffix;
static const string an_p = prefix + "AUDNZD" + suffix;
static const string ac_p = prefix + "AUDCAD" + suffix;
static const string af_p = prefix + "AUDCHF" + suffix;
static const string aj_p = prefix + "AUDJPY" + suffix;
static const string nu_p = prefix + "NZDUSD" + suffix;
static const string nc_p = prefix + "NZDCAD" + suffix;
static const string nf_p = prefix + "NZDCHF" + suffix;
static const string nj_p = prefix + "NZDJPY" + suffix;
static const string uc_p = prefix + "USDCAD" + suffix;
static const string cf_p = prefix + "CADCHF" + suffix;
static const string cj_p = prefix + "CADJPY" + suffix;
static const string uf_p = prefix + "USDCHF" + suffix;
static const string fj_p = prefix + "CHFJPY" + suffix;
static const string uj_p = prefix + "USDJPY" + suffix;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

   SetIndexBuffer(0, rec);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 3, clrWhite);
   SetIndexLabel(0, "");
   SetIndexBuffer(1, greenrec);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 3, clrLimeGreen);
   SetIndexLabel(1, "");
   SetIndexBuffer(2, redrec);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 3, clrRed);
   SetIndexLabel(2, "");
   SetIndexBuffer(3, emarec1);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(3, "");
   SetIndexBuffer(4, emarec2);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(4, "");
   SetIndexBuffer(5, emarec3);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(5, "");
   SetIndexBuffer(6, emarec4);
   SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(6, "");
   SetIndexBuffer(7, emarec5);
   SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(7, "");
   SetIndexBuffer(8, emarec6);
   SetIndexStyle(8, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(8, "");
   SetIndexBuffer(9, emarec7);
   SetIndexStyle(9, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(9, "");
   SetIndexBuffer(10, emarec8);
   SetIndexStyle(10, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(10, "");
   SetIndexBuffer(11, emarec9);
   SetIndexStyle(11, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(11, "");
   SetIndexBuffer(12, emarec0);
   SetIndexStyle(12, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(12, "");
   SetIndexBuffer(13, emarec10);
   SetIndexStyle(13, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(13, "");
   SetIndexBuffer(14, emarec11);
   SetIndexStyle(14, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(14, "");
   SetIndexBuffer(15, emarec12);
   SetIndexStyle(15, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(15, "");
   SetIndexBuffer(16, emarec13);
   SetIndexStyle(16, DRAW_LINE, STYLE_SOLID, 1, clrMagenta);
   SetIndexLabel(16, "");
   
   IndicatorSetInteger(INDICATOR_LEVELS, 2);
   IndicatorSetInteger(INDICATOR_LEVELSTYLE, 0);
   IndicatorSetInteger(INDICATOR_LEVELWIDTH, 2);
   //IndicatorSetInteger(INDICATOR_LEVELCOLOR, 0, clrGray);
   IndicatorSetInteger(INDICATOR_LEVELCOLOR, 0, clrLime);
   //IndicatorSetInteger(INDICATOR_LEVELCOLOR, 2, clrDodgerBlue);
   //IndicatorSetInteger(INDICATOR_LEVELCOLOR, 3, clrLime);
   //IndicatorSetInteger(INDICATOR_LEVELCOLOR, 4, clrWhite);
   //IndicatorSetInteger(INDICATOR_LEVELCOLOR, 5, clrRed);
   IndicatorSetInteger(INDICATOR_LEVELCOLOR, 1, clrRed);
   //IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, 2500);
   IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, 4000);
   //IndicatorSetDouble(INDICATOR_LEVELVALUE, 2, 5000);
   //IndicatorSetDouble(INDICATOR_LEVELVALUE, 3, 6000);
   //IndicatorSetDouble(INDICATOR_LEVELVALUE, 4, 8000);
   //IndicatorSetDouble(INDICATOR_LEVELVALUE, 5, 10000);
   IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, 12000);
   //IndicatorSetString(INDICATOR_LEVELTEXT, 0, "START");
   IndicatorSetString(INDICATOR_LEVELTEXT, 0, "ENTRY");
   //IndicatorSetString(INDICATOR_LEVELTEXT, 2, "MID-ENTRY");
   //IndicatorSetString(INDICATOR_LEVELTEXT, 3, "ENTRY");
   //IndicatorSetString(INDICATOR_LEVELTEXT, 4, "END-ENTRY");
   //IndicatorSetString(INDICATOR_LEVELTEXT, 5, "WATCH");
   IndicatorSetString(INDICATOR_LEVELTEXT, 1, "STOP");
   
   //---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
      static datetime check = 0;
      bool check1 = false;
      if (check < iTime(_Symbol, PERIOD_M1, 0))
      {
         check = iTime(_Symbol, PERIOD_M1, 0);
         check1 = true;
      }
      if (check1)
      {
         ArrayInitialize(rec, 0);
         ArrayInitialize(redrec, EMPTY_VALUE);
         ArrayInitialize(greenrec, EMPTY_VALUE);
         csstuff();
         check1 = false;
      }
      csstuff0();
//--- return value of prev_calculated for next call
   return(rates_total);
  }
  
//+MAIN CS-----------------------------------------------------------+
void csstuff()
{
   //uint start1 = GetTickCount();
   // 0 = EUR
   // 1 = GBP
   // 2 = AUD
   // 3 = NZD
   // 4 = CAD
   // 5 = USD
   // 6 = CHF
   // 7 = USD
      
   int count = iBarShift(_Symbol, PERIOD_M1, iTime(_Symbol, PERIOD_D1, 0), false) + 1;   
      
   double euCC[], egCC[], eaCC[], enCC[], ecCC[], efCC[], ejCC[];
   double guCC[], gaCC[], gnCC[], gcCC[], gfCC[], gjCC[];
   double auCC[], anCC[], acCC[], afCC[], ajCC[];
   double nuCC[], ncCC[], nfCC[], njCC[];
   double ucCC[], cfCC[], cjCC[];
   double ufCC[], fjCC[];
   double ujCC[];
   double euMC[], egMC[], eaMC[], enMC[], ecMC[], efMC[], ejMC[];
   double guMC[], gaMC[], gnMC[], gcMC[], gfMC[], gjMC[];
   double auMC[], anMC[], acMC[], afMC[], ajMC[];
   double nuMC[], ncMC[], nfMC[], njMC[];
   double ucMC[], cfMC[], cjMC[];
   double ufMC[], fjMC[];
   double ujMC[];

   ArrayResize(euCC, 2);
   CopyClose(eu_p, PERIOD_D1, 0, 2, euCC);
   ArraySetAsSeries(euCC, true);
   
   ArrayResize(egCC, 2);
   CopyClose(eg_p, PERIOD_D1, 0, 2, egCC);
   ArraySetAsSeries(egCC, true);
   
   ArrayResize(eaCC, 2);
   CopyClose(ea_p, PERIOD_D1, 0, 2, eaCC);
   ArraySetAsSeries(eaCC, true);
   
   ArrayResize(enCC, 2);
   CopyClose(en_p, PERIOD_D1, 0, 2, enCC);
   ArraySetAsSeries(enCC, true);
   
   ArrayResize(ecCC, 2);
   CopyClose(ec_p, PERIOD_D1, 0, 2, ecCC);
   ArraySetAsSeries(ecCC, true);
   
   ArrayResize(efCC, 2);
   CopyClose(ef_p, PERIOD_D1, 0, 2, efCC);
   ArraySetAsSeries(efCC, true);
   
   ArrayResize(ejCC, 2);
   CopyClose(ej_p, PERIOD_D1, 0, 2, ejCC);
   ArraySetAsSeries(ejCC, true);
   
   ArrayResize(guCC, 2);
   CopyClose(gu_p, PERIOD_D1, 0, 2, guCC);
   ArraySetAsSeries(guCC, true);
   
   ArrayResize(gaCC, 2);
   CopyClose(ga_p, PERIOD_D1, 0, 2, gaCC);
   ArraySetAsSeries(gaCC, true);
   
   ArrayResize(gnCC, 2);
   CopyClose(gn_p, PERIOD_D1, 0, 2, gnCC);
   ArraySetAsSeries(gnCC, true);
   
   ArrayResize(gcCC, 2);
   CopyClose(gc_p, PERIOD_D1, 0, 2, gcCC);
   ArraySetAsSeries(gcCC, true);
   
   ArrayResize(gfCC, 2);
   CopyClose(gf_p, PERIOD_D1, 0, 2, gfCC);
   ArraySetAsSeries(gfCC, true);
   
   ArrayResize(gjCC, 2);
   CopyClose(gj_p, PERIOD_D1, 0, 2, gjCC);
   ArraySetAsSeries(gjCC, true);
   
   ArrayResize(auCC, 2);
   CopyClose(au_p, PERIOD_D1, 0, 2, auCC);
   ArraySetAsSeries(auCC, true);
   
   ArrayResize(anCC, 2);
   CopyClose(an_p, PERIOD_D1, 0, 2, anCC);
   ArraySetAsSeries(anCC, true);
   
   ArrayResize(acCC, 2);
   CopyClose(ac_p, PERIOD_D1, 0, 2, acCC);
   ArraySetAsSeries(acCC, true);
   
   ArrayResize(afCC, 2);
   CopyClose(af_p, PERIOD_D1, 0, 2, afCC);
   ArraySetAsSeries(afCC, true);
   
   ArrayResize(ajCC, 2);
   CopyClose(aj_p, PERIOD_D1, 0, 2, ajCC);
   ArraySetAsSeries(ajCC, true);
   
   ArrayResize(nuCC, 2);
   CopyClose(nu_p, PERIOD_D1, 0, 2, nuCC);
   ArraySetAsSeries(nuCC, true);
   
   ArrayResize(ncCC, 2);
   CopyClose(nc_p, PERIOD_D1, 0, 2, ncCC);
   ArraySetAsSeries(ncCC, true);
   
   ArrayResize(nfCC, 2);
   CopyClose(nf_p, PERIOD_D1, 0, 2, nfCC);
   ArraySetAsSeries(nfCC, true);
   
   ArrayResize(njCC, 2);
   CopyClose(nj_p, PERIOD_D1, 0, 2, njCC);
   ArraySetAsSeries(njCC, true);
   
   ArrayResize(ucCC, 2);
   CopyClose(uc_p, PERIOD_D1, 0, 2, ucCC);
   ArraySetAsSeries(ucCC, true);
   
   ArrayResize(cfCC, 2);
   CopyClose(cf_p, PERIOD_D1, 0, 2, cfCC);
   ArraySetAsSeries(cfCC, true);
   
   ArrayResize(cjCC, 2);
   CopyClose(cj_p, PERIOD_D1, 0, 2, cjCC);
   ArraySetAsSeries(cjCC, true);
   
   ArrayResize(ufCC, 2);
   CopyClose(uf_p, PERIOD_D1, 0, 2, ufCC);
   ArraySetAsSeries(ufCC, true);
   
   ArrayResize(fjCC, 2);
   CopyClose(fj_p, PERIOD_D1, 0, 2, fjCC);
   ArraySetAsSeries(fjCC, true);
   
   ArrayResize(ujCC, 2);
   CopyClose(uj_p, PERIOD_D1, 0, 2, ujCC);
   ArraySetAsSeries(ujCC, true);
   
   ArrayResize(euMC, count + 2);
   CopyClose(eu_p, PERIOD_M1, 0, count + 1, euMC);
   ArraySetAsSeries(euMC, true);
   
   ArrayResize(egMC, count + 2);
   CopyClose(eg_p, PERIOD_M1, 0, count + 1, egMC);
   ArraySetAsSeries(egMC, true);
   
   ArrayResize(eaMC, count + 2);
   CopyClose(ea_p, PERIOD_M1, 0, count + 1, eaMC);
   ArraySetAsSeries(eaMC, true);
   
   ArrayResize(enMC, count + 2);
   CopyClose(en_p, PERIOD_M1, 0, count + 1, enMC);
   ArraySetAsSeries(enMC, true);
   
   ArrayResize(ecMC, count + 2);
   CopyClose(ec_p, PERIOD_M1, 0, count + 1, ecMC);
   ArraySetAsSeries(ecMC, true);
   
   ArrayResize(efMC, count + 2);
   CopyClose(ef_p, PERIOD_M1, 0, count + 1, efMC);
   ArraySetAsSeries(efMC, true);
   
   ArrayResize(ejMC, count + 2);
   CopyClose(ej_p, PERIOD_M1, 0, count + 1, ejMC);
   ArraySetAsSeries(ejMC, true);
   
   ArrayResize(guMC, count + 2);
   CopyClose(gu_p, PERIOD_M1, 0, count + 1, guMC);
   ArraySetAsSeries(guMC, true);
   
   ArrayResize(gaMC, count + 2);
   CopyClose(ga_p, PERIOD_M1, 0, count + 1, gaMC);
   ArraySetAsSeries(gaMC, true);
   
   ArrayResize(gnMC, count + 2);
   CopyClose(gn_p, PERIOD_M1, 0, count + 1, gnMC);
   ArraySetAsSeries(gnMC, true);
   
   ArrayResize(gcMC, count + 2);
   CopyClose(gc_p, PERIOD_M1, 0, count + 1, gcMC);
   ArraySetAsSeries(gcMC, true);
   
   ArrayResize(gfMC, count + 2);
   CopyClose(gf_p, PERIOD_M1, 0, count + 1, gfMC);
   ArraySetAsSeries(gfMC, true);
   
   ArrayResize(gjMC, count + 2);
   CopyClose(gj_p, PERIOD_M1, 0, count + 1, gjMC);
   ArraySetAsSeries(gjMC, true);
   
   ArrayResize(auMC, count + 2);
   CopyClose(au_p, PERIOD_M1, 0, count + 1, auMC);
   ArraySetAsSeries(auMC, true);
   
   ArrayResize(anMC, count + 2);
   CopyClose(an_p, PERIOD_M1, 0, count + 1, anMC);
   ArraySetAsSeries(anMC, true);
   
   ArrayResize(acMC, count + 2);
   CopyClose(ac_p, PERIOD_M1, 0, count + 1, acMC);
   ArraySetAsSeries(acMC, true);
   
   ArrayResize(afMC, count + 2);
   CopyClose(af_p, PERIOD_M1, 0, count + 1, afMC);
   ArraySetAsSeries(afMC, true);
   
   ArrayResize(ajMC, count + 2);
   CopyClose(aj_p, PERIOD_M1, 0, count + 1, ajMC);
   ArraySetAsSeries(ajMC, true);
   
   ArrayResize(nuMC, count + 2);
   CopyClose(nu_p, PERIOD_M1, 0, count + 1, nuMC);
   ArraySetAsSeries(nuMC, true);
   
   ArrayResize(ncMC, count + 2);
   CopyClose(nc_p, PERIOD_M1, 0, count + 1, ncMC);
   ArraySetAsSeries(ncMC, true);
   
   ArrayResize(nfMC, count + 2);
   CopyClose(nf_p, PERIOD_M1, 0, count + 1, nfMC);
   ArraySetAsSeries(nfMC, true);
   
   ArrayResize(njMC, count + 2);
   CopyClose(nj_p, PERIOD_M1, 0, count + 1, njMC);
   ArraySetAsSeries(njMC, true);
   
   ArrayResize(ucMC, count + 2);
   CopyClose(uc_p, PERIOD_M1, 0, count + 1, ucMC);
   ArraySetAsSeries(ucMC, true);
   
   ArrayResize(cfMC, count + 2);
   CopyClose(cf_p, PERIOD_M1, 0, count + 1, cfMC);
   ArraySetAsSeries(cfMC, true);
   
   ArrayResize(cjMC, count + 2);
   CopyClose(cj_p, PERIOD_M1, 0, count + 1, cjMC);
   ArraySetAsSeries(cjMC, true);
   
   ArrayResize(ufMC, count + 2);
   CopyClose(uf_p, PERIOD_M1, 0, count + 1, ufMC);
   ArraySetAsSeries(ufMC, true);
   
   ArrayResize(fjMC, count + 2);
   CopyClose(fj_p, PERIOD_M1, 0, count + 1, fjMC);
   ArraySetAsSeries(fjMC, true);
   
   ArrayResize(ujMC, count + 2);
   CopyClose(uj_p, PERIOD_M1, 0, count + 1, ujMC);
   ArraySetAsSeries(ujMC, true);
      
   //PIP values
   double eupl = dblPipValue(eu_p);
   double egpl = dblPipValue(eg_p);
   double eapl = dblPipValue(ea_p);
   double enpl = dblPipValue(en_p);
   double ecpl = dblPipValue(ec_p);
   double efpl = dblPipValue(ef_p);
   double ejpl = dblPipValue(ej_p);
   double eurpl = (eupl + egpl + eapl + enpl + ecpl + efpl + ejpl) / 7;
   
   double gupl = dblPipValue(gu_p);
   double gapl = dblPipValue(ga_p);
   double gnpl = dblPipValue(gn_p);
   double gcpl = dblPipValue(gc_p);
   double gfpl = dblPipValue(gf_p);
   double gjpl = dblPipValue(gj_p);
   double gbppl = (gupl + egpl + gapl + gnpl + gcpl + gfpl + gjpl) / 7;
   
   double aupl = dblPipValue(au_p);
   double anpl = dblPipValue(an_p);
   double acpl = dblPipValue(ac_p);
   double afpl = dblPipValue(af_p);
   double ajpl = dblPipValue(aj_p);
   double audpl = (aupl + eapl + gapl + anpl + acpl + afpl + ajpl) / 7;
   
   double nupl = dblPipValue(nu_p);
   double ncpl = dblPipValue(nc_p);
   double nfpl = dblPipValue(nf_p);
   double njpl = dblPipValue(nj_p);
   double nzdpl = (nupl + enpl + gnpl + anpl + ncpl + nfpl + njpl) / 7;
   
   double ucpl = dblPipValue(uc_p);
   double cfpl = dblPipValue(cf_p);
   double cjpl = dblPipValue(cj_p);
   double cadpl = (ucpl + ecpl + gcpl + acpl + ncpl + cfpl + cjpl) / 7;
   
   double ufpl = dblPipValue(uf_p);
   double fjpl = dblPipValue(fj_p);
   double chfpl = (ufpl + efpl + gfpl + afpl + nfpl + cfpl + fjpl) / 7;
   
   double ujpl = dblPipValue(uj_p);
   double jpypl = (ujpl + ejpl + gjpl + ajpl + njpl + cjpl + fjpl) / 7;
   
   double usdpl = (eupl + gupl + aupl + nupl + ucpl + ufpl + ujpl) / 7;
      
   for (int i = count; i >= 1; i--)
   {
      double eufopen = (euMC[i] - euCC[1]) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;   
      double egfopen = (egMC[i] - egCC[1]) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
      double eafopen = (eaMC[i] - eaCC[1]) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
      double enfopen = (enMC[i] - enCC[1]) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
      double ecfopen = (ecMC[i] - ecCC[1]) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
      double effopen = (efMC[i] - efCC[1]) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
      double ejfopen = (ejMC[i] - ejCC[1]) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
      double gufopen = (guMC[i] - guCC[1]) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
      double gafopen = (gaMC[i] - gaCC[1]) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
      double gnfopen = (gnMC[i] - gnCC[1]) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
      double gcfopen = (gcMC[i] - gcCC[1]) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
      double gffopen = (gfMC[i] - gfCC[1]) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
      double gjfopen = (gjMC[i] - gjCC[1]) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
      double aufopen = (auMC[i] - auCC[1]) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
      double anfopen = (anMC[i] - anCC[1]) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
      double acfopen = (acMC[i] - acCC[1]) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
      double affopen = (afMC[i] - afCC[1]) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
      double ajfopen = (ajMC[i] - ajCC[1]) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
      double nufopen = (nuMC[i] - nuCC[1]) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
      double ncfopen = (ncMC[i] - ncCC[1]) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
      double nffopen = (nfMC[i] - nfCC[1]) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
      double njfopen = (njMC[i] - njCC[1]) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
      double ucfopen = (ucMC[i] - ucCC[1]) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
      double cffopen = (cfMC[i] - cfCC[1]) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
      double cjfopen = (cjMC[i] - cjCC[1]) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
      double uffopen = (ufMC[i] - ufCC[1]) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
      double fjfopen = (fjMC[i] - fjCC[1]) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
      double ujfopen = (ujMC[i] - ujCC[1]) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;
   
      if (MathAbs(eufopen) >= threshold) rec[i] += MathAbs(eufopen);
      if (MathAbs(egfopen) >= threshold) rec[i] += MathAbs(egfopen);
      if (MathAbs(eafopen) >= threshold) rec[i] += MathAbs(eafopen);
      if (MathAbs(enfopen) >= threshold) rec[i] += MathAbs(enfopen);
      if (MathAbs(ecfopen) >= threshold) rec[i] += MathAbs(ecfopen);
      if (MathAbs(effopen) >= threshold) rec[i] += MathAbs(effopen);
      if (MathAbs(ejfopen) >= threshold) rec[i] += MathAbs(ejfopen);
      if (MathAbs(gufopen) >= threshold) rec[i] += MathAbs(gufopen);
      if (MathAbs(gafopen) >= threshold) rec[i] += MathAbs(gafopen);
      if (MathAbs(gnfopen) >= threshold) rec[i] += MathAbs(gnfopen);
      if (MathAbs(gcfopen) >= threshold) rec[i] += MathAbs(gcfopen);
      if (MathAbs(gffopen) >= threshold) rec[i] += MathAbs(gffopen);
      if (MathAbs(gjfopen) >= threshold) rec[i] += MathAbs(gjfopen);
      if (MathAbs(aufopen) >= threshold) rec[i] += MathAbs(aufopen);
      if (MathAbs(anfopen) >= threshold) rec[i] += MathAbs(anfopen);
      if (MathAbs(acfopen) >= threshold) rec[i] += MathAbs(acfopen);
      if (MathAbs(affopen) >= threshold) rec[i] += MathAbs(affopen);
      if (MathAbs(ajfopen) >= threshold) rec[i] += MathAbs(ajfopen);
      if (MathAbs(nufopen) >= threshold) rec[i] += MathAbs(nufopen);
      if (MathAbs(ncfopen) >= threshold) rec[i] += MathAbs(ncfopen);
      if (MathAbs(nffopen) >= threshold) rec[i] += MathAbs(nffopen);
      if (MathAbs(njfopen) >= threshold) rec[i] += MathAbs(njfopen);
      if (MathAbs(ucfopen) >= threshold) rec[i] += MathAbs(ucfopen);
      if (MathAbs(cffopen) >= threshold) rec[i] += MathAbs(cffopen);
      if (MathAbs(cjfopen) >= threshold) rec[i] += MathAbs(cjfopen);
      if (MathAbs(uffopen) >= threshold) rec[i] += MathAbs(uffopen);
      if (MathAbs(fjfopen) >= threshold) rec[i] += MathAbs(fjfopen);
      if (MathAbs(ujfopen) >= threshold) rec[i] += MathAbs(ujfopen);
   }
   
   for (int i = count - 2; i >= 1; i--)
   {
      if (rec[i] < 4000 || rec[i] > 14000)
      redrec[i] = rec[i];
   }
}

void csstuff0()
{
   //uint start1 = GetTickCount();
   // 0 = EUR
   // 1 = GBP
   // 2 = AUD
   // 3 = NZD
   // 4 = CAD
   // 5 = USD
   // 6 = CHF
   // 7 = USD

   //PIP values
   double eupl = dblPipValue(eu_p);
   double egpl = dblPipValue(eg_p);
   double eapl = dblPipValue(ea_p);
   double enpl = dblPipValue(en_p);
   double ecpl = dblPipValue(ec_p);
   double efpl = dblPipValue(ef_p);
   double ejpl = dblPipValue(ej_p);
   double eurpl = (eupl + egpl + eapl + enpl + ecpl + efpl + ejpl) / 7;
   
   double gupl = dblPipValue(gu_p);
   double gapl = dblPipValue(ga_p);
   double gnpl = dblPipValue(gn_p);
   double gcpl = dblPipValue(gc_p);
   double gfpl = dblPipValue(gf_p);
   double gjpl = dblPipValue(gj_p);
   double gbppl = (gupl + egpl + gapl + gnpl + gcpl + gfpl + gjpl) / 7;
   
   double aupl = dblPipValue(au_p);
   double anpl = dblPipValue(an_p);
   double acpl = dblPipValue(ac_p);
   double afpl = dblPipValue(af_p);
   double ajpl = dblPipValue(aj_p);
   double audpl = (aupl + eapl + gapl + anpl + acpl + afpl + ajpl) / 7;
   
   double nupl = dblPipValue(nu_p);
   double ncpl = dblPipValue(nc_p);
   double nfpl = dblPipValue(nf_p);
   double njpl = dblPipValue(nj_p);
   double nzdpl = (nupl + enpl + gnpl + anpl + ncpl + nfpl + njpl) / 7;
   
   double ucpl = dblPipValue(uc_p);
   double cfpl = dblPipValue(cf_p);
   double cjpl = dblPipValue(cj_p);
   double cadpl = (ucpl + ecpl + gcpl + acpl + ncpl + cfpl + cjpl) / 7;
   
   double ufpl = dblPipValue(uf_p);
   double fjpl = dblPipValue(fj_p);
   double chfpl = (ufpl + efpl + gfpl + afpl + nfpl + cfpl + fjpl) / 7;
   
   double ujpl = dblPipValue(uj_p);
   double jpypl = (ujpl + ejpl + gjpl + ajpl + njpl + cjpl + fjpl) / 7;
   
   double usdpl = (eupl + gupl + aupl + nupl + ucpl + ufpl + ujpl) / 7;
   
   double euCC[], egCC[], eaCC[], enCC[], ecCC[], efCC[], ejCC[];
   double guCC[], gaCC[], gnCC[], gcCC[], gfCC[], gjCC[];
   double auCC[], anCC[], acCC[], afCC[], ajCC[];
   double nuCC[], ncCC[], nfCC[], njCC[];
   double ucCC[], cfCC[], cjCC[];
   double ufCC[], fjCC[];
   double ujCC[];
   double euMC[], egMC[], eaMC[], enMC[], ecMC[], efMC[], ejMC[];
   double guMC[], gaMC[], gnMC[], gcMC[], gfMC[], gjMC[];
   double auMC[], anMC[], acMC[], afMC[], ajMC[];
   double nuMC[], ncMC[], nfMC[], njMC[];
   double ucMC[], cfMC[], cjMC[];
   double ufMC[], fjMC[];
   double ujMC[];

   ArrayResize(euCC, 2);
   CopyClose(eu_p, PERIOD_D1, 0, 2, euCC);
   ArraySetAsSeries(euCC, true);
   
   ArrayResize(egCC, 2);
   CopyClose(eg_p, PERIOD_D1, 0, 2, egCC);
   ArraySetAsSeries(egCC, true);
   
   ArrayResize(eaCC, 2);
   CopyClose(ea_p, PERIOD_D1, 0, 2, eaCC);
   ArraySetAsSeries(eaCC, true);
   
   ArrayResize(enCC, 2);
   CopyClose(en_p, PERIOD_D1, 0, 2, enCC);
   ArraySetAsSeries(enCC, true);
   
   ArrayResize(ecCC, 2);
   CopyClose(ec_p, PERIOD_D1, 0, 2, ecCC);
   ArraySetAsSeries(ecCC, true);
   
   ArrayResize(efCC, 2);
   CopyClose(ef_p, PERIOD_D1, 0, 2, efCC);
   ArraySetAsSeries(efCC, true);
   
   ArrayResize(ejCC, 2);
   CopyClose(ej_p, PERIOD_D1, 0, 2, ejCC);
   ArraySetAsSeries(ejCC, true);
   
   ArrayResize(guCC, 2);
   CopyClose(gu_p, PERIOD_D1, 0, 2, guCC);
   ArraySetAsSeries(guCC, true);
   
   ArrayResize(gaCC, 2);
   CopyClose(ga_p, PERIOD_D1, 0, 2, gaCC);
   ArraySetAsSeries(gaCC, true);
   
   ArrayResize(gnCC, 2);
   CopyClose(gn_p, PERIOD_D1, 0, 2, gnCC);
   ArraySetAsSeries(gnCC, true);
   
   ArrayResize(gcCC, 2);
   CopyClose(gc_p, PERIOD_D1, 0, 2, gcCC);
   ArraySetAsSeries(gcCC, true);
   
   ArrayResize(gfCC, 2);
   CopyClose(gf_p, PERIOD_D1, 0, 2, gfCC);
   ArraySetAsSeries(gfCC, true);
   
   ArrayResize(gjCC, 2);
   CopyClose(gj_p, PERIOD_D1, 0, 2, gjCC);
   ArraySetAsSeries(gjCC, true);
   
   ArrayResize(auCC, 2);
   CopyClose(au_p, PERIOD_D1, 0, 2, auCC);
   ArraySetAsSeries(auCC, true);
   
   ArrayResize(anCC, 2);
   CopyClose(an_p, PERIOD_D1, 0, 2, anCC);
   ArraySetAsSeries(anCC, true);
   
   ArrayResize(acCC, 2);
   CopyClose(ac_p, PERIOD_D1, 0, 2, acCC);
   ArraySetAsSeries(acCC, true);
   
   ArrayResize(afCC, 2);
   CopyClose(af_p, PERIOD_D1, 0, 2, afCC);
   ArraySetAsSeries(afCC, true);
   
   ArrayResize(ajCC, 2);
   CopyClose(aj_p, PERIOD_D1, 0, 2, ajCC);
   ArraySetAsSeries(ajCC, true);
   
   ArrayResize(nuCC, 2);
   CopyClose(nu_p, PERIOD_D1, 0, 2, nuCC);
   ArraySetAsSeries(nuCC, true);
   
   ArrayResize(ncCC, 2);
   CopyClose(nc_p, PERIOD_D1, 0, 2, ncCC);
   ArraySetAsSeries(ncCC, true);
   
   ArrayResize(nfCC, 2);
   CopyClose(nf_p, PERIOD_D1, 0, 2, nfCC);
   ArraySetAsSeries(nfCC, true);
   
   ArrayResize(njCC, 2);
   CopyClose(nj_p, PERIOD_D1, 0, 2, njCC);
   ArraySetAsSeries(njCC, true);
   
   ArrayResize(ucCC, 2);
   CopyClose(uc_p, PERIOD_D1, 0, 2, ucCC);
   ArraySetAsSeries(ucCC, true);
   
   ArrayResize(cfCC, 2);
   CopyClose(cf_p, PERIOD_D1, 0, 2, cfCC);
   ArraySetAsSeries(cfCC, true);
   
   ArrayResize(cjCC, 2);
   CopyClose(cj_p, PERIOD_D1, 0, 2, cjCC);
   ArraySetAsSeries(cjCC, true);
   
   ArrayResize(ufCC, 2);
   CopyClose(uf_p, PERIOD_D1, 0, 2, ufCC);
   ArraySetAsSeries(ufCC, true);
   
   ArrayResize(fjCC, 2);
   CopyClose(fj_p, PERIOD_D1, 0, 2, fjCC);
   ArraySetAsSeries(fjCC, true);
   
   ArrayResize(ujCC, 2);
   CopyClose(uj_p, PERIOD_D1, 0, 2, ujCC);
   ArraySetAsSeries(ujCC, true);
   
   ArrayResize(euMC, 1);
   CopyClose(eu_p, PERIOD_M1, 0, 1, euMC);
   ArraySetAsSeries(euMC, true);
   
   ArrayResize(egMC, 1);
   CopyClose(eg_p, PERIOD_M1, 0, 1, egMC);
   ArraySetAsSeries(egMC, true);
   
   ArrayResize(eaMC, 1);
   CopyClose(ea_p, PERIOD_M1, 0, 1, eaMC);
   ArraySetAsSeries(eaMC, true);
   
   ArrayResize(enMC, 1);
   CopyClose(en_p, PERIOD_M1, 0, 1, enMC);
   ArraySetAsSeries(enMC, true);
   
   ArrayResize(ecMC, 1);
   CopyClose(ec_p, PERIOD_M1, 0, 1, ecMC);
   ArraySetAsSeries(ecMC, true);
   
   ArrayResize(efMC, 1);
   CopyClose(ef_p, PERIOD_M1, 0, 1, efMC);
   ArraySetAsSeries(efMC, true);
   
   ArrayResize(ejMC, 1);
   CopyClose(ej_p, PERIOD_M1, 0, 1, ejMC);
   ArraySetAsSeries(ejMC, true);
   
   ArrayResize(guMC, 1);
   CopyClose(gu_p, PERIOD_M1, 0, 1, guMC);
   ArraySetAsSeries(guMC, true);
   
   ArrayResize(gaMC, 1);
   CopyClose(ga_p, PERIOD_M1, 0, 1, gaMC);
   ArraySetAsSeries(gaMC, true);
   
   ArrayResize(gnMC, 1);
   CopyClose(gn_p, PERIOD_M1, 0, 1, gnMC);
   ArraySetAsSeries(gnMC, true);
   
   ArrayResize(gcMC, 1);
   CopyClose(gc_p, PERIOD_M1, 0, 1, gcMC);
   ArraySetAsSeries(gcMC, true);
   
   ArrayResize(gfMC, 1);
   CopyClose(gf_p, PERIOD_M1, 0, 1, gfMC);
   ArraySetAsSeries(gfMC, true);
   
   ArrayResize(gjMC, 1);
   CopyClose(gj_p, PERIOD_M1, 0, 1, gjMC);
   ArraySetAsSeries(gjMC, true);
   
   ArrayResize(auMC, 1);
   CopyClose(au_p, PERIOD_M1, 0, 1, auMC);
   ArraySetAsSeries(auMC, true);
   
   ArrayResize(anMC, 1);
   CopyClose(an_p, PERIOD_M1, 0, 1, anMC);
   ArraySetAsSeries(anMC, true);
   
   ArrayResize(acMC, 1);
   CopyClose(ac_p, PERIOD_M1, 0, 1, acMC);
   ArraySetAsSeries(acMC, true);
   
   ArrayResize(afMC, 1);
   CopyClose(af_p, PERIOD_M1, 0, 1, afMC);
   ArraySetAsSeries(afMC, true);
   
   ArrayResize(ajMC, 1);
   CopyClose(aj_p, PERIOD_M1, 0, 1, ajMC);
   ArraySetAsSeries(ajMC, true);
   
   ArrayResize(nuMC, 1);
   CopyClose(nu_p, PERIOD_M1, 0, 1, nuMC);
   ArraySetAsSeries(nuMC, true);
   
   ArrayResize(ncMC, 1);
   CopyClose(nc_p, PERIOD_M1, 0, 1, ncMC);
   ArraySetAsSeries(ncMC, true);
   
   ArrayResize(nfMC, 1);
   CopyClose(nf_p, PERIOD_M1, 0, 1, nfMC);
   ArraySetAsSeries(nfMC, true);
   
   ArrayResize(njMC, 1);
   CopyClose(nj_p, PERIOD_M1, 0, 1, njMC);
   ArraySetAsSeries(njMC, true);
   
   ArrayResize(ucMC, 1);
   CopyClose(uc_p, PERIOD_M1, 0, 1, ucMC);
   ArraySetAsSeries(ucMC, true);
   
   ArrayResize(cfMC, 1);
   CopyClose(cf_p, PERIOD_M1, 0, 1, cfMC);
   ArraySetAsSeries(cfMC, true);
   
   ArrayResize(cjMC, 1);
   CopyClose(cj_p, PERIOD_M1, 0, 1, cjMC);
   ArraySetAsSeries(cjMC, true);
   
   ArrayResize(ufMC, 1);
   CopyClose(uf_p, PERIOD_M1, 0, 1, ufMC);
   ArraySetAsSeries(ufMC, true);
   
   ArrayResize(fjMC, 1);
   CopyClose(fj_p, PERIOD_M1, 0, 1, fjMC);
   ArraySetAsSeries(fjMC, true);
   
   ArrayResize(ujMC, 1);
   CopyClose(uj_p, PERIOD_M1, 0, 1, ujMC);
   ArraySetAsSeries(ujMC, true);
   
   rec[0] = 0;
   
   double eufopen = (euMC[0] - euCC[1]) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;   
   double egfopen = (egMC[0] - egCC[1]) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
   double eafopen = (eaMC[0] - eaCC[1]) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
   double enfopen = (enMC[0] - enCC[1]) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
   double ecfopen = (ecMC[0] - ecCC[1]) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
   double effopen = (efMC[0] - efCC[1]) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
   double ejfopen = (ejMC[0] - ejCC[1]) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
   double gufopen = (guMC[0] - guCC[1]) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
   double gafopen = (gaMC[0] - gaCC[1]) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
   double gnfopen = (gnMC[0] - gnCC[1]) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
   double gcfopen = (gcMC[0] - gcCC[1]) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
   double gffopen = (gfMC[0] - gfCC[1]) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
   double gjfopen = (gjMC[0] - gjCC[1]) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
   double aufopen = (auMC[0] - auCC[1]) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
   double anfopen = (anMC[0] - anCC[1]) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
   double acfopen = (acMC[0] - acCC[1]) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
   double affopen = (afMC[0] - afCC[1]) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
   double ajfopen = (ajMC[0] - ajCC[1]) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
   double nufopen = (nuMC[0] - nuCC[1]) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
   double ncfopen = (ncMC[0] - ncCC[1]) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
   double nffopen = (nfMC[0] - nfCC[1]) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
   double njfopen = (njMC[0] - njCC[1]) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
   double ucfopen = (ucMC[0] - ucCC[1]) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
   double cffopen = (cfMC[0] - cfCC[1]) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
   double cjfopen = (cjMC[0] - cjCC[1]) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
   double uffopen = (ufMC[0] - ufCC[1]) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
   double fjfopen = (fjMC[0] - fjCC[1]) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
   double ujfopen = (ujMC[0] - ujCC[1]) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;

   if (MathAbs(eufopen) >= threshold) rec[0] += MathAbs(eufopen);
   if (MathAbs(egfopen) >= threshold) rec[0] += MathAbs(egfopen);
   if (MathAbs(eafopen) >= threshold) rec[0] += MathAbs(eafopen);
   if (MathAbs(enfopen) >= threshold) rec[0] += MathAbs(enfopen);
   if (MathAbs(ecfopen) >= threshold) rec[0] += MathAbs(ecfopen);
   if (MathAbs(effopen) >= threshold) rec[0] += MathAbs(effopen);
   if (MathAbs(ejfopen) >= threshold) rec[0] += MathAbs(ejfopen);
   if (MathAbs(gufopen) >= threshold) rec[0] += MathAbs(gufopen);
   if (MathAbs(gafopen) >= threshold) rec[0] += MathAbs(gafopen);
   if (MathAbs(gnfopen) >= threshold) rec[0] += MathAbs(gnfopen);
   if (MathAbs(gcfopen) >= threshold) rec[0] += MathAbs(gcfopen);
   if (MathAbs(gffopen) >= threshold) rec[0] += MathAbs(gffopen);
   if (MathAbs(gjfopen) >= threshold) rec[0] += MathAbs(gjfopen);
   if (MathAbs(aufopen) >= threshold) rec[0] += MathAbs(aufopen);
   if (MathAbs(anfopen) >= threshold) rec[0] += MathAbs(anfopen);
   if (MathAbs(acfopen) >= threshold) rec[0] += MathAbs(acfopen);
   if (MathAbs(affopen) >= threshold) rec[0] += MathAbs(affopen);
   if (MathAbs(ajfopen) >= threshold) rec[0] += MathAbs(ajfopen);
   if (MathAbs(nufopen) >= threshold) rec[0] += MathAbs(nufopen);
   if (MathAbs(ncfopen) >= threshold) rec[0] += MathAbs(ncfopen);
   if (MathAbs(nffopen) >= threshold) rec[0] += MathAbs(nffopen);
   if (MathAbs(njfopen) >= threshold) rec[0] += MathAbs(njfopen);
   if (MathAbs(ucfopen) >= threshold) rec[0] += MathAbs(ucfopen);
   if (MathAbs(cffopen) >= threshold) rec[0] += MathAbs(cffopen);
   if (MathAbs(cjfopen) >= threshold) rec[0] += MathAbs(cjfopen);
   if (MathAbs(uffopen) >= threshold) rec[0] += MathAbs(uffopen);
   if (MathAbs(fjfopen) >= threshold) rec[0] += MathAbs(fjfopen);
   if (MathAbs(ujfopen) >= threshold) rec[0] += MathAbs(ujfopen);
   
   if (rec[0] < 4000 || rec[0] > 14000)
   redrec[0] = rec[0];
   
   
   int count = iBarShift(_Symbol, PERIOD_M1, iTime(_Symbol, PERIOD_D1, 0), false) + 1;   
   double brec[];
   ArrayResize(brec, count + 2);
   ArraySetAsSeries(brec, true);
   
   for (int x = count - 2; x >= 0; x--)
   {
      brec[x] = rec[x] - (multiplier * threshold);
   }
   for (int y = count - 2; y >= 0; y--)
   {
      emarec1[y] = iMAOnArray(brec, 0, 20, 0, MODE_EMA, y);
      /*
      emarec2[y] = iMAOnArray(brec, 0, ema2, 0, MODE_LWMA, y);
      emarec3[y] = iMAOnArray(brec, 0, ema3, 0, MODE_LWMA, y);
      emarec4[y] = iMAOnArray(brec, 0, ema4, 0, MODE_LWMA, y);
      emarec5[y] = iMAOnArray(brec, 0, ema5, 0, MODE_LWMA, y);
      emarec6[y] = iMAOnArray(brec, 0, ema6, 0, MODE_LWMA, y);
      emarec7[y] = iMAOnArray(brec, 0, ema7, 0, MODE_LWMA, y);
      emarec8[y] = iMAOnArray(brec, 0, ema8, 0, MODE_LWMA, y);
      emarec9[y] = iMAOnArray(brec, 0, ema9, 0, MODE_LWMA, y);
      emarec0[y] = iMAOnArray(brec, 0, ema0, 0, MODE_LWMA, y);
      emarec10[y] = iMAOnArray(brec, 0, ema10, 0, MODE_LWMA, y);
      emarec11[y] = iMAOnArray(brec, 0, ema11, 0, MODE_LWMA, y);
      emarec12[y] = iMAOnArray(brec, 0, ema12, 0, MODE_LWMA, y);
      emarec13[y] = iMAOnArray(brec, 0, ema13, 0, MODE_LWMA, y);
      */
   }
   for (int y = count - 2; y >= 0; y--)
   {
      //if (rec[y] - rec[y + 1] <= -25)
      if (rec[y] < emarec1[y])
      greenrec[y] = rec[y];
   }
   
   static datetime dbomb = 0;
   bool bomb = false;
   if (dbomb < iTime(_Symbol, PERIOD_M1, 0))
   {
      dbomb = iTime(_Symbol, PERIOD_M1, 0);
      bomb = true;
   }
   if (bomb)
   {
      if (rec[1] < emarec1[1] && rec[1] >= 4000 && rec[1] <= 12000)
      PlaySound("explosion.wav");
      bomb = false;
   }
   
}

//+TICK VALUE CALC---------------------------------------------------+
double dblTickValue( string strSymbol )
{
   return( MarketInfo( strSymbol, MODE_TICKVALUE ) );
}
double dblPipValue( string strSymbol )
{
   double dblCalcPipValue = dblTickValue( strSymbol );
   if (MarketInfo(strSymbol, MODE_DIGITS) == 3) dblCalcPipValue *= 10;
   if (MarketInfo(strSymbol, MODE_DIGITS) == 5) dblCalcPipValue *= 10;
   return( dblCalcPipValue );
}
//+------------------------------------------------------------------+