#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property strict

#property indicator_chart_window

input int counter = 60; // How many days to calculate
input int wd = 2; // Lines width
input bool deleteold = true; // Delete old and crossed lines

enum lineabled {
	ndis = 0, // False
	yena = 1 // True
};
lineabled enabled = 1; // Enable / Disable lines

#define Name WindowExpertName()

//+INIT SEQUENCE-----------------------------------------------------+
int OnInit()
{
	if (GlobalVariableCheck("eq" + _Symbol) == false) GlobalVariableSet("eq" + _Symbol, 1);
	enabled = (lineabled)GlobalVariableGet("eq" + _Symbol);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+MAIN RUN----------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("MProfile equilibrium expired, contact sakisf for an update/new version.");
		YesStop = true;
		ObjectsDeleteAll(0, Name);
	}

	if (YesStop != true) {
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_H4, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_H4, 0);
		}
		if (new_1m_check)
		{
			if (ChartPeriod() >= 1 && ChartPeriod() <= 240) { // Only run from M1 to H4
				tpocount();
			}
			new_1m_check = false;
		}
	}//YesStop (expiry) end
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT SEQUENCE---------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+ChartEvent function-----------------------------------------------+
void OnChartEvent(const int id,
	const long &lparam,
	const double &dparam,
	const string &sparam)
{
	//---
	{//Show lines
		if (id == CHARTEVENT_KEYDOWN) {
			if (lparam == StringGetChar("E", 0) && enabled == false) { GlobalVariableSet("eq" + _Symbol, 1); enabled = (lineabled)GlobalVariableGet("eq" + _Symbol); ObjectsDeleteAll(0, Name); tpocount(); }
			if (lparam == StringGetChar("Q", 0) && enabled == true) { GlobalVariableSet("eq" + _Symbol, 0); enabled = (lineabled)GlobalVariableGet("eq" + _Symbol); ObjectsDeleteAll(0, Name); tpocount(); }
		}
	}
}
//+------------------------------------------------------------------+

//+TPO COUNTER & LINE DRAWING----------------------------------------+
void tpocount() {
	double Pip = (_Point * MathPow(10, MathMod(_Digits, 2))); // Pip size calculator

	double trocount[]; // Init main array to count TPOs
	ArrayResize(trocount, 1000); // Adjust size of main array
	double maxim[]; // Array to hold max TPO values
	double pipper[]; // Array to hold pips
	ArrayResize(maxim, counter + 10);
	ArrayResize(pipper, counter + 10);

	for (int n = counter; n >= 1; n--) {
		double drlow = iLow(_Symbol, PERIOD_D1, n); // Find day lows for *counter* days
		for (int i = iBarShift(_Symbol, PERIOD_M30, iTime(_Symbol, PERIOD_D1, n), true); i > iBarShift(_Symbol, PERIOD_M30, iTime(_Symbol, PERIOD_D1, 0), true) + ((n - 1) * 48); i--) { //Shift 48 bars in M30 for *counter* days
			int b = (int)((iLow(_Symbol, PERIOD_M30, i) - drlow) / Pip); // Diff from M30 candle low to daily low
			int x = (int)((iHigh(_Symbol, PERIOD_M30, i) - iLow(_Symbol, PERIOD_M30, i)) / Pip); // Diff between M30 candle high to M30 candle low

			while (x > 0) {
				trocount[x + b]++; // Count TPOs per bar
				x--;
			}
		}
		maxim[n] = trocount[ArrayMaximum(trocount, 0, 0)];
		for (int d = ArraySize(trocount) - 1; d >= 0; d--) {
			if (trocount[d] == maxim[n]) {
				pipper[n] = d*Pip; break;
			}
		}
		ArrayInitialize(trocount, 0);
	}
	if (enabled) {
		string obname;
		for (int n = counter; n >= 1; n--) {
			if ((iHigh(_Symbol, PERIOD_D1, n) - pipper[n]) > (iLow(_Symbol, PERIOD_D1, n) + pipper[n])) { obname = Name + " pos " + IntegerToString(n) + " " + TimeToStr(iTime(_Symbol, PERIOD_D1, n), TIME_DATE); objbase(obname, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, n), false)], Time[0], iHigh(_Symbol, PERIOD_D1, n) - 2 * (iHigh(_Symbol, PERIOD_D1, n) - (iLow(_Symbol, PERIOD_D1, n) + pipper[n])), clrWhite); if (ChartPeriod() < 240) { ObjectSetText(obname, TimeToStr(iTime(_Symbol, PERIOD_D1, n), TIME_DATE) + " PROJ L@ " + DoubleToStr(ObjectGet(obname, OBJPROP_PRICE1), _Digits)); } if (deleteold) { if (iLow(_Symbol, PERIOD_D1, iLowest(_Symbol, PERIOD_D1, MODE_LOW, n, 1)) < ObjectGet(obname, OBJPROP_PRICE1)) ObjectDelete(obname); } }

			else if ((iHigh(_Symbol, PERIOD_D1, n) - pipper[n]) < (iLow(_Symbol, PERIOD_D1, n) + pipper[n])) { obname = Name + " neg " + IntegerToString(n) + " " + TimeToStr(iTime(_Symbol, PERIOD_D1, n), TIME_DATE); objbase(obname, Time[iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, n), false)], Time[0], iLow(_Symbol, PERIOD_D1, n) + 2 * (pipper[n]), clrRed); if (ChartPeriod() < 240) { ObjectSetText(obname, TimeToStr(iTime(_Symbol, PERIOD_D1, n), TIME_DATE) + " PROJ H@ " + DoubleToStr(ObjectGet(obname, OBJPROP_PRICE1), _Digits)); } if (deleteold) { if (iHigh(_Symbol, PERIOD_D1, iHighest(_Symbol, PERIOD_D1, MODE_HIGH, n, 1)) > ObjectGet(obname, OBJPROP_PRICE1)) ObjectDelete(obname); } }
		}
	}
}

//+CREATE T-LINES----------------------------------------------------+
void objbase(string oname, datetime t1, datetime t2, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSet(oname, OBJPROP_WIDTH, wd);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_TIME1, t1);
	ObjectSet(oname, OBJPROP_TIME2, t2);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_PRICE2, pr1);
	ObjectSet(oname, OBJPROP_RAY, false);
}
//+------------------------------------------------------------------+