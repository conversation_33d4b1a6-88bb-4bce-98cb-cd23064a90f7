//+------------------------------------------------------------------+
//|                                 ZigZagStdDevRectangles.mq4       |
//|                        Optimized and Corrected Version           |
//+------------------------------------------------------------------+
#property copyright "ZigZag StdDev Rectangles"
#property link      "https://www.example.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 6
#property indicator_color1 Blue
#property indicator_color2 Red
#property indicator_style1 STYLE_SOLID
#property indicator_style2 STYLE_SOLID
#property indicator_width1 2
#property indicator_width2 2
#property indicator_color3 White
#property indicator_color4 White
#property indicator_style3 STYLE_SOLID
#property indicator_style4 STYLE_SOLID
#property indicator_width3 2
#property indicator_width4 2
#property indicator_color5 Lime
#property indicator_color6 Lime
#property indicator_style5 STYLE_SOLID
#property indicator_style6 STYLE_SOLID
#property indicator_width5 5
#property indicator_width6 5


//--- input parameters
extern int ZigZagDepth = 8;
extern int ZigZagDeviation = 5;
extern int ZigZagBackstep = 3;
extern int SwingPointsToAnalyze = 30;
input bool NormalStd = true;
extern double StdDevMultiplier1 = 1.0;
extern double StdDevMultiplier2 = 2.0;
extern color HighRectColor = LightGreen;
extern color LowRectColor = Pink;
extern color SwingLineColor = Yellow;

int ZZDepth;

//--- indicator buffers
double ZigZagBufferHigh[];
double ZigZagBufferLow[];
double RsiHigh[];
double RsiLow[];
double ERC1[];
double ERC2[];

//--- global arrays
int storedBarIndicesHigh[];
int storedBarIndicesLow[];

//+------------------------------------------------------------------+
int OnInit()
{
   if (ZigZagDepth <= 0 || ZigZagDeviation <= 0 || ZigZagBackstep <= 0 || SwingPointsToAnalyze <= 0)
   {
      Print("Invalid input parameters");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   // Set up indicator buffers
   SetIndexStyle(0, DRAW_ARROW, STYLE_SOLID, 4, Blue);
   SetIndexBuffer(0, ZigZagBufferHigh);
   SetIndexArrow(0, 159);  // Up arrow
   SetIndexLabel(0, "ZigZag High");
   
   SetIndexStyle(1, DRAW_ARROW, STYLE_SOLID, 4, Red);
   SetIndexBuffer(1, ZigZagBufferLow);
   SetIndexArrow(1, 159);  // Down arrow
   SetIndexLabel(1, "ZigZag Low");
   
   SetIndexStyle(2, DRAW_ARROW, STYLE_SOLID, 2, White);
   SetIndexBuffer(2, RsiHigh);
   SetIndexArrow(2, 226);  // Down arrow
   SetIndexLabel(2, "RSI High");
   
   SetIndexStyle(3, DRAW_ARROW, STYLE_SOLID, 2, White);
   SetIndexBuffer(3, RsiLow);
   SetIndexArrow(3, 225);  // Down arrow
   SetIndexLabel(3, "RSI Low");
   
   SetIndexStyle(4, DRAW_HISTOGRAM, STYLE_SOLID, 3, Red);
   SetIndexBuffer(4, ERC1);
   SetIndexLabel(4, "ERC");
   
   SetIndexStyle(5, DRAW_HISTOGRAM, STYLE_SOLID, 3, Blue);
   SetIndexBuffer(5, ERC2);
   SetIndexLabel(5, "ERC");
   
   if (Period() >= PERIOD_H1) ZZDepth = 12;
   else ZZDepth = 8;
   if (NormalStd)
   {
      StdDevMultiplier1 = 1.0;
      StdDevMultiplier2 = 2.0;
   }
   else
   {
      StdDevMultiplier1 = 1.229;
      StdDevMultiplier2 = 1.771;
   }
      
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if (rates_total < ZigZagDepth + SwingPointsToAnalyze)
   {
      Print("Not enough bars");
      return (0);
   }

   static datetime lastRun = 0;
   if (TimeCurrent() - lastRun < 120) return (rates_total);
   lastRun = TimeCurrent();

   // Clear previous buffer values
   ArrayInitialize(ZigZagBufferHigh, EMPTY_VALUE);
   ArrayInitialize(ZigZagBufferLow, EMPTY_VALUE);
   ArrayInitialize(RsiHigh, EMPTY_VALUE);
   ArrayInitialize(RsiLow, EMPTY_VALUE);

   double zigzagHigh[];
   double zigzagLow[];
   ArrayResize(zigzagHigh, SwingPointsToAnalyze);
   ArrayResize(zigzagLow, SwingPointsToAnalyze);
   ArrayInitialize(zigzagHigh, 0.0);
   ArrayInitialize(zigzagLow, 0.0);

   ArrayResize(storedBarIndicesHigh, SwingPointsToAnalyze);
   ArrayResize(storedBarIndicesLow, SwingPointsToAnalyze);

   int validHighs = 0, validLows = 0;
   int maxBarsToScan = 1000;

   for (int i = 0; i < maxBarsToScan && (validHighs < SwingPointsToAnalyze || validLows < SwingPointsToAnalyze); i++)
   {
      double zz = iCustom(NULL, 0, "ZigZag", ZZDepth, ZigZagDeviation, ZigZagBackstep, 0, i);
      double rs = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, i);
      
      if (rs > 70)
      {
         RsiHigh[i] = High[i] + (Close[i] - Open[i]) / 2;
      }
      if (rs < 30)
      {
         RsiLow[i] = Low[i] - (Open[i] - Close[i]) / 2;
      }
      /*
      if ((close[i + 4] - open[i + 4] > 0) && (close[i + 3] - open[i + 3] < 0) && (close[i + 2] - open[i + 2] < 0) && (close[i + 1] - open[i + 1] < 0) && (close[i] - open[i] < 0))
      {
         ERC1[i + 4] = High[i + 4]; ERC2[i + 4] = Low[i + 4];
      }
      
      if ((close[i + 4] - open[i + 4] < 0) && (close[i + 3] - open[i + 3] > 0) && (close[i + 2] - open[i + 2] > 0) && (close[i + 1] - open[i + 1] > 0) && (close[i] - open[i] > 0))
      {
         ERC1[i + 4] = Low[i + 4]; ERC2[i + 4] = High[i + 4];
      }
      */

      if (zz != 0.0 && zz != EMPTY_VALUE)
      {
         if (MathAbs(zz - High[i]) < Point && validHighs < SwingPointsToAnalyze)
         {
            zigzagHigh[validHighs] = zz;
            storedBarIndicesHigh[validHighs] = i;
            ZigZagBufferHigh[i] = zz;  // Populate indicator buffer
            validHighs++;
         }
         else if (MathAbs(zz - Low[i]) < Point && validLows < SwingPointsToAnalyze)
         {
            zigzagLow[validLows] = zz;
            storedBarIndicesLow[validLows] = i;
            ZigZagBufferLow[i] = zz;  // Populate indicator buffer
            validLows++;
         }
      }
   }

   if (validHighs == 0 && validLows == 0)
   {
      Print("No ZigZag points found");
      return (rates_total);
   }

   //ArrayResize(storedBarIndicesHigh, validHighs);
   //ArrayResize(storedBarIndicesLow, validLows);
   //ArrayResize(zigzagHigh, validHighs);
   //ArrayResize(zigzagLow, validLows);

   ObjectsDeleteAll(0, "ZZRect_");
   ObjectsDeleteAll(0, "ZZLine_");
   ObjectsDeleteAll(0, "ZZLevel229_");
   ObjectsDeleteAll(0, "ZZLevel771_");
   ObjectsDeleteAll(0, "ZZLevel50_");
   ProcessSwingPoints(zigzagHigh, zigzagLow, storedBarIndicesHigh, storedBarIndicesLow, high, low, time, rates_total);

   return (rates_total);
}

//+------------------------------------------------------------------+
void ProcessSwingPoints(double &zigzagHigh[], double &zigzagLow[],
                        int &storedBarIndicesHigh1[], int &storedBarIndicesLow1[],
                        const double &high[], const double &low[],
                        const datetime &time[], int rates_total)
{
   for (int i = 1; i < ArraySize(zigzagHigh); i++)
   {
      int currIdx = storedBarIndicesHigh1[i];
      int prevIdx = storedBarIndicesLow1[i];
      int midIdx = ((currIdx + prevIdx) / 2);
      
      if (currIdx >= rates_total) continue;

      double stdDev = CalculateStdDev(currIdx, storedBarIndicesHigh1[i - 1], high, low);
      string rectName = "ZZRect_H_" + IntegerToString(time[currIdx]);

      bool wasHit = false;
      datetime hitTime = time[0];
      double top = zigzagHigh[i] + stdDev * StdDevMultiplier2;
      double bottom = zigzagHigh[i] + stdDev * StdDevMultiplier1;

      for (int j = currIdx - 1; j >= 0; j--)
      {
         if (high[j] >= bottom)// && high[j] <= top)
         {
            hitTime = time[j];
            wasHit = true;
            break;
         }
      }

      datetime endTime = wasHit ? hitTime : time[0];

      ObjectCreate(0, rectName, OBJ_RECTANGLE, 0, time[midIdx], top, endTime, bottom);
      ObjectSetInteger(0, rectName, OBJPROP_COLOR, HighRectColor);
      ObjectSetInteger(0, rectName, OBJPROP_STYLE, wasHit ? STYLE_DASH : STYLE_SOLID);
      ObjectSetInteger(0, rectName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, rectName, OBJPROP_BACK, false);
   }

   for (int i = 1; i < ArraySize(zigzagLow); i++)
   {
      int currIdx = storedBarIndicesLow1[i];
      int prevIdx = storedBarIndicesHigh1[i];
      int midIdx = ((currIdx + prevIdx) / 2);
      
      if (currIdx >= rates_total) continue;

      double stdDev = CalculateStdDev(currIdx, storedBarIndicesLow1[i - 1], high, low);
      string rectName = "ZZRect_L_" + IntegerToString(time[currIdx]);

      bool wasHit = false;
      datetime hitTime = time[0];
      double top = zigzagLow[i] - stdDev * StdDevMultiplier1;
      double bottom = zigzagLow[i] - stdDev * StdDevMultiplier2;

      for (int j = currIdx - 1; j >= 0; j--)
      {
         if (low[j] <= top)// && low[j] >= bottom)
         {
            hitTime = time[j];
            wasHit = true;
            break;
         }
      }

      datetime endTime = wasHit ? hitTime : time[0];

      ObjectCreate(0, rectName, OBJ_RECTANGLE, 0, time[midIdx], top, endTime, bottom);
      ObjectSetInteger(0, rectName, OBJPROP_COLOR, LowRectColor);
      ObjectSetInteger(0, rectName, OBJPROP_STYLE, wasHit ? STYLE_DASH : STYLE_SOLID);
      ObjectSetInteger(0, rectName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, rectName, OBJPROP_BACK, false);
   }
   
   // Create a combined array of all ZigZag points with their types
   double allZigZagPrices[];
   int allZigZagIndices[];
   bool allZigZagIsHigh[];
   
   int totalPoints = ArraySize(storedBarIndicesHigh1) + ArraySize(storedBarIndicesLow1);
   ArrayResize(allZigZagPrices, totalPoints);
   ArrayResize(allZigZagIndices, totalPoints);
   ArrayResize(allZigZagIsHigh, totalPoints);
   
   int pointIndex = 0;
   
   // Add all highs
   for (int h = 0; h < ArraySize(storedBarIndicesHigh1); h++)
   {
      allZigZagPrices[pointIndex] = zigzagHigh[h];
      allZigZagIndices[pointIndex] = storedBarIndicesHigh1[h];
      allZigZagIsHigh[pointIndex] = true;
      pointIndex++;
   }
   
   // Add all lows
   for (int l = 0; l < ArraySize(storedBarIndicesLow1); l++)
   {
      allZigZagPrices[pointIndex] = zigzagLow[l];
      allZigZagIndices[pointIndex] = storedBarIndicesLow1[l];
      allZigZagIsHigh[pointIndex] = false;
      pointIndex++;
   }
   
   // Sort by bar index (most recent first, since indices are in reverse chronological order)
   for (int i = 0; i < totalPoints - 1; i++)
   {
      for (int j = i + 1; j < totalPoints; j++)
      {
         if (allZigZagIndices[i] > allZigZagIndices[j])
         {
            // Swap all arrays
            double tempPrice = allZigZagPrices[i];
            int tempIndex = allZigZagIndices[i];
            bool tempIsHigh = allZigZagIsHigh[i];
            
            allZigZagPrices[i] = allZigZagPrices[j];
            allZigZagIndices[i] = allZigZagIndices[j];
            allZigZagIsHigh[i] = allZigZagIsHigh[j];
            
            allZigZagPrices[j] = tempPrice;
            allZigZagIndices[j] = tempIndex;
            allZigZagIsHigh[j] = tempIsHigh;
         }
      }
   }
   
   // Now draw lines between consecutive alternating points
   for (int i = 0; i < totalPoints - 1; i++)
   {
      int startIdx = allZigZagIndices[i];
      int endIdx = allZigZagIndices[i + 1];
      int midIdx = ((allZigZagIndices[i] + allZigZagIndices[i + 1]) / 2);
      
      double startPrice = allZigZagPrices[i];
      double endPrice = allZigZagPrices[i + 1];
      bool isUpwardSwing = !allZigZagIsHigh[i] && allZigZagIsHigh[i + 1];
      
      datetime startTime = time[midIdx];
      datetime startTime2 = time[startIdx];
      
      double swingRange = endPrice - startPrice;
      if (swingRange == 0.0) continue;
      
      double level_229, level_771, level_50;
      
      if (isUpwardSwing) // Low to High swing (upward)
      {
         level_229 = startPrice + swingRange * 0.25;
         level_771 = startPrice + swingRange * 0.75;
         level_50 = startPrice + swingRange * 0.5;
      }
      else // High to Low swing (downward)
      {
         level_229 = startPrice + swingRange * 0.75; // 77.1% retracement from high
         level_771 = startPrice + swingRange * 0.25; // 22.9% retracement from high
         level_50 = startPrice + swingRange * 0.5; // 22.9% retracement from high
      }
      
      // Check future bars to see if levels are hit
      datetime hitTime229 = time[0];
      datetime hitTime771 = time[0];
      datetime hitTime50 = time[0];
      bool hit229 = false;
      bool hit771 = false;
      bool hit50 = false;
      
      for (int j = startIdx - 1; j >= 0; j--)
      {
         if (!hit229)
         {
            if (isUpwardSwing) // Upward swing - check if price reaches retracement level going up
            {
               if (high[j] >= level_229)
               {
                  hitTime229 = time[j];
                  hit229 = true;
               }
               if (high[j] >= level_50)
               {
                  hitTime50 = time[j];
                  hit50 = true;
               }
            }
            else // Downward swing - check if price reaches retracement level going down
            {
               if (low[j] <= level_229)
               {
                  hitTime229 = time[j];
                  hit229 = true;
               }
               if (low[j] <= level_50)
               {
                  hitTime50 = time[j];
                  hit50 = true;
               }
            }
         }
      
         if (!hit771)
         {
            if (isUpwardSwing) // Upward swing - check if price reaches retracement level going up
            {
               if (high[j] >= level_771)
               {
                  hitTime771 = time[j];
                  hit771 = true;
               }
               if (high[j] >= level_50)
               {
                  hitTime50 = time[j];
                  hit50 = true;
               }
            }
            else // Downward swing - check if price reaches retracement level going down
            {
               if (low[j] <= level_771)
               {
                  hitTime771 = time[j];
                  hit771 = true;
               }
               if (low[j] <= level_50)
               {
                  hitTime50 = time[j];
                  hit50 = true;
               }
            }
         }
      
         if (hit229 && hit771) break;
      }
      
      // If not hit, extend to current bar (time[0])
      datetime endTime229 = hit229 ? hitTime229 : time[0];
      datetime endTime771 = hit771 ? hitTime771 : time[0];
      datetime endTime50 = hit50 ? hitTime50 : time[0];
      
      string lineName1 = "ZZLevel229_" + IntegerToString(startTime) + "_" + IntegerToString(i);
      string lineName2 = "ZZLevel771_" + IntegerToString(startTime) + "_" + IntegerToString(i);
      string lineName3 = "ZZLevel50_" + IntegerToString(startTime) + "_" + IntegerToString(i);
      
      if (!hit229) ObjectCreate(0, lineName1, OBJ_TREND, 0, startTime, level_229, endTime229, level_229);
      ObjectSetInteger(0, lineName1, OBJPROP_COLOR, hit229 ? DarkGray : SwingLineColor);
      ObjectSetInteger(0, lineName1, OBJPROP_WIDTH, hit229 ? 1 : 1);
      ObjectSetInteger(0, lineName1, OBJPROP_STYLE, hit229 ? STYLE_SOLID : STYLE_DASH);
      ObjectSetInteger(0, lineName1, OBJPROP_RAY, false);
      
      if (!hit771) ObjectCreate(0, lineName2, OBJ_TREND, 0, startTime, level_771, endTime771, level_771);
      ObjectSetInteger(0, lineName2, OBJPROP_COLOR, SwingLineColor);
      ObjectSetInteger(0, lineName2, OBJPROP_WIDTH, hit771 ? 1 : 1);
      ObjectSetInteger(0, lineName2, OBJPROP_STYLE, hit771 ? STYLE_SOLID : STYLE_DASH);
      ObjectSetInteger(0, lineName2, OBJPROP_RAY, false);
      
      if (!hit50) ObjectCreate(0, lineName3, OBJ_TREND, 0, startTime, level_50, endTime50, level_50);
      ObjectSetInteger(0, lineName3, OBJPROP_COLOR, Aqua);
      ObjectSetInteger(0, lineName3, OBJPROP_WIDTH, hit50 ? 2 : 1);
      ObjectSetInteger(0, lineName3, OBJPROP_STYLE, hit50 ? STYLE_SOLID : STYLE_DOT);
      ObjectSetInteger(0, lineName3, OBJPROP_RAY, false);
   }
}

//+------------------------------------------------------------------+
double CalculateStdDev(int startBar, int endBar, const double &high[], const double &low[])
{
   int count = MathAbs(startBar - endBar);
   if (count < 2) return 0;

   double sum = 0, sumSq = 0;
   int valid = 0;

   for (int i = MathMin(startBar, endBar); i <= MathMax(startBar, endBar); i++)
   {
      double mid = (high[i] + low[i]) / 2;
      if (mid <= 0) continue;
      sum += mid;
      sumSq += mid * mid;
      valid++;
   }

   if (valid < 2) return 0;

   double mean = sum / valid;
   double variance = (sumSq / valid) - (mean * mean);
   return MathSqrt(MathAbs(variance));
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectsDeleteAll(0, "ZZRect_");
   ObjectsDeleteAll(0, "ZZLine_");   
   ObjectsDeleteAll(0, "ZZLevel229_");
   ObjectsDeleteAll(0, "ZZLevel771_");
   ObjectsDeleteAll(0, "ZZLevel50_");
} //+------------------------------------------------------------------+