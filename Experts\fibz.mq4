//+------------------------------------------------------------------+
//|                                                        rsi_a.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

#define Name MQLInfoString(MQL_PROGRAM_NAME)
input int tim = 3; //Time to start checking
bool active = false;
bool trading = false;
double rstr = 0;
input string prefix = ""; //Broker prefix
input string suffix = ""; //Broker suffix
#include <StructSort.mqh>
#include <JAson.mqh>
#include <requests/requests.mqh>

static const string eu_p = prefix + "EURUSD" + suffix;
static const string eg_p = prefix + "EURGBP" + suffix;
static const string ea_p = prefix + "EURAUD" + suffix;
static const string en_p = prefix + "EURNZD" + suffix;
static const string ec_p = prefix + "EURCAD" + suffix;
static const string ef_p = prefix + "EURCHF" + suffix;
static const string ej_p = prefix + "EURJPY" + suffix;
static const string gu_p = prefix + "GBPUSD" + suffix;
static const string ga_p = prefix + "GBPAUD" + suffix;
static const string gn_p = prefix + "GBPNZD" + suffix;
static const string gc_p = prefix + "GBPCAD" + suffix;
static const string gf_p = prefix + "GBPCHF" + suffix;
static const string gj_p = prefix + "GBPJPY" + suffix;
static const string au_p = prefix + "AUDUSD" + suffix;
static const string an_p = prefix + "AUDNZD" + suffix;
static const string ac_p = prefix + "AUDCAD" + suffix;
static const string af_p = prefix + "AUDCHF" + suffix;
static const string aj_p = prefix + "AUDJPY" + suffix;
static const string nu_p = prefix + "NZDUSD" + suffix;
static const string nc_p = prefix + "NZDCAD" + suffix;
static const string nf_p = prefix + "NZDCHF" + suffix;
static const string nj_p = prefix + "NZDJPY" + suffix;
static const string uc_p = prefix + "USDCAD" + suffix;
static const string cf_p = prefix + "CADCHF" + suffix;
static const string cj_p = prefix + "CADJPY" + suffix;
static const string uf_p = prefix + "USDCHF" + suffix;
static const string fj_p = prefix + "CHFJPY" + suffix;
static const string uj_p = prefix + "USDJPY" + suffix;

CJAVal mainsort;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping

	      baskets();
	      signal();
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
void OnTick()
  {
//---

	bool check = false;
	static datetime check_time = 0;
	if (check_time < iTime(_Symbol, PERIOD_CURRENT, 0) && (Hour() >= tim && Hour() < 21) && (TimeCurrent() >= (iTime(NULL, PERIOD_CURRENT, 0) + 5)))
	{
	   check_time = iTime(_Symbol, PERIOD_CURRENT, 0);
	   check = true;
	}
	if (check)
	{
      baskets();
      signal();
	   check = false;
	}
	if (active && rstr > 0) watchdog(rstr);
//--- return value of prev_calculated for next call
}
//+------------------------------------------------------------------+

//+STRUCTS-----------------------------------------------------------+
//+------------------------------------------------------------------+
struct DWM
{
   double daily;
   double result;
   string pair;
};

void AddToDWM(DWM &arr[], double daily, string pair)
{
   int currentSize = ArraySize(arr);
   ArrayResize(arr, currentSize + 1);
   arr[currentSize].daily = daily;
   arr[currentSize].pair = pair;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
struct Data
{
   double daily;
   double pips;
   string pair;
   double rsis;
   double rsid;
   double dtd;
};

void AddToSortData(Data &arr[], double daily, double pips, const string pair)
{
   int currentSize = ArraySize(arr);
   ArrayResize(arr, currentSize + 1);
   arr[currentSize].pips = pips;
   arr[currentSize].pair = pair;
   arr[currentSize].daily = daily;
}

void AddToData(Data &arr[], double daily, double rsid)
{
   int currentSize = ArraySize(arr);
   ArrayResize(arr, currentSize + 1);
   arr[currentSize].daily = daily;
   arr[currentSize].rsid = rsid;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
struct FibData
{
   double dailyscore;
   int ashilo;
   bool asmid;
   int wmid;
   bool d1ch;
   bool d2ch;
   bool d3ch;
   bool d4ch;
   string pair;
};

void AddToDrata(FibData &arr[], double dailyscore, int ashilo, bool asmid, int wmid, bool d1ch, bool d2ch, bool d3ch, bool d4ch, const string pair)
{
   int currentSize = ArraySize(arr);
   ArrayResize(arr, currentSize + 1);
   arr[currentSize].dailyscore = dailyscore;
   arr[currentSize].ashilo = ashilo;
   arr[currentSize].asmid = asmid;
   arr[currentSize].wmid = wmid;
   arr[currentSize].d1ch = d1ch;
   arr[currentSize].d2ch = d2ch;
   arr[currentSize].d3ch = d3ch;
   arr[currentSize].d4ch = d4ch;
   arr[currentSize].pair = pair;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void baskets()
{
   //PIP values
   double eupl = dblPipValue(eu_p);
   double egpl = dblPipValue(eg_p);
   double eapl = dblPipValue(ea_p);
   double enpl = dblPipValue(en_p);
   double ecpl = dblPipValue(ec_p);
   double efpl = dblPipValue(ef_p);
   double ejpl = dblPipValue(ej_p);
   double eurpl = (eupl + egpl + eapl + enpl + ecpl + efpl + ejpl) / 7;
   
   double gupl = dblPipValue(gu_p);
   double gapl = dblPipValue(ga_p);
   double gnpl = dblPipValue(gn_p);
   double gcpl = dblPipValue(gc_p);
   double gfpl = dblPipValue(gf_p);
   double gjpl = dblPipValue(gj_p);
   double gbppl = (gupl + egpl + gapl + gnpl + gcpl + gfpl + gjpl) / 7;
   
   double aupl = dblPipValue(au_p);
   double anpl = dblPipValue(an_p);
   double acpl = dblPipValue(ac_p);
   double afpl = dblPipValue(af_p);
   double ajpl = dblPipValue(aj_p);
   double audpl = (aupl + eapl + gapl + anpl + acpl + afpl + ajpl) / 7;
   
   double nupl = dblPipValue(nu_p);
   double ncpl = dblPipValue(nc_p);
   double nfpl = dblPipValue(nf_p);
   double njpl = dblPipValue(nj_p);
   double nzdpl = (nupl + enpl + gnpl + anpl + ncpl + nfpl + njpl) / 7;
   
   double ucpl = dblPipValue(uc_p);
   double cfpl = dblPipValue(cf_p);
   double cjpl = dblPipValue(cj_p);
   double cadpl = (ucpl + ecpl + gcpl + acpl + ncpl + cfpl + cjpl) / 7;
   
   double ufpl = dblPipValue(uf_p);
   double fjpl = dblPipValue(fj_p);
   double chfpl = (ufpl + efpl + gfpl + afpl + nfpl + cfpl + fjpl) / 7;
   
   double ujpl = dblPipValue(uj_p);
   double jpypl = (ujpl + ejpl + gjpl + ajpl + njpl + cjpl + fjpl) / 7;
   
   double usdpl = (eupl + gupl + aupl + nupl + ucpl + ufpl + ujpl) / 7;
   
   double euCC[], egCC[], eaCC[], enCC[], ecCC[], efCC[], ejCC[];
   double guCC[], gaCC[], gnCC[], gcCC[], gfCC[], gjCC[];
   double auCC[], anCC[], acCC[], afCC[], ajCC[];
   double nuCC[], ncCC[], nfCC[], njCC[];
   double ucCC[], cfCC[], cjCC[];
   double ufCC[], fjCC[];
   double ujCC[];

   ArrayResize(euCC, 2);
   CopyClose(eu_p, PERIOD_D1, 0, 2, euCC);
   ArraySetAsSeries(euCC, true);
   
   ArrayResize(egCC, 2);
   CopyClose(eg_p, PERIOD_D1, 0, 2, egCC);
   ArraySetAsSeries(egCC, true);
   
   ArrayResize(eaCC, 2);
   CopyClose(ea_p, PERIOD_D1, 0, 2, eaCC);
   ArraySetAsSeries(eaCC, true);
   
   ArrayResize(enCC, 2);
   CopyClose(en_p, PERIOD_D1, 0, 2, enCC);
   ArraySetAsSeries(enCC, true);
   
   ArrayResize(ecCC, 2);
   CopyClose(ec_p, PERIOD_D1, 0, 2, ecCC);
   ArraySetAsSeries(ecCC, true);
   
   ArrayResize(efCC, 2);
   CopyClose(ef_p, PERIOD_D1, 0, 2, efCC);
   ArraySetAsSeries(efCC, true);
   
   ArrayResize(ejCC, 2);
   CopyClose(ej_p, PERIOD_D1, 0, 2, ejCC);
   ArraySetAsSeries(ejCC, true);
   
   ArrayResize(guCC, 2);
   CopyClose(gu_p, PERIOD_D1, 0, 2, guCC);
   ArraySetAsSeries(guCC, true);
   
   ArrayResize(gaCC, 2);
   CopyClose(ga_p, PERIOD_D1, 0, 2, gaCC);
   ArraySetAsSeries(gaCC, true);
   
   ArrayResize(gnCC, 2);
   CopyClose(gn_p, PERIOD_D1, 0, 2, gnCC);
   ArraySetAsSeries(gnCC, true);
   
   ArrayResize(gcCC, 2);
   CopyClose(gc_p, PERIOD_D1, 0, 2, gcCC);
   ArraySetAsSeries(gcCC, true);
   
   ArrayResize(gfCC, 2);
   CopyClose(gf_p, PERIOD_D1, 0, 2, gfCC);
   ArraySetAsSeries(gfCC, true);
   
   ArrayResize(gjCC, 2);
   CopyClose(gj_p, PERIOD_D1, 0, 2, gjCC);
   ArraySetAsSeries(gjCC, true);
   
   ArrayResize(auCC, 2);
   CopyClose(au_p, PERIOD_D1, 0, 2, auCC);
   ArraySetAsSeries(auCC, true);
   
   ArrayResize(anCC, 2);
   CopyClose(an_p, PERIOD_D1, 0, 2, anCC);
   ArraySetAsSeries(anCC, true);
   
   ArrayResize(acCC, 2);
   CopyClose(ac_p, PERIOD_D1, 0, 2, acCC);
   ArraySetAsSeries(acCC, true);
   
   ArrayResize(afCC, 2);
   CopyClose(af_p, PERIOD_D1, 0, 2, afCC);
   ArraySetAsSeries(afCC, true);
   
   ArrayResize(ajCC, 2);
   CopyClose(aj_p, PERIOD_D1, 0, 2, ajCC);
   ArraySetAsSeries(ajCC, true);
   
   ArrayResize(nuCC, 2);
   CopyClose(nu_p, PERIOD_D1, 0, 2, nuCC);
   ArraySetAsSeries(nuCC, true);
   
   ArrayResize(ncCC, 2);
   CopyClose(nc_p, PERIOD_D1, 0, 2, ncCC);
   ArraySetAsSeries(ncCC, true);
   
   ArrayResize(nfCC, 2);
   CopyClose(nf_p, PERIOD_D1, 0, 2, nfCC);
   ArraySetAsSeries(nfCC, true);
   
   ArrayResize(njCC, 2);
   CopyClose(nj_p, PERIOD_D1, 0, 2, njCC);
   ArraySetAsSeries(njCC, true);
   
   ArrayResize(ucCC, 2);
   CopyClose(uc_p, PERIOD_D1, 0, 2, ucCC);
   ArraySetAsSeries(ucCC, true);
   
   ArrayResize(cfCC, 2);
   CopyClose(cf_p, PERIOD_D1, 0, 2, cfCC);
   ArraySetAsSeries(cfCC, true);
   
   ArrayResize(cjCC, 2);
   CopyClose(cj_p, PERIOD_D1, 0, 2, cjCC);
   ArraySetAsSeries(cjCC, true);
   
   ArrayResize(ufCC, 2);
   CopyClose(uf_p, PERIOD_D1, 0, 2, ufCC);
   ArraySetAsSeries(ufCC, true);
   
   ArrayResize(fjCC, 2);
   CopyClose(fj_p, PERIOD_D1, 0, 2, fjCC);
   ArraySetAsSeries(fjCC, true);
   
   ArrayResize(ujCC, 2);
   CopyClose(uj_p, PERIOD_D1, 0, 2, ujCC);
   ArraySetAsSeries(ujCC, true);

   //Daily / Weekly / Monthly % moves from current close CC[0] = current day close, WC[0] = previous week close, MC[0] = previous week close - FOR PAIRS
   DWM dwm[];
   
   //0. EU
   AddToDWM(dwm, ((euCC[0] - euCC[1]) / euCC[1]) * 100, eu_p);
   //1. EG
   AddToDWM(dwm, ((egCC[0] - egCC[1]) / egCC[1]) * 100, eg_p);
   //2. EA
   AddToDWM(dwm, ((eaCC[0] - eaCC[1]) / eaCC[1]) * 100, ea_p);
   //3. EN
   AddToDWM(dwm, ((enCC[0] - enCC[1]) / enCC[1]) * 100, en_p);
   //4. EC
   AddToDWM(dwm, ((ecCC[0] - ecCC[1]) / ecCC[1]) * 100, ec_p);
   //5. EF
   AddToDWM(dwm, ((efCC[0] - efCC[1]) / efCC[1]) * 100, ef_p);
   //6. EJ
   AddToDWM(dwm, ((ejCC[0] - ejCC[1]) / ejCC[1]) * 100, ej_p);
   //7. GU
   AddToDWM(dwm, ((guCC[0] - guCC[1]) / guCC[1]) * 100, gu_p);
   //8. GA
   AddToDWM(dwm, ((gaCC[0] - gaCC[1]) / gaCC[1]) * 100, ga_p);
   //9. GN
   AddToDWM(dwm, ((gnCC[0] - gnCC[1]) / gnCC[1]) * 100, gn_p);
   //10. GC
   AddToDWM(dwm, ((gcCC[0] - gcCC[1]) / gcCC[1]) * 100, gc_p);
   //11. GF
   AddToDWM(dwm, ((gfCC[0] - gfCC[1]) / gfCC[1]) * 100, gf_p);
   //12. GJ
   AddToDWM(dwm, ((gjCC[0] - gjCC[1]) / gjCC[1]) * 100, gj_p);
   //13. AU
   AddToDWM(dwm, ((auCC[0] - auCC[1]) / auCC[1]) * 100, au_p);
   //14. AN
   AddToDWM(dwm, ((anCC[0] - anCC[1]) / anCC[1]) * 100, an_p);
   //15. AC
   AddToDWM(dwm, ((acCC[0] - acCC[1]) / acCC[1]) * 100, ac_p);
   //16. AF
   AddToDWM(dwm, ((afCC[0] - afCC[1]) / afCC[1]) * 100, af_p);
   //17. AJ
   AddToDWM(dwm, ((ajCC[0] - ajCC[1]) / ajCC[1]) * 100, aj_p);
   //18. NU
   AddToDWM(dwm, ((nuCC[0] - nuCC[1]) / nuCC[1]) * 100, nu_p);
   //19. NC
   AddToDWM(dwm, ((ncCC[0] - ncCC[1]) / ncCC[1]) * 100, nc_p);
   //20. NF
   AddToDWM(dwm, ((nfCC[0] - nfCC[1]) / nfCC[1]) * 100, nf_p);
   //21. NJ
   AddToDWM(dwm, ((njCC[0] - njCC[1]) / njCC[1]) * 100, nj_p);
   //22. UC
   AddToDWM(dwm, ((ucCC[0] - ucCC[1]) / ucCC[1]) * 100, uc_p);
   //23. CF
   AddToDWM(dwm, ((cfCC[0] - cfCC[1]) / cfCC[1]) * 100, cf_p);
   //24. CJ
   AddToDWM(dwm, ((cjCC[0] - cjCC[1]) / cjCC[1]) * 100, cj_p);
   //25. UF
   AddToDWM(dwm, ((ufCC[0] - ufCC[1]) / ufCC[1]) * 100, uf_p);
   //26. FJ
   AddToDWM(dwm, ((fjCC[0] - fjCC[1]) / fjCC[1]) * 100, fj_p);
   //27. UJ
   AddToDWM(dwm, ((ujCC[0] - ujCC[1]) / ujCC[1]) * 100, uj_p);
   
   double eufopen = (euCC[0] - euCC[1]) / MarketInfo(eu_p, MODE_POINT) / 10 * eupl;   
   double egfopen = (egCC[0] - egCC[1]) / MarketInfo(eg_p, MODE_POINT) / 10 * egpl;
   double eafopen = (eaCC[0] - eaCC[1]) / MarketInfo(ea_p, MODE_POINT) / 10 * eapl;
   double enfopen = (enCC[0] - enCC[1]) / MarketInfo(en_p, MODE_POINT) / 10 * enpl;
   double ecfopen = (ecCC[0] - ecCC[1]) / MarketInfo(ec_p, MODE_POINT) / 10 * ecpl;
   double effopen = (efCC[0] - efCC[1]) / MarketInfo(ef_p, MODE_POINT) / 10 * efpl;
   double ejfopen = (ejCC[0] - ejCC[1]) / MarketInfo(ej_p, MODE_POINT) / 10 * ejpl;
   double gufopen = (guCC[0] - guCC[1]) / MarketInfo(gu_p, MODE_POINT) / 10 * gupl;
   double gafopen = (gaCC[0] - gaCC[1]) / MarketInfo(ga_p, MODE_POINT) / 10 * gapl;
   double gnfopen = (gnCC[0] - gnCC[1]) / MarketInfo(gn_p, MODE_POINT) / 10 * gnpl;
   double gcfopen = (gcCC[0] - gcCC[1]) / MarketInfo(gc_p, MODE_POINT) / 10 * gcpl;
   double gffopen = (gfCC[0] - gfCC[1]) / MarketInfo(gf_p, MODE_POINT) / 10 * gfpl;
   double gjfopen = (gjCC[0] - gjCC[1]) / MarketInfo(gj_p, MODE_POINT) / 10 * gjpl;
   double aufopen = (auCC[0] - auCC[1]) / MarketInfo(au_p, MODE_POINT) / 10 * aupl;
   double anfopen = (anCC[0] - anCC[1]) / MarketInfo(an_p, MODE_POINT) / 10 * anpl;
   double acfopen = (acCC[0] - acCC[1]) / MarketInfo(ac_p, MODE_POINT) / 10 * acpl;
   double affopen = (afCC[0] - afCC[1]) / MarketInfo(af_p, MODE_POINT) / 10 * afpl;
   double ajfopen = (ajCC[0] - ajCC[1]) / MarketInfo(aj_p, MODE_POINT) / 10 * ajpl;
   double nufopen = (nuCC[0] - nuCC[1]) / MarketInfo(nu_p, MODE_POINT) / 10 * nupl;
   double ncfopen = (ncCC[0] - ncCC[1]) / MarketInfo(nc_p, MODE_POINT) / 10 * ncpl;
   double nffopen = (nfCC[0] - nfCC[1]) / MarketInfo(nf_p, MODE_POINT) / 10 * nfpl;
   double njfopen = (njCC[0] - njCC[1]) / MarketInfo(nj_p, MODE_POINT) / 10 * njpl;
   double ucfopen = (ucCC[0] - ucCC[1]) / MarketInfo(uc_p, MODE_POINT) / 10 * ucpl;
   double cffopen = (cfCC[0] - cfCC[1]) / MarketInfo(cf_p, MODE_POINT) / 10 * cfpl;
   double cjfopen = (cjCC[0] - cjCC[1]) / MarketInfo(cj_p, MODE_POINT) / 10 * cjpl;
   double uffopen = (ufCC[0] - ufCC[1]) / MarketInfo(uf_p, MODE_POINT) / 10 * ufpl;
   double fjfopen = (fjCC[0] - fjCC[1]) / MarketInfo(fj_p, MODE_POINT) / 10 * fjpl;
   double ujfopen = (ujCC[0] - ujCC[1]) / MarketInfo(uj_p, MODE_POINT) / 10 * ujpl;
   
   //JSON pass sorted data to sort()
   Data dynamicArray[];
   
   AddToSortData(dynamicArray, dwm[0].daily, eufopen, eu_p);
   AddToSortData(dynamicArray, dwm[1].daily, egfopen, eg_p);
   AddToSortData(dynamicArray, dwm[2].daily, eafopen, ea_p);
   AddToSortData(dynamicArray, dwm[3].daily, enfopen, en_p);
   AddToSortData(dynamicArray, dwm[4].daily, ecfopen, ec_p);
   AddToSortData(dynamicArray, dwm[5].daily, effopen, ef_p);
   AddToSortData(dynamicArray, dwm[6].daily, ejfopen, ej_p);
   AddToSortData(dynamicArray, dwm[7].daily, gufopen, gu_p);
   AddToSortData(dynamicArray, dwm[8].daily, gafopen, ga_p);
   AddToSortData(dynamicArray, dwm[9].daily, gnfopen, gn_p);
   AddToSortData(dynamicArray, dwm[10].daily, gcfopen, gc_p);
   AddToSortData(dynamicArray, dwm[11].daily, gffopen, gf_p);
   AddToSortData(dynamicArray, dwm[12].daily, gjfopen, gj_p);
   AddToSortData(dynamicArray, dwm[13].daily, aufopen, au_p);
   AddToSortData(dynamicArray, dwm[14].daily, anfopen, an_p);
   AddToSortData(dynamicArray, dwm[15].daily, acfopen, ac_p);
   AddToSortData(dynamicArray, dwm[16].daily, affopen, af_p);
   AddToSortData(dynamicArray, dwm[17].daily, ajfopen, aj_p);
   AddToSortData(dynamicArray, dwm[18].daily, nufopen, nu_p);
   AddToSortData(dynamicArray, dwm[19].daily, ncfopen, nc_p);
   AddToSortData(dynamicArray, dwm[20].daily, nffopen, nf_p);
   AddToSortData(dynamicArray, dwm[21].daily, njfopen, nj_p);
   AddToSortData(dynamicArray, dwm[22].daily, ucfopen, uc_p);
   AddToSortData(dynamicArray, dwm[23].daily, cffopen, cf_p);
   AddToSortData(dynamicArray, dwm[24].daily, cjfopen, cj_p);
   AddToSortData(dynamicArray, dwm[25].daily, uffopen, uf_p);
   AddToSortData(dynamicArray, dwm[26].daily, fjfopen, fj_p);
   AddToSortData(dynamicArray, dwm[27].daily, ujfopen, uj_p);
      
   ArraySortStruct(dynamicArray, daily);
      
   mainsort["a"][0].Add(dynamicArray[27].pair);
   mainsort["a"][0].Add(dynamicArray[27].pips, 1);
   
   mainsort["a"][1].Add(dynamicArray[26].pair);
   mainsort["a"][1].Add(dynamicArray[26].pips, 1);
   
   mainsort["a"][2].Add(dynamicArray[25].pair);
   mainsort["a"][2].Add(dynamicArray[25].pips, 1);
   
   mainsort["a"][3].Add(dynamicArray[24].pair);
   mainsort["a"][3].Add(dynamicArray[24].pips, 1);
   
   mainsort["a"][4].Add(dynamicArray[23].pair);
   mainsort["a"][4].Add(dynamicArray[23].pips, 1);
   
   mainsort["a"][5].Add(dynamicArray[22].pair);
   mainsort["a"][5].Add(dynamicArray[22].pips, 1);
   
   mainsort["a"][6].Add(dynamicArray[21].pair);
   mainsort["a"][6].Add(dynamicArray[21].pips, 1);
   
   mainsort["a"][7].Add(dynamicArray[20].pair);
   mainsort["a"][7].Add(dynamicArray[20].pips, 1);
   
   mainsort["a"][8].Add(dynamicArray[19].pair);
   mainsort["a"][8].Add(dynamicArray[19].pips, 1);
   
   mainsort["a"][9].Add(dynamicArray[18].pair);
   mainsort["a"][9].Add(dynamicArray[18].pips, 1);
   
   mainsort["a"][10].Add(dynamicArray[17].pair);
   mainsort["a"][10].Add(dynamicArray[17].pips, 1);
   
   mainsort["a"][11].Add(dynamicArray[16].pair);
   mainsort["a"][11].Add(dynamicArray[16].pips, 1);
   
   mainsort["a"][12].Add(dynamicArray[15].pair);
   mainsort["a"][12].Add(dynamicArray[15].pips, 1);
   
   mainsort["a"][13].Add(dynamicArray[14].pair);
   mainsort["a"][13].Add(dynamicArray[14].pips, 1);
   
   mainsort["a"][14].Add(dynamicArray[13].pair);
   mainsort["a"][14].Add(dynamicArray[13].pips, 1);
   
   mainsort["a"][15].Add(dynamicArray[12].pair);
   mainsort["a"][15].Add(dynamicArray[12].pips, 1);
   
   mainsort["a"][16].Add(dynamicArray[11].pair);
   mainsort["a"][16].Add(dynamicArray[11].pips, 1);
   
   mainsort["a"][17].Add(dynamicArray[10].pair);
   mainsort["a"][17].Add(dynamicArray[10].pips, 1);
   
   mainsort["a"][18].Add(dynamicArray[9].pair);
   mainsort["a"][18].Add(dynamicArray[9].pips, 1);
   
   mainsort["a"][19].Add(dynamicArray[8].pair);
   mainsort["a"][19].Add(dynamicArray[8].pips, 1);
   
   mainsort["a"][20].Add(dynamicArray[7].pair);
   mainsort["a"][20].Add(dynamicArray[7].pips, 1);
   
   mainsort["a"][21].Add(dynamicArray[6].pair);
   mainsort["a"][21].Add(dynamicArray[6].pips, 1);
   
   mainsort["a"][22].Add(dynamicArray[5].pair);
   mainsort["a"][22].Add(dynamicArray[5].pips, 1);
   
   mainsort["a"][23].Add(dynamicArray[4].pair);
   mainsort["a"][23].Add(dynamicArray[4].pips, 1);
   
   mainsort["a"][24].Add(dynamicArray[3].pair);
   mainsort["a"][24].Add(dynamicArray[3].pips, 1);
   
   mainsort["a"][25].Add(dynamicArray[2].pair);
   mainsort["a"][25].Add(dynamicArray[2].pips, 1);
   
   mainsort["a"][26].Add(dynamicArray[1].pair);
   mainsort["a"][26].Add(dynamicArray[1].pips, 1);
   
   mainsort["a"][27].Add(dynamicArray[0].pair);
   mainsort["a"][27].Add(dynamicArray[0].pips, 1);
   
   ArrayFree(dynamicArray);
   
   FibData Fibz[];
   
   //FIBS
   int hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - tim;
   //EURUSD
   double eu_p_bid = MarketInfo(eu_p, MODE_BID);
   int eu_p_ascheck = 0;
   bool eu_p_midcheck = 0;
   int eu_p_wmidcheck = 0;
   bool eu_p_d1check = false;
   bool eu_p_d2check = false;
   bool eu_p_d3check = false;
   bool eu_p_d4check = false;
   
   double eu_p_midline = (iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double eu_p_ashi = iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double eu_p_aslo = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (eu_p_bid > eu_p_ashi) eu_p_ascheck = 1;
   else if (eu_p_bid < eu_p_ashi && eu_p_bid > eu_p_aslo) eu_p_ascheck = 2;
   else if (eu_p_bid < eu_p_aslo) eu_p_ascheck = 3;
   
   if (eu_p_bid > eu_p_midline) eu_p_midcheck = true;
   if (eu_p_bid < eu_p_midline) eu_p_midcheck = false;
   
   double eu_p_pricea = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double eu_p_priceb = 0;
   if (iClose(eu_p, PERIOD_D1, 1) > iClose(eu_p, PERIOD_D1, 2))
   eu_p_priceb = iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   eu_p_priceb = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double eu_p_midline2 = (eu_p_pricea + eu_p_priceb) / 2;
   double eu_p_d1up2 = MathMax(eu_p_pricea, eu_p_priceb) + 2 * MathAbs(eu_p_pricea - eu_p_priceb);
   double eu_p_d1dn2 = MathMin(eu_p_pricea, eu_p_priceb) - 2 * MathAbs(eu_p_pricea - eu_p_priceb);
   
   if (eu_p_bid > MathMax(eu_p_midline, eu_p_midline2)) eu_p_wmidcheck = 1;
   if (eu_p_bid < MathMax(eu_p_midline, eu_p_midline2) && eu_p_bid > MathMin(eu_p_midline, eu_p_midline2)) eu_p_wmidcheck = 2;
   if (eu_p_bid < MathMin(eu_p_midline, eu_p_midline2)) eu_p_wmidcheck = 3;
   
   if (eu_p_bid > eu_p_d1up2 || eu_p_bid < eu_p_d1dn2) eu_p_d1check = true;   
      
   double eu_p_pricen = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double eu_p_priceo = 0;
   if (iClose(eu_p, PERIOD_D1, 2) > iClose(eu_p, PERIOD_D1, 3))
   eu_p_priceo = iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   eu_p_priceo = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double eu_p_midline3 = (eu_p_pricen + eu_p_priceo) / 2;
   double eu_p_d2up2 = MathMax(eu_p_pricen, eu_p_priceo) + 2 * MathAbs(eu_p_pricen - eu_p_priceo);
   double eu_p_d2dn2 = MathMin(eu_p_pricen, eu_p_priceo) - 2 * MathAbs(eu_p_pricen - eu_p_priceo);
   
   if (eu_p_bid > eu_p_d2up2 || eu_p_bid < eu_p_d2dn2) eu_p_d2check = true;
   
   double eu_p_pricer = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double eu_p_prices = 0;
   if (iClose(eu_p, PERIOD_D1, 3) > iClose(eu_p, PERIOD_D1, 4))
   eu_p_prices = iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   eu_p_prices = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double eu_p_midline4 = (eu_p_pricer + eu_p_prices) / 2;
   double eu_p_d3up2 = MathMax(eu_p_pricer, eu_p_prices) + 2 * MathAbs(eu_p_pricer - eu_p_prices);
   double eu_p_d3dn2 = MathMin(eu_p_pricer, eu_p_prices) - 2 * MathAbs(eu_p_pricer - eu_p_prices);
   
   if (eu_p_bid > eu_p_d3up2 || eu_p_bid < eu_p_d3dn2) eu_p_d3check = true;
   
   double eu_p_pricex = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double eu_p_pricey = 0;
   if (iClose(eu_p, PERIOD_D1, 4) > iClose(eu_p, PERIOD_D1, 5))
   eu_p_pricey = iHigh(eu_p, PERIOD_H1, iHighest(eu_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   eu_p_pricey = iLow(eu_p, PERIOD_H1, iLowest(eu_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double eu_p_midline5 = (eu_p_pricex + eu_p_pricey) / 2;
   double eu_p_d4up2 = MathMax(eu_p_pricex, eu_p_pricey) + 2 * MathAbs(eu_p_pricex - eu_p_pricey);
   double eu_p_d4dn2 = MathMin(eu_p_pricex, eu_p_pricey) - 2 * MathAbs(eu_p_pricex - eu_p_pricey);
   
   if (eu_p_bid > eu_p_d4up2 || eu_p_bid < eu_p_d4dn2) eu_p_d4check = true;
   
   AddToDrata(Fibz, dwm[0].daily, eu_p_ascheck, eu_p_midcheck, eu_p_wmidcheck, eu_p_d1check, eu_p_d2check, eu_p_d3check, eu_p_d4check, eu_p);
     
   //EURGBP
   double eg_p_bid = MarketInfo(eg_p, MODE_BID);
   int eg_p_ascheck = 0;
   bool eg_p_midcheck = 0;
   int eg_p_wmidcheck = 0;
   bool eg_p_d1check = false;
   bool eg_p_d2check = false;
   bool eg_p_d3check = false;
   bool eg_p_d4check = false;
   
   double eg_p_midline = (iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double eg_p_ashi = iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double eg_p_aslo = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (eg_p_bid > eg_p_ashi) eg_p_ascheck = 1;
   else if (eg_p_bid < eg_p_ashi && eg_p_bid > eg_p_aslo) eg_p_ascheck = 2;
   else if (eg_p_bid < eg_p_aslo) eg_p_ascheck = 3;
   
   if (eg_p_bid > eg_p_midline) eg_p_midcheck = true;
   if (eg_p_bid < eg_p_midline) eg_p_midcheck = false;
   
   double eg_p_pricea = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double eg_p_priceb = 0;
   if (iClose(eg_p, PERIOD_D1, 1) > iClose(eg_p, PERIOD_D1, 2))
   eg_p_priceb = iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   eg_p_priceb = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double eg_p_midline2 = (eg_p_pricea + eg_p_priceb) / 2;
   double eg_p_d1up2 = MathMax(eg_p_pricea, eg_p_priceb) + 2 * MathAbs(eg_p_pricea - eg_p_priceb);
   double eg_p_d1dn2 = MathMin(eg_p_pricea, eg_p_priceb) - 2 * MathAbs(eg_p_pricea - eg_p_priceb);
   
   if (eg_p_bid > MathMax(eg_p_midline, eg_p_midline2)) eg_p_wmidcheck = 1;
   if (eg_p_bid < MathMax(eg_p_midline, eg_p_midline2) && eg_p_bid > MathMin(eg_p_midline, eg_p_midline2)) eg_p_wmidcheck = 2;
   if (eg_p_bid < MathMin(eg_p_midline, eg_p_midline2)) eg_p_wmidcheck = 3;
   
   if (eg_p_bid > eg_p_d1up2 || eg_p_bid < eg_p_d1dn2) eg_p_d1check = true;   
      
   double eg_p_pricen = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double eg_p_priceo = 0;
   if (iClose(eg_p, PERIOD_D1, 2) > iClose(eg_p, PERIOD_D1, 3))
   eg_p_priceo = iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   eg_p_priceo = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double eg_p_midline3 = (eg_p_pricen + eg_p_priceo) / 2;
   double eg_p_d2up2 = MathMax(eg_p_pricen, eg_p_priceo) + 2 * MathAbs(eg_p_pricen - eg_p_priceo);
   double eg_p_d2dn2 = MathMin(eg_p_pricen, eg_p_priceo) - 2 * MathAbs(eg_p_pricen - eg_p_priceo);
   
   if (eg_p_bid > eg_p_d2up2 || eg_p_bid < eg_p_d2dn2) eg_p_d2check = true;
   
   double eg_p_pricer = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double eg_p_prices = 0;
   if (iClose(eg_p, PERIOD_D1, 3) > iClose(eg_p, PERIOD_D1, 4))
   eg_p_prices = iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   eg_p_prices = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double eg_p_midline4 = (eg_p_pricer + eg_p_prices) / 2;
   double eg_p_d3up2 = MathMax(eg_p_pricer, eg_p_prices) + 2 * MathAbs(eg_p_pricer - eg_p_prices);
   double eg_p_d3dn2 = MathMin(eg_p_pricer, eg_p_prices) - 2 * MathAbs(eg_p_pricer - eg_p_prices);
   
   if (eg_p_bid > eg_p_d3up2 || eg_p_bid < eg_p_d3dn2) eg_p_d3check = true;
   
   double eg_p_pricex = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double eg_p_pricey = 0;
   if (iClose(eg_p, PERIOD_D1, 4) > iClose(eg_p, PERIOD_D1, 5))
   eg_p_pricey = iHigh(eg_p, PERIOD_H1, iHighest(eg_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   eg_p_pricey = iLow(eg_p, PERIOD_H1, iLowest(eg_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double eg_p_midline5 = (eg_p_pricex + eg_p_pricey) / 2;
   double eg_p_d4up2 = MathMax(eg_p_pricex, eg_p_pricey) + 2 * MathAbs(eg_p_pricex - eg_p_pricey);
   double eg_p_d4dn2 = MathMin(eg_p_pricex, eg_p_pricey) - 2 * MathAbs(eg_p_pricex - eg_p_pricey);
   
   if (eg_p_bid > eg_p_d4up2 || eg_p_bid < eg_p_d4dn2) eg_p_d4check = true;
   
   AddToDrata(Fibz, dwm[1].daily, eg_p_ascheck, eg_p_midcheck, eg_p_wmidcheck, eg_p_d1check, eg_p_d2check, eg_p_d3check, eg_p_d4check, eg_p);

   //EURAUD
   double ea_p_bid = MarketInfo(ea_p, MODE_BID);
   int ea_p_ascheck = 0;
   bool ea_p_midcheck = 0;
   int ea_p_wmidcheck = 0;
   bool ea_p_d1check = false;
   bool ea_p_d2check = false;
   bool ea_p_d3check = false;
   bool ea_p_d4check = false;
   
   double ea_p_midline = (iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ea_p_ashi = iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ea_p_aslo = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ea_p_bid > ea_p_ashi) ea_p_ascheck = 1;
   else if (ea_p_bid < ea_p_ashi && ea_p_bid > ea_p_aslo) ea_p_ascheck = 2;
   else if (ea_p_bid < ea_p_aslo) ea_p_ascheck = 3;
   
   if (ea_p_bid > ea_p_midline) ea_p_midcheck = true;
   if (ea_p_bid < ea_p_midline) ea_p_midcheck = false;
   
   double ea_p_pricea = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ea_p_priceb = 0;
   if (iClose(ea_p, PERIOD_D1, 1) > iClose(ea_p, PERIOD_D1, 2))
   ea_p_priceb = iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ea_p_priceb = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ea_p_midline2 = (ea_p_pricea + ea_p_priceb) / 2;
   double ea_p_d1up2 = MathMax(ea_p_pricea, ea_p_priceb) + 2 * MathAbs(ea_p_pricea - ea_p_priceb);
   double ea_p_d1dn2 = MathMin(ea_p_pricea, ea_p_priceb) - 2 * MathAbs(ea_p_pricea - ea_p_priceb);
   
   if (ea_p_bid > MathMax(ea_p_midline, ea_p_midline2)) ea_p_wmidcheck = 1;
   if (ea_p_bid < MathMax(ea_p_midline, ea_p_midline2) && ea_p_bid > MathMin(ea_p_midline, ea_p_midline2)) ea_p_wmidcheck = 2;
   if (ea_p_bid < MathMin(ea_p_midline, ea_p_midline2)) ea_p_wmidcheck = 3;
   
   if (ea_p_bid > ea_p_d1up2 || ea_p_bid < ea_p_d1dn2) ea_p_d1check = true;   
      
   double ea_p_pricen = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ea_p_priceo = 0;
   if (iClose(ea_p, PERIOD_D1, 2) > iClose(ea_p, PERIOD_D1, 3))
   ea_p_priceo = iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ea_p_priceo = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ea_p_midline3 = (ea_p_pricen + ea_p_priceo) / 2;
   double ea_p_d2up2 = MathMax(ea_p_pricen, ea_p_priceo) + 2 * MathAbs(ea_p_pricen - ea_p_priceo);
   double ea_p_d2dn2 = MathMin(ea_p_pricen, ea_p_priceo) - 2 * MathAbs(ea_p_pricen - ea_p_priceo);
   
   if (ea_p_bid > ea_p_d2up2 || ea_p_bid < ea_p_d2dn2) ea_p_d2check = true;
   
   double ea_p_pricer = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ea_p_prices = 0;
   if (iClose(ea_p, PERIOD_D1, 3) > iClose(ea_p, PERIOD_D1, 4))
   ea_p_prices = iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ea_p_prices = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ea_p_midline4 = (ea_p_pricer + ea_p_prices) / 2;
   double ea_p_d3up2 = MathMax(ea_p_pricer, ea_p_prices) + 2 * MathAbs(ea_p_pricer - ea_p_prices);
   double ea_p_d3dn2 = MathMin(ea_p_pricer, ea_p_prices) - 2 * MathAbs(ea_p_pricer - ea_p_prices);
   
   if (ea_p_bid > ea_p_d3up2 || ea_p_bid < ea_p_d3dn2) ea_p_d3check = true;
   
   double ea_p_pricex = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ea_p_pricey = 0;
   if (iClose(ea_p, PERIOD_D1, 4) > iClose(ea_p, PERIOD_D1, 5))
   ea_p_pricey = iHigh(ea_p, PERIOD_H1, iHighest(ea_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ea_p_pricey = iLow(ea_p, PERIOD_H1, iLowest(ea_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ea_p_midline5 = (ea_p_pricex + ea_p_pricey) / 2;
   double ea_p_d4up2 = MathMax(ea_p_pricex, ea_p_pricey) + 2 * MathAbs(ea_p_pricex - ea_p_pricey);
   double ea_p_d4dn2 = MathMin(ea_p_pricex, ea_p_pricey) - 2 * MathAbs(ea_p_pricex - ea_p_pricey);
   
   if (ea_p_bid > ea_p_d4up2 || ea_p_bid < ea_p_d4dn2) ea_p_d4check = true;
   
   AddToDrata(Fibz, dwm[2].daily, ea_p_ascheck, ea_p_midcheck, ea_p_wmidcheck, ea_p_d1check, ea_p_d2check, ea_p_d3check, ea_p_d4check, ea_p);
   
   //EURNZD
   double en_p_bid = MarketInfo(en_p, MODE_BID);
   int en_p_ascheck = 0;
   bool en_p_midcheck = 0;
   int en_p_wmidcheck = 0;
   bool en_p_d1check = false;
   bool en_p_d2check = false;
   bool en_p_d3check = false;
   bool en_p_d4check = false;
   
   double en_p_midline = (iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double en_p_ashi = iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double en_p_aslo = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (en_p_bid > en_p_ashi) en_p_ascheck = 1;
   else if (en_p_bid < en_p_ashi && en_p_bid > en_p_aslo) en_p_ascheck = 2;
   else if (en_p_bid < en_p_aslo) en_p_ascheck = 3;
   
   if (en_p_bid > en_p_midline) en_p_midcheck = true;
   if (en_p_bid < en_p_midline) en_p_midcheck = false;
   
   double en_p_pricea = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double en_p_priceb = 0;
   if (iClose(en_p, PERIOD_D1, 1) > iClose(en_p, PERIOD_D1, 2))
   en_p_priceb = iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   en_p_priceb = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double en_p_midline2 = (en_p_pricea + en_p_priceb) / 2;
   double en_p_d1up2 = MathMax(en_p_pricea, en_p_priceb) + 2 * MathAbs(en_p_pricea - en_p_priceb);
   double en_p_d1dn2 = MathMin(en_p_pricea, en_p_priceb) - 2 * MathAbs(en_p_pricea - en_p_priceb);
   
   if (en_p_bid > MathMax(en_p_midline, en_p_midline2)) en_p_wmidcheck = 1;
   if (en_p_bid < MathMax(en_p_midline, en_p_midline2) && en_p_bid > MathMin(en_p_midline, en_p_midline2)) en_p_wmidcheck = 2;
   if (en_p_bid < MathMin(en_p_midline, en_p_midline2)) en_p_wmidcheck = 3;
   
   if (en_p_bid > en_p_d1up2 || en_p_bid < en_p_d1dn2) en_p_d1check = true;   
      
   double en_p_pricen = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double en_p_priceo = 0;
   if (iClose(en_p, PERIOD_D1, 2) > iClose(en_p, PERIOD_D1, 3))
   en_p_priceo = iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   en_p_priceo = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double en_p_midline3 = (en_p_pricen + en_p_priceo) / 2;
   double en_p_d2up2 = MathMax(en_p_pricen, en_p_priceo) + 2 * MathAbs(en_p_pricen - en_p_priceo);
   double en_p_d2dn2 = MathMin(en_p_pricen, en_p_priceo) - 2 * MathAbs(en_p_pricen - en_p_priceo);
   
   if (en_p_bid > en_p_d2up2 || en_p_bid < en_p_d2dn2) en_p_d2check = true;
   
   double en_p_pricer = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double en_p_prices = 0;
   if (iClose(en_p, PERIOD_D1, 3) > iClose(en_p, PERIOD_D1, 4))
   en_p_prices = iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   en_p_prices = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double en_p_midline4 = (en_p_pricer + en_p_prices) / 2;
   double en_p_d3up2 = MathMax(en_p_pricer, en_p_prices) + 2 * MathAbs(en_p_pricer - en_p_prices);
   double en_p_d3dn2 = MathMin(en_p_pricer, en_p_prices) - 2 * MathAbs(en_p_pricer - en_p_prices);
   
   if (en_p_bid > en_p_d3up2 || en_p_bid < en_p_d3dn2) en_p_d3check = true;
   
   double en_p_pricex = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double en_p_pricey = 0;
   if (iClose(en_p, PERIOD_D1, 4) > iClose(en_p, PERIOD_D1, 5))
   en_p_pricey = iHigh(en_p, PERIOD_H1, iHighest(en_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   en_p_pricey = iLow(en_p, PERIOD_H1, iLowest(en_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double en_p_midline5 = (en_p_pricex + en_p_pricey) / 2;
   double en_p_d4up2 = MathMax(en_p_pricex, en_p_pricey) + 2 * MathAbs(en_p_pricex - en_p_pricey);
   double en_p_d4dn2 = MathMin(en_p_pricex, en_p_pricey) - 2 * MathAbs(en_p_pricex - en_p_pricey);
   
   if (en_p_bid > en_p_d4up2 || en_p_bid < en_p_d4dn2) en_p_d4check = true;
   
   AddToDrata(Fibz, dwm[3].daily, en_p_ascheck, en_p_midcheck, en_p_wmidcheck, en_p_d1check, en_p_d2check, en_p_d3check, en_p_d4check, en_p);
   
   //EURCAD
   double ec_p_bid = MarketInfo(ec_p, MODE_BID);
   int ec_p_ascheck = 0;
   bool ec_p_midcheck = 0;
   int ec_p_wmidcheck = 0;
   bool ec_p_d1check = false;
   bool ec_p_d2check = false;
   bool ec_p_d3check = false;
   bool ec_p_d4check = false;
   
   double ec_p_midline = (iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ec_p_ashi = iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ec_p_aslo = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ec_p_bid > ec_p_ashi) ec_p_ascheck = 1;
   else if (ec_p_bid < ec_p_ashi && ec_p_bid > ec_p_aslo) ec_p_ascheck = 2;
   else if (ec_p_bid < ec_p_aslo) ec_p_ascheck = 3;
   
   if (ec_p_bid > ec_p_midline) ec_p_midcheck = true;
   if (ec_p_bid < ec_p_midline) ec_p_midcheck = false;
   
   double ec_p_pricea = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ec_p_priceb = 0;
   if (iClose(ec_p, PERIOD_D1, 1) > iClose(ec_p, PERIOD_D1, 2))
   ec_p_priceb = iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ec_p_priceb = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ec_p_midline2 = (ec_p_pricea + ec_p_priceb) / 2;
   double ec_p_d1up2 = MathMax(ec_p_pricea, ec_p_priceb) + 2 * MathAbs(ec_p_pricea - ec_p_priceb);
   double ec_p_d1dn2 = MathMin(ec_p_pricea, ec_p_priceb) - 2 * MathAbs(ec_p_pricea - ec_p_priceb);
   
   if (ec_p_bid > MathMax(ec_p_midline, ec_p_midline2)) ec_p_wmidcheck = 1;
   if (ec_p_bid < MathMax(ec_p_midline, ec_p_midline2) && ec_p_bid > MathMin(ec_p_midline, ec_p_midline2)) ec_p_wmidcheck = 2;
   if (ec_p_bid < MathMin(ec_p_midline, ec_p_midline2)) ec_p_wmidcheck = 3;
   
   if (ec_p_bid > ec_p_d1up2 || ec_p_bid < ec_p_d1dn2) ec_p_d1check = true;   
      
   double ec_p_pricen = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ec_p_priceo = 0;
   if (iClose(ec_p, PERIOD_D1, 2) > iClose(ec_p, PERIOD_D1, 3))
   ec_p_priceo = iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ec_p_priceo = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ec_p_midline3 = (ec_p_pricen + ec_p_priceo) / 2;
   double ec_p_d2up2 = MathMax(ec_p_pricen, ec_p_priceo) + 2 * MathAbs(ec_p_pricen - ec_p_priceo);
   double ec_p_d2dn2 = MathMin(ec_p_pricen, ec_p_priceo) - 2 * MathAbs(ec_p_pricen - ec_p_priceo);
   
   if (ec_p_bid > ec_p_d2up2 || ec_p_bid < ec_p_d2dn2) ec_p_d2check = true;
   
   double ec_p_pricer = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ec_p_prices = 0;
   if (iClose(ec_p, PERIOD_D1, 3) > iClose(ec_p, PERIOD_D1, 4))
   ec_p_prices = iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ec_p_prices = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ec_p_midline4 = (ec_p_pricer + ec_p_prices) / 2;
   double ec_p_d3up2 = MathMax(ec_p_pricer, ec_p_prices) + 2 * MathAbs(ec_p_pricer - ec_p_prices);
   double ec_p_d3dn2 = MathMin(ec_p_pricer, ec_p_prices) - 2 * MathAbs(ec_p_pricer - ec_p_prices);
   
   if (ec_p_bid > ec_p_d3up2 || ec_p_bid < ec_p_d3dn2) ec_p_d3check = true;
   
   double ec_p_pricex = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ec_p_pricey = 0;
   if (iClose(ec_p, PERIOD_D1, 4) > iClose(ec_p, PERIOD_D1, 5))
   ec_p_pricey = iHigh(ec_p, PERIOD_H1, iHighest(ec_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ec_p_pricey = iLow(ec_p, PERIOD_H1, iLowest(ec_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ec_p_midline5 = (ec_p_pricex + ec_p_pricey) / 2;
   double ec_p_d4up2 = MathMax(ec_p_pricex, ec_p_pricey) + 2 * MathAbs(ec_p_pricex - ec_p_pricey);
   double ec_p_d4dn2 = MathMin(ec_p_pricex, ec_p_pricey) - 2 * MathAbs(ec_p_pricex - ec_p_pricey);
   
   if (ec_p_bid > ec_p_d4up2 || ec_p_bid < ec_p_d4dn2) ec_p_d4check = true;
   
   AddToDrata(Fibz, dwm[4].daily, ec_p_ascheck, ec_p_midcheck, ec_p_wmidcheck, ec_p_d1check, ec_p_d2check, ec_p_d3check, ec_p_d4check, ec_p);
   
   //EURCHF
   double ef_p_bid = MarketInfo(ef_p, MODE_BID);
   int ef_p_ascheck = 0;
   bool ef_p_midcheck = 0;
   int ef_p_wmidcheck = 0;
   bool ef_p_d1check = false;
   bool ef_p_d2check = false;
   bool ef_p_d3check = false;
   bool ef_p_d4check = false;
   
   double ef_p_midline = (iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ef_p_ashi = iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ef_p_aslo = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ef_p_bid > ef_p_ashi) ef_p_ascheck = 1;
   else if (ef_p_bid < ef_p_ashi && ef_p_bid > ef_p_aslo) ef_p_ascheck = 2;
   else if (ef_p_bid < ef_p_aslo) ef_p_ascheck = 3;
   
   if (ef_p_bid > ef_p_midline) ef_p_midcheck = true;
   if (ef_p_bid < ef_p_midline) ef_p_midcheck = false;
   
   double ef_p_pricea = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ef_p_priceb = 0;
   if (iClose(ef_p, PERIOD_D1, 1) > iClose(ef_p, PERIOD_D1, 2))
   ef_p_priceb = iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ef_p_priceb = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ef_p_midline2 = (ef_p_pricea + ef_p_priceb) / 2;
   double ef_p_d1up2 = MathMax(ef_p_pricea, ef_p_priceb) + 2 * MathAbs(ef_p_pricea - ef_p_priceb);
   double ef_p_d1dn2 = MathMin(ef_p_pricea, ef_p_priceb) - 2 * MathAbs(ef_p_pricea - ef_p_priceb);
   
   if (ef_p_bid > MathMax(ef_p_midline, ef_p_midline2)) ef_p_wmidcheck = 1;
   if (ef_p_bid < MathMax(ef_p_midline, ef_p_midline2) && ef_p_bid > MathMin(ef_p_midline, ef_p_midline2)) ef_p_wmidcheck = 2;
   if (ef_p_bid < MathMin(ef_p_midline, ef_p_midline2)) ef_p_wmidcheck = 3;
   
   if (ef_p_bid > ef_p_d1up2 || ef_p_bid < ef_p_d1dn2) ef_p_d1check = true;   
      
   double ef_p_pricen = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ef_p_priceo = 0;
   if (iClose(ef_p, PERIOD_D1, 2) > iClose(ef_p, PERIOD_D1, 3))
   ef_p_priceo = iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ef_p_priceo = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ef_p_midline3 = (ef_p_pricen + ef_p_priceo) / 2;
   double ef_p_d2up2 = MathMax(ef_p_pricen, ef_p_priceo) + 2 * MathAbs(ef_p_pricen - ef_p_priceo);
   double ef_p_d2dn2 = MathMin(ef_p_pricen, ef_p_priceo) - 2 * MathAbs(ef_p_pricen - ef_p_priceo);
   
   if (ef_p_bid > ef_p_d2up2 || ef_p_bid < ef_p_d2dn2) ef_p_d2check = true;
   
   double ef_p_pricer = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ef_p_prices = 0;
   if (iClose(ef_p, PERIOD_D1, 3) > iClose(ef_p, PERIOD_D1, 4))
   ef_p_prices = iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ef_p_prices = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ef_p_midline4 = (ef_p_pricer + ef_p_prices) / 2;
   double ef_p_d3up2 = MathMax(ef_p_pricer, ef_p_prices) + 2 * MathAbs(ef_p_pricer - ef_p_prices);
   double ef_p_d3dn2 = MathMin(ef_p_pricer, ef_p_prices) - 2 * MathAbs(ef_p_pricer - ef_p_prices);
   
   if (ef_p_bid > ef_p_d3up2 || ef_p_bid < ef_p_d3dn2) ef_p_d3check = true;
   
   double ef_p_pricex = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ef_p_pricey = 0;
   if (iClose(ef_p, PERIOD_D1, 4) > iClose(ef_p, PERIOD_D1, 5))
   ef_p_pricey = iHigh(ef_p, PERIOD_H1, iHighest(ef_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ef_p_pricey = iLow(ef_p, PERIOD_H1, iLowest(ef_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ef_p_midline5 = (ef_p_pricex + ef_p_pricey) / 2;
   double ef_p_d4up2 = MathMax(ef_p_pricex, ef_p_pricey) + 2 * MathAbs(ef_p_pricex - ef_p_pricey);
   double ef_p_d4dn2 = MathMin(ef_p_pricex, ef_p_pricey) - 2 * MathAbs(ef_p_pricex - ef_p_pricey);
   
   if (ef_p_bid > ef_p_d4up2 || ef_p_bid < ef_p_d4dn2) ef_p_d4check = true;
   
   AddToDrata(Fibz, dwm[5].daily, ef_p_ascheck, ef_p_midcheck, ef_p_wmidcheck, ef_p_d1check, ef_p_d2check, ef_p_d3check, ef_p_d4check, ef_p);
   
   //EURJPY
   double ej_p_bid = MarketInfo(ej_p, MODE_BID);
   int ej_p_ascheck = 0;
   bool ej_p_midcheck = 0;
   int ej_p_wmidcheck = 0;
   bool ej_p_d1check = false;
   bool ej_p_d2check = false;
   bool ej_p_d3check = false;
   bool ej_p_d4check = false;
   
   double ej_p_midline = (iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ej_p_ashi = iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ej_p_aslo = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ej_p_bid > ej_p_ashi) ej_p_ascheck = 1;
   else if (ej_p_bid < ej_p_ashi && ej_p_bid > ej_p_aslo) ej_p_ascheck = 2;
   else if (ej_p_bid < ej_p_aslo) ej_p_ascheck = 3;
   
   if (ej_p_bid > ej_p_midline) ej_p_midcheck = true;
   if (ej_p_bid < ej_p_midline) ej_p_midcheck = false;
   
   double ej_p_pricea = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ej_p_priceb = 0;
   if (iClose(ej_p, PERIOD_D1, 1) > iClose(ej_p, PERIOD_D1, 2))
   ej_p_priceb = iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ej_p_priceb = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ej_p_midline2 = (ej_p_pricea + ej_p_priceb) / 2;
   double ej_p_d1up2 = MathMax(ej_p_pricea, ej_p_priceb) + 2 * MathAbs(ej_p_pricea - ej_p_priceb);
   double ej_p_d1dn2 = MathMin(ej_p_pricea, ej_p_priceb) - 2 * MathAbs(ej_p_pricea - ej_p_priceb);
   
   if (ej_p_bid > MathMax(ej_p_midline, ej_p_midline2)) ej_p_wmidcheck = 1;
   if (ej_p_bid < MathMax(ej_p_midline, ej_p_midline2) && ej_p_bid > MathMin(ej_p_midline, ej_p_midline2)) ej_p_wmidcheck = 2;
   if (ej_p_bid < MathMin(ej_p_midline, ej_p_midline2)) ej_p_wmidcheck = 3;
   
   if (ej_p_bid > ej_p_d1up2 || ej_p_bid < ej_p_d1dn2) ej_p_d1check = true;   
      
   double ej_p_pricen = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ej_p_priceo = 0;
   if (iClose(ej_p, PERIOD_D1, 2) > iClose(ej_p, PERIOD_D1, 3))
   ej_p_priceo = iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ej_p_priceo = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ej_p_midline3 = (ej_p_pricen + ej_p_priceo) / 2;
   double ej_p_d2up2 = MathMax(ej_p_pricen, ej_p_priceo) + 2 * MathAbs(ej_p_pricen - ej_p_priceo);
   double ej_p_d2dn2 = MathMin(ej_p_pricen, ej_p_priceo) - 2 * MathAbs(ej_p_pricen - ej_p_priceo);
   
   if (ej_p_bid > ej_p_d2up2 || ej_p_bid < ej_p_d2dn2) ej_p_d2check = true;
   
   double ej_p_pricer = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ej_p_prices = 0;
   if (iClose(ej_p, PERIOD_D1, 3) > iClose(ej_p, PERIOD_D1, 4))
   ej_p_prices = iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ej_p_prices = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ej_p_midline4 = (ej_p_pricer + ej_p_prices) / 2;
   double ej_p_d3up2 = MathMax(ej_p_pricer, ej_p_prices) + 2 * MathAbs(ej_p_pricer - ej_p_prices);
   double ej_p_d3dn2 = MathMin(ej_p_pricer, ej_p_prices) - 2 * MathAbs(ej_p_pricer - ej_p_prices);
   
   if (ej_p_bid > ej_p_d3up2 || ej_p_bid < ej_p_d3dn2) ej_p_d3check = true;
   
   double ej_p_pricex = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ej_p_pricey = 0;
   if (iClose(ej_p, PERIOD_D1, 4) > iClose(ej_p, PERIOD_D1, 5))
   ej_p_pricey = iHigh(ej_p, PERIOD_H1, iHighest(ej_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ej_p_pricey = iLow(ej_p, PERIOD_H1, iLowest(ej_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ej_p_midline5 = (ej_p_pricex + ej_p_pricey) / 2;
   double ej_p_d4up2 = MathMax(ej_p_pricex, ej_p_pricey) + 2 * MathAbs(ej_p_pricex - ej_p_pricey);
   double ej_p_d4dn2 = MathMin(ej_p_pricex, ej_p_pricey) - 2 * MathAbs(ej_p_pricex - ej_p_pricey);
   
   if (ej_p_bid > ej_p_d4up2 || ej_p_bid < ej_p_d4dn2) ej_p_d4check = true;
   
   AddToDrata(Fibz, dwm[6].daily, ej_p_ascheck, ej_p_midcheck, ej_p_wmidcheck, ej_p_d1check, ej_p_d2check, ej_p_d3check, ej_p_d4check, ej_p);
   
   //GBPUSD
   double gu_p_bid = MarketInfo(gu_p, MODE_BID);
   int gu_p_ascheck = 0;
   bool gu_p_midcheck = 0;
   int gu_p_wmidcheck = 0;
   bool gu_p_d1check = false;
   bool gu_p_d2check = false;
   bool gu_p_d3check = false;
   bool gu_p_d4check = false;
   
   double gu_p_midline = (iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double gu_p_ashi = iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double gu_p_aslo = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (gu_p_bid > gu_p_ashi) gu_p_ascheck = 1;
   else if (gu_p_bid < gu_p_ashi && gu_p_bid > gu_p_aslo) gu_p_ascheck = 2;
   else if (gu_p_bid < gu_p_aslo) gu_p_ascheck = 3;
   
   if (gu_p_bid > gu_p_midline) gu_p_midcheck = true;
   if (gu_p_bid < gu_p_midline) gu_p_midcheck = false;
   
   double gu_p_pricea = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double gu_p_priceb = 0;
   if (iClose(gu_p, PERIOD_D1, 1) > iClose(gu_p, PERIOD_D1, 2))
   gu_p_priceb = iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   gu_p_priceb = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double gu_p_midline2 = (gu_p_pricea + gu_p_priceb) / 2;
   double gu_p_d1up2 = MathMax(gu_p_pricea, gu_p_priceb) + 2 * MathAbs(gu_p_pricea - gu_p_priceb);
   double gu_p_d1dn2 = MathMin(gu_p_pricea, gu_p_priceb) - 2 * MathAbs(gu_p_pricea - gu_p_priceb);
   
   if (gu_p_bid > MathMax(gu_p_midline, gu_p_midline2)) gu_p_wmidcheck = 1;
   if (gu_p_bid < MathMax(gu_p_midline, gu_p_midline2) && gu_p_bid > MathMin(gu_p_midline, gu_p_midline2)) gu_p_wmidcheck = 2;
   if (gu_p_bid < MathMin(gu_p_midline, gu_p_midline2)) gu_p_wmidcheck = 3;
   
   if (gu_p_bid > gu_p_d1up2 || gu_p_bid < gu_p_d1dn2) gu_p_d1check = true;   
      
   double gu_p_pricen = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double gu_p_priceo = 0;
   if (iClose(gu_p, PERIOD_D1, 2) > iClose(gu_p, PERIOD_D1, 3))
   gu_p_priceo = iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   gu_p_priceo = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double gu_p_midline3 = (gu_p_pricen + gu_p_priceo) / 2;
   double gu_p_d2up2 = MathMax(gu_p_pricen, gu_p_priceo) + 2 * MathAbs(gu_p_pricen - gu_p_priceo);
   double gu_p_d2dn2 = MathMin(gu_p_pricen, gu_p_priceo) - 2 * MathAbs(gu_p_pricen - gu_p_priceo);
   
   if (gu_p_bid > gu_p_d2up2 || gu_p_bid < gu_p_d2dn2) gu_p_d2check = true;
   
   double gu_p_pricer = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double gu_p_prices = 0;
   if (iClose(gu_p, PERIOD_D1, 3) > iClose(gu_p, PERIOD_D1, 4))
   gu_p_prices = iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   gu_p_prices = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double gu_p_midline4 = (gu_p_pricer + gu_p_prices) / 2;
   double gu_p_d3up2 = MathMax(gu_p_pricer, gu_p_prices) + 2 * MathAbs(gu_p_pricer - gu_p_prices);
   double gu_p_d3dn2 = MathMin(gu_p_pricer, gu_p_prices) - 2 * MathAbs(gu_p_pricer - gu_p_prices);
   
   if (gu_p_bid > gu_p_d3up2 || gu_p_bid < gu_p_d3dn2) gu_p_d3check = true;
   
   double gu_p_pricex = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double gu_p_pricey = 0;
   if (iClose(gu_p, PERIOD_D1, 4) > iClose(gu_p, PERIOD_D1, 5))
   gu_p_pricey = iHigh(gu_p, PERIOD_H1, iHighest(gu_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   gu_p_pricey = iLow(gu_p, PERIOD_H1, iLowest(gu_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double gu_p_midline5 = (gu_p_pricex + gu_p_pricey) / 2;
   double gu_p_d4up2 = MathMax(gu_p_pricex, gu_p_pricey) + 2 * MathAbs(gu_p_pricex - gu_p_pricey);
   double gu_p_d4dn2 = MathMin(gu_p_pricex, gu_p_pricey) - 2 * MathAbs(gu_p_pricex - gu_p_pricey);
   
   if (gu_p_bid > gu_p_d4up2 || gu_p_bid < gu_p_d4dn2) gu_p_d4check = true;
   
   AddToDrata(Fibz, dwm[7].daily, gu_p_ascheck, gu_p_midcheck, gu_p_wmidcheck, gu_p_d1check, gu_p_d2check, gu_p_d3check, gu_p_d4check, gu_p);
   
   //GBPAUD
   double ga_p_bid = MarketInfo(ga_p, MODE_BID);
   int ga_p_ascheck = 0;
   bool ga_p_midcheck = 0;
   int ga_p_wmidcheck = 0;
   bool ga_p_d1check = false;
   bool ga_p_d2check = false;
   bool ga_p_d3check = false;
   bool ga_p_d4check = false;
   
   double ga_p_midline = (iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ga_p_ashi = iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ga_p_aslo = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ga_p_bid > ga_p_ashi) ga_p_ascheck = 1;
   else if (ga_p_bid < ga_p_ashi && ga_p_bid > ga_p_aslo) ga_p_ascheck = 2;
   else if (ga_p_bid < ga_p_aslo) ga_p_ascheck = 3;
   
   if (ga_p_bid > ga_p_midline) ga_p_midcheck = true;
   if (ga_p_bid < ga_p_midline) ga_p_midcheck = false;
   
   double ga_p_pricea = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ga_p_priceb = 0;
   if (iClose(ga_p, PERIOD_D1, 1) > iClose(ga_p, PERIOD_D1, 2))
   ga_p_priceb = iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ga_p_priceb = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ga_p_midline2 = (ga_p_pricea + ga_p_priceb) / 2;
   double ga_p_d1up2 = MathMax(ga_p_pricea, ga_p_priceb) + 2 * MathAbs(ga_p_pricea - ga_p_priceb);
   double ga_p_d1dn2 = MathMin(ga_p_pricea, ga_p_priceb) - 2 * MathAbs(ga_p_pricea - ga_p_priceb);
   
   if (ga_p_bid > MathMax(ga_p_midline, ga_p_midline2)) ga_p_wmidcheck = 1;
   if (ga_p_bid < MathMax(ga_p_midline, ga_p_midline2) && ga_p_bid > MathMin(ga_p_midline, ga_p_midline2)) ga_p_wmidcheck = 2;
   if (ga_p_bid < MathMin(ga_p_midline, ga_p_midline2)) ga_p_wmidcheck = 3;
   
   if (ga_p_bid > ga_p_d1up2 || ga_p_bid < ga_p_d1dn2) ga_p_d1check = true;   
      
   double ga_p_pricen = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ga_p_priceo = 0;
   if (iClose(ga_p, PERIOD_D1, 2) > iClose(ga_p, PERIOD_D1, 3))
   ga_p_priceo = iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ga_p_priceo = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ga_p_midline3 = (ga_p_pricen + ga_p_priceo) / 2;
   double ga_p_d2up2 = MathMax(ga_p_pricen, ga_p_priceo) + 2 * MathAbs(ga_p_pricen - ga_p_priceo);
   double ga_p_d2dn2 = MathMin(ga_p_pricen, ga_p_priceo) - 2 * MathAbs(ga_p_pricen - ga_p_priceo);
   
   if (ga_p_bid > ga_p_d2up2 || ga_p_bid < ga_p_d2dn2) ga_p_d2check = true;
   
   double ga_p_pricer = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ga_p_prices = 0;
   if (iClose(ga_p, PERIOD_D1, 3) > iClose(ga_p, PERIOD_D1, 4))
   ga_p_prices = iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ga_p_prices = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ga_p_midline4 = (ga_p_pricer + ga_p_prices) / 2;
   double ga_p_d3up2 = MathMax(ga_p_pricer, ga_p_prices) + 2 * MathAbs(ga_p_pricer - ga_p_prices);
   double ga_p_d3dn2 = MathMin(ga_p_pricer, ga_p_prices) - 2 * MathAbs(ga_p_pricer - ga_p_prices);
   
   if (ga_p_bid > ga_p_d3up2 || ga_p_bid < ga_p_d3dn2) ga_p_d3check = true;
   
   double ga_p_pricex = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ga_p_pricey = 0;
   if (iClose(ga_p, PERIOD_D1, 4) > iClose(ga_p, PERIOD_D1, 5))
   ga_p_pricey = iHigh(ga_p, PERIOD_H1, iHighest(ga_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ga_p_pricey = iLow(ga_p, PERIOD_H1, iLowest(ga_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ga_p_midline5 = (ga_p_pricex + ga_p_pricey) / 2;
   double ga_p_d4up2 = MathMax(ga_p_pricex, ga_p_pricey) + 2 * MathAbs(ga_p_pricex - ga_p_pricey);
   double ga_p_d4dn2 = MathMin(ga_p_pricex, ga_p_pricey) - 2 * MathAbs(ga_p_pricex - ga_p_pricey);
   
   if (ga_p_bid > ga_p_d4up2 || ga_p_bid < ga_p_d4dn2) ga_p_d4check = true;
   
   AddToDrata(Fibz, dwm[8].daily, ga_p_ascheck, ga_p_midcheck, ga_p_wmidcheck, ga_p_d1check, ga_p_d2check, ga_p_d3check, ga_p_d4check, ga_p);
   
   //GBPNZD
   double gn_p_bid = MarketInfo(gn_p, MODE_BID);
   int gn_p_ascheck = 0;
   bool gn_p_midcheck = 0;
   int gn_p_wmidcheck = 0;
   bool gn_p_d1check = false;
   bool gn_p_d2check = false;
   bool gn_p_d3check = false;
   bool gn_p_d4check = false;
   
   double gn_p_midline = (iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double gn_p_ashi = iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double gn_p_aslo = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (gn_p_bid > gn_p_ashi) gn_p_ascheck = 1;
   else if (gn_p_bid < gn_p_ashi && gn_p_bid > gn_p_aslo) gn_p_ascheck = 2;
   else if (gn_p_bid < gn_p_aslo) gn_p_ascheck = 3;
   
   if (gn_p_bid > gn_p_midline) gn_p_midcheck = true;
   if (gn_p_bid < gn_p_midline) gn_p_midcheck = false;
   
   double gn_p_pricea = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double gn_p_priceb = 0;
   if (iClose(gn_p, PERIOD_D1, 1) > iClose(gn_p, PERIOD_D1, 2))
   gn_p_priceb = iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   gn_p_priceb = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double gn_p_midline2 = (gn_p_pricea + gn_p_priceb) / 2;
   double gn_p_d1up2 = MathMax(gn_p_pricea, gn_p_priceb) + 2 * MathAbs(gn_p_pricea - gn_p_priceb);
   double gn_p_d1dn2 = MathMin(gn_p_pricea, gn_p_priceb) - 2 * MathAbs(gn_p_pricea - gn_p_priceb);
   
   if (gn_p_bid > MathMax(gn_p_midline, gn_p_midline2)) gn_p_wmidcheck = 1;
   if (gn_p_bid < MathMax(gn_p_midline, gn_p_midline2) && gn_p_bid > MathMin(gn_p_midline, gn_p_midline2)) gn_p_wmidcheck = 2;
   if (gn_p_bid < MathMin(gn_p_midline, gn_p_midline2)) gn_p_wmidcheck = 3;
   
   if (gn_p_bid > gn_p_d1up2 || gn_p_bid < gn_p_d1dn2) gn_p_d1check = true;   
      
   double gn_p_pricen = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double gn_p_priceo = 0;
   if (iClose(gn_p, PERIOD_D1, 2) > iClose(gn_p, PERIOD_D1, 3))
   gn_p_priceo = iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   gn_p_priceo = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double gn_p_midline3 = (gn_p_pricen + gn_p_priceo) / 2;
   double gn_p_d2up2 = MathMax(gn_p_pricen, gn_p_priceo) + 2 * MathAbs(gn_p_pricen - gn_p_priceo);
   double gn_p_d2dn2 = MathMin(gn_p_pricen, gn_p_priceo) - 2 * MathAbs(gn_p_pricen - gn_p_priceo);
   
   if (gn_p_bid > gn_p_d2up2 || gn_p_bid < gn_p_d2dn2) gn_p_d2check = true;
   
   double gn_p_pricer = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double gn_p_prices = 0;
   if (iClose(gn_p, PERIOD_D1, 3) > iClose(gn_p, PERIOD_D1, 4))
   gn_p_prices = iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   gn_p_prices = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double gn_p_midline4 = (gn_p_pricer + gn_p_prices) / 2;
   double gn_p_d3up2 = MathMax(gn_p_pricer, gn_p_prices) + 2 * MathAbs(gn_p_pricer - gn_p_prices);
   double gn_p_d3dn2 = MathMin(gn_p_pricer, gn_p_prices) - 2 * MathAbs(gn_p_pricer - gn_p_prices);
   
   if (gn_p_bid > gn_p_d3up2 || gn_p_bid < gn_p_d3dn2) gn_p_d3check = true;
   
   double gn_p_pricex = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double gn_p_pricey = 0;
   if (iClose(gn_p, PERIOD_D1, 4) > iClose(gn_p, PERIOD_D1, 5))
   gn_p_pricey = iHigh(gn_p, PERIOD_H1, iHighest(gn_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   gn_p_pricey = iLow(gn_p, PERIOD_H1, iLowest(gn_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double gn_p_midline5 = (gn_p_pricex + gn_p_pricey) / 2;
   double gn_p_d4up2 = MathMax(gn_p_pricex, gn_p_pricey) + 2 * MathAbs(gn_p_pricex - gn_p_pricey);
   double gn_p_d4dn2 = MathMin(gn_p_pricex, gn_p_pricey) - 2 * MathAbs(gn_p_pricex - gn_p_pricey);
   
   if (gn_p_bid > gn_p_d4up2 || gn_p_bid < gn_p_d4dn2) gn_p_d4check = true;
   
   AddToDrata(Fibz, dwm[9].daily, gn_p_ascheck, gn_p_midcheck, gn_p_wmidcheck, gn_p_d1check, gn_p_d2check, gn_p_d3check, gn_p_d4check, gn_p);
   
   //GBPCAD
   double gc_p_bid = MarketInfo(gc_p, MODE_BID);
   int gc_p_ascheck = 0;
   bool gc_p_midcheck = 0;
   int gc_p_wmidcheck = 0;
   bool gc_p_d1check = false;
   bool gc_p_d2check = false;
   bool gc_p_d3check = false;
   bool gc_p_d4check = false;
   
   double gc_p_midline = (iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double gc_p_ashi = iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double gc_p_aslo = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (gc_p_bid > gc_p_ashi) gc_p_ascheck = 1;
   else if (gc_p_bid < gc_p_ashi && gc_p_bid > gc_p_aslo) gc_p_ascheck = 2;
   else if (gc_p_bid < gc_p_aslo) gc_p_ascheck = 3;
   
   if (gc_p_bid > gc_p_midline) gc_p_midcheck = true;
   if (gc_p_bid < gc_p_midline) gc_p_midcheck = false;
   
   double gc_p_pricea = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double gc_p_priceb = 0;
   if (iClose(gc_p, PERIOD_D1, 1) > iClose(gc_p, PERIOD_D1, 2))
   gc_p_priceb = iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   gc_p_priceb = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double gc_p_midline2 = (gc_p_pricea + gc_p_priceb) / 2;
   double gc_p_d1up2 = MathMax(gc_p_pricea, gc_p_priceb) + 2 * MathAbs(gc_p_pricea - gc_p_priceb);
   double gc_p_d1dn2 = MathMin(gc_p_pricea, gc_p_priceb) - 2 * MathAbs(gc_p_pricea - gc_p_priceb);
   
   if (gc_p_bid > MathMax(gc_p_midline, gc_p_midline2)) gc_p_wmidcheck = 1;
   if (gc_p_bid < MathMax(gc_p_midline, gc_p_midline2) && gc_p_bid > MathMin(gc_p_midline, gc_p_midline2)) gc_p_wmidcheck = 2;
   if (gc_p_bid < MathMin(gc_p_midline, gc_p_midline2)) gc_p_wmidcheck = 3;
   
   if (gc_p_bid > gc_p_d1up2 || gc_p_bid < gc_p_d1dn2) gc_p_d1check = true;   
      
   double gc_p_pricen = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double gc_p_priceo = 0;
   if (iClose(gc_p, PERIOD_D1, 2) > iClose(gc_p, PERIOD_D1, 3))
   gc_p_priceo = iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   gc_p_priceo = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double gc_p_midline3 = (gc_p_pricen + gc_p_priceo) / 2;
   double gc_p_d2up2 = MathMax(gc_p_pricen, gc_p_priceo) + 2 * MathAbs(gc_p_pricen - gc_p_priceo);
   double gc_p_d2dn2 = MathMin(gc_p_pricen, gc_p_priceo) - 2 * MathAbs(gc_p_pricen - gc_p_priceo);
   
   if (gc_p_bid > gc_p_d2up2 || gc_p_bid < gc_p_d2dn2) gc_p_d2check = true;
   
   double gc_p_pricer = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double gc_p_prices = 0;
   if (iClose(gc_p, PERIOD_D1, 3) > iClose(gc_p, PERIOD_D1, 4))
   gc_p_prices = iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   gc_p_prices = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double gc_p_midline4 = (gc_p_pricer + gc_p_prices) / 2;
   double gc_p_d3up2 = MathMax(gc_p_pricer, gc_p_prices) + 2 * MathAbs(gc_p_pricer - gc_p_prices);
   double gc_p_d3dn2 = MathMin(gc_p_pricer, gc_p_prices) - 2 * MathAbs(gc_p_pricer - gc_p_prices);
   
   if (gc_p_bid > gc_p_d3up2 || gc_p_bid < gc_p_d3dn2) gc_p_d3check = true;
   
   double gc_p_pricex = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double gc_p_pricey = 0;
   if (iClose(gc_p, PERIOD_D1, 4) > iClose(gc_p, PERIOD_D1, 5))
   gc_p_pricey = iHigh(gc_p, PERIOD_H1, iHighest(gc_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   gc_p_pricey = iLow(gc_p, PERIOD_H1, iLowest(gc_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double gc_p_midline5 = (gc_p_pricex + gc_p_pricey) / 2;
   double gc_p_d4up2 = MathMax(gc_p_pricex, gc_p_pricey) + 2 * MathAbs(gc_p_pricex - gc_p_pricey);
   double gc_p_d4dn2 = MathMin(gc_p_pricex, gc_p_pricey) - 2 * MathAbs(gc_p_pricex - gc_p_pricey);
   
   if (gc_p_bid > gc_p_d4up2 || gc_p_bid < gc_p_d4dn2) gc_p_d4check = true;
   
   AddToDrata(Fibz, dwm[10].daily, gc_p_ascheck, gc_p_midcheck, gc_p_wmidcheck, gc_p_d1check, gc_p_d2check, gc_p_d3check, gc_p_d4check, gc_p);
   
   //GBPCHF
   double gf_p_bid = MarketInfo(gf_p, MODE_BID);
   int gf_p_ascheck = 0;
   bool gf_p_midcheck = 0;
   int gf_p_wmidcheck = 0;
   bool gf_p_d1check = false;
   bool gf_p_d2check = false;
   bool gf_p_d3check = false;
   bool gf_p_d4check = false;
   
   double gf_p_midline = (iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double gf_p_ashi = iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double gf_p_aslo = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (gf_p_bid > gf_p_ashi) gf_p_ascheck = 1;
   else if (gf_p_bid < gf_p_ashi && gf_p_bid > gf_p_aslo) gf_p_ascheck = 2;
   else if (gf_p_bid < gf_p_aslo) gf_p_ascheck = 3;
   
   if (gf_p_bid > gf_p_midline) gf_p_midcheck = true;
   if (gf_p_bid < gf_p_midline) gf_p_midcheck = false;
   
   double gf_p_pricea = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double gf_p_priceb = 0;
   if (iClose(gf_p, PERIOD_D1, 1) > iClose(gf_p, PERIOD_D1, 2))
   gf_p_priceb = iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   gf_p_priceb = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double gf_p_midline2 = (gf_p_pricea + gf_p_priceb) / 2;
   double gf_p_d1up2 = MathMax(gf_p_pricea, gf_p_priceb) + 2 * MathAbs(gf_p_pricea - gf_p_priceb);
   double gf_p_d1dn2 = MathMin(gf_p_pricea, gf_p_priceb) - 2 * MathAbs(gf_p_pricea - gf_p_priceb);
   
   if (gf_p_bid > MathMax(gf_p_midline, gf_p_midline2)) gf_p_wmidcheck = 1;
   if (gf_p_bid < MathMax(gf_p_midline, gf_p_midline2) && gf_p_bid > MathMin(gf_p_midline, gf_p_midline2)) gf_p_wmidcheck = 2;
   if (gf_p_bid < MathMin(gf_p_midline, gf_p_midline2)) gf_p_wmidcheck = 3;
   
   if (gf_p_bid > gf_p_d1up2 || gf_p_bid < gf_p_d1dn2) gf_p_d1check = true;   
      
   double gf_p_pricen = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double gf_p_priceo = 0;
   if (iClose(gf_p, PERIOD_D1, 2) > iClose(gf_p, PERIOD_D1, 3))
   gf_p_priceo = iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   gf_p_priceo = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double gf_p_midline3 = (gf_p_pricen + gf_p_priceo) / 2;
   double gf_p_d2up2 = MathMax(gf_p_pricen, gf_p_priceo) + 2 * MathAbs(gf_p_pricen - gf_p_priceo);
   double gf_p_d2dn2 = MathMin(gf_p_pricen, gf_p_priceo) - 2 * MathAbs(gf_p_pricen - gf_p_priceo);
   
   if (gf_p_bid > gf_p_d2up2 || gf_p_bid < gf_p_d2dn2) gf_p_d2check = true;
   
   double gf_p_pricer = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double gf_p_prices = 0;
   if (iClose(gf_p, PERIOD_D1, 3) > iClose(gf_p, PERIOD_D1, 4))
   gf_p_prices = iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   gf_p_prices = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double gf_p_midline4 = (gf_p_pricer + gf_p_prices) / 2;
   double gf_p_d3up2 = MathMax(gf_p_pricer, gf_p_prices) + 2 * MathAbs(gf_p_pricer - gf_p_prices);
   double gf_p_d3dn2 = MathMin(gf_p_pricer, gf_p_prices) - 2 * MathAbs(gf_p_pricer - gf_p_prices);
   
   if (gf_p_bid > gf_p_d3up2 || gf_p_bid < gf_p_d3dn2) gf_p_d3check = true;
   
   double gf_p_pricex = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double gf_p_pricey = 0;
   if (iClose(gf_p, PERIOD_D1, 4) > iClose(gf_p, PERIOD_D1, 5))
   gf_p_pricey = iHigh(gf_p, PERIOD_H1, iHighest(gf_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   gf_p_pricey = iLow(gf_p, PERIOD_H1, iLowest(gf_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double gf_p_midline5 = (gf_p_pricex + gf_p_pricey) / 2;
   double gf_p_d4up2 = MathMax(gf_p_pricex, gf_p_pricey) + 2 * MathAbs(gf_p_pricex - gf_p_pricey);
   double gf_p_d4dn2 = MathMin(gf_p_pricex, gf_p_pricey) - 2 * MathAbs(gf_p_pricex - gf_p_pricey);
   
   if (gf_p_bid > gf_p_d4up2 || gf_p_bid < gf_p_d4dn2) gf_p_d4check = true;
   
   AddToDrata(Fibz, dwm[11].daily, gf_p_ascheck, gf_p_midcheck, gf_p_wmidcheck, gf_p_d1check, gf_p_d2check, gf_p_d3check, gf_p_d4check, gf_p);
   
   //GBPJPY
   double gj_p_bid = MarketInfo(gj_p, MODE_BID);
   int gj_p_ascheck = 0;
   bool gj_p_midcheck = 0;
   int gj_p_wmidcheck = 0;
   bool gj_p_d1check = false;
   bool gj_p_d2check = false;
   bool gj_p_d3check = false;
   bool gj_p_d4check = false;
   
   double gj_p_midline = (iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double gj_p_ashi = iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double gj_p_aslo = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (gj_p_bid > gj_p_ashi) gj_p_ascheck = 1;
   else if (gj_p_bid < gj_p_ashi && gj_p_bid > gj_p_aslo) gj_p_ascheck = 2;
   else if (gj_p_bid < gj_p_aslo) gj_p_ascheck = 3;
   
   if (gj_p_bid > gj_p_midline) gj_p_midcheck = true;
   if (gj_p_bid < gj_p_midline) gj_p_midcheck = false;
   
   double gj_p_pricea = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double gj_p_priceb = 0;
   if (iClose(gj_p, PERIOD_D1, 1) > iClose(gj_p, PERIOD_D1, 2))
   gj_p_priceb = iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   gj_p_priceb = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double gj_p_midline2 = (gj_p_pricea + gj_p_priceb) / 2;
   double gj_p_d1up2 = MathMax(gj_p_pricea, gj_p_priceb) + 2 * MathAbs(gj_p_pricea - gj_p_priceb);
   double gj_p_d1dn2 = MathMin(gj_p_pricea, gj_p_priceb) - 2 * MathAbs(gj_p_pricea - gj_p_priceb);
   
   if (gj_p_bid > MathMax(gj_p_midline, gj_p_midline2)) gj_p_wmidcheck = 1;
   if (gj_p_bid < MathMax(gj_p_midline, gj_p_midline2) && gj_p_bid > MathMin(gj_p_midline, gj_p_midline2)) gj_p_wmidcheck = 2;
   if (gj_p_bid < MathMin(gj_p_midline, gj_p_midline2)) gj_p_wmidcheck = 3;
   
   if (gj_p_bid > gj_p_d1up2 || gj_p_bid < gj_p_d1dn2) gj_p_d1check = true;   
      
   double gj_p_pricen = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double gj_p_priceo = 0;
   if (iClose(gj_p, PERIOD_D1, 2) > iClose(gj_p, PERIOD_D1, 3))
   gj_p_priceo = iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   gj_p_priceo = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double gj_p_midline3 = (gj_p_pricen + gj_p_priceo) / 2;
   double gj_p_d2up2 = MathMax(gj_p_pricen, gj_p_priceo) + 2 * MathAbs(gj_p_pricen - gj_p_priceo);
   double gj_p_d2dn2 = MathMin(gj_p_pricen, gj_p_priceo) - 2 * MathAbs(gj_p_pricen - gj_p_priceo);
   
   if (gj_p_bid > gj_p_d2up2 || gj_p_bid < gj_p_d2dn2) gj_p_d2check = true;
   
   double gj_p_pricer = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double gj_p_prices = 0;
   if (iClose(gj_p, PERIOD_D1, 3) > iClose(gj_p, PERIOD_D1, 4))
   gj_p_prices = iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   gj_p_prices = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double gj_p_midline4 = (gj_p_pricer + gj_p_prices) / 2;
   double gj_p_d3up2 = MathMax(gj_p_pricer, gj_p_prices) + 2 * MathAbs(gj_p_pricer - gj_p_prices);
   double gj_p_d3dn2 = MathMin(gj_p_pricer, gj_p_prices) - 2 * MathAbs(gj_p_pricer - gj_p_prices);
   
   if (gj_p_bid > gj_p_d3up2 || gj_p_bid < gj_p_d3dn2) gj_p_d3check = true;
   
   double gj_p_pricex = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double gj_p_pricey = 0;
   if (iClose(gj_p, PERIOD_D1, 4) > iClose(gj_p, PERIOD_D1, 5))
   gj_p_pricey = iHigh(gj_p, PERIOD_H1, iHighest(gj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   gj_p_pricey = iLow(gj_p, PERIOD_H1, iLowest(gj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double gj_p_midline5 = (gj_p_pricex + gj_p_pricey) / 2;
   double gj_p_d4up2 = MathMax(gj_p_pricex, gj_p_pricey) + 2 * MathAbs(gj_p_pricex - gj_p_pricey);
   double gj_p_d4dn2 = MathMin(gj_p_pricex, gj_p_pricey) - 2 * MathAbs(gj_p_pricex - gj_p_pricey);
   
   if (gj_p_bid > gj_p_d4up2 || gj_p_bid < gj_p_d4dn2) gj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[12].daily, gj_p_ascheck, gj_p_midcheck, gj_p_wmidcheck, gj_p_d1check, gj_p_d2check, gj_p_d3check, gj_p_d4check, gj_p);
   
   //AUDUSD
   double au_p_bid = MarketInfo(au_p, MODE_BID);
   int au_p_ascheck = 0;
   bool au_p_midcheck = 0;
   int au_p_wmidcheck = 0;
   bool au_p_d1check = false;
   bool au_p_d2check = false;
   bool au_p_d3check = false;
   bool au_p_d4check = false;
   
   double au_p_midline = (iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double au_p_ashi = iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double au_p_aslo = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (au_p_bid > au_p_ashi) au_p_ascheck = 1;
   else if (au_p_bid < au_p_ashi && au_p_bid > au_p_aslo) au_p_ascheck = 2;
   else if (au_p_bid < au_p_aslo) au_p_ascheck = 3;
   
   if (au_p_bid > au_p_midline) au_p_midcheck = true;
   if (au_p_bid < au_p_midline) au_p_midcheck = false;
   
   double au_p_pricea = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double au_p_priceb = 0;
   if (iClose(au_p, PERIOD_D1, 1) > iClose(au_p, PERIOD_D1, 2))
   au_p_priceb = iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   au_p_priceb = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double au_p_midline2 = (au_p_pricea + au_p_priceb) / 2;
   double au_p_d1up2 = MathMax(au_p_pricea, au_p_priceb) + 2 * MathAbs(au_p_pricea - au_p_priceb);
   double au_p_d1dn2 = MathMin(au_p_pricea, au_p_priceb) - 2 * MathAbs(au_p_pricea - au_p_priceb);
   
   if (au_p_bid > MathMax(au_p_midline, au_p_midline2)) au_p_wmidcheck = 1;
   if (au_p_bid < MathMax(au_p_midline, au_p_midline2) && au_p_bid > MathMin(au_p_midline, au_p_midline2)) au_p_wmidcheck = 2;
   if (au_p_bid < MathMin(au_p_midline, au_p_midline2)) au_p_wmidcheck = 3;
   
   if (au_p_bid > au_p_d1up2 || au_p_bid < au_p_d1dn2) au_p_d1check = true;   
      
   double au_p_pricen = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double au_p_priceo = 0;
   if (iClose(au_p, PERIOD_D1, 2) > iClose(au_p, PERIOD_D1, 3))
   au_p_priceo = iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   au_p_priceo = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double au_p_midline3 = (au_p_pricen + au_p_priceo) / 2;
   double au_p_d2up2 = MathMax(au_p_pricen, au_p_priceo) + 2 * MathAbs(au_p_pricen - au_p_priceo);
   double au_p_d2dn2 = MathMin(au_p_pricen, au_p_priceo) - 2 * MathAbs(au_p_pricen - au_p_priceo);
   
   if (au_p_bid > au_p_d2up2 || au_p_bid < au_p_d2dn2) au_p_d2check = true;
   
   double au_p_pricer = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double au_p_prices = 0;
   if (iClose(au_p, PERIOD_D1, 3) > iClose(au_p, PERIOD_D1, 4))
   au_p_prices = iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   au_p_prices = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double au_p_midline4 = (au_p_pricer + au_p_prices) / 2;
   double au_p_d3up2 = MathMax(au_p_pricer, au_p_prices) + 2 * MathAbs(au_p_pricer - au_p_prices);
   double au_p_d3dn2 = MathMin(au_p_pricer, au_p_prices) - 2 * MathAbs(au_p_pricer - au_p_prices);
   
   if (au_p_bid > au_p_d3up2 || au_p_bid < au_p_d3dn2) au_p_d3check = true;
   
   double au_p_pricex = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double au_p_pricey = 0;
   if (iClose(au_p, PERIOD_D1, 4) > iClose(au_p, PERIOD_D1, 5))
   au_p_pricey = iHigh(au_p, PERIOD_H1, iHighest(au_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   au_p_pricey = iLow(au_p, PERIOD_H1, iLowest(au_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double au_p_midline5 = (au_p_pricex + au_p_pricey) / 2;
   double au_p_d4up2 = MathMax(au_p_pricex, au_p_pricey) + 2 * MathAbs(au_p_pricex - au_p_pricey);
   double au_p_d4dn2 = MathMin(au_p_pricex, au_p_pricey) - 2 * MathAbs(au_p_pricex - au_p_pricey);
   
   if (au_p_bid > au_p_d4up2 || au_p_bid < au_p_d4dn2) au_p_d4check = true;
   
   AddToDrata(Fibz, dwm[13].daily, au_p_ascheck, au_p_midcheck, au_p_wmidcheck, au_p_d1check, au_p_d2check, au_p_d3check, au_p_d4check, au_p);
   
   //AUDNZD
   double an_p_bid = MarketInfo(an_p, MODE_BID);
   int an_p_ascheck = 0;
   bool an_p_midcheck = 0;
   int an_p_wmidcheck = 0;
   bool an_p_d1check = false;
   bool an_p_d2check = false;
   bool an_p_d3check = false;
   bool an_p_d4check = false;
   
   double an_p_midline = (iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double an_p_ashi = iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double an_p_aslo = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (an_p_bid > an_p_ashi) an_p_ascheck = 1;
   else if (an_p_bid < an_p_ashi && an_p_bid > an_p_aslo) an_p_ascheck = 2;
   else if (an_p_bid < an_p_aslo) an_p_ascheck = 3;
   
   if (an_p_bid > an_p_midline) an_p_midcheck = true;
   if (an_p_bid < an_p_midline) an_p_midcheck = false;
   
   double an_p_pricea = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double an_p_priceb = 0;
   if (iClose(an_p, PERIOD_D1, 1) > iClose(an_p, PERIOD_D1, 2))
   an_p_priceb = iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   an_p_priceb = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double an_p_midline2 = (an_p_pricea + an_p_priceb) / 2;
   double an_p_d1up2 = MathMax(an_p_pricea, an_p_priceb) + 2 * MathAbs(an_p_pricea - an_p_priceb);
   double an_p_d1dn2 = MathMin(an_p_pricea, an_p_priceb) - 2 * MathAbs(an_p_pricea - an_p_priceb);
   
   if (an_p_bid > MathMax(an_p_midline, an_p_midline2)) an_p_wmidcheck = 1;
   if (an_p_bid < MathMax(an_p_midline, an_p_midline2) && an_p_bid > MathMin(an_p_midline, an_p_midline2)) an_p_wmidcheck = 2;
   if (an_p_bid < MathMin(an_p_midline, an_p_midline2)) an_p_wmidcheck = 3;
   
   if (an_p_bid > an_p_d1up2 || an_p_bid < an_p_d1dn2) an_p_d1check = true;   
      
   double an_p_pricen = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double an_p_priceo = 0;
   if (iClose(an_p, PERIOD_D1, 2) > iClose(an_p, PERIOD_D1, 3))
   an_p_priceo = iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   an_p_priceo = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double an_p_midline3 = (an_p_pricen + an_p_priceo) / 2;
   double an_p_d2up2 = MathMax(an_p_pricen, an_p_priceo) + 2 * MathAbs(an_p_pricen - an_p_priceo);
   double an_p_d2dn2 = MathMin(an_p_pricen, an_p_priceo) - 2 * MathAbs(an_p_pricen - an_p_priceo);
   
   if (an_p_bid > an_p_d2up2 || an_p_bid < an_p_d2dn2) an_p_d2check = true;
   
   double an_p_pricer = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double an_p_prices = 0;
   if (iClose(an_p, PERIOD_D1, 3) > iClose(an_p, PERIOD_D1, 4))
   an_p_prices = iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   an_p_prices = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double an_p_midline4 = (an_p_pricer + an_p_prices) / 2;
   double an_p_d3up2 = MathMax(an_p_pricer, an_p_prices) + 2 * MathAbs(an_p_pricer - an_p_prices);
   double an_p_d3dn2 = MathMin(an_p_pricer, an_p_prices) - 2 * MathAbs(an_p_pricer - an_p_prices);
   
   if (an_p_bid > an_p_d3up2 || an_p_bid < an_p_d3dn2) an_p_d3check = true;
   
   double an_p_pricex = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double an_p_pricey = 0;
   if (iClose(an_p, PERIOD_D1, 4) > iClose(an_p, PERIOD_D1, 5))
   an_p_pricey = iHigh(an_p, PERIOD_H1, iHighest(an_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   an_p_pricey = iLow(an_p, PERIOD_H1, iLowest(an_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double an_p_midline5 = (an_p_pricex + an_p_pricey) / 2;
   double an_p_d4up2 = MathMax(an_p_pricex, an_p_pricey) + 2 * MathAbs(an_p_pricex - an_p_pricey);
   double an_p_d4dn2 = MathMin(an_p_pricex, an_p_pricey) - 2 * MathAbs(an_p_pricex - an_p_pricey);
   
   if (an_p_bid > an_p_d4up2 || an_p_bid < an_p_d4dn2) an_p_d4check = true;
   
   AddToDrata(Fibz, dwm[14].daily, an_p_ascheck, an_p_midcheck, an_p_wmidcheck, an_p_d1check, an_p_d2check, an_p_d3check, an_p_d4check, an_p);
   
   //AUDCAD
   double ac_p_bid = MarketInfo(ac_p, MODE_BID);
   int ac_p_ascheck = 0;
   bool ac_p_midcheck = 0;
   int ac_p_wmidcheck = 0;
   bool ac_p_d1check = false;
   bool ac_p_d2check = false;
   bool ac_p_d3check = false;
   bool ac_p_d4check = false;
   
   double ac_p_midline = (iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double ac_p_ashi = iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double ac_p_aslo = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (ac_p_bid > ac_p_ashi) ac_p_ascheck = 1;
   else if (ac_p_bid < ac_p_ashi && ac_p_bid > ac_p_aslo) ac_p_ascheck = 2;
   else if (ac_p_bid < ac_p_aslo) ac_p_ascheck = 3;
   
   if (ac_p_bid > ac_p_midline) ac_p_midcheck = true;
   if (ac_p_bid < ac_p_midline) ac_p_midcheck = false;
   
   double ac_p_pricea = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double ac_p_priceb = 0;
   if (iClose(ac_p, PERIOD_D1, 1) > iClose(ac_p, PERIOD_D1, 2))
   ac_p_priceb = iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   ac_p_priceb = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double ac_p_midline2 = (ac_p_pricea + ac_p_priceb) / 2;
   double ac_p_d1up2 = MathMax(ac_p_pricea, ac_p_priceb) + 2 * MathAbs(ac_p_pricea - ac_p_priceb);
   double ac_p_d1dn2 = MathMin(ac_p_pricea, ac_p_priceb) - 2 * MathAbs(ac_p_pricea - ac_p_priceb);
   
   if (ac_p_bid > MathMax(ac_p_midline, ac_p_midline2)) ac_p_wmidcheck = 1;
   if (ac_p_bid < MathMax(ac_p_midline, ac_p_midline2) && ac_p_bid > MathMin(ac_p_midline, ac_p_midline2)) ac_p_wmidcheck = 2;
   if (ac_p_bid < MathMin(ac_p_midline, ac_p_midline2)) ac_p_wmidcheck = 3;
   
   if (ac_p_bid > ac_p_d1up2 || ac_p_bid < ac_p_d1dn2) ac_p_d1check = true;   
      
   double ac_p_pricen = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double ac_p_priceo = 0;
   if (iClose(ac_p, PERIOD_D1, 2) > iClose(ac_p, PERIOD_D1, 3))
   ac_p_priceo = iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   ac_p_priceo = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double ac_p_midline3 = (ac_p_pricen + ac_p_priceo) / 2;
   double ac_p_d2up2 = MathMax(ac_p_pricen, ac_p_priceo) + 2 * MathAbs(ac_p_pricen - ac_p_priceo);
   double ac_p_d2dn2 = MathMin(ac_p_pricen, ac_p_priceo) - 2 * MathAbs(ac_p_pricen - ac_p_priceo);
   
   if (ac_p_bid > ac_p_d2up2 || ac_p_bid < ac_p_d2dn2) ac_p_d2check = true;
   
   double ac_p_pricer = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double ac_p_prices = 0;
   if (iClose(ac_p, PERIOD_D1, 3) > iClose(ac_p, PERIOD_D1, 4))
   ac_p_prices = iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   ac_p_prices = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double ac_p_midline4 = (ac_p_pricer + ac_p_prices) / 2;
   double ac_p_d3up2 = MathMax(ac_p_pricer, ac_p_prices) + 2 * MathAbs(ac_p_pricer - ac_p_prices);
   double ac_p_d3dn2 = MathMin(ac_p_pricer, ac_p_prices) - 2 * MathAbs(ac_p_pricer - ac_p_prices);
   
   if (ac_p_bid > ac_p_d3up2 || ac_p_bid < ac_p_d3dn2) ac_p_d3check = true;
   
   double ac_p_pricex = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double ac_p_pricey = 0;
   if (iClose(ac_p, PERIOD_D1, 4) > iClose(ac_p, PERIOD_D1, 5))
   ac_p_pricey = iHigh(ac_p, PERIOD_H1, iHighest(ac_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   ac_p_pricey = iLow(ac_p, PERIOD_H1, iLowest(ac_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double ac_p_midline5 = (ac_p_pricex + ac_p_pricey) / 2;
   double ac_p_d4up2 = MathMax(ac_p_pricex, ac_p_pricey) + 2 * MathAbs(ac_p_pricex - ac_p_pricey);
   double ac_p_d4dn2 = MathMin(ac_p_pricex, ac_p_pricey) - 2 * MathAbs(ac_p_pricex - ac_p_pricey);
   
   if (ac_p_bid > ac_p_d4up2 || ac_p_bid < ac_p_d4dn2) ac_p_d4check = true;
   
   AddToDrata(Fibz, dwm[15].daily, ac_p_ascheck, ac_p_midcheck, ac_p_wmidcheck, ac_p_d1check, ac_p_d2check, ac_p_d3check, ac_p_d4check, ac_p);
   
   //AUDCHF
   double af_p_bid = MarketInfo(af_p, MODE_BID);
   int af_p_ascheck = 0;
   bool af_p_midcheck = 0;
   int af_p_wmidcheck = 0;
   bool af_p_d1check = false;
   bool af_p_d2check = false;
   bool af_p_d3check = false;
   bool af_p_d4check = false;
   
   double af_p_midline = (iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double af_p_ashi = iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double af_p_aslo = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (af_p_bid > af_p_ashi) af_p_ascheck = 1;
   else if (af_p_bid < af_p_ashi && af_p_bid > af_p_aslo) af_p_ascheck = 2;
   else if (af_p_bid < af_p_aslo) af_p_ascheck = 3;
   
   if (af_p_bid > af_p_midline) af_p_midcheck = true;
   if (af_p_bid < af_p_midline) af_p_midcheck = false;
   
   double af_p_pricea = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double af_p_priceb = 0;
   if (iClose(af_p, PERIOD_D1, 1) > iClose(af_p, PERIOD_D1, 2))
   af_p_priceb = iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   af_p_priceb = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double af_p_midline2 = (af_p_pricea + af_p_priceb) / 2;
   double af_p_d1up2 = MathMax(af_p_pricea, af_p_priceb) + 2 * MathAbs(af_p_pricea - af_p_priceb);
   double af_p_d1dn2 = MathMin(af_p_pricea, af_p_priceb) - 2 * MathAbs(af_p_pricea - af_p_priceb);
   
   if (af_p_bid > MathMax(af_p_midline, af_p_midline2)) af_p_wmidcheck = 1;
   if (af_p_bid < MathMax(af_p_midline, af_p_midline2) && af_p_bid > MathMin(af_p_midline, af_p_midline2)) af_p_wmidcheck = 2;
   if (af_p_bid < MathMin(af_p_midline, af_p_midline2)) af_p_wmidcheck = 3;
   
   if (af_p_bid > af_p_d1up2 || af_p_bid < af_p_d1dn2) af_p_d1check = true;   
      
   double af_p_pricen = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double af_p_priceo = 0;
   if (iClose(af_p, PERIOD_D1, 2) > iClose(af_p, PERIOD_D1, 3))
   af_p_priceo = iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   af_p_priceo = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double af_p_midline3 = (af_p_pricen + af_p_priceo) / 2;
   double af_p_d2up2 = MathMax(af_p_pricen, af_p_priceo) + 2 * MathAbs(af_p_pricen - af_p_priceo);
   double af_p_d2dn2 = MathMin(af_p_pricen, af_p_priceo) - 2 * MathAbs(af_p_pricen - af_p_priceo);
   
   if (af_p_bid > af_p_d2up2 || af_p_bid < af_p_d2dn2) af_p_d2check = true;
   
   double af_p_pricer = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double af_p_prices = 0;
   if (iClose(af_p, PERIOD_D1, 3) > iClose(af_p, PERIOD_D1, 4))
   af_p_prices = iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   af_p_prices = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double af_p_midline4 = (af_p_pricer + af_p_prices) / 2;
   double af_p_d3up2 = MathMax(af_p_pricer, af_p_prices) + 2 * MathAbs(af_p_pricer - af_p_prices);
   double af_p_d3dn2 = MathMin(af_p_pricer, af_p_prices) - 2 * MathAbs(af_p_pricer - af_p_prices);
   
   if (af_p_bid > af_p_d3up2 || af_p_bid < af_p_d3dn2) af_p_d3check = true;
   
   double af_p_pricex = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double af_p_pricey = 0;
   if (iClose(af_p, PERIOD_D1, 4) > iClose(af_p, PERIOD_D1, 5))
   af_p_pricey = iHigh(af_p, PERIOD_H1, iHighest(af_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   af_p_pricey = iLow(af_p, PERIOD_H1, iLowest(af_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double af_p_midline5 = (af_p_pricex + af_p_pricey) / 2;
   double af_p_d4up2 = MathMax(af_p_pricex, af_p_pricey) + 2 * MathAbs(af_p_pricex - af_p_pricey);
   double af_p_d4dn2 = MathMin(af_p_pricex, af_p_pricey) - 2 * MathAbs(af_p_pricex - af_p_pricey);
   
   if (af_p_bid > af_p_d4up2 || af_p_bid < af_p_d4dn2) af_p_d4check = true;
   
   AddToDrata(Fibz, dwm[16].daily, af_p_ascheck, af_p_midcheck, af_p_wmidcheck, af_p_d1check, af_p_d2check, af_p_d3check, af_p_d4check, af_p);
   
   //AUDJPY
   double aj_p_bid = MarketInfo(aj_p, MODE_BID);
   int aj_p_ascheck = 0;
   bool aj_p_midcheck = 0;
   int aj_p_wmidcheck = 0;
   bool aj_p_d1check = false;
   bool aj_p_d2check = false;
   bool aj_p_d3check = false;
   bool aj_p_d4check = false;
   
   double aj_p_midline = (iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double aj_p_ashi = iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double aj_p_aslo = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (aj_p_bid > aj_p_ashi) aj_p_ascheck = 1;
   else if (aj_p_bid < aj_p_ashi && aj_p_bid > aj_p_aslo) aj_p_ascheck = 2;
   else if (aj_p_bid < aj_p_aslo) aj_p_ascheck = 3;
   
   if (aj_p_bid > aj_p_midline) aj_p_midcheck = true;
   if (aj_p_bid < aj_p_midline) aj_p_midcheck = false;
   
   double aj_p_pricea = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double aj_p_priceb = 0;
   if (iClose(aj_p, PERIOD_D1, 1) > iClose(aj_p, PERIOD_D1, 2))
   aj_p_priceb = iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   aj_p_priceb = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double aj_p_midline2 = (aj_p_pricea + aj_p_priceb) / 2;
   double aj_p_d1up2 = MathMax(aj_p_pricea, aj_p_priceb) + 2 * MathAbs(aj_p_pricea - aj_p_priceb);
   double aj_p_d1dn2 = MathMin(aj_p_pricea, aj_p_priceb) - 2 * MathAbs(aj_p_pricea - aj_p_priceb);
   
   if (aj_p_bid > MathMax(aj_p_midline, aj_p_midline2)) aj_p_wmidcheck = 1;
   if (aj_p_bid < MathMax(aj_p_midline, aj_p_midline2) && aj_p_bid > MathMin(aj_p_midline, aj_p_midline2)) aj_p_wmidcheck = 2;
   if (aj_p_bid < MathMin(aj_p_midline, aj_p_midline2)) aj_p_wmidcheck = 3;
   
   if (aj_p_bid > aj_p_d1up2 || aj_p_bid < aj_p_d1dn2) aj_p_d1check = true;   
      
   double aj_p_pricen = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double aj_p_priceo = 0;
   if (iClose(aj_p, PERIOD_D1, 2) > iClose(aj_p, PERIOD_D1, 3))
   aj_p_priceo = iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   aj_p_priceo = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double aj_p_midline3 = (aj_p_pricen + aj_p_priceo) / 2;
   double aj_p_d2up2 = MathMax(aj_p_pricen, aj_p_priceo) + 2 * MathAbs(aj_p_pricen - aj_p_priceo);
   double aj_p_d2dn2 = MathMin(aj_p_pricen, aj_p_priceo) - 2 * MathAbs(aj_p_pricen - aj_p_priceo);
   
   if (aj_p_bid > aj_p_d2up2 || aj_p_bid < aj_p_d2dn2) aj_p_d2check = true;
   
   double aj_p_pricer = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double aj_p_prices = 0;
   if (iClose(aj_p, PERIOD_D1, 3) > iClose(aj_p, PERIOD_D1, 4))
   aj_p_prices = iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   aj_p_prices = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double aj_p_midline4 = (aj_p_pricer + aj_p_prices) / 2;
   double aj_p_d3up2 = MathMax(aj_p_pricer, aj_p_prices) + 2 * MathAbs(aj_p_pricer - aj_p_prices);
   double aj_p_d3dn2 = MathMin(aj_p_pricer, aj_p_prices) - 2 * MathAbs(aj_p_pricer - aj_p_prices);
   
   if (aj_p_bid > aj_p_d3up2 || aj_p_bid < aj_p_d3dn2) aj_p_d3check = true;
   
   double aj_p_pricex = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double aj_p_pricey = 0;
   if (iClose(aj_p, PERIOD_D1, 4) > iClose(aj_p, PERIOD_D1, 5))
   aj_p_pricey = iHigh(aj_p, PERIOD_H1, iHighest(aj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   aj_p_pricey = iLow(aj_p, PERIOD_H1, iLowest(aj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double aj_p_midline5 = (aj_p_pricex + aj_p_pricey) / 2;
   double aj_p_d4up2 = MathMax(aj_p_pricex, aj_p_pricey) + 2 * MathAbs(aj_p_pricex - aj_p_pricey);
   double aj_p_d4dn2 = MathMin(aj_p_pricex, aj_p_pricey) - 2 * MathAbs(aj_p_pricex - aj_p_pricey);
   
   if (aj_p_bid > aj_p_d4up2 || aj_p_bid < aj_p_d4dn2) aj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[17].daily, aj_p_ascheck, aj_p_midcheck, aj_p_wmidcheck, aj_p_d1check, aj_p_d2check, aj_p_d3check, aj_p_d4check, aj_p);
   
   //NZDUSD
   double nu_p_bid = MarketInfo(nu_p, MODE_BID);
   int nu_p_ascheck = 0;
   bool nu_p_midcheck = 0;
   int nu_p_wmidcheck = 0;
   bool nu_p_d1check = false;
   bool nu_p_d2check = false;
   bool nu_p_d3check = false;
   bool nu_p_d4check = false;
   
   double nu_p_midline = (iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double nu_p_ashi = iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double nu_p_aslo = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (nu_p_bid > nu_p_ashi) nu_p_ascheck = 1;
   else if (nu_p_bid < nu_p_ashi && nu_p_bid > nu_p_aslo) nu_p_ascheck = 2;
   else if (nu_p_bid < nu_p_aslo) nu_p_ascheck = 3;
   
   if (nu_p_bid > nu_p_midline) nu_p_midcheck = true;
   if (nu_p_bid < nu_p_midline) nu_p_midcheck = false;
   
   double nu_p_pricea = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double nu_p_priceb = 0;
   if (iClose(nu_p, PERIOD_D1, 1) > iClose(nu_p, PERIOD_D1, 2))
   nu_p_priceb = iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   nu_p_priceb = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double nu_p_midline2 = (nu_p_pricea + nu_p_priceb) / 2;
   double nu_p_d1up2 = MathMax(nu_p_pricea, nu_p_priceb) + 2 * MathAbs(nu_p_pricea - nu_p_priceb);
   double nu_p_d1dn2 = MathMin(nu_p_pricea, nu_p_priceb) - 2 * MathAbs(nu_p_pricea - nu_p_priceb);
   
   if (nu_p_bid > MathMax(nu_p_midline, nu_p_midline2)) nu_p_wmidcheck = 1;
   if (nu_p_bid < MathMax(nu_p_midline, nu_p_midline2) && nu_p_bid > MathMin(nu_p_midline, nu_p_midline2)) nu_p_wmidcheck = 2;
   if (nu_p_bid < MathMin(nu_p_midline, nu_p_midline2)) nu_p_wmidcheck = 3;
   
   if (nu_p_bid > nu_p_d1up2 || nu_p_bid < nu_p_d1dn2) nu_p_d1check = true;   
      
   double nu_p_pricen = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double nu_p_priceo = 0;
   if (iClose(nu_p, PERIOD_D1, 2) > iClose(nu_p, PERIOD_D1, 3))
   nu_p_priceo = iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   nu_p_priceo = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double nu_p_midline3 = (nu_p_pricen + nu_p_priceo) / 2;
   double nu_p_d2up2 = MathMax(nu_p_pricen, nu_p_priceo) + 2 * MathAbs(nu_p_pricen - nu_p_priceo);
   double nu_p_d2dn2 = MathMin(nu_p_pricen, nu_p_priceo) - 2 * MathAbs(nu_p_pricen - nu_p_priceo);
   
   if (nu_p_bid > nu_p_d2up2 || nu_p_bid < nu_p_d2dn2) nu_p_d2check = true;
   
   double nu_p_pricer = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double nu_p_prices = 0;
   if (iClose(nu_p, PERIOD_D1, 3) > iClose(nu_p, PERIOD_D1, 4))
   nu_p_prices = iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   nu_p_prices = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double nu_p_midline4 = (nu_p_pricer + nu_p_prices) / 2;
   double nu_p_d3up2 = MathMax(nu_p_pricer, nu_p_prices) + 2 * MathAbs(nu_p_pricer - nu_p_prices);
   double nu_p_d3dn2 = MathMin(nu_p_pricer, nu_p_prices) - 2 * MathAbs(nu_p_pricer - nu_p_prices);
   
   if (nu_p_bid > nu_p_d3up2 || nu_p_bid < nu_p_d3dn2) nu_p_d3check = true;
   
   double nu_p_pricex = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double nu_p_pricey = 0;
   if (iClose(nu_p, PERIOD_D1, 4) > iClose(nu_p, PERIOD_D1, 5))
   nu_p_pricey = iHigh(nu_p, PERIOD_H1, iHighest(nu_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   nu_p_pricey = iLow(nu_p, PERIOD_H1, iLowest(nu_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double nu_p_midline5 = (nu_p_pricex + nu_p_pricey) / 2;
   double nu_p_d4up2 = MathMax(nu_p_pricex, nu_p_pricey) + 2 * MathAbs(nu_p_pricex - nu_p_pricey);
   double nu_p_d4dn2 = MathMin(nu_p_pricex, nu_p_pricey) - 2 * MathAbs(nu_p_pricex - nu_p_pricey);
   
   if (nu_p_bid > nu_p_d4up2 || nu_p_bid < nu_p_d4dn2) nu_p_d4check = true;
   
   AddToDrata(Fibz, dwm[18].daily, nu_p_ascheck, nu_p_midcheck, nu_p_wmidcheck, nu_p_d1check, nu_p_d2check, nu_p_d3check, nu_p_d4check, nu_p);
   
   //NZDCAD
   double nc_p_bid = MarketInfo(nc_p, MODE_BID);
   int nc_p_ascheck = 0;
   bool nc_p_midcheck = 0;
   int nc_p_wmidcheck = 0;
   bool nc_p_d1check = false;
   bool nc_p_d2check = false;
   bool nc_p_d3check = false;
   bool nc_p_d4check = false;
   
   double nc_p_midline = (iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double nc_p_ashi = iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double nc_p_aslo = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (nc_p_bid > nc_p_ashi) nc_p_ascheck = 1;
   else if (nc_p_bid < nc_p_ashi && nc_p_bid > nc_p_aslo) nc_p_ascheck = 2;
   else if (nc_p_bid < nc_p_aslo) nc_p_ascheck = 3;
   
   if (nc_p_bid > nc_p_midline) nc_p_midcheck = true;
   if (nc_p_bid < nc_p_midline) nc_p_midcheck = false;
   
   double nc_p_pricea = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double nc_p_priceb = 0;
   if (iClose(nc_p, PERIOD_D1, 1) > iClose(nc_p, PERIOD_D1, 2))
   nc_p_priceb = iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   nc_p_priceb = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double nc_p_midline2 = (nc_p_pricea + nc_p_priceb) / 2;
   double nc_p_d1up2 = MathMax(nc_p_pricea, nc_p_priceb) + 2 * MathAbs(nc_p_pricea - nc_p_priceb);
   double nc_p_d1dn2 = MathMin(nc_p_pricea, nc_p_priceb) - 2 * MathAbs(nc_p_pricea - nc_p_priceb);
   
   if (nc_p_bid > MathMax(nc_p_midline, nc_p_midline2)) nc_p_wmidcheck = 1;
   if (nc_p_bid < MathMax(nc_p_midline, nc_p_midline2) && nc_p_bid > MathMin(nc_p_midline, nc_p_midline2)) nc_p_wmidcheck = 2;
   if (nc_p_bid < MathMin(nc_p_midline, nc_p_midline2)) nc_p_wmidcheck = 3;
   
   if (nc_p_bid > nc_p_d1up2 || nc_p_bid < nc_p_d1dn2) nc_p_d1check = true;   
      
   double nc_p_pricen = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double nc_p_priceo = 0;
   if (iClose(nc_p, PERIOD_D1, 2) > iClose(nc_p, PERIOD_D1, 3))
   nc_p_priceo = iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   nc_p_priceo = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double nc_p_midline3 = (nc_p_pricen + nc_p_priceo) / 2;
   double nc_p_d2up2 = MathMax(nc_p_pricen, nc_p_priceo) + 2 * MathAbs(nc_p_pricen - nc_p_priceo);
   double nc_p_d2dn2 = MathMin(nc_p_pricen, nc_p_priceo) - 2 * MathAbs(nc_p_pricen - nc_p_priceo);
   
   if (nc_p_bid > nc_p_d2up2 || nc_p_bid < nc_p_d2dn2) nc_p_d2check = true;
   
   double nc_p_pricer = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double nc_p_prices = 0;
   if (iClose(nc_p, PERIOD_D1, 3) > iClose(nc_p, PERIOD_D1, 4))
   nc_p_prices = iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   nc_p_prices = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double nc_p_midline4 = (nc_p_pricer + nc_p_prices) / 2;
   double nc_p_d3up2 = MathMax(nc_p_pricer, nc_p_prices) + 2 * MathAbs(nc_p_pricer - nc_p_prices);
   double nc_p_d3dn2 = MathMin(nc_p_pricer, nc_p_prices) - 2 * MathAbs(nc_p_pricer - nc_p_prices);
   
   if (nc_p_bid > nc_p_d3up2 || nc_p_bid < nc_p_d3dn2) nc_p_d3check = true;
   
   double nc_p_pricex = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double nc_p_pricey = 0;
   if (iClose(nc_p, PERIOD_D1, 4) > iClose(nc_p, PERIOD_D1, 5))
   nc_p_pricey = iHigh(nc_p, PERIOD_H1, iHighest(nc_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   nc_p_pricey = iLow(nc_p, PERIOD_H1, iLowest(nc_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double nc_p_midline5 = (nc_p_pricex + nc_p_pricey) / 2;
   double nc_p_d4up2 = MathMax(nc_p_pricex, nc_p_pricey) + 2 * MathAbs(nc_p_pricex - nc_p_pricey);
   double nc_p_d4dn2 = MathMin(nc_p_pricex, nc_p_pricey) - 2 * MathAbs(nc_p_pricex - nc_p_pricey);
   
   if (nc_p_bid > nc_p_d4up2 || nc_p_bid < nc_p_d4dn2) nc_p_d4check = true;
   
   AddToDrata(Fibz, dwm[19].daily, nc_p_ascheck, nc_p_midcheck, nc_p_wmidcheck, nc_p_d1check, nc_p_d2check, nc_p_d3check, nc_p_d4check, nc_p);
   
   //NZDCHF
   double nf_p_bid = MarketInfo(nf_p, MODE_BID);
   int nf_p_ascheck = 0;
   bool nf_p_midcheck = 0;
   int nf_p_wmidcheck = 0;
   bool nf_p_d1check = false;
   bool nf_p_d2check = false;
   bool nf_p_d3check = false;
   bool nf_p_d4check = false;
   
   double nf_p_midline = (iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double nf_p_ashi = iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double nf_p_aslo = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (nf_p_bid > nf_p_ashi) nf_p_ascheck = 1;
   else if (nf_p_bid < nf_p_ashi && nf_p_bid > nf_p_aslo) nf_p_ascheck = 2;
   else if (nf_p_bid < nf_p_aslo) nf_p_ascheck = 3;
   
   if (nf_p_bid > nf_p_midline) nf_p_midcheck = true;
   if (nf_p_bid < nf_p_midline) nf_p_midcheck = false;
   
   double nf_p_pricea = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double nf_p_priceb = 0;
   if (iClose(nf_p, PERIOD_D1, 1) > iClose(nf_p, PERIOD_D1, 2))
   nf_p_priceb = iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   nf_p_priceb = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double nf_p_midline2 = (nf_p_pricea + nf_p_priceb) / 2;
   double nf_p_d1up2 = MathMax(nf_p_pricea, nf_p_priceb) + 2 * MathAbs(nf_p_pricea - nf_p_priceb);
   double nf_p_d1dn2 = MathMin(nf_p_pricea, nf_p_priceb) - 2 * MathAbs(nf_p_pricea - nf_p_priceb);
   
   if (nf_p_bid > MathMax(nf_p_midline, nf_p_midline2)) nf_p_wmidcheck = 1;
   if (nf_p_bid < MathMax(nf_p_midline, nf_p_midline2) && nf_p_bid > MathMin(nf_p_midline, nf_p_midline2)) nf_p_wmidcheck = 2;
   if (nf_p_bid < MathMin(nf_p_midline, nf_p_midline2)) nf_p_wmidcheck = 3;
   
   if (nf_p_bid > nf_p_d1up2 || nf_p_bid < nf_p_d1dn2) nf_p_d1check = true;   
      
   double nf_p_pricen = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double nf_p_priceo = 0;
   if (iClose(nf_p, PERIOD_D1, 2) > iClose(nf_p, PERIOD_D1, 3))
   nf_p_priceo = iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   nf_p_priceo = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double nf_p_midline3 = (nf_p_pricen + nf_p_priceo) / 2;
   double nf_p_d2up2 = MathMax(nf_p_pricen, nf_p_priceo) + 2 * MathAbs(nf_p_pricen - nf_p_priceo);
   double nf_p_d2dn2 = MathMin(nf_p_pricen, nf_p_priceo) - 2 * MathAbs(nf_p_pricen - nf_p_priceo);
   
   if (nf_p_bid > nf_p_d2up2 || nf_p_bid < nf_p_d2dn2) nf_p_d2check = true;
   
   double nf_p_pricer = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double nf_p_prices = 0;
   if (iClose(nf_p, PERIOD_D1, 3) > iClose(nf_p, PERIOD_D1, 4))
   nf_p_prices = iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   nf_p_prices = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double nf_p_midline4 = (nf_p_pricer + nf_p_prices) / 2;
   double nf_p_d3up2 = MathMax(nf_p_pricer, nf_p_prices) + 2 * MathAbs(nf_p_pricer - nf_p_prices);
   double nf_p_d3dn2 = MathMin(nf_p_pricer, nf_p_prices) - 2 * MathAbs(nf_p_pricer - nf_p_prices);
   
   if (nf_p_bid > nf_p_d3up2 || nf_p_bid < nf_p_d3dn2) nf_p_d3check = true;
   
   double nf_p_pricex = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double nf_p_pricey = 0;
   if (iClose(nf_p, PERIOD_D1, 4) > iClose(nf_p, PERIOD_D1, 5))
   nf_p_pricey = iHigh(nf_p, PERIOD_H1, iHighest(nf_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   nf_p_pricey = iLow(nf_p, PERIOD_H1, iLowest(nf_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double nf_p_midline5 = (nf_p_pricex + nf_p_pricey) / 2;
   double nf_p_d4up2 = MathMax(nf_p_pricex, nf_p_pricey) + 2 * MathAbs(nf_p_pricex - nf_p_pricey);
   double nf_p_d4dn2 = MathMin(nf_p_pricex, nf_p_pricey) - 2 * MathAbs(nf_p_pricex - nf_p_pricey);
   
   if (nf_p_bid > nf_p_d4up2 || nf_p_bid < nf_p_d4dn2) nf_p_d4check = true;
   
   AddToDrata(Fibz, dwm[20].daily, nf_p_ascheck, nf_p_midcheck, nf_p_wmidcheck, nf_p_d1check, nf_p_d2check, nf_p_d3check, nf_p_d4check, nf_p);
   
   //NZDJPY
   double nj_p_bid = MarketInfo(nj_p, MODE_BID);
   int nj_p_ascheck = 0;
   bool nj_p_midcheck = 0;
   int nj_p_wmidcheck = 0;
   bool nj_p_d1check = false;
   bool nj_p_d2check = false;
   bool nj_p_d3check = false;
   bool nj_p_d4check = false;
   
   double nj_p_midline = (iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double nj_p_ashi = iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double nj_p_aslo = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (nj_p_bid > nj_p_ashi) nj_p_ascheck = 1;
   else if (nj_p_bid < nj_p_ashi && nj_p_bid > nj_p_aslo) nj_p_ascheck = 2;
   else if (nj_p_bid < nj_p_aslo) nj_p_ascheck = 3;
   
   if (nj_p_bid > nj_p_midline) nj_p_midcheck = true;
   if (nj_p_bid < nj_p_midline) nj_p_midcheck = false;
   
   double nj_p_pricea = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double nj_p_priceb = 0;
   if (iClose(nj_p, PERIOD_D1, 1) > iClose(nj_p, PERIOD_D1, 2))
   nj_p_priceb = iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   nj_p_priceb = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double nj_p_midline2 = (nj_p_pricea + nj_p_priceb) / 2;
   double nj_p_d1up2 = MathMax(nj_p_pricea, nj_p_priceb) + 2 * MathAbs(nj_p_pricea - nj_p_priceb);
   double nj_p_d1dn2 = MathMin(nj_p_pricea, nj_p_priceb) - 2 * MathAbs(nj_p_pricea - nj_p_priceb);
   
   if (nj_p_bid > MathMax(nj_p_midline, nj_p_midline2)) nj_p_wmidcheck = 1;
   if (nj_p_bid < MathMax(nj_p_midline, nj_p_midline2) && nj_p_bid > MathMin(nj_p_midline, nj_p_midline2)) nj_p_wmidcheck = 2;
   if (nj_p_bid < MathMin(nj_p_midline, nj_p_midline2)) nj_p_wmidcheck = 3;
   
   if (nj_p_bid > nj_p_d1up2 || nj_p_bid < nj_p_d1dn2) nj_p_d1check = true;   
      
   double nj_p_pricen = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double nj_p_priceo = 0;
   if (iClose(nj_p, PERIOD_D1, 2) > iClose(nj_p, PERIOD_D1, 3))
   nj_p_priceo = iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   nj_p_priceo = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double nj_p_midline3 = (nj_p_pricen + nj_p_priceo) / 2;
   double nj_p_d2up2 = MathMax(nj_p_pricen, nj_p_priceo) + 2 * MathAbs(nj_p_pricen - nj_p_priceo);
   double nj_p_d2dn2 = MathMin(nj_p_pricen, nj_p_priceo) - 2 * MathAbs(nj_p_pricen - nj_p_priceo);
   
   if (nj_p_bid > nj_p_d2up2 || nj_p_bid < nj_p_d2dn2) nj_p_d2check = true;
   
   double nj_p_pricer = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double nj_p_prices = 0;
   if (iClose(nj_p, PERIOD_D1, 3) > iClose(nj_p, PERIOD_D1, 4))
   nj_p_prices = iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   nj_p_prices = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double nj_p_midline4 = (nj_p_pricer + nj_p_prices) / 2;
   double nj_p_d3up2 = MathMax(nj_p_pricer, nj_p_prices) + 2 * MathAbs(nj_p_pricer - nj_p_prices);
   double nj_p_d3dn2 = MathMin(nj_p_pricer, nj_p_prices) - 2 * MathAbs(nj_p_pricer - nj_p_prices);
   
   if (nj_p_bid > nj_p_d3up2 || nj_p_bid < nj_p_d3dn2) nj_p_d3check = true;
   
   double nj_p_pricex = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double nj_p_pricey = 0;
   if (iClose(nj_p, PERIOD_D1, 4) > iClose(nj_p, PERIOD_D1, 5))
   nj_p_pricey = iHigh(nj_p, PERIOD_H1, iHighest(nj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   nj_p_pricey = iLow(nj_p, PERIOD_H1, iLowest(nj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double nj_p_midline5 = (nj_p_pricex + nj_p_pricey) / 2;
   double nj_p_d4up2 = MathMax(nj_p_pricex, nj_p_pricey) + 2 * MathAbs(nj_p_pricex - nj_p_pricey);
   double nj_p_d4dn2 = MathMin(nj_p_pricex, nj_p_pricey) - 2 * MathAbs(nj_p_pricex - nj_p_pricey);
   
   if (nj_p_bid > nj_p_d4up2 || nj_p_bid < nj_p_d4dn2) nj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[21].daily, nj_p_ascheck, nj_p_midcheck, nj_p_wmidcheck, nj_p_d1check, nj_p_d2check, nj_p_d3check, nj_p_d4check, nj_p);
   
   //USDCAD
   double uc_p_bid = MarketInfo(uc_p, MODE_BID);
   int uc_p_ascheck = 0;
   bool uc_p_midcheck = 0;
   int uc_p_wmidcheck = 0;
   bool uc_p_d1check = false;
   bool uc_p_d2check = false;
   bool uc_p_d3check = false;
   bool uc_p_d4check = false;
   
   double uc_p_midline = (iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double uc_p_ashi = iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double uc_p_aslo = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (uc_p_bid > uc_p_ashi) uc_p_ascheck = 1;
   else if (uc_p_bid < uc_p_ashi && uc_p_bid > uc_p_aslo) uc_p_ascheck = 2;
   else if (uc_p_bid < uc_p_aslo) uc_p_ascheck = 3;
   
   if (uc_p_bid > uc_p_midline) uc_p_midcheck = true;
   if (uc_p_bid < uc_p_midline) uc_p_midcheck = false;
   
   double uc_p_pricea = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double uc_p_priceb = 0;
   if (iClose(uc_p, PERIOD_D1, 1) > iClose(uc_p, PERIOD_D1, 2))
   uc_p_priceb = iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   uc_p_priceb = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double uc_p_midline2 = (uc_p_pricea + uc_p_priceb) / 2;
   double uc_p_d1up2 = MathMax(uc_p_pricea, uc_p_priceb) + 2 * MathAbs(uc_p_pricea - uc_p_priceb);
   double uc_p_d1dn2 = MathMin(uc_p_pricea, uc_p_priceb) - 2 * MathAbs(uc_p_pricea - uc_p_priceb);
   
   if (uc_p_bid > MathMax(uc_p_midline, uc_p_midline2)) uc_p_wmidcheck = 1;
   if (uc_p_bid < MathMax(uc_p_midline, uc_p_midline2) && uc_p_bid > MathMin(uc_p_midline, uc_p_midline2)) uc_p_wmidcheck = 2;
   if (uc_p_bid < MathMin(uc_p_midline, uc_p_midline2)) uc_p_wmidcheck = 3;
   
   if (uc_p_bid > uc_p_d1up2 || uc_p_bid < uc_p_d1dn2) uc_p_d1check = true;   
      
   double uc_p_pricen = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double uc_p_priceo = 0;
   if (iClose(uc_p, PERIOD_D1, 2) > iClose(uc_p, PERIOD_D1, 3))
   uc_p_priceo = iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   uc_p_priceo = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double uc_p_midline3 = (uc_p_pricen + uc_p_priceo) / 2;
   double uc_p_d2up2 = MathMax(uc_p_pricen, uc_p_priceo) + 2 * MathAbs(uc_p_pricen - uc_p_priceo);
   double uc_p_d2dn2 = MathMin(uc_p_pricen, uc_p_priceo) - 2 * MathAbs(uc_p_pricen - uc_p_priceo);
   
   if (uc_p_bid > uc_p_d2up2 || uc_p_bid < uc_p_d2dn2) uc_p_d2check = true;
   
   double uc_p_pricer = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double uc_p_prices = 0;
   if (iClose(uc_p, PERIOD_D1, 3) > iClose(uc_p, PERIOD_D1, 4))
   uc_p_prices = iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   uc_p_prices = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double uc_p_midline4 = (uc_p_pricer + uc_p_prices) / 2;
   double uc_p_d3up2 = MathMax(uc_p_pricer, uc_p_prices) + 2 * MathAbs(uc_p_pricer - uc_p_prices);
   double uc_p_d3dn2 = MathMin(uc_p_pricer, uc_p_prices) - 2 * MathAbs(uc_p_pricer - uc_p_prices);
   
   if (uc_p_bid > uc_p_d3up2 || uc_p_bid < uc_p_d3dn2) uc_p_d3check = true;
   
   double uc_p_pricex = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double uc_p_pricey = 0;
   if (iClose(uc_p, PERIOD_D1, 4) > iClose(uc_p, PERIOD_D1, 5))
   uc_p_pricey = iHigh(uc_p, PERIOD_H1, iHighest(uc_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   uc_p_pricey = iLow(uc_p, PERIOD_H1, iLowest(uc_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double uc_p_midline5 = (uc_p_pricex + uc_p_pricey) / 2;
   double uc_p_d4up2 = MathMax(uc_p_pricex, uc_p_pricey) + 2 * MathAbs(uc_p_pricex - uc_p_pricey);
   double uc_p_d4dn2 = MathMin(uc_p_pricex, uc_p_pricey) - 2 * MathAbs(uc_p_pricex - uc_p_pricey);
   
   if (uc_p_bid > uc_p_d4up2 || uc_p_bid < uc_p_d4dn2) uc_p_d4check = true;
   
   AddToDrata(Fibz, dwm[22].daily, uc_p_ascheck, uc_p_midcheck, uc_p_wmidcheck, uc_p_d1check, uc_p_d2check, uc_p_d3check, uc_p_d4check, uc_p);
   
   //CADCHF
   double cf_p_bid = MarketInfo(cf_p, MODE_BID);
   int cf_p_ascheck = 0;
   bool cf_p_midcheck = 0;
   int cf_p_wmidcheck = 0;
   bool cf_p_d1check = false;
   bool cf_p_d2check = false;
   bool cf_p_d3check = false;
   bool cf_p_d4check = false;
   
   double cf_p_midline = (iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double cf_p_ashi = iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double cf_p_aslo = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (cf_p_bid > cf_p_ashi) cf_p_ascheck = 1;
   else if (cf_p_bid < cf_p_ashi && cf_p_bid > cf_p_aslo) cf_p_ascheck = 2;
   else if (cf_p_bid < cf_p_aslo) cf_p_ascheck = 3;
   
   if (cf_p_bid > cf_p_midline) cf_p_midcheck = true;
   if (cf_p_bid < cf_p_midline) cf_p_midcheck = false;
   
   double cf_p_pricea = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double cf_p_priceb = 0;
   if (iClose(cf_p, PERIOD_D1, 1) > iClose(cf_p, PERIOD_D1, 2))
   cf_p_priceb = iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   cf_p_priceb = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double cf_p_midline2 = (cf_p_pricea + cf_p_priceb) / 2;
   double cf_p_d1up2 = MathMax(cf_p_pricea, cf_p_priceb) + 2 * MathAbs(cf_p_pricea - cf_p_priceb);
   double cf_p_d1dn2 = MathMin(cf_p_pricea, cf_p_priceb) - 2 * MathAbs(cf_p_pricea - cf_p_priceb);
   
   if (cf_p_bid > MathMax(cf_p_midline, cf_p_midline2)) cf_p_wmidcheck = 1;
   if (cf_p_bid < MathMax(cf_p_midline, cf_p_midline2) && cf_p_bid > MathMin(cf_p_midline, cf_p_midline2)) cf_p_wmidcheck = 2;
   if (cf_p_bid < MathMin(cf_p_midline, cf_p_midline2)) cf_p_wmidcheck = 3;
   
   if (cf_p_bid > cf_p_d1up2 || cf_p_bid < cf_p_d1dn2) cf_p_d1check = true;   
      
   double cf_p_pricen = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double cf_p_priceo = 0;
   if (iClose(cf_p, PERIOD_D1, 2) > iClose(cf_p, PERIOD_D1, 3))
   cf_p_priceo = iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   cf_p_priceo = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double cf_p_midline3 = (cf_p_pricen + cf_p_priceo) / 2;
   double cf_p_d2up2 = MathMax(cf_p_pricen, cf_p_priceo) + 2 * MathAbs(cf_p_pricen - cf_p_priceo);
   double cf_p_d2dn2 = MathMin(cf_p_pricen, cf_p_priceo) - 2 * MathAbs(cf_p_pricen - cf_p_priceo);
   
   if (cf_p_bid > cf_p_d2up2 || cf_p_bid < cf_p_d2dn2) cf_p_d2check = true;
   
   double cf_p_pricer = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double cf_p_prices = 0;
   if (iClose(cf_p, PERIOD_D1, 3) > iClose(cf_p, PERIOD_D1, 4))
   cf_p_prices = iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   cf_p_prices = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double cf_p_midline4 = (cf_p_pricer + cf_p_prices) / 2;
   double cf_p_d3up2 = MathMax(cf_p_pricer, cf_p_prices) + 2 * MathAbs(cf_p_pricer - cf_p_prices);
   double cf_p_d3dn2 = MathMin(cf_p_pricer, cf_p_prices) - 2 * MathAbs(cf_p_pricer - cf_p_prices);
   
   if (cf_p_bid > cf_p_d3up2 || cf_p_bid < cf_p_d3dn2) cf_p_d3check = true;
   
   double cf_p_pricex = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double cf_p_pricey = 0;
   if (iClose(cf_p, PERIOD_D1, 4) > iClose(cf_p, PERIOD_D1, 5))
   cf_p_pricey = iHigh(cf_p, PERIOD_H1, iHighest(cf_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   cf_p_pricey = iLow(cf_p, PERIOD_H1, iLowest(cf_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double cf_p_midline5 = (cf_p_pricex + cf_p_pricey) / 2;
   double cf_p_d4up2 = MathMax(cf_p_pricex, cf_p_pricey) + 2 * MathAbs(cf_p_pricex - cf_p_pricey);
   double cf_p_d4dn2 = MathMin(cf_p_pricex, cf_p_pricey) - 2 * MathAbs(cf_p_pricex - cf_p_pricey);
   
   if (cf_p_bid > cf_p_d4up2 || cf_p_bid < cf_p_d4dn2) cf_p_d4check = true;
   
   AddToDrata(Fibz, dwm[23].daily, cf_p_ascheck, cf_p_midcheck, cf_p_wmidcheck, cf_p_d1check, cf_p_d2check, cf_p_d3check, cf_p_d4check, cf_p);
   
   //CADJPY
   double cj_p_bid = MarketInfo(cj_p, MODE_BID);
   int cj_p_ascheck = 0;
   bool cj_p_midcheck = 0;
   int cj_p_wmidcheck = 0;
   bool cj_p_d1check = false;
   bool cj_p_d2check = false;
   bool cj_p_d3check = false;
   bool cj_p_d4check = false;
   
   double cj_p_midline = (iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double cj_p_ashi = iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double cj_p_aslo = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (cj_p_bid > cj_p_ashi) cj_p_ascheck = 1;
   else if (cj_p_bid < cj_p_ashi && cj_p_bid > cj_p_aslo) cj_p_ascheck = 2;
   else if (cj_p_bid < cj_p_aslo) cj_p_ascheck = 3;
   
   if (cj_p_bid > cj_p_midline) cj_p_midcheck = true;
   if (cj_p_bid < cj_p_midline) cj_p_midcheck = false;
   
   double cj_p_pricea = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double cj_p_priceb = 0;
   if (iClose(cj_p, PERIOD_D1, 1) > iClose(cj_p, PERIOD_D1, 2))
   cj_p_priceb = iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   cj_p_priceb = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double cj_p_midline2 = (cj_p_pricea + cj_p_priceb) / 2;
   double cj_p_d1up2 = MathMax(cj_p_pricea, cj_p_priceb) + 2 * MathAbs(cj_p_pricea - cj_p_priceb);
   double cj_p_d1dn2 = MathMin(cj_p_pricea, cj_p_priceb) - 2 * MathAbs(cj_p_pricea - cj_p_priceb);
   
   if (cj_p_bid > MathMax(cj_p_midline, cj_p_midline2)) cj_p_wmidcheck = 1;
   if (cj_p_bid < MathMax(cj_p_midline, cj_p_midline2) && cj_p_bid > MathMin(cj_p_midline, cj_p_midline2)) cj_p_wmidcheck = 2;
   if (cj_p_bid < MathMin(cj_p_midline, cj_p_midline2)) cj_p_wmidcheck = 3;
   
   if (cj_p_bid > cj_p_d1up2 || cj_p_bid < cj_p_d1dn2) cj_p_d1check = true;   
      
   double cj_p_pricen = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double cj_p_priceo = 0;
   if (iClose(cj_p, PERIOD_D1, 2) > iClose(cj_p, PERIOD_D1, 3))
   cj_p_priceo = iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   cj_p_priceo = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double cj_p_midline3 = (cj_p_pricen + cj_p_priceo) / 2;
   double cj_p_d2up2 = MathMax(cj_p_pricen, cj_p_priceo) + 2 * MathAbs(cj_p_pricen - cj_p_priceo);
   double cj_p_d2dn2 = MathMin(cj_p_pricen, cj_p_priceo) - 2 * MathAbs(cj_p_pricen - cj_p_priceo);
   
   if (cj_p_bid > cj_p_d2up2 || cj_p_bid < cj_p_d2dn2) cj_p_d2check = true;
   
   double cj_p_pricer = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double cj_p_prices = 0;
   if (iClose(cj_p, PERIOD_D1, 3) > iClose(cj_p, PERIOD_D1, 4))
   cj_p_prices = iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   cj_p_prices = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double cj_p_midline4 = (cj_p_pricer + cj_p_prices) / 2;
   double cj_p_d3up2 = MathMax(cj_p_pricer, cj_p_prices) + 2 * MathAbs(cj_p_pricer - cj_p_prices);
   double cj_p_d3dn2 = MathMin(cj_p_pricer, cj_p_prices) - 2 * MathAbs(cj_p_pricer - cj_p_prices);
   
   if (cj_p_bid > cj_p_d3up2 || cj_p_bid < cj_p_d3dn2) cj_p_d3check = true;
   
   double cj_p_pricex = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double cj_p_pricey = 0;
   if (iClose(cj_p, PERIOD_D1, 4) > iClose(cj_p, PERIOD_D1, 5))
   cj_p_pricey = iHigh(cj_p, PERIOD_H1, iHighest(cj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   cj_p_pricey = iLow(cj_p, PERIOD_H1, iLowest(cj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double cj_p_midline5 = (cj_p_pricex + cj_p_pricey) / 2;
   double cj_p_d4up2 = MathMax(cj_p_pricex, cj_p_pricey) + 2 * MathAbs(cj_p_pricex - cj_p_pricey);
   double cj_p_d4dn2 = MathMin(cj_p_pricex, cj_p_pricey) - 2 * MathAbs(cj_p_pricex - cj_p_pricey);
   
   if (cj_p_bid > cj_p_d4up2 || cj_p_bid < cj_p_d4dn2) cj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[24].daily, cj_p_ascheck, cj_p_midcheck, cj_p_wmidcheck, cj_p_d1check, cj_p_d2check, cj_p_d3check, cj_p_d4check, cj_p);
   
   //USDCHF
   double uf_p_bid = MarketInfo(uf_p, MODE_BID);
   int uf_p_ascheck = 0;
   bool uf_p_midcheck = 0;
   int uf_p_wmidcheck = 0;
   bool uf_p_d1check = false;
   bool uf_p_d2check = false;
   bool uf_p_d3check = false;
   bool uf_p_d4check = false;
   
   double uf_p_midline = (iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double uf_p_ashi = iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double uf_p_aslo = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (uf_p_bid > uf_p_ashi) uf_p_ascheck = 1;
   else if (uf_p_bid < uf_p_ashi && uf_p_bid > uf_p_aslo) uf_p_ascheck = 2;
   else if (uf_p_bid < uf_p_aslo) uf_p_ascheck = 3;
   
   if (uf_p_bid > uf_p_midline) uf_p_midcheck = true;
   if (uf_p_bid < uf_p_midline) uf_p_midcheck = false;
   
   double uf_p_pricea = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double uf_p_priceb = 0;
   if (iClose(uf_p, PERIOD_D1, 1) > iClose(uf_p, PERIOD_D1, 2))
   uf_p_priceb = iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   uf_p_priceb = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double uf_p_midline2 = (uf_p_pricea + uf_p_priceb) / 2;
   double uf_p_d1up2 = MathMax(uf_p_pricea, uf_p_priceb) + 2 * MathAbs(uf_p_pricea - uf_p_priceb);
   double uf_p_d1dn2 = MathMin(uf_p_pricea, uf_p_priceb) - 2 * MathAbs(uf_p_pricea - uf_p_priceb);
   
   if (uf_p_bid > MathMax(uf_p_midline, uf_p_midline2)) uf_p_wmidcheck = 1;
   if (uf_p_bid < MathMax(uf_p_midline, uf_p_midline2) && uf_p_bid > MathMin(uf_p_midline, uf_p_midline2)) uf_p_wmidcheck = 2;
   if (uf_p_bid < MathMin(uf_p_midline, uf_p_midline2)) uf_p_wmidcheck = 3;
   
   if (uf_p_bid > uf_p_d1up2 || uf_p_bid < uf_p_d1dn2) uf_p_d1check = true;   
      
   double uf_p_pricen = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double uf_p_priceo = 0;
   if (iClose(uf_p, PERIOD_D1, 2) > iClose(uf_p, PERIOD_D1, 3))
   uf_p_priceo = iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   uf_p_priceo = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double uf_p_midline3 = (uf_p_pricen + uf_p_priceo) / 2;
   double uf_p_d2up2 = MathMax(uf_p_pricen, uf_p_priceo) + 2 * MathAbs(uf_p_pricen - uf_p_priceo);
   double uf_p_d2dn2 = MathMin(uf_p_pricen, uf_p_priceo) - 2 * MathAbs(uf_p_pricen - uf_p_priceo);
   
   if (uf_p_bid > uf_p_d2up2 || uf_p_bid < uf_p_d2dn2) uf_p_d2check = true;
   
   double uf_p_pricer = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double uf_p_prices = 0;
   if (iClose(uf_p, PERIOD_D1, 3) > iClose(uf_p, PERIOD_D1, 4))
   uf_p_prices = iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   uf_p_prices = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double uf_p_midline4 = (uf_p_pricer + uf_p_prices) / 2;
   double uf_p_d3up2 = MathMax(uf_p_pricer, uf_p_prices) + 2 * MathAbs(uf_p_pricer - uf_p_prices);
   double uf_p_d3dn2 = MathMin(uf_p_pricer, uf_p_prices) - 2 * MathAbs(uf_p_pricer - uf_p_prices);
   
   if (uf_p_bid > uf_p_d3up2 || uf_p_bid < uf_p_d3dn2) uf_p_d3check = true;
   
   double uf_p_pricex = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double uf_p_pricey = 0;
   if (iClose(uf_p, PERIOD_D1, 4) > iClose(uf_p, PERIOD_D1, 5))
   uf_p_pricey = iHigh(uf_p, PERIOD_H1, iHighest(uf_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   uf_p_pricey = iLow(uf_p, PERIOD_H1, iLowest(uf_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double uf_p_midline5 = (uf_p_pricex + uf_p_pricey) / 2;
   double uf_p_d4up2 = MathMax(uf_p_pricex, uf_p_pricey) + 2 * MathAbs(uf_p_pricex - uf_p_pricey);
   double uf_p_d4dn2 = MathMin(uf_p_pricex, uf_p_pricey) - 2 * MathAbs(uf_p_pricex - uf_p_pricey);
   
   if (uf_p_bid > uf_p_d4up2 || uf_p_bid < uf_p_d4dn2) uf_p_d4check = true;
   
   AddToDrata(Fibz, dwm[25].daily, uf_p_ascheck, uf_p_midcheck, uf_p_wmidcheck, uf_p_d1check, uf_p_d2check, uf_p_d3check, uf_p_d4check, uf_p);
   
   //CHFJPY
   double fj_p_bid = MarketInfo(fj_p, MODE_BID);
   int fj_p_ascheck = 0;
   bool fj_p_midcheck = 0;
   int fj_p_wmidcheck = 0;
   bool fj_p_d1check = false;
   bool fj_p_d2check = false;
   bool fj_p_d3check = false;
   bool fj_p_d4check = false;
   
   double fj_p_midline = (iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double fj_p_ashi = iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double fj_p_aslo = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (fj_p_bid > fj_p_ashi) fj_p_ascheck = 1;
   else if (fj_p_bid < fj_p_ashi && fj_p_bid > fj_p_aslo) fj_p_ascheck = 2;
   else if (fj_p_bid < fj_p_aslo) fj_p_ascheck = 3;
   
   if (fj_p_bid > fj_p_midline) fj_p_midcheck = true;
   if (fj_p_bid < fj_p_midline) fj_p_midcheck = false;
   
   double fj_p_pricea = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double fj_p_priceb = 0;
   if (iClose(fj_p, PERIOD_D1, 1) > iClose(fj_p, PERIOD_D1, 2))
   fj_p_priceb = iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   fj_p_priceb = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double fj_p_midline2 = (fj_p_pricea + fj_p_priceb) / 2;
   double fj_p_d1up2 = MathMax(fj_p_pricea, fj_p_priceb) + 2 * MathAbs(fj_p_pricea - fj_p_priceb);
   double fj_p_d1dn2 = MathMin(fj_p_pricea, fj_p_priceb) - 2 * MathAbs(fj_p_pricea - fj_p_priceb);
   
   if (fj_p_bid > MathMax(fj_p_midline, fj_p_midline2)) fj_p_wmidcheck = 1;
   if (fj_p_bid < MathMax(fj_p_midline, fj_p_midline2) && fj_p_bid > MathMin(fj_p_midline, fj_p_midline2)) fj_p_wmidcheck = 2;
   if (fj_p_bid < MathMin(fj_p_midline, fj_p_midline2)) fj_p_wmidcheck = 3;
   
   if (fj_p_bid > fj_p_d1up2 || fj_p_bid < fj_p_d1dn2) fj_p_d1check = true;   
      
   double fj_p_pricen = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double fj_p_priceo = 0;
   if (iClose(fj_p, PERIOD_D1, 2) > iClose(fj_p, PERIOD_D1, 3))
   fj_p_priceo = iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   fj_p_priceo = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double fj_p_midline3 = (fj_p_pricen + fj_p_priceo) / 2;
   double fj_p_d2up2 = MathMax(fj_p_pricen, fj_p_priceo) + 2 * MathAbs(fj_p_pricen - fj_p_priceo);
   double fj_p_d2dn2 = MathMin(fj_p_pricen, fj_p_priceo) - 2 * MathAbs(fj_p_pricen - fj_p_priceo);
   
   if (fj_p_bid > fj_p_d2up2 || fj_p_bid < fj_p_d2dn2) fj_p_d2check = true;
   
   double fj_p_pricer = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double fj_p_prices = 0;
   if (iClose(fj_p, PERIOD_D1, 3) > iClose(fj_p, PERIOD_D1, 4))
   fj_p_prices = iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   fj_p_prices = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double fj_p_midline4 = (fj_p_pricer + fj_p_prices) / 2;
   double fj_p_d3up2 = MathMax(fj_p_pricer, fj_p_prices) + 2 * MathAbs(fj_p_pricer - fj_p_prices);
   double fj_p_d3dn2 = MathMin(fj_p_pricer, fj_p_prices) - 2 * MathAbs(fj_p_pricer - fj_p_prices);
   
   if (fj_p_bid > fj_p_d3up2 || fj_p_bid < fj_p_d3dn2) fj_p_d3check = true;
   
   double fj_p_pricex = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double fj_p_pricey = 0;
   if (iClose(fj_p, PERIOD_D1, 4) > iClose(fj_p, PERIOD_D1, 5))
   fj_p_pricey = iHigh(fj_p, PERIOD_H1, iHighest(fj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   fj_p_pricey = iLow(fj_p, PERIOD_H1, iLowest(fj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double fj_p_midline5 = (fj_p_pricex + fj_p_pricey) / 2;
   double fj_p_d4up2 = MathMax(fj_p_pricex, fj_p_pricey) + 2 * MathAbs(fj_p_pricex - fj_p_pricey);
   double fj_p_d4dn2 = MathMin(fj_p_pricex, fj_p_pricey) - 2 * MathAbs(fj_p_pricex - fj_p_pricey);
   
   if (fj_p_bid > fj_p_d4up2 || fj_p_bid < fj_p_d4dn2) fj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[26].daily, fj_p_ascheck, fj_p_midcheck, fj_p_wmidcheck, fj_p_d1check, fj_p_d2check, fj_p_d3check, fj_p_d4check, fj_p);
   
   //USDJPY
   double uj_p_bid = MarketInfo(uj_p, MODE_BID);
   int uj_p_ascheck = 0;
   bool uj_p_midcheck = 0;
   int uj_p_wmidcheck = 0;
   bool uj_p_d1check = false;
   bool uj_p_d2check = false;
   bool uj_p_d3check = false;
   bool uj_p_d4check = false;
   
   double uj_p_midline = (iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1)) + iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 1))) / 2;
   double uj_p_ashi = iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   double uj_p_aslo = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   
   if (uj_p_bid > uj_p_ashi) uj_p_ascheck = 1;
   else if (uj_p_bid < uj_p_ashi && uj_p_bid > uj_p_aslo) uj_p_ascheck = 2;
   else if (uj_p_bid < uj_p_aslo) uj_p_ascheck = 3;
   
   if (uj_p_bid > uj_p_midline) uj_p_midcheck = true;
   if (uj_p_bid < uj_p_midline) uj_p_midcheck = false;
   
   double uj_p_pricea = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1)) + (iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 24 + 1)) - iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 24 + 1))) / 2;
   double uj_p_priceb = 0;
   if (iClose(uj_p, PERIOD_D1, 1) > iClose(uj_p, PERIOD_D1, 2))
   uj_p_priceb = iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 1));
   else
   uj_p_priceb = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 1));
   double uj_p_midline2 = (uj_p_pricea + uj_p_priceb) / 2;
   double uj_p_d1up2 = MathMax(uj_p_pricea, uj_p_priceb) + 2 * MathAbs(uj_p_pricea - uj_p_priceb);
   double uj_p_d1dn2 = MathMin(uj_p_pricea, uj_p_priceb) - 2 * MathAbs(uj_p_pricea - uj_p_priceb);
   
   if (uj_p_bid > MathMax(uj_p_midline, uj_p_midline2)) uj_p_wmidcheck = 1;
   if (uj_p_bid < MathMax(uj_p_midline, uj_p_midline2) && uj_p_bid > MathMin(uj_p_midline, uj_p_midline2)) uj_p_wmidcheck = 2;
   if (uj_p_bid < MathMin(uj_p_midline, uj_p_midline2)) uj_p_wmidcheck = 3;
   
   if (uj_p_bid > uj_p_d1up2 || uj_p_bid < uj_p_d1dn2) uj_p_d1check = true;   
      
   double uj_p_pricen = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1)) + (iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 48 + 1)) - iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 48 + 1))) / 2;
   double uj_p_priceo = 0;
   if (iClose(uj_p, PERIOD_D1, 2) > iClose(uj_p, PERIOD_D1, 3))
   uj_p_priceo = iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, 24 + hr + 1));
   else
   uj_p_priceo = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, 24 + hr + 1));
   double uj_p_midline3 = (uj_p_pricen + uj_p_priceo) / 2;
   double uj_p_d2up2 = MathMax(uj_p_pricen, uj_p_priceo) + 2 * MathAbs(uj_p_pricen - uj_p_priceo);
   double uj_p_d2dn2 = MathMin(uj_p_pricen, uj_p_priceo) - 2 * MathAbs(uj_p_pricen - uj_p_priceo);
   
   if (uj_p_bid > uj_p_d2up2 || uj_p_bid < uj_p_d2dn2) uj_p_d2check = true;
   
   double uj_p_pricer = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1)) + (iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 72 + 1)) - iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 72 + 1))) / 2;
   double uj_p_prices = 0;
   if (iClose(uj_p, PERIOD_D1, 3) > iClose(uj_p, PERIOD_D1, 4))
   uj_p_prices = iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, 48 + hr + 1));
   else
   uj_p_prices = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, 48 + hr + 1));
   double uj_p_midline4 = (uj_p_pricer + uj_p_prices) / 2;
   double uj_p_d3up2 = MathMax(uj_p_pricer, uj_p_prices) + 2 * MathAbs(uj_p_pricer - uj_p_prices);
   double uj_p_d3dn2 = MathMin(uj_p_pricer, uj_p_prices) - 2 * MathAbs(uj_p_pricer - uj_p_prices);
   
   if (uj_p_bid > uj_p_d3up2 || uj_p_bid < uj_p_d3dn2) uj_p_d3check = true;
   
   double uj_p_pricex = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1)) + (iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, hr + 96 + 1)) - iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, hr + 96 + 1))) / 2;
   double uj_p_pricey = 0;
   if (iClose(uj_p, PERIOD_D1, 4) > iClose(uj_p, PERIOD_D1, 5))
   uj_p_pricey = iHigh(uj_p, PERIOD_H1, iHighest(uj_p, PERIOD_H1, MODE_HIGH, tim, 72 + hr + 1));
   else
   uj_p_pricey = iLow(uj_p, PERIOD_H1, iLowest(uj_p, PERIOD_H1, MODE_LOW, tim, 72 + hr + 1));
   double uj_p_midline5 = (uj_p_pricex + uj_p_pricey) / 2;
   double uj_p_d4up2 = MathMax(uj_p_pricex, uj_p_pricey) + 2 * MathAbs(uj_p_pricex - uj_p_pricey);
   double uj_p_d4dn2 = MathMin(uj_p_pricex, uj_p_pricey) - 2 * MathAbs(uj_p_pricex - uj_p_pricey);
   
   if (uj_p_bid > uj_p_d4up2 || uj_p_bid < uj_p_d4dn2) uj_p_d4check = true;
   
   AddToDrata(Fibz, dwm[27].daily, uj_p_ascheck, uj_p_midcheck, uj_p_wmidcheck, uj_p_d1check, uj_p_d2check, uj_p_d3check, uj_p_d4check, uj_p);
   
   ArraySortStruct(Fibz, dailyscore);
   
   mainsort["b"][0].Add(Fibz[27].dailyscore);
   mainsort["b"][0].Add(Fibz[27].ashilo);
   mainsort["b"][0].Add(Fibz[27].asmid);
   mainsort["b"][0].Add(Fibz[27].wmid);
   
   mainsort["b"][1].Add(Fibz[26].dailyscore);
   mainsort["b"][1].Add(Fibz[26].ashilo);
   mainsort["b"][1].Add(Fibz[26].asmid);
   mainsort["b"][1].Add(Fibz[26].wmid);
   
   mainsort["b"][2].Add(Fibz[25].dailyscore);
   mainsort["b"][2].Add(Fibz[25].ashilo);
   mainsort["b"][2].Add(Fibz[25].asmid);
   mainsort["b"][2].Add(Fibz[25].wmid);
   
   mainsort["b"][3].Add(Fibz[24].dailyscore);
   mainsort["b"][3].Add(Fibz[24].ashilo);
   mainsort["b"][3].Add(Fibz[24].asmid);
   mainsort["b"][3].Add(Fibz[24].wmid);
   
   mainsort["b"][4].Add(Fibz[23].dailyscore);
   mainsort["b"][4].Add(Fibz[23].ashilo);
   mainsort["b"][4].Add(Fibz[23].asmid);
   mainsort["b"][4].Add(Fibz[23].wmid);
   
   mainsort["b"][5].Add(Fibz[22].dailyscore);
   mainsort["b"][5].Add(Fibz[22].ashilo);
   mainsort["b"][5].Add(Fibz[22].asmid);
   mainsort["b"][5].Add(Fibz[22].wmid);
   
   mainsort["b"][6].Add(Fibz[21].dailyscore);
   mainsort["b"][6].Add(Fibz[21].ashilo);
   mainsort["b"][6].Add(Fibz[21].asmid);
   mainsort["b"][6].Add(Fibz[21].wmid);
   
   mainsort["b"][7].Add(Fibz[20].dailyscore);
   mainsort["b"][7].Add(Fibz[20].ashilo);
   mainsort["b"][7].Add(Fibz[20].asmid);
   mainsort["b"][7].Add(Fibz[20].wmid);
   
   mainsort["b"][8].Add(Fibz[19].dailyscore);
   mainsort["b"][8].Add(Fibz[19].ashilo);
   mainsort["b"][8].Add(Fibz[19].asmid);
   mainsort["b"][8].Add(Fibz[19].wmid);
   
   mainsort["b"][9].Add(Fibz[18].dailyscore);
   mainsort["b"][9].Add(Fibz[18].ashilo);
   mainsort["b"][9].Add(Fibz[18].asmid);
   mainsort["b"][9].Add(Fibz[18].wmid);
   
   mainsort["b"][10].Add(Fibz[17].dailyscore);
   mainsort["b"][10].Add(Fibz[17].ashilo);
   mainsort["b"][10].Add(Fibz[17].asmid);
   mainsort["b"][10].Add(Fibz[17].wmid);
   
   mainsort["b"][11].Add(Fibz[16].dailyscore);
   mainsort["b"][11].Add(Fibz[16].ashilo);
   mainsort["b"][11].Add(Fibz[16].asmid);
   mainsort["b"][11].Add(Fibz[16].wmid);
   
   mainsort["b"][12].Add(Fibz[15].dailyscore);
   mainsort["b"][12].Add(Fibz[15].ashilo);
   mainsort["b"][12].Add(Fibz[15].asmid);
   mainsort["b"][12].Add(Fibz[15].wmid);
   
   mainsort["b"][13].Add(Fibz[14].dailyscore);
   mainsort["b"][13].Add(Fibz[14].ashilo);
   mainsort["b"][13].Add(Fibz[14].asmid);
   mainsort["b"][13].Add(Fibz[14].wmid);
   
   mainsort["b"][14].Add(Fibz[13].dailyscore);
   mainsort["b"][14].Add(Fibz[13].ashilo);
   mainsort["b"][14].Add(Fibz[13].asmid);
   mainsort["b"][14].Add(Fibz[13].wmid);
   
   mainsort["b"][15].Add(Fibz[12].dailyscore);
   mainsort["b"][15].Add(Fibz[12].ashilo);
   mainsort["b"][15].Add(Fibz[12].asmid);
   mainsort["b"][15].Add(Fibz[12].wmid);
   
   mainsort["b"][16].Add(Fibz[11].dailyscore);
   mainsort["b"][16].Add(Fibz[11].ashilo);
   mainsort["b"][16].Add(Fibz[11].asmid);
   mainsort["b"][16].Add(Fibz[11].wmid);
   
   mainsort["b"][17].Add(Fibz[10].dailyscore);
   mainsort["b"][17].Add(Fibz[10].ashilo);
   mainsort["b"][17].Add(Fibz[10].asmid);
   mainsort["b"][17].Add(Fibz[10].wmid);
   
   mainsort["b"][18].Add(Fibz[9].dailyscore);
   mainsort["b"][18].Add(Fibz[9].ashilo);
   mainsort["b"][18].Add(Fibz[9].asmid);
   mainsort["b"][18].Add(Fibz[9].wmid);
   
   mainsort["b"][19].Add(Fibz[8].dailyscore);
   mainsort["b"][19].Add(Fibz[8].ashilo);
   mainsort["b"][19].Add(Fibz[8].asmid);
   mainsort["b"][19].Add(Fibz[8].wmid);
   
   mainsort["b"][20].Add(Fibz[7].dailyscore);
   mainsort["b"][20].Add(Fibz[7].ashilo);
   mainsort["b"][20].Add(Fibz[7].asmid);
   mainsort["b"][20].Add(Fibz[7].wmid);
   
   mainsort["b"][21].Add(Fibz[6].dailyscore);
   mainsort["b"][21].Add(Fibz[6].ashilo);
   mainsort["b"][21].Add(Fibz[6].asmid);
   mainsort["b"][21].Add(Fibz[6].wmid);
   
   mainsort["b"][22].Add(Fibz[5].dailyscore);
   mainsort["b"][22].Add(Fibz[5].ashilo);
   mainsort["b"][22].Add(Fibz[5].asmid);
   mainsort["b"][22].Add(Fibz[5].wmid);
   
   mainsort["b"][23].Add(Fibz[4].dailyscore);
   mainsort["b"][23].Add(Fibz[4].ashilo);
   mainsort["b"][23].Add(Fibz[4].asmid);
   mainsort["b"][23].Add(Fibz[4].wmid);
   
   mainsort["b"][24].Add(Fibz[3].dailyscore);
   mainsort["b"][24].Add(Fibz[3].ashilo);
   mainsort["b"][24].Add(Fibz[3].asmid);
   mainsort["b"][24].Add(Fibz[3].wmid);
   
   mainsort["b"][25].Add(Fibz[2].dailyscore);
   mainsort["b"][25].Add(Fibz[2].ashilo);
   mainsort["b"][25].Add(Fibz[2].asmid);
   mainsort["b"][25].Add(Fibz[2].wmid);
   
   mainsort["b"][26].Add(Fibz[1].dailyscore);
   mainsort["b"][26].Add(Fibz[1].ashilo);
   mainsort["b"][26].Add(Fibz[1].asmid);
   mainsort["b"][26].Add(Fibz[1].wmid);
   
   mainsort["b"][27].Add(Fibz[0].dailyscore);
   mainsort["b"][27].Add(Fibz[0].ashilo);
   mainsort["b"][27].Add(Fibz[0].asmid);
   mainsort["b"][27].Add(Fibz[0].wmid);

   mainsort["b"][0].Add(Fibz[27].d1ch);
   mainsort["b"][0].Add(Fibz[27].d2ch);
   mainsort["b"][0].Add(Fibz[27].d3ch);
   mainsort["b"][0].Add(Fibz[27].d4ch);
   
   mainsort["b"][1].Add(Fibz[26].d1ch);
   mainsort["b"][1].Add(Fibz[26].d2ch);
   mainsort["b"][1].Add(Fibz[26].d3ch);
   mainsort["b"][1].Add(Fibz[26].d4ch);
   
   mainsort["b"][2].Add(Fibz[25].d1ch);
   mainsort["b"][2].Add(Fibz[25].d2ch);
   mainsort["b"][2].Add(Fibz[25].d3ch);
   mainsort["b"][2].Add(Fibz[25].d4ch);
   
   mainsort["b"][3].Add(Fibz[24].d1ch);
   mainsort["b"][3].Add(Fibz[24].d2ch);
   mainsort["b"][3].Add(Fibz[24].d3ch);
   mainsort["b"][3].Add(Fibz[24].d4ch);
   
   mainsort["b"][4].Add(Fibz[23].d1ch);
   mainsort["b"][4].Add(Fibz[23].d2ch);
   mainsort["b"][4].Add(Fibz[23].d3ch);
   mainsort["b"][4].Add(Fibz[23].d4ch);
   
   mainsort["b"][5].Add(Fibz[22].d1ch);
   mainsort["b"][5].Add(Fibz[22].d2ch);
   mainsort["b"][5].Add(Fibz[22].d3ch);
   mainsort["b"][5].Add(Fibz[22].d4ch);
   
   mainsort["b"][6].Add(Fibz[21].d1ch);
   mainsort["b"][6].Add(Fibz[21].d2ch);
   mainsort["b"][6].Add(Fibz[21].d3ch);
   mainsort["b"][6].Add(Fibz[21].d4ch);
   
   mainsort["b"][7].Add(Fibz[20].d1ch);
   mainsort["b"][7].Add(Fibz[20].d2ch);
   mainsort["b"][7].Add(Fibz[20].d3ch);
   mainsort["b"][7].Add(Fibz[20].d4ch);
   
   mainsort["b"][8].Add(Fibz[19].d1ch);
   mainsort["b"][8].Add(Fibz[19].d2ch);
   mainsort["b"][8].Add(Fibz[19].d3ch);
   mainsort["b"][8].Add(Fibz[19].d4ch);
   
   mainsort["b"][9].Add(Fibz[18].d1ch);
   mainsort["b"][9].Add(Fibz[18].d2ch);
   mainsort["b"][9].Add(Fibz[18].d3ch);
   mainsort["b"][9].Add(Fibz[18].d4ch);
   
   mainsort["b"][10].Add(Fibz[17].d1ch);
   mainsort["b"][10].Add(Fibz[17].d2ch);
   mainsort["b"][10].Add(Fibz[17].d3ch);
   mainsort["b"][10].Add(Fibz[17].d4ch);
   
   mainsort["b"][11].Add(Fibz[16].d1ch);
   mainsort["b"][11].Add(Fibz[16].d2ch);
   mainsort["b"][11].Add(Fibz[16].d3ch);
   mainsort["b"][11].Add(Fibz[16].d4ch);
   
   mainsort["b"][12].Add(Fibz[15].d1ch);
   mainsort["b"][12].Add(Fibz[15].d2ch);
   mainsort["b"][12].Add(Fibz[15].d3ch);
   mainsort["b"][12].Add(Fibz[15].d4ch);
   
   mainsort["b"][13].Add(Fibz[14].d1ch);
   mainsort["b"][13].Add(Fibz[14].d2ch);
   mainsort["b"][13].Add(Fibz[14].d3ch);
   mainsort["b"][13].Add(Fibz[14].d4ch);
   
   mainsort["b"][14].Add(Fibz[13].d1ch);
   mainsort["b"][14].Add(Fibz[13].d2ch);
   mainsort["b"][14].Add(Fibz[13].d3ch);
   mainsort["b"][14].Add(Fibz[13].d4ch);
   
   mainsort["b"][15].Add(Fibz[12].d1ch);
   mainsort["b"][15].Add(Fibz[12].d2ch);
   mainsort["b"][15].Add(Fibz[12].d3ch);
   mainsort["b"][15].Add(Fibz[12].d4ch);
   
   mainsort["b"][16].Add(Fibz[11].d1ch);
   mainsort["b"][16].Add(Fibz[11].d2ch);
   mainsort["b"][16].Add(Fibz[11].d3ch);
   mainsort["b"][16].Add(Fibz[11].d4ch);
   
   mainsort["b"][17].Add(Fibz[10].d1ch);
   mainsort["b"][17].Add(Fibz[10].d2ch);
   mainsort["b"][17].Add(Fibz[10].d3ch);
   mainsort["b"][17].Add(Fibz[10].d4ch);
   
   mainsort["b"][18].Add(Fibz[9].d1ch);
   mainsort["b"][18].Add(Fibz[9].d2ch);
   mainsort["b"][18].Add(Fibz[9].d3ch);
   mainsort["b"][18].Add(Fibz[9].d4ch);
   
   mainsort["b"][19].Add(Fibz[8].d1ch);
   mainsort["b"][19].Add(Fibz[8].d2ch);
   mainsort["b"][19].Add(Fibz[8].d3ch);
   mainsort["b"][19].Add(Fibz[8].d4ch);
   
   mainsort["b"][20].Add(Fibz[7].d1ch);
   mainsort["b"][20].Add(Fibz[7].d2ch);
   mainsort["b"][20].Add(Fibz[7].d3ch);
   mainsort["b"][20].Add(Fibz[7].d4ch);
   
   mainsort["b"][21].Add(Fibz[6].d1ch);
   mainsort["b"][21].Add(Fibz[6].d2ch);
   mainsort["b"][21].Add(Fibz[6].d3ch);
   mainsort["b"][21].Add(Fibz[6].d4ch);
   
   mainsort["b"][22].Add(Fibz[5].d1ch);
   mainsort["b"][22].Add(Fibz[5].d2ch);
   mainsort["b"][22].Add(Fibz[5].d3ch);
   mainsort["b"][22].Add(Fibz[5].d4ch);
   
   mainsort["b"][23].Add(Fibz[4].d1ch);
   mainsort["b"][23].Add(Fibz[4].d2ch);
   mainsort["b"][23].Add(Fibz[4].d3ch);
   mainsort["b"][23].Add(Fibz[4].d4ch);
   
   mainsort["b"][24].Add(Fibz[3].d1ch);
   mainsort["b"][24].Add(Fibz[3].d2ch);
   mainsort["b"][24].Add(Fibz[3].d3ch);
   mainsort["b"][24].Add(Fibz[3].d4ch);
   
   mainsort["b"][25].Add(Fibz[2].d1ch);
   mainsort["b"][25].Add(Fibz[2].d2ch);
   mainsort["b"][25].Add(Fibz[2].d3ch);
   mainsort["b"][25].Add(Fibz[2].d4ch);
   
   mainsort["b"][26].Add(Fibz[1].d1ch);
   mainsort["b"][26].Add(Fibz[1].d2ch);
   mainsort["b"][26].Add(Fibz[1].d3ch);
   mainsort["b"][26].Add(Fibz[1].d4ch);
   
   mainsort["b"][27].Add(Fibz[0].d1ch);
   mainsort["b"][27].Add(Fibz[0].d2ch);
   mainsort["b"][27].Add(Fibz[0].d3ch);
   mainsort["b"][27].Add(Fibz[0].d4ch);
   
   ArrayFree(Fibz);
   
   Data rvola[];
   
   {
      AddToData(rvola, dwm[0].daily, cdr(eu_p, 0) / adr(eu_p, 5));
      AddToData(rvola, dwm[1].daily, cdr(eg_p, 0) / adr(eg_p, 5));
      AddToData(rvola, dwm[2].daily, cdr(ea_p, 0) / adr(ea_p, 5));
      AddToData(rvola, dwm[3].daily, cdr(en_p, 0) / adr(en_p, 5));
      AddToData(rvola, dwm[4].daily, cdr(ec_p, 0) / adr(ec_p, 5));
      AddToData(rvola, dwm[5].daily, cdr(ef_p, 0) / adr(ef_p, 5));
      AddToData(rvola, dwm[6].daily, cdr(ej_p, 0) / adr(ej_p, 5));
      AddToData(rvola, dwm[7].daily, cdr(gu_p, 0) / adr(gu_p, 5));
      AddToData(rvola, dwm[8].daily, cdr(ga_p, 0) / adr(ga_p, 5));
      AddToData(rvola, dwm[9].daily, cdr(gn_p, 0) / adr(gn_p, 5));
      AddToData(rvola, dwm[10].daily, cdr(gc_p, 0) / adr(gc_p, 5));
      AddToData(rvola, dwm[11].daily, cdr(gf_p, 0) / adr(gf_p, 5));
      AddToData(rvola, dwm[12].daily, cdr(gj_p, 0) / adr(gj_p, 5));
      AddToData(rvola, dwm[13].daily, cdr(au_p, 0) / adr(au_p, 5));
      AddToData(rvola, dwm[14].daily, cdr(an_p, 0) / adr(an_p, 5));
      AddToData(rvola, dwm[15].daily, cdr(ac_p, 0) / adr(ac_p, 5));
      AddToData(rvola, dwm[16].daily, cdr(af_p, 0) / adr(af_p, 5));
      AddToData(rvola, dwm[17].daily, cdr(aj_p, 0) / adr(aj_p, 5));
      AddToData(rvola, dwm[18].daily, cdr(nu_p, 0) / adr(nu_p, 5));
      AddToData(rvola, dwm[19].daily, cdr(nc_p, 0) / adr(nc_p, 5));
      AddToData(rvola, dwm[20].daily, cdr(nf_p, 0) / adr(nf_p, 5));
      AddToData(rvola, dwm[21].daily, cdr(nj_p, 0) / adr(nj_p, 5));
      AddToData(rvola, dwm[22].daily, cdr(uc_p, 0) / adr(uc_p, 5));
      AddToData(rvola, dwm[23].daily, cdr(cf_p, 0) / adr(cf_p, 5));
      AddToData(rvola, dwm[24].daily, cdr(cj_p, 0) / adr(cj_p, 5));
      AddToData(rvola, dwm[25].daily, cdr(uf_p, 0) / adr(uf_p, 5));
      AddToData(rvola, dwm[26].daily, cdr(fj_p, 0) / adr(fj_p, 5));
      AddToData(rvola, dwm[27].daily, cdr(uj_p, 0) / adr(uj_p, 5));
   }
      
   ArraySortStruct(rvola, daily);
      
   mainsort["c"][0].Add(rvola[27].rsid, 2);
   mainsort["c"][1].Add(rvola[26].rsid, 2);
   mainsort["c"][2].Add(rvola[25].rsid, 2);
   mainsort["c"][3].Add(rvola[24].rsid, 2);
   mainsort["c"][4].Add(rvola[23].rsid, 2);
   mainsort["c"][5].Add(rvola[22].rsid, 2);
   mainsort["c"][6].Add(rvola[21].rsid, 2);
   mainsort["c"][7].Add(rvola[20].rsid, 2);
   mainsort["c"][8].Add(rvola[19].rsid, 2);
   mainsort["c"][9].Add(rvola[18].rsid, 2);
   mainsort["c"][10].Add(rvola[17].rsid, 2);
   mainsort["c"][11].Add(rvola[16].rsid, 2);
   mainsort["c"][12].Add(rvola[15].rsid, 2);
   mainsort["c"][13].Add(rvola[14].rsid, 2);
   mainsort["c"][14].Add(rvola[13].rsid, 2);
   mainsort["c"][15].Add(rvola[12].rsid, 2);
   mainsort["c"][16].Add(rvola[11].rsid, 2);
   mainsort["c"][17].Add(rvola[10].rsid, 2);
   mainsort["c"][18].Add(rvola[9].rsid, 2);
   mainsort["c"][19].Add(rvola[8].rsid, 2);
   mainsort["c"][20].Add(rvola[7].rsid, 2);
   mainsort["c"][21].Add(rvola[6].rsid, 2);
   mainsort["c"][22].Add(rvola[5].rsid, 2);
   mainsort["c"][23].Add(rvola[4].rsid, 2);
   mainsort["c"][24].Add(rvola[3].rsid, 2);
   mainsort["c"][25].Add(rvola[2].rsid, 2);
   mainsort["c"][26].Add(rvola[1].rsid, 2);
   mainsort["c"][27].Add(rvola[0].rsid, 2);
         
   ArrayFree(rvola);
}

void signal()
{
   //FIBPLAY
   
   string tokillb[];
   string tokills[];
   
   double pigal = 0;
   int count11 = 0;
   for (int x = 27; x >= 0 ; x--)
   {
      if (mainsort["a"][x][1].ToDbl() >= 200 && (mainsort["b"][x][1].ToInt() == 1 || mainsort["b"][x][1].ToInt() == 3) && mainsort["b"][x][3].ToInt() != 2 && mainsort["c"][x][0].ToDbl() <= 0.75)
         { int CurrentSize = ArraySize(tokillb); ArrayResize(tokillb, CurrentSize + 1); tokillb[CurrentSize] = mainsort["a"][x][0].ToStr(); count11++; pigal += MathAbs(mainsort["a"][x][1].ToDbl()); }
      if (mainsort["a"][x][1].ToDbl() <= -200 && (mainsort["b"][x][1].ToInt() == 1 || mainsort["b"][x][1].ToInt() == 3) && mainsort["b"][x][3].ToInt() != 2 && mainsort["c"][x][0].ToDbl() <= 0.75)
         { int CurrentSize = ArraySize(tokills); ArrayResize(tokills, CurrentSize + 1); tokills[CurrentSize] = mainsort["a"][x][0].ToStr(); count11++; pigal += MathAbs(mainsort["a"][x][1].ToDbl()); }
   }
   
   //Print(ArraySize(tokillb) + " "  + ArraySize(tokills));
   
   if (ArraySize(tokillb) > 0)
   for (int x = ArraySize(tokillb) - 1; x >= 0; x--)
   {
      //Print(tokillb[x]);
   }
   if (ArraySize(tokills) > 0)
   for (int x = ArraySize(tokills) - 1; x >= 0; x--)
   {
      //Print(tokills[x]);
   }
   
   double EQ = AccountEquity();
   double lots = NormalizeDouble(EQ / 166666, 2);
   double totwin = count11 * NormalizeDouble(lots, 2) * 100;
   Print(NormalizeDouble(lots, 2) + " " + totwin);
   
   Print(count11 + " " + pigal);
   Print(TimeHour(TimeCurrent()));
   
   bool trade = false;
   if (pigal >= 2000 && count11 >= 6 && TimeHour(TimeCurrent()) >= 4 && TimeHour(TimeCurrent()) <= 20)
   {
      trade = true;
   }
   
   static datetime blue = 0;
   int h = TimeHour(TimeCurrent());
   bool bluetrade = false;
   if (blue < h && blue < iTime(_Symbol, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 240) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 360)) && trade)
   {
      blue = iTime(_Symbol, PERIOD_H1, 0);
      bluetrade = true;
      Print("blue");
   }
   if (bluetrade)
   {
      if (ArraySize(tokillb) > 0) trade(tokillb, lots, totwin, true);
      if (ArraySize(tokills) > 0) trade(tokills, lots, totwin, false);
      bluetrade = false;
   }
      if (ArraySize(tokillb) > 0) trade(tokillb, lots, totwin, true);
      if (ArraySize(tokills) > 0) trade(tokills, lots, totwin, false);
}

void trade(string &trader[], double lotsize, double tpsl, bool buysell)
{
   Print(ArraySize(trader) + " " + lotsize + " " + tpsl + " " + buysell);
   int counta = 0;
   int result = 0;
   if (buysell == true)
   for (int x = ArraySize(trader) - 1; x >= 0; x--)
   {
      Print(trader[x] + " " + buysell);
      counta++;
   }
   if (buysell == false)
   for (int x = ArraySize(trader) - 1; x >= 0; x--)
   {
      Print(trader[x] + " " + buysell);
      counta++;
   }
   
   watchdog_init(counta, lotsize, tpsl);
}


void watchdog_init(int counta, double lotsize, double tpsl)
{
   active = true;
   rstr = tpsl;
   Print("watchdog_init");
   watchdog(tpsl);
}

void watchdog(double tpsl)
{
   Print("watchdog alive");
   if (TimeMinute(TimeCurrent()) == 59) Print("CloseAll");
}

//+TOTAL ACTIVE CALC-------------------------------------------------+
double totalactive()
{
	double total = 0;
	for (int i = OrdersTotal() - 1; i >= 0; i--)
		if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderType() < 2)
		{
			total += OrderProfit() + OrderSwap() + OrderCommission();
		}
	return(total);
}
//+------------------------------------------------------------------+


double cdr(string pair, const int X){
   
   double adri = MathAbs(iClose(pair, PERIOD_D1, 0) - iClose(pair, PERIOD_D1, 1));
   
   return(adri / MarketInfo(pair, MODE_POINT) / 10);
}
double adr(string pair, const int X){
   
   double HD[], LD[];
   ArrayResize(HD, X + 1);
   ArrayResize(LD, X + 1);
   ArraySetAsSeries(HD, true);
   ArraySetAsSeries(LD, true);
   CopyHigh(pair, PERIOD_D1, 1, X + 1, HD);
   CopyLow(pair, PERIOD_D1, 1, X + 1, LD);
   double adra = 0;
   
   for (int x = X - 1; x >= 0; x--){
      adra += (HD[x] - LD[x]);
   }
   
   double adri = 0;
   adri = adra / X;
   
   ArrayFree(HD);
   ArrayFree(LD);
   return(adri / MarketInfo(pair, MODE_POINT) / 10);
}

//+TICK VALUE CALC---------------------------------------------------+
double dblTickValue( string strSymbol )
{
   return( MarketInfo( strSymbol, MODE_TICKVALUE ) );
}
double dblPipValue( string strSymbol )
{
   double dblCalcPipValue = dblTickValue( strSymbol );
   if (MarketInfo(strSymbol, MODE_DIGITS) == 3) dblCalcPipValue *= 10;
   if (MarketInfo(strSymbol, MODE_DIGITS) == 5) dblCalcPipValue *= 10;
   return( dblCalcPipValue );
}
//+------------------------------------------------------------------+