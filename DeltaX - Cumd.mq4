#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property strict

#property indicator_separate_window
#property indicator_buffers 6
#property  indicator_width1  2
#property  indicator_width2  2
#property  indicator_width3  1
#property  indicator_width4  2
#property  indicator_width5  2
#property  indicator_width6  2

#property  indicator_style1  STYLE_SOLID
#property  indicator_style2  STYLE_SOLID
#property  indicator_style3  STYLE_SOLID
#property  indicator_style4  STYLE_SOLID
#property  indicator_style5  STYLE_SOLID
#property  indicator_style6  STYLE_SOLID

/*
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlack
#property indicator_color4 clrWhite
#property indicator_color5 clrRed
#property indicator_color6 clrBlack
*/

#define Name WindowExpertName()

input int checkdays = 15; // Calc for X days
input int sma_period = 24; // SMA Period vs daily cumulative
input bool showlong = false; // Show cumulative delta since X days
input color cumcolor = clrBlack; // Cumulative Delta line color
input color macolor = clrRed; // MA color
input color allcolor = clrWhite; // Cumulative Delta for X days color

double voldeltan[], voldeltap[];
double cumd[], cumdall[];
double cumdma[];

//+INIT SEQUENCE-----------------------------------------------------+
int OnInit()
{
	IndicatorDigits(0);
	IndicatorBuffers(6);

	SetLevelValue(0, 0);
	SetLevelStyle(2, 0, clrWhite);
	
	SetIndexBuffer(0, voldeltap);
	SetIndexStyle(0, DRAW_NONE);
	SetIndexEmptyValue(0, 0);
	SetIndexLabel(0, "+DELTA");
	SetIndexBuffer(1, voldeltan);
	SetIndexStyle(1, DRAW_NONE);
	SetIndexEmptyValue(1, 0);
	SetIndexLabel(1, "-DELTA");
	SetIndexBuffer(3, cumdall);
	if (showlong)SetIndexStyle(3, DRAW_LINE, EMPTY, EMPTY, allcolor);
	else SetIndexStyle(3, DRAW_NONE);
	SetIndexEmptyValue(3, 0);
	if (showlong)SetIndexLabel(3, "CUM D ALL");
	else SetIndexLabel(3, "");
	SetIndexBuffer(4, cumdma);
	SetIndexStyle(4, DRAW_LINE, EMPTY, EMPTY, macolor);
	SetIndexEmptyValue(4, 0);
	SetIndexLabel(4, "CUM D TODAY MA");
	SetIndexBuffer(5, cumd);
	SetIndexStyle(5, DRAW_LINE, EMPTY, EMPTY, cumcolor);
	SetIndexEmptyValue(5, 0);
	SetIndexLabel(5, "CUM D TODAY");

	if (showlong) IndicatorShortName("DeltaX / C All / C Td MA / C Td");
	else IndicatorShortName("DeltaX / C Td MA / C Td");

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+MAIN RUN----------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("Cumulative DeltaX expired, contact sakisf for an updated/new version.");
		YesStop = true;
	}

	if (YesStop != true) {
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1m_check)
		{
			ArrayInitialize(voldeltan, 0);
			ArrayInitialize(voldeltap, 0);
			PastPeriodDelta();
			CumDeltaP();
			//CumAllDayP();
			new_1m_check = false;
		}
		CurPeriodDelta();
		CumDeltaC();
		//CumAllDayC();
		//CumDeltaMA();
	}//YesStop end
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT SEQUENCE---------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+PAST BAR DELTA----------------------------------------------------+
void PastPeriodDelta() {
	int perioda = Period();
	int divisor = PERIOD_M1;
	if (ChartPeriod(0) >= 240 && ChartPeriod(0) < 1440) divisor = PERIOD_M5;
	else if (ChartPeriod(0) >= 1440 && ChartPeriod(0) < 10080) divisor = PERIOD_M15;
	else if (ChartPeriod(0) >= 10080 && ChartPeriod(0) < 43200) divisor = PERIOD_M30;
	else if (ChartPeriod(0) == 43200) divisor = PERIOD_H1;

	
	datetime yst = iTime(_Symbol, PERIOD_D1, checkdays);
	int firstmin = iBarShift(_Symbol, perioda, yst);
	datetime sta1 = iTime(_Symbol, perioda, firstmin);
	int shif1 = iBarShift(_Symbol, divisor, sta1, false);
	int minmin = perioda / divisor;
	int volm = 0, volp = 0;

	double closes[], opens[];
	long volumes[];
	datetime times[];
	ArrayResize(closes, firstmin + shif1 + 2);
	ArrayResize(opens, firstmin + shif1 + 2);
	ArrayResize(volumes, firstmin + shif1 + 2);
	ArrayResize(times, firstmin + shif1 + 2);	
	if (!CopyClose(_Symbol, divisor, 0, firstmin + shif1, closes)) { Print("Error copying closes"); return; }	
	if (!CopyOpen(_Symbol, divisor, 0, firstmin + shif1, opens)) { Print("Error copying opens"); return; }	
	if (!CopyTickVolume(_Symbol, divisor, 0, shif1 + minmin, volumes)) { Print("Error copying volumes"); return; }	
	if (!CopyTime(_Symbol, divisor, 0, firstmin + shif1, times)) { Print("Error copying times"); return; }	
	ArraySetAsSeries(closes, true);
	ArraySetAsSeries(opens, true);
	ArraySetAsSeries(volumes, true);
	ArraySetAsSeries(times, true);

	for (int i = 1; i <= firstmin; i++) {
		datetime sta = iTime(_Symbol, perioda, i);
		int shif = iBarShift(_Symbol, divisor, sta, false);

		for (int j = 0; j <= minmin - 1; j++) {
			if (closes[shif - j] < opens[shif - j]) volm += (int)volumes[shif - j];
			else if (closes[shif - j] > opens[shif - j]) volp += (int)volumes[shif - j];
			if (times[shif - j] >= iTime(_Symbol, perioda, i - 1)) break;
		}

		if (volp > volm)
			voldeltap[i] = volp - volm;
		else if (volm >= volp)
			voldeltan[i] = -(volm - volp);
		volm = 0; volp = 0;
	}
}
//+------------------------------------------------------------------+

//+CURRENT BAR DELTA-------------------------------------------------+
void CurPeriodDelta() {
	int perioda = Period();
	int divisor = PERIOD_M1;
	if (ChartPeriod(0) >= 240 && ChartPeriod(0) < 1440) divisor = PERIOD_M5;
	else if (ChartPeriod(0) >= 1440 && ChartPeriod(0) < 10080) divisor = PERIOD_M15;
	else if (ChartPeriod(0) >= 10080 && ChartPeriod(0) < 43200) divisor = PERIOD_M30;
	else if (ChartPeriod(0) == 43200) divisor = PERIOD_H1;

	int volm = 0, volp = 0;
	datetime sta = iTime(_Symbol, perioda, 0);
	int shif = iBarShift(_Symbol, divisor, sta, false);

	for (int j = shif; j >= 0; j--) {
		if (iClose(_Symbol, divisor, j) < iOpen(_Symbol, divisor, j)) volm += (int)iVolume(_Symbol, divisor, j);
		else if (iClose(_Symbol, divisor, j) > iOpen(_Symbol, divisor, j)) volp += (int)iVolume(_Symbol, divisor, j);
	}

	if (volp > volm)
		voldeltap[0] = volp - volm;
	else if (volm >= volp)
		voldeltan[0] = -(volm - volp);
}

//+CUMULATIVE DELTA CURRENT BAR FOR DAILY----------------------------+
void CumDeltaC() {
	datetime pers0 = iTime(_Symbol, PERIOD_D1, 0);
	int strat0 = iBarShift(_Symbol, PERIOD_CURRENT, pers0, false);
	double blick = 0;
	for (int c = strat0; c >= 0; c--) {
		blick += voldeltan[c] + voldeltap[c];
		cumd[c] = blick;
	}
}
//+------------------------------------------------------------------+

//+CUMULATIVE DELTA PAST BARS FOR DAILY------------------------------+
void CumDeltaP() {
	datetime pers[];
	int strat[];
	ArrayResize(pers, checkdays + 1);
	ArrayResize(strat, checkdays + 1);

	for (int x = checkdays; x >= 0; x--) {
		pers[x] = iTime(_Symbol, PERIOD_D1, x);
	}
	for (int b = checkdays; b >= 0; b--) {
		strat[b] = iBarShift(_Symbol, PERIOD_CURRENT, pers[b], false);
	}

	double blick = 0;
	for (int p = checkdays; p >= 1; p--) {
		for (int d = strat[p]; d > strat[p - 1]; d--) {
			blick += voldeltan[d] + voldeltap[d];
			cumd[d] = blick;
		}
		blick = 0;
	}
}
//+------------------------------------------------------------------+

//+CUMULATIVE DELTA PAST BARS FOR ALL DAYS---------------------------+
void CumAllDayP() {
	datetime start1 = iTime(_Symbol, PERIOD_D1, checkdays);
	int parse1 = iBarShift(_Symbol, PERIOD_CURRENT, start1, false);
	double biatch = 0;
	for (int i = parse1; i >= 1; i--) {
		biatch += voldeltan[i] + voldeltap[i];
		cumdall[i] = biatch;
	}
}
//+------------------------------------------------------------------+

//+CUMULATIVE DELTA CURRENT BAR FOR ALL DAYS-------------------------+
void CumAllDayC() {
	cumdall[0] = cumdall[1] + voldeltan[0] + voldeltap[0];
}
//+------------------------------------------------------------------+

//+SMA24 OF DAILY CUMULATIVE DELTA-----------------------------------+
void CumDeltaMA() {
	datetime start1 = iTime(_Symbol, PERIOD_D1, checkdays);
	int parse1 = iBarShift(_Symbol, PERIOD_CURRENT, start1, false);
	double biatch = 0;
	for (int i = parse1; i >= 0; i--) {
		cumdma[i] = iMAOnArray(cumd, 0, sma_period, 0, MODE_SMA, i);
	}
}
//+------------------------------------------------------------------+

//future use
/*
//+CREATE T-LINES----------------------------------------------------+
void objbase(string oname, datetime t1, datetime t2, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_DASH);
	ObjectSet(oname, OBJPROP_WIDTH, 0);
	ObjectSet(oname, OBJPROP_BACK, false);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_TIME1, t1);
	ObjectSet(oname, OBJPROP_TIME2, t2);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_PRICE2, pr1);
	ObjectSet(oname, OBJPROP_RAY, false);
}
//+------------------------------------------------------------------+
*/