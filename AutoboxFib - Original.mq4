//+------------------------------------------------------------------+
//|                                                   AutoboxFib.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#define Name WindowExpertName()

input color TriggerColor = C'0,40,0'; //Trigger color
double midline;
input int days = 20;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
	datetime expiry = D'2025.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("AutoBoxFib expired on " + TimeToStr(expiry, TIME_DATE));
		YesStop = true;
	}
	
	if (YesStop != true)
	{
      static datetime checkt1 = 0;
      bool check11 = false;
      if (checkt1 < iTime(_Symbol, PERIOD_H1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_H1, 0) + 240) && TimeCurrent() <= (iTime(NULL, PERIOD_H1, 0) + 290)))
      {
         checkt1 = iTime(_Symbol, PERIOD_H1, 0);
         check11 = true;
      }
      if (check11)
      {
         ObjectsDeleteAll(0, Name);
         check11 = false;
      }
      
      static datetime checkt = 0;
      bool check1 = false;
      if (checkt < iTime(_Symbol, PERIOD_M5, 0))
      {
         checkt = iTime(_Symbol, PERIOD_M5, 0);
         check1 = true;
      }
      if (check1)
      {
         Rec();
         double sting[];
         ArrayResize(sting, 20);
         double stinga[];
         ArrayResize(stinga, 20);
         double stingy[];
         ArrayResize(stingy, 20);
         double stingz[];
         ArrayResize(stingz, 20);
         int count = 0, counta = 0;
         string name;
         for (int x = ObjectsTotal(); x >= 0; x--)
         {
            name = ObjectName(x);
            if (ObjectType(name) == OBJ_RECTANGLE && ObjectGetInteger(0, name, OBJPROP_COLOR) == TriggerColor)
            { 
               count++;
               sting[count] = ObjectGet(name, OBJPROP_PRICE1);
               stingy[count] = ObjectGet(name, OBJPROP_PRICE2);
            }
            if (ObjectType(name) == OBJ_FIBO)
            {
               counta++;
               stinga[counta] = ObjectGet(name, OBJPROP_PRICE1);
               stingz[counta] = ObjectGet(name, OBJPROP_PRICE2);
            }
         }
         
         bool check = false;
         for (int x = ArraySize(sting) - 1; x >= 0; x--)
         {
            if (sting[x] != stinga[x] || stingy[x] != stingz[x])
            {
               check = true;
               break;
            }
         }
         
         if (count != counta || check)
         {      
            Objectfind();
            check = false;
         }
      check1 = false;
      }
   }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	EventKillTimer();
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void Rec(){
   string obname;
   int hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10;
   double high = 0, low = 0;
   midline = 0;
   
   double high0[21], low0[21];
   
   double higha[21], lowa[21];
   
   double u2138a[21], u2138b[21];
   double d2138a[21], d2138b[21];
      
   for (int x = days; x >= 1; x--)
   {
      high0[x] = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + x * 24 + 1));
      low0[x] = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + x * 24 + 1));
      
      double dist = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + x * 24 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + x * 24 + 1));
      
      u2138a[x] = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + x * 24 + 1)) - 0.236 * dist;
      u2138b[x] = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + x * 24 + 1)) - 0.382 * dist;
      d2138a[x] = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + x * 24 + 1)) + 0.236 * dist;
      d2138b[x] = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + x * 24 + 1)) + 0.382 * dist;
      
      obname = Name + "xbox0" + IntegerToString(x);
      RecMake(obname, high0[x], low0[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24), false), 0, C'40,40,40', "xbox0" + IntegerToString(x));
      obname = Name + "switch0" + IntegerToString(x);
      RecMake(obname, u2138a[x], u2138b[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 38), false), 0, C'79,79,0', "switch0" + IntegerToString(x));
      if (x > 2) ObjectSetInteger(0, obname, OBJPROP_BACK, false);
      obname = Name + "switch1" + IntegerToString(x);
      RecMake(obname, d2138a[x], d2138b[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 38), false), 0, C'100,0,17', "switch1" + IntegerToString(x));
      if (x > 2) ObjectSetInteger(0, obname, OBJPROP_BACK, false);
      if (x < 2)
      {
      obname = Name + "switch0" + IntegerToString(x);
      RecMake(obname, u2138a[x], u2138b[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 24), false), 0, C'79,79,0', "switch0" + IntegerToString(x));
      obname = Name + "switch1" + IntegerToString(x);
      RecMake(obname, d2138a[x], d2138b[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, x), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + x * 24 - 24), false), 0, C'100,0,17', "switch1" + IntegerToString(x));
      }
   }
      
   if (hr >= 0)
   {
      high = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
      low = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
   }
   midline = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1))) / 2;
   
   obname = Name + "tbox";
   RecMake(obname, high, low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, TriggerColor, "tbox");
   obname = Name + "tboxlow";
   RecMake(obname, low, low - (high - low) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, C'79,21,0', "tboxlow");
   obname = Name + "tboxhigh";
   RecMake(obname, high, high + (high - low) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr), false), 0, C'9,51,47', "tboxhigh");
   obname = Name + "aFib";
   if (ObjectFind(0, Name + "aFib") <= 0) FibMake(obname, iTime(_Symbol, PERIOD_D1, 0), midline + (high - low), iTime(_Symbol, PERIOD_H1, 4), midline - (high - low), clrAqua);
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > midline && iLow(_Symbol, PERIOD_H1, 0) < midline)) { Alert("AutoFib " + _Symbol + " crossing midline. *WATCH*"); SendNotification("AutoFib " + _Symbol + " crossing midline. *WATCH*"); }
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > high && iLow(_Symbol, PERIOD_H1, 0) < high)) { Alert("AutoFib " + _Symbol + " possible HIGH grab/trend. *WATCH*"); SendNotification("AutoFib " + _Symbol + " possible HIGH grab/trend. *WATCH*"); }
   //if (hr >= 0 && (iHigh(_Symbol, PERIOD_H1, 0) > low && iLow(_Symbol, PERIOD_H1, 0) < low)) { Alert("AutoFib " + _Symbol + " possible LOW grab/trend. *WATCH*"); SendNotification("AutoFib " + _Symbol + " possible LOW grab/trend. *WATCH*"); }
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
void Objectfind(){
   string obname;
   double price1 = 0;
   double price2 = 0;
   datetime time1 = 0;
   datetime time2 = 0;
   double midline2 = 0;
   int hr = TimeHour(iTime(_Symbol, PERIOD_CURRENT, 0)) - 10;
   
   string name;
   double pricemid = 0;
   for (int x = ObjectsTotal(); x >= 0; x--)
   {
      name = StringConcatenate(ObjectName(x));
      if (ObjectType(name) == OBJ_RECTANGLE && ObjectGetInteger(0, name, OBJPROP_COLOR) == TriggerColor && ObjectFind(0, Name + "Fib" + IntegerToString(x)) < 0)
         {
            double pricea = 0;
            double priceb = 0;
            
            pricea = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 24 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 24 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 24 + 1))) / 2;
            pricemid = pricea;
            if (iClose(_Symbol, PERIOD_D1, 1) > iClose(_Symbol, PERIOD_D1, 2))
            {
               priceb = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
            }
            else if (iClose(_Symbol, PERIOD_D1, 1) < iClose(_Symbol, PERIOD_D1, 2))
            {
               priceb = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
            }
   
            //double highd0 = MathMax(pricea, priceb) + 2 * MathAbs(pricea - priceb);
            //if (_Symbol == "AUDUSD") Print("d0 h: " + highd0);
            //double lowd0 = MathMin(pricea, priceb) - 2 * MathAbs(pricea - priceb);
            //if (_Symbol == "AUDUSD") Print("d0 l: " + lowd0);
            /*
            if (_Symbol == "EURUSD")
            {
            Print(iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 24 + hr + 1)) + " " + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 24 + hr + 1)));
            Print(pricea + " " + priceb);
            }
            */
            //price1 = ObjectGet(name, OBJPROP_PRICE1);
            //price2 = ObjectGet(name, OBJPROP_PRICE2);
            //if (ChartPeriod() <= 15) time1 = (datetime)ObjectGet(name, OBJPROP_TIME2);
            time1 = iTime(_Symbol, PERIOD_D1, 1);//(datetime)ObjectGet(name, OBJPROP_TIME1);
            time2 = iTime(_Symbol, PERIOD_D1, 0);//(datetime)ObjectGet(name, OBJPROP_TIME2);
            if (pricea > priceb) { price1 = pricea; price2 = priceb; }
            else { price1 = priceb; price2 = pricea; }
            obname = Name + "Fib" + IntegerToString(x);
            //if (ObjectFind(0, Name + "Fib" + IntegerToString(x)) <= 0 && ChartPeriod() <= 240) FibMake(obname, Time[0], price1, Time[0] + 14400, price2, C'47,248,255');
            ObjectSetString(0, obname, OBJPROP_TEXT, "D-1");
            //Print("Done " + x + " " + ObjectFind(0, Name + "Fib" + IntegerToString(x)));
            midline2 = (pricea + priceb) / 2;
         }
      else ObjectDelete(0, Name + "Fib" + IntegerToString(x));
   }
   
   obname = Name + " mds";
   RecMake(obname, midline, midline2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, C'40,40,40', "");
   
   obname = Name + " ymid";
   objtrend2(obname, pricemid, pricemid, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 1), false), 0, 0, 3, STYLE_SOLID, C'100,100,100', "");
   
   double price3 = 0, price4 = 0;
   
   double pricen = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 48 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 48 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 48 + 1))) / 2;
   double priceo = 0;
   
   if (iClose(_Symbol, PERIOD_D1, 2) > iClose(_Symbol, PERIOD_D1, 3))
   priceo = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 24 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 2) < iClose(_Symbol, PERIOD_D1, 3))
   priceo = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 24 + hr + 1));
   
   if (pricen > priceo) { price3 = pricen; price4 = priceo; }
   else { price3 = priceo; price4 = pricen; }
   
   obname = Name + "Fibo";
   datetime time11 = iTime(_Symbol, PERIOD_D1, 2);//(datetime)ObjectGet(name, OBJPROP_TIME1);
   datetime time22 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 48 + 1), false));// iTime(_Symbol, PERIOD_D1, 1);//(datetime)ObjectGet(name, OBJPROP_TIME2);
   //if (ObjectFind(0, Name + "Fibo") <= 0 && ChartPeriod() <= 240) FibMake2(obname, time11, price3, time22, price4, clrYellow);
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-2");
   
   double midd2 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 24 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 24 + 1))) / 2;
   double midd3 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 48 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 48 + 1))) / 2;
   obname = Name + "Bibo";
   //RecMake(obname, midd3, midd2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 34), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 24), false), 0, clrWhite, "");
      
   double highd2 = MathMax(priceo, pricen) + 2 * (price3 - price4);
   //if (_Symbol == "AUDUSD") Print("d2 h: " + highd1);
   double lowd2 = MathMin(priceo, pricen) - 2 * MathAbs(price3 - price4);
   //if (_Symbol == "AUDUSD") Print("d2 l: " + lowd1);
   
   double price5 = 0, price6 = 0;
   
   double pricen1 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 72 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1))) / 2;
   double priceo1 = 0;
   
   if (iClose(_Symbol, PERIOD_D1, 3) > iClose(_Symbol, PERIOD_D1, 4))
   priceo1 = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 48 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 3) < iClose(_Symbol, PERIOD_D1, 4))
   priceo1 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 48 + hr + 1));
   
   if (pricen1 > priceo1) { price5 = pricen1; price6 = priceo1; }
   else { price5 = priceo1; price6 = pricen1; }
   
   obname = Name + "Fiboa";
   datetime time111 = iTime(_Symbol, PERIOD_D1, 3);//(datetime)ObjectGet(name, OBJPROP_TIME1);
   datetime time222 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 72 + 1), false));// iTime(_Symbol, PERIOD_D1, 1);//(datetime)ObjectGet(name, OBJPROP_TIME2);
   //if (ObjectFind(0, Name + "Fiboa") <= 0 && ChartPeriod() <= 240) FibMake2(obname, time111, price5, time222, price6, clrWhite);
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-3");
   
   double midd4 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 72 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 72 + 1))) / 2;
   obname = Name + "Biboa";
   //RecMake(obname, midd3, midd4, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 58), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 48), false), 0, clrWhite, "");
   
   double highd3 = MathMax(pricen1, priceo1) + 2 * (price5 - price6);
   //if (_Symbol == "AUDUSD") Print("d3 h: " + highd2);
   double lowd3 = MathMin(priceo1, pricen1) - 2 * MathAbs(price5 - price6);
   //if (_Symbol == "AUDUSD") Print("d3 l: " + lowd2);
   
   double price7 = 0, price8 = 0;
   
   double pricen2 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1)) + (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 96 + 1)) - iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1))) / 2;
   double priceo2 = 0;
   
   if (iClose(_Symbol, PERIOD_D1, 4) > iClose(_Symbol, PERIOD_D1, 5))
   priceo2 = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, 72 + hr + 1));
   else if (iClose(_Symbol, PERIOD_D1, 4) < iClose(_Symbol, PERIOD_D1, 5))
   priceo2 = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, 72 + hr + 1));
   
   if (pricen2 > priceo2) { price7 = pricen2; price8 = priceo2; }
   else { price7 = priceo2; price8 = pricen2; }
   
   obname = Name + "Fibob";
   datetime time1111 = iTime(_Symbol, PERIOD_D1, 4);//(datetime)ObjectGet(name, OBJPROP_TIME1);
   datetime time2222 = iTime(_Symbol, PERIOD_H1, iBarShift(_Symbol, PERIOD_H1, iTime(_Symbol, PERIOD_H1, hr + 96 + 1), false));// iTime(_Symbol, PERIOD_D1, 1);//(datetime)ObjectGet(name, OBJPROP_TIME2);
   //if (ObjectFind(0, Name + "Fibob") <= 0 && ChartPeriod() <= 240) FibMake2(obname, time1111, price7, time2222, price8, clrGray);
   ObjectSetString(0, obname, OBJPROP_TEXT, "D-4");
   
   double highd4 = MathMax(pricen2, priceo2) + 2 * (price7 - price8);
   //if (_Symbol == "AUDUSD") Print("d4: " + highd3);
   double lowd4 = MathMin(priceo2, pricen2) - 2 * MathAbs(price7 - price8);
   //if (_Symbol == "AUDUSD") Print("d4 l: " + lowd3);
   
   double midd5 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 96 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 96 + 1))) / 2;
   obname = Name + "Bibob";
   //RecMake(obname, midd4, midd5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 82), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 72), false), 0, clrWhite, "");
   
   double midd6 = (iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 120 + 1)) + iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 120 + 1))) / 2;
   obname = Name + "Biboc";
   //RecMake(obname, midd6, midd5, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 106), false), iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_H1, hr + 96), false), 0, clrWhite, "");
   
   obname = Name + "midline";
   objtrend2(obname, midline, midline, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   double high = iHigh(_Symbol, PERIOD_H1, iHighest(_Symbol, PERIOD_H1, MODE_HIGH, 10, hr + 1));
   obname = Name + "highline";
   objtrend2(obname, high, high, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   double low = iLow(_Symbol, PERIOD_H1, iLowest(_Symbol, PERIOD_H1, MODE_LOW, 10, hr + 1));
   obname = Name + "lowline";
   objtrend2(obname, low, low, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false), 0, 0, 3, 0, clrYellow, "");
   
   double ph[3];
   ArrayInitialize(ph, 0);
   double pl[3];
   ArrayInitialize(pl, 0);
   ph[0] = highd2; ph[1] = highd3; ph[2] = highd4;
   pl[0] = lowd2; pl[1] = lowd3; pl[2] = lowd4;
   
   bool phl[6];
   ArrayInitialize(phl, false);
   
   /*
   if (iClose(_Symbol, PERIOD_D1, 1) > highd4) { phl[2] = true; ph[2] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) > highd3) { phl[1] = true; ph[1] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) > highd2) { phl[0] = true; ph[0] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd4) { phl[5] = true; pl[2] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd3) { phl[4] = true; pl[1] = 0; }
   if (iClose(_Symbol, PERIOD_D1, 1) < lowd2) { phl[3] = true; pl[0] = 0; }
   */
    
   int countph = 0;
   
   for (int x = 2; x >= 0; x--)
   {
      if (phl[x] == false) countph++;
   }
   
   double highprice1 = 0, lowprice1 = 0;
   double highprice2 = 0, lowprice2 = 0;
   double highprice3 = 0, lowprice3 = 0;
   
   if (countph == 3)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice1 = ph[0];
      lowprice1 = ph[2];
   }
   if (countph == 2)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice1 = ph[0];
      lowprice1 = ph[1];
   }
   if (countph == 1)
   {
      ArraySort(ph, 0, 0, MODE_DESCEND);
      highprice3 = ph[0];
   }
   
   //countph = 0;
   int countpl = 0;
   
   for (int x = 2; x >= 0; x--)
   {
      if (phl[x + 3] == false) countpl++;
   }
   
   if (countpl == 3)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice2 = pl[0];
      lowprice2 = pl[2];
   }
   if (countpl == 2)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice2 = pl[0];
      lowprice2 = pl[1];
   }
   if (countpl == 1)
   {
      ArraySort(pl, 0, 0, MODE_DESCEND);
      highprice3 = pl[0];
   }
   
   if (countph > 1)
   {
      obname = Name + "highbox";
      RecMake(obname, highprice1, lowprice1, 3, 0, 0, clrBlue, "");
   }
   if (countpl > 1)
   {
      obname = Name + "lowbox";
      RecMake(obname, highprice2, lowprice2, 3, 0, 0, clrRed, "");
   }
   if (countph == 1)
   {
      obname = Name + "highbox";
      objtrend2(obname, highprice3, highprice3, 3, 0, 0, 4, 0, clrBlue, "");
   }
   if (countpl == 1)
   {
      obname = Name + "lowbox";
      objtrend2(obname, lowprice3, lowprice3, 3, 0, 0, 4, 0, clrRed, "");
   }
   //if (_Symbol == "USDCHF") Print(ph[2] + " " + ph[1] + " " + ph[0] + " - " + highprice1 + " " + lowprice1 + " / " + highprice2 + " " + lowprice2);    
}


//+FIBMAKE-----------------------------------------------------------+
bool FibMake(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
		{
			Print(__FUNCTION__,
				": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
			return (false);
		}
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 27);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	//if (ChartPeriod() <= 15) ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	//double levels[27] = { -3, -2.5, -2, -1.618, -1.5, -1.272, -1, -0.618, -0.5, -0.272, 0, 0.25, 0.382, 0.5, 0.618, 0.75, 1, 1.272, 1.5, 1.618, 2, 2.272, 2.5, 2.618, 3, 3.5, 4 };
	//string levell[27] = { "-4 - %$", "-3.5 - %$", "-3 - %$", "-2.618 - %$", "-2.5 - %$", "-2.272 - %$", "-2 - %$", "-1.618 - %$", "-1.5 - %$", "-1.272 - %$", "0 - %$", "0.25 - %$", "0.382 - %$", "0.5 - %$", "0.618 - %$", "0.75 - %$", "1 - %$", "1.272 - %$", "1.5 - %$", "1.618 - %$", "2 - %$", "2.272 - %$", "2.5 - %$", "2.618 - %$", "3 - %$", "3.5 - %$", "4 - %$" };
	double levels[17] = { -3, -2.5, -2, -1.5, -1, -0.5, 0, 0.25, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 3.5, 4 };
	string levell[17] = { "-4 - %$", "-3.5 - %$", "-3 - %$", "-2.5 - %$","-2 - %$", "-1.5 - %$", "0 - %$", "0.25 - %$", "0.5 - %$", "0.75 - %$", "1 - %$", "1.5 - %$", "2 - %$", "2.5 - %$", "3 - %$", "3.5 - %$", "4 - %$" };

	for (int x = 0; x <= 16; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	return(true);
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name,
               double pr1,
               double pr2,
               int t1,
               int t2,
               int t3,
               int wi,
               int st,
               color col,
               string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	datetime Trime[];
	ArrayResize(Trime, iBars(_Symbol, PERIOD_CURRENT));
	CopyTime(_Symbol, PERIOD_CURRENT, 0, iBars(_Symbol, PERIOD_CURRENT), Trime);
	ArraySetAsSeries(Trime, true);
   
	ObjectSetInteger(0, name, OBJPROP_TIME, Trime[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME, 1, Trime[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE, 1, pr2);
	ObjectSetInteger(0, name, OBJPROP_STYLE, st);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, wi);
	ObjectSetInteger(0, name, OBJPROP_RAY, false);
	ObjectSetInteger(0, name, OBJPROP_BACK, false);
	ObjectSetInteger(0, name, OBJPROP_COLOR, col);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//+------------------------------------------------------------------+

//+FIBMAKE-----------------------------------------------------------+
bool FibMake2(const string name,
	datetime time1,
	double price1,
	datetime time2,
	double price2,
	const color clrFib)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_FIBO, 0, time1, price1, time2, price2))
		{
			Print(__FUNCTION__,
				": failed to create \"Fibonacci Retracement\"! Error code = ", GetLastError());
			return (false);
		}
	ObjectSetInteger(0, name, OBJPROP_LEVELS, 6);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrNONE);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_LEVELCOLOR, clrFib);
	//if (ChartPeriod() <= 15) ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_LEVELSTYLE, STYLE_DOT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
	ObjectSetInteger(0, name, OBJPROP_RAY_RIGHT, false);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
	ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);

	double levels[8] = { -3, -2, -1, 2, 3, 4 };
	string levell[8] = { "-4 - %$", "-3 - %$", "-2 - %$", "2 - %$", "3 - %$", "4 - %$" };
   //double levels[8] = { -3, -2, -1.5, -1, 2, 2.5, 3, 4 };
	//string levell[8] = { "-4 - %$", "-3 - %$", "-2.5 - %$", "-2 - %$", "2 - %$", "2.5 - %$", "3 - %$", "4 - %$" };

	for (int x = 0; x <= 7; x++) {
		ObjectSetDouble(0, name, OBJPROP_LEVELVALUE, x, levels[x]);
		ObjectSetString(0, name, OBJPROP_LEVELTEXT, x, levell[x]);
	}
	return(true);
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+