#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"
#property version "1.6"
#property strict

// VERSIONS
// 1.4 Final for volume delta - all ok
// 1.5 Added volume delta bar paint to show divergence in delta / continuations / reversals
// 1.6 Made fixes to remove paint every new bar and not calculate cumulative every tick

#property indicator_separate_window
#property indicator_buffers 2

#property  indicator_width1  2
#property  indicator_width2  2

#property  indicator_style1  STYLE_SOLID
#property  indicator_style2  STYLE_SOLID

#define Name WindowExpertName()

enum linesplace {
	onwicks = 0, // On wick
	onopens = 1, // On open
	onocloses = 2, // On close
	onoccombo = 3 // On OC combo
};

input int checkdays = 15; // Calculate Delta for X days
input bool enablelines = false; // Enable max delta lines & arrows on chart (long- short- term)
input linesplace location = 0; // Show lines and arrows on wick/open/close/OC combo
input int checklines = 15; // Calculate lines and arrows for X days (<= total days)
input bool showshort = false; // Show short term lines (<= 5 days)
input string ablaubla = "Long term = days above, short term = last 2 days"; // Long- Short- delta lines colors
input color poscolor1 = clrBlue; // Positives Color A (long-)
input color poscolors1 = clrYellow; // Positives Color (short-)
input color poscolor2 = clrDodgerBlue; // Positives Color B (long-)
input color negcolor1 = clrRed; // Negatives Color A (long-)
input color negcolors1 = clrBlack; // Negatives Color (short-)
input color negcolor2 = clrPaleVioletRed; // Negatives Color B (long-)
bool showdivrev = true;

double voldeltan[], voldeltap[];

//+INIT SEQUENCE-----------------------------------------------------+
int OnInit()
{
	IndicatorDigits(0);
	IndicatorBuffers(2);

	SetIndexBuffer(0, voldeltap);
	SetIndexStyle(0, DRAW_HISTOGRAM, EMPTY, EMPTY, poscolor1);
	SetIndexEmptyValue(0, 0);
	SetIndexLabel(0, "+DELTA");
	SetIndexBuffer(1, voldeltan);
	SetIndexStyle(1, DRAW_HISTOGRAM, EMPTY, EMPTY, negcolor1);
	SetIndexEmptyValue(1, 0);
	SetIndexLabel(1, "-DELTA");

	SetLevelValue(0, 0);
	SetLevelStyle(2, 0, clrBlack);

	return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+MAIN RUN----------------------------------------------------------+
int OnCalculate(const int rates_total,
	const int prev_calculated,
	const datetime &time[],
	const double &open[],
	const double &high[],
	const double &low[],
	const double &close[],
	const long &tick_volume[],
	const long &volume[],
	const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("DeltaX expired, contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true) {
		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, PERIOD_M1, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, PERIOD_M1, 0);
		}
		if (new_1m_check)
		{
			ArrayInitialize(voldeltan, 0);
			ArrayInitialize(voldeltap, 0);
			PastPeriodDelta();
			if (enablelines && (ChartPeriod() >= 1 && ChartPeriod() <= 240)) Lines();
			new_1m_check = false;
		}
		CurPeriodDelta();

		bool new_paint_check = false;
		static datetime start_paint_time = 0;
		if (start_paint_time < iTime(NULL, PERIOD_CURRENT, 0))
		{
			new_paint_check = true;
			start_paint_time = iTime(NULL, PERIOD_CURRENT, 0);
		}
		if (new_paint_check)
		{
			//deltabarpaint();
			new_paint_check = false;
		}
	}//YesStop (expiry) end
	return(rates_total);
}
//+------------------------------------------------------------------+

//+DEINIT SEQUENCE---------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+PAST BAR DELTA----------------------------------------------------+
void PastPeriodDelta() {
	int perioda = Period();
	int divisor = PERIOD_M1;
	if (ChartPeriod(0) >= 240 && ChartPeriod(0) < 1440) divisor = PERIOD_M5;
	else if (ChartPeriod(0) >= 1440 && ChartPeriod(0) < 10080) divisor = PERIOD_M15;
	else if (ChartPeriod(0) >= 10080 && ChartPeriod(0) < 43200) divisor = PERIOD_M30;
	else if (ChartPeriod(0) == 43200) divisor = PERIOD_H1;
	

	datetime yst = iTime(_Symbol, PERIOD_D1, checkdays);
	int firstmin = iBarShift(_Symbol, perioda, yst);
	datetime sta1 = iTime(_Symbol, perioda, firstmin);
	int shif1 = iBarShift(_Symbol, divisor, sta1, false);
	int minmin = perioda / divisor;
	int volm = 0, volp = 0;

	double closes[], opens[];
	long volumes[];
	datetime times[];
	ArrayResize(closes, firstmin + shif1 + 2);
	ArrayResize(opens, firstmin + shif1 + 2);
	ArrayResize(volumes, firstmin + shif1 + 2);
	ArrayResize(times, firstmin + shif1 + 2);	
	if (!CopyClose(_Symbol, divisor, 0, firstmin + shif1, closes)) { Print("Error copying closes"); return; }	
	if (!CopyOpen(_Symbol, divisor, 0, firstmin + shif1, opens)) { Print("Error copying opens"); return; }	
	if (!CopyTickVolume(_Symbol, divisor, 0, shif1 + minmin, volumes)) { Print("Error copying volumes"); return; }	
	if (!CopyTime(_Symbol, divisor, 0, firstmin + shif1, times)) { Print("Error copying times"); return; }	
	ArraySetAsSeries(closes, true);
	ArraySetAsSeries(opens, true);
	ArraySetAsSeries(volumes, true);
	ArraySetAsSeries(times, true);

	for (int i = 1; i <= firstmin; i++) {
		datetime sta = iTime(_Symbol, perioda, i);
	   int shif = iBarShift(_Symbol, divisor, sta, false);

		for (int j = 0; j <= minmin - 1; j++) {
			if (closes[shif - j] < opens[shif - j]) volm += (int)volumes[shif - j];
			else if (closes[shif - j] > opens[shif - j]) volp += (int)volumes[shif - j];
			if (times[shif - j] >= iTime(_Symbol, perioda, i - 1)) break;
			//if(i==175)Print(DoubleToStr(volm,0)+" "+DoubleToStr(volp,0)+" " +IntegerToString((int)iVolume(_Symbol, divisor, shif-j))+" " +IntegerToString(iBarShift(_Symbol, PERIOD_M1, iTime(_Symbol, PERIOD_D1, 0)))); //tester
		}

		if (volp > volm)
			voldeltap[i] = volp - volm;
		else if (volm >= volp)
			voldeltan[i] = -(volm - volp);
		volm = 0; volp = 0;
	}
}
//+------------------------------------------------------------------+

//+CURRENT BAR DELTA-------------------------------------------------+
void CurPeriodDelta() {
	int perioda = Period();
	int divisor = PERIOD_M1;
	if (ChartPeriod(0) >= 240 && ChartPeriod(0) < 1440) divisor = PERIOD_M5;
	else if (ChartPeriod(0) >= 1440 && ChartPeriod(0) < 10080) divisor = PERIOD_M15;
	else if (ChartPeriod(0) >= 10080 && ChartPeriod(0) < 43200) divisor = PERIOD_M30;
	else if (ChartPeriod(0) == 43200) divisor = PERIOD_H1;
	datetime t32 = iTime(_Symbol, PERIOD_D1, 0) + 60 * 60 * 24 * 3;

	int volm = 0, volp = 0;

	datetime sta = iTime(_Symbol, perioda, 0);
	int shif = iBarShift(_Symbol, divisor, sta, false);

	for (int j = shif; j >= 0; j--) {
		if (iClose(_Symbol, divisor, j) < iOpen(_Symbol, divisor, j)) volm += (int)iVolume(_Symbol, divisor, j);
		else if (iClose(_Symbol, divisor, j) > iOpen(_Symbol, divisor, j)) volp += (int)iVolume(_Symbol, divisor, j);
	}

	if (volp > volm)
		voldeltap[0] = volp - volm;
	else if (volm >= volp)
		voldeltan[0] = -(volm - volp);
}
//+------------------------------------------------------------------+

//+DRAW LINES ON HEAVY VOLUME----------------------------------------+
void Lines() {//HEAVY VOLUME LINES - BLUE/RED = 6 DAY, YELLOW/BLACK = 2 DAY
	datetime t32 = Time[0] + (34 * Period() * 60);
	datetime SoD = iTime(_Symbol, PERIOD_D1, checklines);
	datetime SoDx = iTime(_Symbol, PERIOD_D1, 4);

	int blink = iBarShift(_Symbol, PERIOD_CURRENT, SoD, false);
	int blinkx = iBarShift(_Symbol, PERIOD_CURRENT, SoDx, false);

	double recp[], recn[]; ArrayResize(recp, blink + 1); ArrayResize(recn, blink + 1);
	double recpx[], recnx[]; ArrayResize(recpx, blinkx + 1); ArrayResize(recnx, blinkx + 1);

	ArrayCopy(recp, voldeltap, 0, 0, blink); ArrayCopy(recn, voldeltan, 0, 0, blink);
	ArrayCopy(recpx, voldeltap, 0, 0, blinkx); ArrayCopy(recnx, voldeltan, 0, 0, blinkx);

	ArraySort(recp, 0, 0, MODE_DESCEND);
	ArraySort(recn, 0, 0, MODE_ASCEND);

	double p1 = recp[0];
	double p2 = recp[1];
	double p3 = recp[2];
	double p4 = recp[3];
	double n1 = recn[0];
	double n2 = recn[1];
	double n3 = recn[2];
	double n4 = recn[3];

	ArraySort(recpx, 0, 0, MODE_DESCEND);
	ArraySort(recnx, 0, 0, MODE_ASCEND);

	double pp1 = recpx[0];
	double pp2 = recpx[1];
	double nn1 = recnx[0];
	double nn2 = recnx[1];

	string oname, obname, onname;
	if (location == 0) {
		for (int i = blink; i >= 1; i--) {
			if (voldeltap[i] == p1) { obname = Name + "PastHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxPosDeltaLo"; burnarr(oname, Low[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " MPosP"; Texter(onname, iLow(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "MV - P", poscolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltan[i] == n1) { obname = Name + "PastHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxNegDeltaLo"; burnarr(oname, High[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " MPosN"; Texter(onname, iHigh(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "MV - N", negcolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltap[i] == p2) { obname = Name + "PastHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 2ndPosDeltaLo"; burnarr(oname, Low[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 2PosP"; Texter(onname, iLow(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "2n - P", poscolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltan[i] == n2) { obname = Name + "PastHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 2ndNegDeltaLo"; burnarr(oname, High[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 2NegP"; Texter(onname, iHigh(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "2n - N", negcolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltap[i] == p3) { obname = Name + "PastHV 3rd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 3rdPosDeltaLo"; burnarr(oname, Low[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 3PosP"; Texter(onname, iLow(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "3r - P", poscolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltan[i] == n3) { obname = Name + "PastHV 3rd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 3rdNegDeltaLo"; burnarr(oname, High[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 3NegP"; Texter(onname, iHigh(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "3r - N", negcolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltap[i] == p4) { obname = Name + "PastHV 4th Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 4thPosDeltaLo"; burnarr(oname, Low[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 4PosP"; Texter(onname, iLow(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "4t- P", poscolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
			if (voldeltan[i] == n4) { obname = Name + "PastHV 4th Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 4thNegDeltaLo"; burnarr(oname, High[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); { onname = Name + " 4NegP"; Texter(onname, iHigh(_Symbol, PERIOD_CURRENT, i), t32 + Period() * 60, "4t - N", negcolor1); ObjectSetInteger(0, onname, OBJPROP_FONTSIZE, 6); ObjectSetString(0, onname, OBJPROP_FONT, "Arial"); ObjectSetInteger(0, onname, OBJPROP_ANCHOR, ANCHOR_LEFT); } }
		}
		if (showshort) {
			for (int i = blinkx; i >= 1; i--) {
				if (voldeltap[i] == pp1) { obname = Name + "RecentHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxPosDeltaSh"; burnarr(oname, Low[i] - 50 * _Point, i, 233, poscolors1); }
				if (voldeltap[i] == pp2) { obname = Name + "RecentHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " LowPosDeltaSh"; burnarr(oname, Low[i] - 50 * _Point, i, 233, poscolors1); }
				if (voldeltan[i] == nn1) { obname = Name + "RecentHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxNegDeltaSh"; burnarr(oname, High[i] + 50 * _Point, i, 234, negcolors1); }
				if (voldeltan[i] == nn2) { obname = Name + "RecentHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " LowNegDeltaSh"; burnarr(oname, High[i] + 50 * _Point, i, 234, negcolors1); }
			}
		}
	}

	else if (location == 1) {
		for (int i = blink; i >= 1; i--) {
			if (voldeltap[i] == p1) { obname = Name + "PastHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n1) { obname = Name + "PastHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p2) { obname = Name + "PastHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 2ndPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n2) { obname = Name + "PastHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 2ndNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p3) { obname = Name + "PastHV 3rd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 3rdPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n3) { obname = Name + "PastHV 3rd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 3rdNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p4) { obname = Name + "PastHV 4th Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 4thPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n4) { obname = Name + "PastHV 4th Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 4thNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
		}
		if (showshort) {
			for (int i = blinkx; i >= 1; i--) {
				if (voldeltap[i] == pp1) { obname = Name + "RecentHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxPosDeltaSh"; burnarr(oname, Open[i] - 50 * _Point, i, 233, poscolors1); }
				if (voldeltap[i] == pp2) { obname = Name + "RecentHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " OpenPosDeltaSh"; burnarr(oname, Open[i] - 50 * _Point, i, 233, poscolors1); }
				if (voldeltan[i] == nn1) { obname = Name + "RecentHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxNegDeltaSh"; burnarr(oname, Open[i] + 50 * _Point, i, 234, negcolors1); }
				if (voldeltan[i] == nn2) { obname = Name + "RecentHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " OpenNegDeltaSh"; burnarr(oname, Open[i] + 50 * _Point, i, 234, negcolors1); }
			}
		}
	}

	else if (location == 2) {
		for (int i = blink; i >= 1; i--) {
			if (voldeltap[i] == p1) { obname = Name + "PastHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxPosDeltaLo"; burnarr(oname, Close[i] + 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n1) { obname = Name + "PastHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2); oname = Name + " MaxNegDeltaLo"; burnarr(oname, Close[i] - 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p2) { obname = Name + "PastHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 2ndPosDeltaLo"; burnarr(oname, Close[i] + 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n2) { obname = Name + "PastHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 2ndNegDeltaLo"; burnarr(oname, Close[i] - 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p3) { obname = Name + "PastHV 3rd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 3rdPosDeltaLo"; burnarr(oname, Close[i] + 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n3) { obname = Name + "PastHV 3rd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 3rdNegDeltaLo"; burnarr(oname, Close[i] - 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltap[i] == p4) { obname = Name + "PastHV 4th Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolor1); oname = Name + " 4thPosDeltaLo"; burnarr(oname, Close[i] + 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
			if (voldeltan[i] == n4) { obname = Name + "PastHV 4th Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolor1); oname = Name + " 4thNegDeltaLo"; burnarr(oname, Close[i] - 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1); }
		}
		if (showshort) {
			for (int i = blinkx; i >= 1; i--) {
				if (voldeltap[i] == pp1) { obname = Name + "RecentHV MAX Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxPosDeltaSh"; burnarr(oname, Close[i] + 50 * _Point, i, 233, poscolors1); }
				if (voldeltap[i] == pp2) { obname = Name + "RecentHV 2nd Pos"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " ClosePosDeltaSh"; burnarr(oname, Close[i] + 50 * _Point, i, 233, poscolors1); }
				if (voldeltan[i] == nn1) { obname = Name + "RecentHV MAX Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " MaxNegDeltaSh"; burnarr(oname, Close[i] - 50 * _Point, i, 234, negcolors1); }
				if (voldeltan[i] == nn2) { obname = Name + "RecentHV 2nd Neg"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iClose(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT); oname = Name + " CloseNegDeltaSh"; burnarr(oname, Close[i] - 50 * _Point, i, 234, negcolors1); }
			}
		}
	}

	else if (location == 3) {
		for (int i = blink; i >= 1; i--) {
			if (voldeltap[i] == p1) {
				obname = Name + "PastHV MAX PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				obname = Name + "PastHV MAX PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor2); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				oname = Name + " MaxPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltan[i] == n1) {
				obname = Name + "PastHV MAX NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				obname = Name + "PastHV MAX NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor2); ObjectSetInteger(0, obname, OBJPROP_STYLE, 0); ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
				oname = Name + " MaxNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltap[i] == p2) {
				obname = Name + "PastHV 2nd PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1);
				obname = Name + "PastHV 2nd PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor2);
				oname = Name + " 2ndPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltan[i] == n2) {
				obname = Name + "PastHV 2nd NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1);
				obname = Name + "PastHV 2nd NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor2);
				oname = Name + " 2ndNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltap[i] == p3) {
				obname = Name + "PastHV 3rd PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1);
				obname = Name + "PastHV 3rd PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor2);
				oname = Name + " 3rdPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltan[i] == n3) {
				obname = Name + "PastHV 3rd NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1);
				obname = Name + "PastHV 3rd NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor2);
				oname = Name + " 3rdNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltap[i] == p4) {
				obname = Name + "PastHV 4th PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolor1);
				obname = Name + "PastHV 4th PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolor2);
				oname = Name + " 4thPosDeltaLo"; burnarr(oname, Open[i] - 100 * _Point, i, 233, poscolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
			if (voldeltan[i] == n4) {
				obname = Name + "PastHV 4th NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolor1);
				obname = Name + "PastHV 4th NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolor2);
				oname = Name + " 4thNegDeltaLo"; burnarr(oname, Open[i] + 100 * _Point, i, 234, negcolor1); ObjectSet(oname, OBJPROP_WIDTH, 1);
			}
		}
		if (showshort) {
			for (int i = blinkx; i >= 1; i--) {
				if (voldeltap[i] == pp1) {
					obname = Name + "RecentHV MAX PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					obname = Name + "RecentHV MAX PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					oname = Name + " MaxPosDeltaSh"; burnarr(oname, Open[i] - 50 * _Point, i, 233, poscolors1);
				}
				if (voldeltap[i] == pp2) {
					obname = Name + "RecentHV 2nd PosH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iLow(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					obname = Name + "RecentHV 2nd PosO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), poscolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					oname = Name + " OpenPosDeltaSh"; burnarr(oname, Open[i] - 50 * _Point, i, 233, poscolors1);
				}
				if (voldeltan[i] == nn1) {
					obname = Name + "RecentHV MAX NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					obname = Name + "RecentHV MAX NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					oname = Name + " MaxNegDeltaSh"; burnarr(oname, Open[i] + 50 * _Point, i, 234, negcolors1);
				}
				if (voldeltan[i] == nn2) {
					obname = Name + "RecentHV 2nd NegH"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iHigh(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					obname = Name + "RecentHV 2nd NegO"; objbase(obname, iTime(_Symbol, PERIOD_CURRENT, i), t32, iOpen(_Symbol, PERIOD_CURRENT, i), negcolors1); ObjectSet(obname, OBJPROP_STYLE, STYLE_DOT);
					oname = Name + " OpenNegDeltaSh"; burnarr(oname, Open[i] + 50 * _Point, i, 234, negcolors1);
				}
			}
		}
	}

	/*
	int total = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, checkdays), false);

	for(int i=total-1; i>=0; i--){
		string obname=Name+"ReverseArr"+IntegerToString(i);
		if(Close[i]>Open[i] && voldeltap[i+1]>0 && MathAbs(voldeltan[i]) > voldeltap[i+1] ) burnarr(obname, Close[i]+100*_Point, i, 234, negcolor1);
		if(Close[i]<Open[i] && voldeltan[i+1]<0 && voldeltap[i] > MathAbs(voldeltan[i+1]) ) burnarr(obname, Close[i]-100*_Point, i, 233, poscolor1);
		obname=Name+"DivergeArr"+IntegerToString(i);
		if(Close[i]>Open[i] && voldeltap[i]==0 && voldeltan[i]<=-500) { burnarr(obname, Close[i]+100*_Point, i, 234, negcolors1); ObjectSet(obname, OBJPROP_WIDTH, 1); }
		if(Close[i]<Open[i] && voldeltan[i]==0 && voldeltap[i]>=500) { burnarr(obname, Close[i]-100*_Point, i, 233, poscolors1); ObjectSet(obname, OBJPROP_WIDTH, 1); }
	}
	*/
}
//+------------------------------------------------------------------+

//+DELTA BAR PAINT DIV/REVERSAL/CONTINUATION-------------------------+
void deltabarpaint() {
	datetime start1 = iTime(_Symbol, PERIOD_D1, checkdays);
	int parse1 = iBarShift(_Symbol, PERIOD_CURRENT, start1, false);
	int Buffer1[], Buffer2[];
	ArrayResize(Buffer1, parse1 + 1); ArrayResize(Buffer2, parse1 + 1);

	ObjectsDeleteAll(0, Name + " Pt");

	for (int i = parse1; i >= 0; i--) {
		if (showdivrev) {
			if ((voldeltap[i] > MathAbs(voldeltan[i])) && (iOpen(_Symbol, PERIOD_CURRENT, i) > iClose(_Symbol, PERIOD_CURRENT, i))) { Buffer1[i] = 1; }
			else { Buffer1[i] = EMPTY_VALUE; }
			if ((voldeltap[i] < MathAbs(voldeltan[i])) && (iOpen(_Symbol, PERIOD_CURRENT, i) < iClose(_Symbol, PERIOD_CURRENT, i))) { Buffer2[i] = 1; }
			else { Buffer2[i] = EMPTY_VALUE; }
		}
	}

	for (int x = parse1 - 1; x >= 1; x--) {
		if ((Buffer1[x] != EMPTY_VALUE && Buffer2[x + 1] != EMPTY_VALUE)) RecMake2(Name + " PtPosDiv " + IntegerToString(x), iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 2, x)), iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 2, x)), iTime(_Symbol, PERIOD_CURRENT, x), iTime(_Symbol, PERIOD_CURRENT, x + 1), clrDodgerBlue);
		if ((Buffer1[x] != EMPTY_VALUE && Buffer1[x + 1] != EMPTY_VALUE)) RecMake2(Name + " PtPosRev " + IntegerToString(x), iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 2, x)), iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 2, x)), iTime(_Symbol, PERIOD_CURRENT, x), iTime(_Symbol, PERIOD_CURRENT, x + 1), clrAqua);
		if ((Buffer2[x] != EMPTY_VALUE && Buffer1[x + 1] != EMPTY_VALUE)) RecMake2(Name + " PtNegDiv " + IntegerToString(x), iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 2, x)), iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 2, x)), iTime(_Symbol, PERIOD_CURRENT, x), iTime(_Symbol, PERIOD_CURRENT, x + 1), clrCoral);
		if ((Buffer2[x] != EMPTY_VALUE && Buffer2[x + 1] != EMPTY_VALUE)) RecMake2(Name + " PtNegRev " + IntegerToString(x), iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 2, x)), iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 2, x)), iTime(_Symbol, PERIOD_CURRENT, x), iTime(_Symbol, PERIOD_CURRENT, x + 1), clrViolet);
	}
}
//+------------------------------------------------------------------+

//+CREATE T-LINES----------------------------------------------------+
void objbase(string oname, datetime t1, datetime t2, double pr1, color col) {
	if (ObjectFind(0, oname) < 0)
		if (!ObjectCreate(0, oname, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(oname, OBJPROP_STYLE, STYLE_DASH);
	ObjectSet(oname, OBJPROP_WIDTH, 0);
	ObjectSet(oname, OBJPROP_BACK, true);
	ObjectSet(oname, OBJPROP_COLOR, col);
	ObjectSet(oname, OBJPROP_TIME1, t1);
	ObjectSet(oname, OBJPROP_TIME2, t2);
	ObjectSet(oname, OBJPROP_PRICE1, pr1);
	ObjectSet(oname, OBJPROP_PRICE2, pr1);
	ObjectSet(oname, OBJPROP_RAY, false);
}
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const datetime y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+CREATE ARROWS-----------------------------------------------------+
void burnarr(string name, double p, int t, int arrow, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 0);
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION ON PRICE & TIME----------------------------------+
void RecMake2(const string name, const double x, const double y, const datetime xtime, const datetime ytime, const color BCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, y);
	ObjectSetInteger(0, name, OBJPROP_TIME1, xtime);
	ObjectSetInteger(0, name, OBJPROP_TIME2, ytime);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_FILL, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, name);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+