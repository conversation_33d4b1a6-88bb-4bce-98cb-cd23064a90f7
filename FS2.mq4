#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 12
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed
#property indicator_color5 clrBlue
#property indicator_color6 clrRed
#property indicator_color7 clrBlue
#property indicator_color8 clrRed
#property indicator_color9 clrBlue
#property indicator_color10 clrRed
#property indicator_color11 clrBlue
#property indicator_color12 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
extern int periods = 1000;				  // Candles back to check
extern int drawlines = 1000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period
static extern ENUM_TIMEFRAMES divisor = PERIOD_CURRENT; // Initial active drawing period
static extern int check = 5; //Number of boxes to draw for FVG/OB
input bool show50 = false; // Show 50% line in OBs
static bool alloff = false; // Close all drawings
static bool fvg = false;
static bool ob = true;
static bool fr = true;
static extern bool alerts = false; // Alerts ON/OFF initial
static bool clean = true; // Show only tradeable OB (3 conditions) ON/OFF initial
input string initialize = ""; //
double dist = 0.25;

double finp[];
double finn[];
double linp[];
double linn[];
double obfinp[];
double obfinn[];
double oblinp[];
double oblinn[];
double obsinp[];
double obsinn[];
double fickp[];
double fickn[];

double newarrn[], newarrn1[], newarrp[], newarrp1[];

//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(10);
	IndicatorShortName("FVG-OB /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexStyle(0, DRAW_NONE, 0, 0);
	SetIndexArrow(0, 233);
	SetIndexBuffer(0, finp);
	SetIndexLabel(0, "");
	SetIndexStyle(1, DRAW_NONE, 0, 0);
	SetIndexArrow(1, 234);
	SetIndexBuffer(1, finn);
	SetIndexLabel(1, "");
	SetIndexStyle(2, DRAW_NONE, 0, 0);
	SetIndexArrow(2, 233);
	SetIndexBuffer(2, linp);
	SetIndexLabel(2, "");
	SetIndexStyle(3, DRAW_NONE, 0, 0);
	SetIndexArrow(3, 234);
	SetIndexBuffer(3, linn);
	SetIndexLabel(3, "");
	SetIndexStyle(4, DRAW_NONE, 0, 0);
	SetIndexArrow(4, 233);
	SetIndexBuffer(4, obfinp);
	SetIndexLabel(4, "");
	SetIndexStyle(5, DRAW_NONE, 0, 0);
	SetIndexArrow(5, 234);
	SetIndexBuffer(5, obfinn);
	SetIndexLabel(5, "");
	SetIndexStyle(6, DRAW_NONE, 0, 0);
	SetIndexArrow(6, 233);
	SetIndexBuffer(6, oblinp);
	SetIndexLabel(6, "");
	SetIndexStyle(7, DRAW_NONE, 0, 0);
	SetIndexArrow(7, 234);
	SetIndexBuffer(7, oblinn);
	SetIndexLabel(7, "");
	SetIndexStyle(8, DRAW_NONE, 0, 0);
	SetIndexArrow(8, 233);
	SetIndexBuffer(8, obsinp);
	SetIndexLabel(8, "");
	SetIndexStyle(9, DRAW_NONE, 0, 0);
	SetIndexArrow(9, 234);
	SetIndexBuffer(9, obsinn);
	SetIndexLabel(9, "");
	SetIndexStyle(10, DRAW_NONE, 0, 0);
	SetIndexArrow(10, 234);
	SetIndexBuffer(10, fickp);
	SetIndexLabel(10, "");
	SetIndexStyle(11, DRAW_NONE, 0, 0);
	SetIndexArrow(11, 234);
	SetIndexBuffer(11, fickn);
	SetIndexLabel(11, "");
	
	if (ChartPeriod() < PERIOD_M30 || clean) check = 10;
	
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| ChartEvent function |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
				  const long &lparam,
				  const double &dparam,
				  const string &sparam)
{
	//---
	{ //Currency select tables 1-4 button events
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFa" + IntegerToString(0)))
			{
			   divisor = PERIOD_M1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFa" + IntegerToString(1)))
			{
			   divisor = PERIOD_M5;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFa" + IntegerToString(2)))
			{
			   divisor = PERIOD_M15;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFa" + IntegerToString(3)))
			{
			   divisor = PERIOD_M30;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFb" + IntegerToString(0)))
			{
			   divisor = PERIOD_H1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFb" + IntegerToString(1)))
			{
			   divisor = PERIOD_H4;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFb" + IntegerToString(2)))
			{
			   divisor = PERIOD_D1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "TFb" + IntegerToString(3)))
			{
			   divisor = PERIOD_W1;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CH" + IntegerToString(0)))
			{
			   check = 2;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CH" + IntegerToString(1)))
			{
			   check = 5;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CH" + IntegerToString(2)))
			{
			   check = 10;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CH" + IntegerToString(3)))
			{
			   check = 15;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CH" + IntegerToString(4)))
			{
			   check = 20;
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alloff == false)
		{
			if (sparam == StringConcatenate(Name + "OFF"))
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			alloff = true;
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alloff == true)
		{
			if (sparam == StringConcatenate(Name + "ON"))
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			buttons();
   			alloff = false;
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && fvg == true)
		{
			if (sparam == StringConcatenate(Name + "FVGb") && ObjectGet(Name + "FVGb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			fvg = false;
   			ObjectSetInteger(0, Name + "FVGb", OBJPROP_COLOR, clrRed);
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && fvg == false)
		{
			if (sparam == StringConcatenate(Name + "FVGb") && ObjectGet(Name + "FVGb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			fvg = true;
   			ObjectSetInteger(0, Name + "FVGb", OBJPROP_COLOR, clrBlue);
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && ob == true)
		{
			if (sparam == StringConcatenate(Name + "OBb") && ObjectGetInteger(0, Name + "OBb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			ob = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && ob == false)
		{
			if (sparam == StringConcatenate(Name + "OBb") && ObjectGetInteger(0, Name + "OBb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			ob = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && fr == true)
		{
			if (sparam == StringConcatenate(Name + "FRb") && ObjectGetInteger(0, Name + "FRb", OBJPROP_COLOR) == clrBlue)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			fr = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && fr == false)
		{
			if (sparam == StringConcatenate(Name + "FRb") && ObjectGetInteger(0, Name + "FRb", OBJPROP_COLOR) == clrRed)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			fr = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && alerts == false)
		{
			if (sparam == StringConcatenate(Name + "AL") && ObjectGetInteger(0, Name + "AL", OBJPROP_COLOR) == clrRed)
			{
   			ChartRedraw();
   			alerts = true;
			   buttons();
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && alerts == true)
		{
			if (sparam == StringConcatenate(Name + "AL") && ObjectGetInteger(0, Name + "AL", OBJPROP_COLOR) == clrGreen)
			{
   			ChartRedraw();
			   alerts = false;
			   buttons();
			}
		}
		if (id == CHARTEVENT_OBJECT_CLICK && clean == false)
		{
			if (sparam == StringConcatenate(Name + "CL") && ObjectGetInteger(0, Name + "CL", OBJPROP_COLOR) == clrMagenta)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			clean = true;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
		else if (id == CHARTEVENT_OBJECT_CLICK && clean == true)
		{
			if (sparam == StringConcatenate(Name + "CL") && ObjectGetInteger(0, Name + "CL", OBJPROP_COLOR) == clrGreen)
			{
   			ObjectsDeleteAll(0, Name);
   			ChartRedraw();
   			RefreshRates();
   			clean = false;
   			buttons();
   			if (!alloff)
   			{ 
   			   if (fvg) checkpre();
   			   if (ob) checkpre1();
   			   if (fr) checkpre2();
   			}
			}
		}
   }
}

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2026.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG/OB expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			ChartRedraw();
			RefreshRates();
			buttons();
			if (!alloff)
			{
			   if (fvg) checkpre();
			   if (ob) checkpre1();
   			if (fr) checkpre2();
			}
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}
//+------------------------------------------------------------------+

//+BUTTONS CREATE FOR TF & NUMBER------------------------------------+
void buttons()
{

	string obname;
   string tfs[8] = { "1", "5", "15", "30", "H1", "H4", "D1", "W1" };
   string checks[5] = { "2", "5", "10", "15", "20" };
   
   for (int x = 3; x >= 0; x--)
   {
      obname = Name + "TFa" + IntegerToString(x);
      LabelMake(obname, 3, 110 - x * 25, 60, tfs[x], 10, clrWhite);
      obname = Name + "TFb" + IntegerToString(x);
      LabelMake(obname, 3, 110 - x * 25, 40, tfs[x + 4], 10, clrWhite);
   }
   
   for (int x = 4; x >= 0; x--)
   {
      obname = Name + "CH" + IntegerToString(x);
      LabelMake(obname, 3, 135 - x * 25, 20, checks[x], 10, clrWhite);
   }
   
   string bull[4] = { "-", "-", "-", "-" };
   for (int x = 2; x >= 0; x--)
   {
      obname = Name + "--" + IntegerToString(x);
      LabelMake(obname, 3, 118 - x * 25, 20, bull[x], 10, clrWhite);
   }
   
   obname = Name + "ON";
   LabelMake(obname, 3, 95, 80, "ON /", 10, clrWhite);
   obname = Name + "OFF";
   LabelMake(obname, 3, 68, 80, " OFF", 10, clrWhite);
   
   obname = Name + "FRb";
   LabelMake(obname, 3, 122, 100, "F /", 10, clrWhite);
   if (fr) ObjectSet(obname, OBJPROP_COLOR, clrBlue);
   if (!fr) ObjectSet(obname, OBJPROP_COLOR, clrRed);
   obname = Name + "FVGb";
   LabelMake(obname, 3, 100, 100, "V /", 10, clrWhite);
   if (fvg) ObjectSet(obname, OBJPROP_COLOR, clrBlue);
   if (!fvg) ObjectSet(obname, OBJPROP_COLOR, clrRed);
   obname = Name + "OBb";
   LabelMake(obname, 3, 78, 100, " O", 10, clrWhite);
   if (ob) ObjectSet(obname, OBJPROP_COLOR, clrBlue);
   if (!ob) ObjectSet(obname, OBJPROP_COLOR, clrRed);
   obname = Name + "AL";
   LabelMake(obname, 3, 30, 80, "AL", 10, clrWhite);
   if (alerts) ObjectSet(obname, OBJPROP_COLOR, clrGreen);
   if (!alerts) ObjectSet(obname, OBJPROP_COLOR, clrRed);
   obname = Name + "CL";
   LabelMake(obname, 3, 30, 100, "CL", 10, clrWhite);
   if (clean) ObjectSet(obname, OBJPROP_COLOR, clrGreen);
   if (!clean) ObjectSet(obname, OBJPROP_COLOR, clrMagenta);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{	
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();
	string obname;

	double CD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);
		
	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] < CD1[x + 2]) && LD1[x + 1] - HD1[x + 3] > 0)
		{
			finp[x] = HD1[x + 3];
			linp[x] = LD1[x + 1];
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] > CD1[x + 2]) && (LD1[x + 3] - HD1[x + 1] > 0))
		{
			finn[x] = LD1[x + 3];
			linn[x] = HD1[x + 1];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	int countpos = 0, countneg = 0;
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "b" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];
      if (countpos == check) break;
		if (finp[x] != EMPTY_VALUE && x < drawlines && LL > finp[x])
		{
		   countpos++;
   		/*int counta = 0, countb = 0;
		   if (ChartPeriod() == divisor)
		   {
   		   for (int y = x; y >= 1; y--)
   		   {
   		      if (LD1[y] <= linp[x]) counta++;
   		      if (LD1[y] <= linp[x] && iRSI(_Symbol, divisor, 14, PRICE_CLOSE, y) < 45 && LD1[y] < iBands(_Symbol, divisor, 20, 2, 0, PRICE_CLOSE, MODE_MAIN, y)) 
   		      { 
   		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
   		         burnarr(obname, LD1[y] - 3 * _Point, 139, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, y), false), clrYellow);
   		         if (x == 1 && alerts) lert(_Symbol, "Combo up arrow FVG", CD1[y], ChartPeriod());
   		         countb++;
   		      }
   		   }
   		}*/
		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, finp[x], linp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, clrPowderBlue, "FVG: " + DoubleToString(finp[x], _Digits) + " - " + DoubleToString(linp[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (LL1 <= linp[x] && LL > finp[x])
			{
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, clrWhite);
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_BACK, false);
		      /*
		      obname = Name + "FVGtouch" + intrepl;
		      if (ChartPeriod() == divisor && counta >= 5) { Texter(obname, finp[x], 3 * Period() * 60, IntegerToString(counta) + " - " + IntegerToString(countb), clrWhite);
   		   ObjectSetString(0, Name + "FVGtouch" + intrepl, OBJPROP_TOOLTIP, "TOUCH - COMBO " + DoubleToString(finp[x], _Digits));
		      ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER); }
		      */
			}
		}
		if (finp[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "FVGrec" + intrepl);
			//ObjectDelete(Name + "FVGtouch" + intrepl);
		}
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "s" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, x, 1)];
		double HH1 = HD1[ArrayMaximum(HD1, x, 1)];
      if (countneg == check) break;
		if (finn[x] != EMPTY_VALUE && x < drawlines && HH < finn[x])
		{
		   countneg++;
   		/*int counta = 0, countb = 0;
		   if (ChartPeriod() == divisor)
		   {
   		   for (int y = x; y >= 1; y--)
   		   {
   		      if (HD1[y] >= linn[x]) counta++;
   		      if (HD1[y] >= linn[x] && iRSI(_Symbol, divisor, 14, PRICE_CLOSE, y) > 55 && HD1[y] > iBands(_Symbol, divisor, 20, 2, 0, PRICE_CLOSE, MODE_MAIN, y)) 
   		      { 
   		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
   		         burnarr(obname, HD1[y] + 3 * _Point, 139, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, y), false), clrYellow);
   		         ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   		         if (x == 1 && alerts) lert(_Symbol, "Combo dn arrow FVG", CD1[y], ChartPeriod());
   		         countb++;
   		      }
   		   }
   		}*/
		   obname = Name + "FVGrec" + intrepl;
		   RecMake(obname, linn[x], finn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, C'255,191,223', "FVG: " + DoubleToString(finn[x], _Digits) + " - " + DoubleToString(linn[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
			if (HH1 >= linn[x] && HH < finn[x])
			{
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_COLOR, clrWhite);
   		   ObjectSetInteger(0, Name + "FVGrec" + intrepl, OBJPROP_BACK, false);
   		   /*
   		   obname = Name + "FVGtouch" + intrepl;
   		   if (ChartPeriod() == divisor && counta >= 10) { Texter(obname, finn[x], 3 * Period() * 60, IntegerToString(counta) + " - " + IntegerToString(countb), clrWhite);
   		   ObjectSetString(0, Name + "FVGtouch" + intrepl, OBJPROP_TOOLTIP, "TOUCH - COMBO " + DoubleToString(finn[x], _Digits));
   	      ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER); }
   	      */
			}
		}
		if (finn[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "FVGrec" + intrepl);
			//ObjectDelete(Name + "FVGtouch" + intrepl);
		}
	}
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre1()
{	
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();
	string obname;

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyOpen(_Symbol, divisor, 0, periods + 3, OD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);
	
	for (int x = 1; x <= periods - 4; x++)
	{
		//if (((OD1[x + 3] > CD1[x + 3]) && ((CD1[x + 2] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))) || ((OD1[x + 3] > CD1[x + 3]) && ((CD1[x + 1] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))))
		if ((OD1[x + 3] > CD1[x + 3]) && (((CD1[x + 2] > OD1[x + 2]) && (CD1[x + 1] > OD1[x + 1]) && ((CD1[x + 1] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3]))) || ((CD1[x + 2] > OD1[x + 2]) && ((CD1[x + 2] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3])))))
		{
		   //Print(periods + " " + iBars(_Symbol, divisor) + " " + iBars(_Symbol, PERIOD_CURRENT));
			obfinp[x] = LD1[x + 3];
			oblinp[x] = HD1[x + 3];
		}
		else { obfinp[x] = EMPTY_VALUE; oblinp[x] = EMPTY_VALUE; obsinp[x] = EMPTY_VALUE; }
	}

	for (int x = 1; x <= periods - 4; x++)
	{
		if ((CD1[x + 3] > OD1[x + 3]) && (((OD1[x + 2] > CD1[x + 2]) && (OD1[x + 1] > CD1[x + 1]) && ((OD1[x + 2] - CD1[x + 1]) > (CD1[x + 3] - OD1[x + 3]))) || ((OD1[x + 2] > CD1[x + 2]) && ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3])))))
		{
			obfinn[x] = HD1[x + 3];
			oblinn[x] = LD1[x + 3];
		}
		else { obfinn[x] = EMPTY_VALUE; oblinn[x] = EMPTY_VALUE; obsinn[x] = EMPTY_VALUE; }
	}
   
   int countpos = 0, countneg = 0;
   int z = 0, w = 0;
   int countnewp = 0;
   double newarrayp[];
   double newarrayp1[];
   int countnewn = 0;
   double newarrayn[];
   double newarrayn1[];
   
   double LLa[];
   ArrayResize(LLa, periods);
   
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "b" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];
		double HH1 = HD1[ArrayMaximum(HD1, x, 1)];
		//ArrayFree(LLa);
		ArrayCopy(LLa, CD1, 1, 1, x);
		ArraySort(LLa, 0, 0, MODE_ASCEND);
		
		//double LLa1 = LLa[0];
		//double LLa2 = LLa[1];
		double LLa3 = LLa[2];
		
		if (countpos == check) break;
      if (obfinp[x] != EMPTY_VALUE && x < drawlines && LLa3 > obfinp[x])
		{
	      z += 4;
		   countpos++;
   		//int counta = 0, countb = 0;
		   /*if (ChartPeriod() == divisor)
		   {
   		   for (int y = x; y >= 1; y--)
   		   {
   		      //if (LD1[y] <= oblinp[x]) counta++;
   		      if (LD1[y] <= oblinp[x] && iRSI(_Symbol, divisor, 14, PRICE_CLOSE, y) < 45 && LD1[y] < iBands(_Symbol, divisor, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) 
   		      { 
   		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
   		         burnarr(obname, LD1[y] - 3 * _Point, 140, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, y), false), clrLime);
   		         if (x == 1 && alerts) lert(_Symbol, "Combo up arrow OB", CD1[y], ChartPeriod());
   		         //countb++;
   		      }
   		   }
   		}*/
   		double maxgo = HH1 - oblinp[x];
   		double size = oblinp[x] - obfinp[x];
   		double rr = MathFloor(maxgo / size);
   		
   		bool newton = false;
   		bool moonsoon = false;
   		bool lgrab = false;
         if (x > 2) { if (LD1[x + 2] < LD1[x + 3] || LD1[x + 1] < LD1[x + 3]) lgrab = true; }
   		if(x > 2 && obfinp[x] != EMPTY_VALUE)
   		{
      		for (int y = x + 3; y >= x; y--)
      		{
   		      double hh = MathMax(HD1[y - 1], HD1[y - 2]);
   		      double hh1 = HD1[ArrayMaximum(HD1, 50, y)];
         		if (CD1[y] < CD1[y - 2] && (hh - oblinp[x]) > 2 * (oblinp[x] - obfinp[x]))
         		{
         		   newton = true;
         		}
         		if (HH1 > hh1) moonsoon = true;
            }
         }
   		
		   //obname = Name + "OBsl" + intrepl;
		   //Texter(obname, LD1[x + 3], 3 * Period() * 60, IntegerToString(z / 4) + " " + DoubleToString(rr, 0), clrMediumOrchid);
		   ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		   ObjectSetString(0, Name + "OBsl" + intrepl, OBJPROP_TOOLTIP, IntegerToString(z / 4) + " " + DoubleToString(oblinp[x], _Digits) + " rr: " + DoubleToString(rr, 0));
		   if (Bid < oblinp[x] || iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 10, 1)) < oblinp[x]) 
		   {
		      obname = Name + "OBTouch" + intrepl;
		      RecMake(obname, oblinp[x], obfinp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, clrDodgerBlue, "OB: " + DoubleToString(obfinp[x], _Digits) + " - " + DoubleToString(oblinp[x], _Digits) + " @ " + intrepl);
		      ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		      ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		   }
		   obname = Name + "OBrec" + intrepl;
		   objtrend2(obname, oblinp[x], oblinp[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, 1, 0, clrWhite, "OB: " + DoubleToString(oblinp[x], _Digits) + " - " + DoubleToString(obfinp[x], _Digits) + " @ " + intrepl);
		   obname = Name + "OBreca" + intrepl;
		   RecMake(obname, oblinp[x], obfinp[x], z/*iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false)*/, 0, 3 * Period() * 60, clrWhite, "OB: " + DoubleToString(oblinp[x], _Digits) + " - " + DoubleToString(obfinp[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		   //if (rr >= 3)
			//ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_BACK, true);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		   if (show50)
		   {
		      obname = Name + "50pc" + intrepl;
		      objtrend2(obname, (oblinp[x] + obfinp[x]) / 2, (oblinp[x] + obfinp[x]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, 1, 2, clrWhite, "50%");
		   }
			if (LL < oblinp[x] || rr <= 2)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 1);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 1);
			}
			if (LL > oblinp[x] && LL1 <= oblinp[x] && LL1 > obfinp[x])
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (LL1 > oblinp[x] && rr >= 3)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (clean)
			{
   			if ((!newton || !moonsoon) || rr < 3)
      			{
         			ObjectDelete(0, Name + "OBrec" + intrepl);
         			ObjectDelete(0, Name + "OBreca" + intrepl);
         			ObjectDelete(0, Name + "OBTouch" + intrepl);
         			ObjectDelete(0, Name + "OBsl" + intrepl);
      			}
			}			
			if ((newton && moonsoon) && rr >= 3)
			{
			   countnewp++;
			   ArrayResize(newarrayp, countnewp + 1);
			   ArrayResize(newarrayp1, countnewp + 1);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_TIME, Time[0] + 5 * Period() * 60);
			   newarrayp[countnewp] = oblinp[x];
			   newarrayp1[countnewp] = obfinp[x];
			   newton = false;
			   moonsoon = false;
			   if (lgrab)
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrDimGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrDimGray);
			   }
			   if (LL1 < obfinp[x])
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrGray);
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			   }
			   lgrab = false;
			}
		}
		if (obfinp[x] == EMPTY_VALUE)
		{
		   ObjectDelete(Name + "OBrec" + intrepl);
			ObjectDelete(Name + "OBTouch" + intrepl);
			ObjectDelete(Name + "50pc" + intrepl);
			ObjectDelete(Name + "OBsl" + intrepl);
			ObjectDelete(Name + "OBreca" + intrepl);
		}
	}
	//Print(ArraySize(newarrayp) +  " " + newarrayp[0] + " " + newarrayp[1] + " " + newarrayp[ArraySize(newarrayp) - 1]);
	if (ArraySize(newarrayp) > 0) { if (((LD1[1] - newarrayp[1]) < 10 * _Point) && alerts) lert(_Symbol, "near OB support", newarrayp[1], divisor); }
   
   double LLb[];
   ArrayResize(LLb, periods);
   
	for (int x = 1; x <= periods - 4; x++)
	{
		string intrepl = "s" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, x, 1)];
		double HH1 = HD1[ArrayMaximum(HD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];
      if (countneg == check) break;
      //ArrayFree(LLb);
		ArrayCopy(LLb, CD1, 1, 1, x);
		ArraySort(LLb, 0, 0, MODE_DESCEND);
		
		//double LLb1 = LLb[0];
		//double LLb2 = LLb[1];
		double LLb3 = LLb[2];
		if (obfinn[x] != EMPTY_VALUE && x < drawlines && LLb3 < obfinn[x])
		{
		   w += 4;
		   countneg++;
   		//int counta = 0, countb = 0;
		   /*if (ChartPeriod() == divisor)
		   {
   		   for (int y = x; y >= 1; y--)
   		   {
   		      //if (HD1[y] >= oblinn[x]) counta++;
   		      if (HD1[y] >= oblinn[x] && iRSI(_Symbol, divisor, 14, PRICE_CLOSE, y) > 55 && HD1[y] > iBands(_Symbol, divisor, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) 
   		      { 
   		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
   		         burnarr(obname, HD1[y] + 3 * _Point, 140, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, y), false), clrLime);
   		         ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   		         if (x == 1 && alerts) lert(_Symbol, "Combo dn arrow OB", CD1[y], ChartPeriod());
   		         //countb++;
   		      }
   		   }
   		}*/
   		double maxgo = oblinn[x] - LL1;
   		double size = obfinn[x] - oblinn[x];
   		double rr = MathFloor(maxgo / size);
   		
   		bool newton = false;
   		bool moonsoon = false;
   		bool lgrab = false;
         if (x > 2) { if (HD1[x + 2] > HD1[x + 3] || HD1[x + 1] > HD1[x + 3]) lgrab = true; }
   		if(x > 2 && obfinn[x] != EMPTY_VALUE)
   		{
      		for (int y = x + 3; y >= x; y--)
      		{
   		      double ll = MathMin(LD1[y - 1], LD1[y - 2]);
   		      double ll1 = LD1[ArrayMinimum(LD1, 50, y)];
         		if (CD1[y] > CD1[y - 2] && (oblinn[x] - ll) > 2 * (obfinn[x] - oblinn[x]))
         		{
         		   newton = true;
         		}
         		if (LL1 < ll1) moonsoon = true;
            }
         }

		   //obname = Name + "OBsl" + intrepl;
		   //Texter(obname, HD1[x + 3], 3 * Period() * 60, IntegerToString(w / 4) + " " + DoubleToString(rr, 0), clrMediumOrchid);
		   ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT);
		   ObjectSetString(0, Name + "OBsl" + intrepl, OBJPROP_TOOLTIP, IntegerToString(w / 4) + " " + DoubleToString(oblinn[x], _Digits) + " rr: "  + DoubleToString(rr, 0));
		   if (Bid > oblinn[x] || iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 10, 1)) > oblinn[x])
		   {
		      obname = Name + "OBTouch" + intrepl;
		      RecMake(obname, obfinn[x], oblinn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, clrOrangeRed, "OB: " + DoubleToString(obfinn[x], _Digits) + " - " + DoubleToString(oblinn[x], _Digits) + " @ " + intrepl);
		      ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		      ObjectSetInteger(0, obname, OBJPROP_WIDTH, 2);
		   }
		   obname = Name + "OBrec" + intrepl;
		   objtrend2(obname, oblinn[x], oblinn[x], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, 1, 0, clrWhite, "OB: " + DoubleToString(oblinn[x], _Digits) + " - " + DoubleToString(obfinn[x], _Digits) + " @ " + intrepl);
		   obname = Name + "OBreca" + intrepl;
		   RecMake(obname, obfinn[x], oblinn[x], w/*iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false)*/, 0, 3 * Period() * 60, clrWhite, "OB: " + DoubleToString(oblinn[x], _Digits) + " - " + DoubleToString(obfinn[x], _Digits) + " @ " + intrepl);
		   ObjectSetInteger(0, obname, OBJPROP_BACK, false);
		   //if (rr >= 3)
			//ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_BACK, true);
		   ObjectSetInteger(0, obname, OBJPROP_WIDTH, 1);
		   if (show50)
		   {
   		   obname = Name + "50pc" + intrepl;
   		   objtrend2(obname, (oblinn[x] + obfinn[x]) / 2, (oblinn[x] + obfinn[x]) / 2, iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 3), false), 0, 3 * Period() * 60, 1, 2, clrWhite, "50%");
		   }
			if (HH > oblinn[x] || rr <= 2)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 1);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 1);
			}
			if (HH < oblinn[x] && HH1 >= oblinn[x] && HH1 < obfinn[x])
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (HH1 < oblinn[x] && rr >= 3)
			{
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrYellow);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_WIDTH, 2);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			}
			if (clean)
			{
   			if ((!newton || !moonsoon) || rr < 3)
      			{
         			ObjectDelete(0, Name + "OBrec" + intrepl);
         			ObjectDelete(0, Name + "OBreca" + intrepl);
         			ObjectDelete(0, Name + "OBTouch" + intrepl);
         			ObjectDelete(0, Name + "OBsl" + intrepl);
      			}
			}
			if ((newton && moonsoon) && rr >= 3)
			{
			   countnewn++;
			   ArrayResize(newarrayn, countnewn + 1);
			   ArrayResize(newarrayn1, countnewn + 1);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_COLOR, clrWhite);
			   ObjectSetInteger(0, Name + "OBrec" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_TIME2, Time[0] + 5 * Period() * 60);
			   ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_TIME, Time[0] + 5 * Period() * 60);
			   newarrayn[countnewn] = oblinn[x];
			   newarrayn1[countnewn] = obfinn[x];
			   newton = false;
			   moonsoon = false;
			   if (lgrab)
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrDimGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrDimGray);
			   }
			   if (HH1 > obfinn[x]) 
			   {
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_COLOR, clrGray);
			      ObjectSetInteger(0, Name + "OBsl" + intrepl, OBJPROP_COLOR, clrGray);
			      ObjectSetInteger(0, Name + "OBreca" + intrepl, OBJPROP_WIDTH, 2);
			   }
			   lgrab = false;
			}
		}
		if (obfinn[x] == EMPTY_VALUE)
		{
		   ObjectDelete(Name + "OBrec" + intrepl);
			ObjectDelete(Name + "OBTouch" + intrepl);
			ObjectDelete(Name + "50pc" + intrepl);
			ObjectDelete(Name + "OBsl" + intrepl);
		   ObjectDelete(Name + "OBreca" + intrepl);
		}
	}
	//Print(ArraySize(newarrayn) +  " " + newarrayn[0] + " " + newarrayn[1] + " " + newarrayn[ArraySize(newarrayn) - 1]);
	if (ArraySize(newarrayn) > 0) { if (((newarrayn[1] - HD1[1]) < 10 * _Point) && alerts) lert(_Symbol, "near OB resistance", newarrayn[1], divisor); }
	
	ArrayCopy(newarrn, newarrayn, 0, 0, 0);
	ArrayCopy(newarrn1, newarrayn1, 0, 0, 0);
	ArrayCopy(newarrp, newarrayp, 0, 0, 0);
	ArrayCopy(newarrp1, newarrayp1, 0, 0, 0);
	
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre2()
{
	if (iBars(_Symbol, divisor) < periods)
		periods = iBars(_Symbol, divisor) - 4;
	if (iBars(_Symbol, PERIOD_CURRENT) < periods)
	   periods = iBars(_Symbol, PERIOD_CURRENT) - 4;
   if (divisor == PERIOD_CURRENT) divisor = ChartPeriod();
	string obname;
	
	double nexp[], prep[];
	double nexn[], pren[];
	ArrayResize(prep, periods + 2);
	ArrayResize(nexp, periods + 1);
	ArrayResize(pren, periods + 2);
	ArrayResize(nexn, periods + 1);
	ArrayInitialize(fickp, EMPTY_VALUE);
	ArrayInitialize(fickn, EMPTY_VALUE);

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, divisor, 0, periods + 3, CD1);
	CopyOpen(_Symbol, divisor, 0, periods + 3, OD1);
	CopyHigh(_Symbol, divisor, 0, periods + 3, HD1);
	CopyLow(_Symbol, divisor, 0, periods + 3, LD1);
	CopyTime(_Symbol, divisor, 0, periods + 3, TD1);

	for (int x = 1; x <= periods; x++)
	{
		if ((CD1[x + 1] - OD1[x + 1]) > 0 && (CD1[x] - OD1[x]) > 0)
		{
			prep[x] = CD1[x + 1] - OD1[x + 1];
			nexp[x] = OD1[x] - LD1[x];
			if (nexp[x] < dist * prep[x] && OD1[x + 1] + 0.75 * prep[x] < LD1[x] && (CD1[ArrayMinimum(CD1, x - 1, 1)] >= LD1[x + 1]))
				fickp[x] = LD1[x] - 40 * _Point;
			else
				fickp[x] = EMPTY_VALUE;
		}
	}
	
	for (int x = 1; x <= periods; x++)
	{
		if ((OD1[x + 1] - CD1[x + 1]) > 0 && (OD1[x] - CD1[x]) > 0)
		{
			pren[x] = OD1[x + 1] - CD1[x + 1];
			nexn[x] = HD1[x] - OD1[x];
			if (nexn[x] < dist * pren[x] && OD1[x + 1] - 0.75 * pren[x] > HD1[x] && (CD1[ArrayMaximum(CD1, x - 1, 1)] <= HD1[x + 1]))
				fickn[x] = HD1[x] + 40 * _Point;
			else
				fickn[x] = EMPTY_VALUE;
		}
	}
	
	int countpos = 0, countneg = 0;

   double LLc[];
   ArrayResize(LLc, periods + 1);
   
	for (int x = 1; x <= periods; x++)
	{
		string intrepl = "s" + TimeToStr(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double LL = LD1[ArrayMinimum(LD1, x - 1, 1)];
		if (countpos == check) break;
		//ArrayFree(LLc);
		ArrayCopy(LLc, CD1, 1, 1, x);
		ArraySort(LLc, 0, 0, MODE_ASCEND);
		
		double LLc3 = LLc[2];
		
		if (fickp[x] != EMPTY_VALUE && x < drawlines && (LLc3 >= LD1[x + 1]))// && OD1[x + 1] <= LL) (CD1[ArrayMinimum(CD1, x - 1, 1)] >= LD1[x + 1]))// && OD1[x + 1] <= LL)
		{
		   countpos++;
		   obname = Name + "BasLin" + intrepl;
			objtrend2(obname, OD1[x + 1], OD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 5 * Period() * 60, 1, 0, clrWhite, "OSup1");
			obname = Name + "Lin" + intrepl;
			objtrend2(obname, LD1[x + 1], LD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 5 * Period() * 60, 2, 0, clrWhite, "LSup2");
			obname = Name + "ConLin" + intrepl;
   		objtrend2(obname, LD1[x + 1], OD1[x + 1], 4, 0, 5 * Period() * 60, 2, 0, clrWhite, "SCon");
   		if (LL > OD1[x + 1])
   		{
   		   ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGray);
   		   ObjectDelete(0, Name + "Lin" + intrepl);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGray);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_WIDTH, 1);
   		}
		}
		else if (fickp[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "BasLin" + intrepl);
			ObjectDelete(Name + "Lin" + intrepl);
			ObjectDelete(Name + "ConLin" + intrepl);
		}
	}

   double LLd[];
   ArrayResize(LLd, periods + 1);
   
	for (int x = 1; x <= periods; x++)
	{
		string intrepl = "b" + TimeToStr(TD1[x + 1], TIME_DATE | TIME_MINUTES);
		double HH = HD1[ArrayMaximum(HD1, x - 1, 1)];
		if (countneg == check) break;
		//ArrayFree(LLd);
		ArrayCopy(LLd, CD1, 1, 1, x);
		ArraySort(LLd, 0, 0, MODE_DESCEND);
		
		double LLd3 = LLd[2];
		
		if (fickn[x] != EMPTY_VALUE && x < drawlines && (LLd3 <= HD1[x + 1]))// && OD1[x + 1] >= HH) //(CD1[ArrayMaximum(CD1, x - 1, 1)] <= HD1[x + 1]))// && OD1[x + 1] >= HH)
		{
		   countneg++;
			obname = Name + "BasLin" + intrepl;
			objtrend2(obname, OD1[x + 1], OD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 5 * Period() * 60, 1, 0, clrWhite, "ORes1");
			obname = Name + "Lin" + intrepl;
			objtrend2(obname, HD1[x + 1], HD1[x + 1], iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, divisor, x + 1), false), 0, 5 * Period() * 60, 2, 0, clrWhite, "HRes2");
			obname = Name + "ConLin" + intrepl;
			objtrend2(obname, HD1[x + 1], OD1[x + 1], 4, 0, 5 * Period() * 60, 2, 0, clrWhite, "RCon");
   		if (HH < OD1[x + 1])
   		{
   		   ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGray);
   		   ObjectDelete(0, Name + "Lin" + intrepl);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGray);
   		   ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_WIDTH, 1);
   		}
		}
		else if (fickn[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "BasLin" + intrepl);
			ObjectDelete(Name + "Lin" + intrepl);
			ObjectDelete(Name + "ConLin" + intrepl);
		}
	}
	ArrayResize(newarrn, check + 1);
	ArrayResize(newarrn1, check + 1);
	ArrayResize(newarrp, check + 1);
	ArrayResize(newarrp1, check + 1);
	
	int xcount1 = 0, xcount2 = 0;
	
   double newarrayp[];
   int countnewp = 0;
	for (int x = 1; x < periods; x++)
	{
	   string intrepl = "s" + TimeToStr(TD1[x + 1], TIME_DATE | TIME_MINUTES);
	   if (fickp[x] != EMPTY_VALUE)
	   {
	      for (int y = 1; y < check; y++)
	      {
            if ((OD1[x + 1] < newarrp[y] && OD1[x + 1] > newarrp1[y]) || (LD1[x + 1] < newarrp[y] && LD1[x + 1] > newarrp1[y]))
            {
   			   countnewp++;
   			   ArrayResize(newarrayp, countnewp + 1);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_WIDTH, 2);
			      newarrayp[countnewp] = OD1[x + 1];
            }
	      }
	   }
	}
	if (ArraySize(newarrayp) > 0) { if (((LD1[1] - newarrayp[1]) < 10 * _Point) && alerts) lert(_Symbol, "near FR support", newarrayp[1], divisor); }
	
   double newarrayn[];
   int countnewn = 0;
	for (int x = 1; x < periods; x++)
	{
	   string intrepl = "b" + TimeToStr(TD1[x + 1], TIME_DATE | TIME_MINUTES);
	   if (fickn[x] != EMPTY_VALUE)
	   {
	      for (int y = 1; y < check; y++)
	      {
            if ((OD1[x + 1] < newarrn1[y] && OD1[x + 1] > newarrn[y]) || (HD1[x + 1] < newarrn1[y] && HD1[x + 1] > newarrn[y]))
            {
   			   countnewn++;
   			   ArrayResize(newarrayn, countnewn + 1);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "Lin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "ConLin" + intrepl, OBJPROP_COLOR, clrGold);
               ObjectSetInteger(0, Name + "BasLin" + intrepl, OBJPROP_WIDTH, 2);
			      newarrayn[countnewn] = OD1[x + 1];
            }
	      }
	   }  
	}
	if (ArraySize(newarrayn) > 0) { if (((newarrayn[1]) - HD1[1] < 10 * _Point) && alerts) lert(_Symbol, "near FR resistance", newarrayn[1], divisor); }
}
//+------------------------------------------------------------------+

void lert(string symbol, string text, double price, int period)
{
   Alert(symbol, " ", text, " ", DoubleToString(price, _Digits), " TF: ", IntegerToString(period));
}

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[0] + y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+BUTTON BUILD FUNC-------------------------------------------------+
void buildbut(const string buttonID,
			  const string labelID,
			  const int x,
			  const int y,
			  const int xs,
			  const int ys)
{
	string obname;

	obname = Name + buttonID;
	ObjectCreate(0, obname, OBJ_BUTTON, 0, 0, 0);
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	ObjectSetInteger(0, obname, OBJPROP_BGCOLOR, clrGray);
	ObjectSetInteger(0, obname, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, obname, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, obname, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, obname, OBJPROP_YSIZE, ys);
	ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
	ObjectSetString(0, obname, OBJPROP_TEXT, labelID);
	ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 7);
	ObjectSetInteger(0, obname, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, obname, OBJPROP_STATE, false);
	ObjectSetInteger(0, obname, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, obname, OBJPROP_SELECTED, false);
	ObjectSetString(0, obname, OBJPROP_TOOLTIP, "\n");
}
//Creates the buttons which switch the currencies in currency focus table
//+------------------------------------------------------------------+