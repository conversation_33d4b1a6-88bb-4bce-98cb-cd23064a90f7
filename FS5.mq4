#property indicator_chart_window
#property copyright "sakisf"
#property link "https://www.forexfactory.com/sakisf"

#property strict
#property indicator_buffers 4
#property indicator_color1 clrBlue
#property indicator_color2 clrRed
#property indicator_color3 clrBlue
#property indicator_color4 clrRed

#define Name WindowExpertName()

//+INPUTS------------------------------------------------------------+
//extern int daytocountback = 5000;		  // Lookback period in days
extern int periods = 8000;				  // Candles back to check
int drawlines = 8000;					  // Candles back to mark with trendlines
ENUM_TIMEFRAMES DROP_TF = PERIOD_CURRENT; // Check every X period

double finp[];
double finn[];
double linp[];
double linn[];
//+------------------------------------------------------------------+

//+INIT--------------------------------------------------------------+
int OnInit()
{
	IndicatorBuffers(4);
	IndicatorShortName("FVG /w limit");
	ObjectsDeleteAll(0, Name);
	SetIndexStyle(0, DRAW_NONE, 0, 0);
	SetIndexArrow(0, 233);
	SetIndexBuffer(0, finp);
	SetIndexLabel(0, "");
	SetIndexStyle(1, DRAW_NONE, 0, 0);
	SetIndexArrow(1, 234);
	SetIndexBuffer(1, finn);
	SetIndexLabel(1, "");
	SetIndexStyle(2, DRAW_NONE, 0, 0);
	SetIndexArrow(2, 233);
	SetIndexBuffer(2, linp);
	SetIndexLabel(2, "");
	SetIndexStyle(3, DRAW_NONE, 0, 0);
	SetIndexArrow(3, 234);
	SetIndexBuffer(3, linn);
	SetIndexLabel(3, "");

	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+DEINIT------------------------------------------------------------+
void OnDeinit(const int reason)
{
	ObjectsDeleteAll(0, Name);
	return;
}
//+------------------------------------------------------------------+

//+MAIN PROGRAM------------------------------------------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2023.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("FVG expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf on FF for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{

		bool new_1m_check = false;
		static datetime start_1m_time = 0;
		if (start_1m_time < iTime(NULL, DROP_TF, 0))
		{
			new_1m_check = true;
			start_1m_time = iTime(NULL, DROP_TF, 0);
		}
		if (new_1m_check)
		{
			ObjectsDeleteAll(0, Name);
			ChartRedraw();
			RefreshRates();
			checkpre();
			//if (showdwminfo) ydayweek();
			new_1m_check = false;
		}
	} //YesStop (expiry) end
	   
	return (rates_total);
}
//+------------------------------------------------------------------+

//+MAIN FUNCTION FRACTALS--------------------------------------------+
void checkpre()
{
	if (Bars < periods)
		periods = Bars - 4;
	string obname;
	ArrayInitialize(finp, EMPTY_VALUE);
	ArrayInitialize(finn, EMPTY_VALUE);

	double CD1[], OD1[], HD1[], LD1[];
	datetime TD1[];
	ArraySetAsSeries(CD1, true);
	ArraySetAsSeries(OD1, true);
	ArraySetAsSeries(HD1, true);
	ArraySetAsSeries(LD1, true);
	ArraySetAsSeries(TD1, true);
	ArrayResize(CD1, periods + 3);
	ArrayResize(OD1, periods + 3);
	ArrayResize(HD1, periods + 3);
	ArrayResize(LD1, periods + 3);
	ArrayResize(TD1, periods + 3);
	CopyClose(_Symbol, PERIOD_CURRENT, 0, periods + 3, CD1);
	CopyOpen(_Symbol, PERIOD_CURRENT, 0, periods + 3, OD1);
	CopyHigh(_Symbol, PERIOD_CURRENT, 0, periods + 3, HD1);
	CopyLow(_Symbol, PERIOD_CURRENT, 0, periods + 3, LD1);
	CopyTime(_Symbol, PERIOD_CURRENT, 0, periods + 3, TD1);
	
	for (int x = periods - 3; x >= 1; x--)
	{
		if ((OD1[x + 3] > CD1[x + 3]) && (((CD1[x + 2] > OD1[x + 2]) && (CD1[x + 1] > OD1[x + 1]) && ((CD1[x + 1] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3]))) || ((CD1[x + 2] > OD1[x + 2]) && ((CD1[x + 2] - OD1[x + 2]) > (CD1[x + 3] - OD1[x + 3])))))
		{
			finp[x] = OD1[x + 3];
			linp[x] = HD1[x + 3];			
		}
		else { finp[x] = EMPTY_VALUE; linp[x] = EMPTY_VALUE; }
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		if ((CD1[x + 3] > OD1[x + 3]) && (((OD1[x + 2] > CD1[x + 2]) && (OD1[x + 1] > CD1[x + 1]) && ((OD1[x + 2] - CD1[x + 1]) > (OD1[x + 3] - CD1[x + 3]))) || ((OD1[x + 2] > CD1[x + 2]) && ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3])))))
		{
			finn[x] = OD1[x + 3];
			linn[x] = LD1[x + 3];
		}
		else { finn[x] = EMPTY_VALUE; linn[x] = EMPTY_VALUE; }
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		string intrepl = "b" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double LL = CD1[ArrayMinimum(CD1, x, 1)];
		double LL1 = LD1[ArrayMinimum(LD1, x, 1)];

		if (finp[x] != EMPTY_VALUE && x < drawlines && LL > finp[x])
		{
		   int count = 0, count1 = 0, counta = 0, countb = 0;
		   for (int y = x; y >= 1; y--)
		   {
		      if (LD1[y] <= linp[x]) counta++;
		      
		      if (LD1[y] <= linp[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) < 45) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y], 140, y, clrWhite);
		         count1++;
		      }
		      if (LD1[y] <= linp[x] && LD1[y] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y], 141, y, clrAqua);
		         count++;
		      }
		      if (LD1[y] <= linp[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) < 45 && LD1[y] < iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, y)) 
		      { 
		         obname = Name + "ArrU" + intrepl + IntegerToString(y);
		         burnarr(obname, LD1[y], 142, y, clrYellow);
		         countb++;
		      }
		   }
		   obname = Name + "OBFirst" + intrepl;
		   objtrend2(obname, linp[x], linp[x], x + 3, 0, 3 * Period() * 60, 1, 2, clrWhite, "OB: " + DoubleToString(linp[x], _Digits) + " - " + DoubleToString(finp[x]) + " @" + intrepl);
		   if ((CD1[x + 2] - OD1[x + 2]) > (OD1[x + 3] - CD1[x + 3])) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
		   obname = Name + "OBTouch" + intrepl;
		   Texter(obname, OD1[x + 3], 3 * Period() * 60, "NO touch", clrMediumOrchid);
		   ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
		   obname = Name + "OBSecond" + intrepl;
		   objtrend2(obname, finp[x], finp[x], x + 3, 0, 3 * Period() * 60, 1, 2, clrWhite, "OB: " + DoubleToString(linp[x], _Digits) + " - " + DoubleToString(finp[x], _Digits) + " @" + intrepl);
		   obname = Name + "OBCon" + intrepl;
		   objtrend2(obname, linp[x], finp[x], 4, 0, 3 * Period() * 60, 1, 2, clrWhite,"OBCon: " + DoubleToString(linp[x], _Digits) + " - " +  DoubleToString(finp[x], _Digits) + " @ " + intrepl);
			if (LL1 <= linp[x] && LL > finp[x])
			{
				//ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_COLOR, clrGreen);
				//if (ObjectGet(Name + "OBFirst" + intrepl, OBJPROP_COLOR) == clrYellow)
				//ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_COLOR, clrGold);
				ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_WIDTH, 2);
				//ObjectSetInteger(0, Name + "OBSecond" + intrepl, OBJPROP_COLOR, clrGreen);
				ObjectSetInteger(0, Name + "OBSecond" + intrepl, OBJPROP_WIDTH, 2);
				//ObjectSetInteger(0, Name + "OBCon" + intrepl, OBJPROP_COLOR, clrGreen);
				//ObjectSetInteger(0, Name + "OBCon" + intrepl, OBJPROP_WIDTH, 1);
		      //obname = Name + "OBTouch" + intrepl;
		      ObjectSetString(0, Name + "OBTouch" + intrepl, OBJPROP_TEXT, IntegerToString(counta) + " - " + IntegerToString(count1) + " - " + IntegerToString(count) + " - " + IntegerToString(countb));
		      ObjectSetInteger(0, Name + "OBTouch" + intrepl, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
			}
		}
		if (finp[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "OBFirst" + intrepl);
			ObjectDelete(Name + "OBSecond" + intrepl);
			ObjectDelete(Name + "OBTouch" + intrepl);
		}
	}

	for (int x = periods - 3; x >= 1; x--)
	{
		string intrepl = "s" + TimeToStr(TD1[x + 3], TIME_DATE | TIME_MINUTES);
		double HH = CD1[ArrayMaximum(CD1, x, 1)];
		double HH1 = HD1[ArrayMaximum(HD1, x, 1)];

		if (finn[x] != EMPTY_VALUE && x < drawlines && HH < finn[x])
		{
		   int count = 0, count1 = 0, counta = 0, countb = 0;
		   for (int y = x; y >= 1; y--)
		   {
		      if (HD1[y] >= linn[x]) counta++;
		      
		      if (HD1[y] >= linn[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) > 55) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y], 140, y, clrWhite);
		         ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
		         count1++;
		      }
		      if (HD1[y] >= linn[x] && HD1[y] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y], 141, y, clrAqua);
		         ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
		         count++;
		      }
		      if (HD1[y] >= linn[x] && iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE, y) > 55 && HD1[y] > iBands(_Symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, y)) 
		      { 
		         obname = Name + "ArrD" + intrepl + IntegerToString(y);
		         burnarr(obname, HD1[y], 142, y, clrYellow);
		         ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
		         countb++;
		      }
		   }
		   obname = Name + "OBFirst" + intrepl;
		   objtrend2(obname, linn[x], linn[x], x + 3, 0, 3 * Period() * 60, 1, 2, clrWhite, "OB: " + DoubleToString(linn[x], _Digits) + " - " + DoubleToString(finn[x], _Digits)+ " @ " + intrepl);
		   if ((OD1[x + 2] - CD1[x + 2]) > (CD1[x + 3] - OD1[x + 3])) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrYellow);
		   obname = Name + "OBTouch" + intrepl;
		   Texter(obname, OD1[x + 3], 3 * Period() * 60, "NO touch", clrMediumOrchid);
		   obname = Name + "OBSecond" + intrepl;
		   objtrend2(obname, finn[x], finn[x], x + 3, 0, 3 * Period() * 60, 1, 2, clrWhite, "OB: " + DoubleToString(linn[x], _Digits) + " - " + DoubleToString(finn[x], _Digits)+ " @ " + intrepl);
		   obname = Name + "OBCon" + intrepl;
		   objtrend2(obname, linn[x], finn[x], 4, 0, 3 * Period() * 60, 1, 2, clrWhite, "OBCon: " + DoubleToString(linn[x], _Digits) + " - " + DoubleToString(finn[x], _Digits)+ " @ " + intrepl);
		   if (HH1 >= linn[x] && HH < finn[x])
			{
				//ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_COLOR, clrWhite);
				//if (ObjectGet(Name + "OBFirst" + intrepl, OBJPROP_COLOR) == clrYellow)
				//ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_COLOR, clrGold);
				ObjectSetInteger(0, Name + "OBFirst" + intrepl, OBJPROP_WIDTH, 2);
				//ObjectSetInteger(0, Name + "OBSecond" + intrepl, OBJPROP_COLOR, clrWhite);
				ObjectSetInteger(0, Name + "OBSecond" + intrepl, OBJPROP_WIDTH, 2);
				//ObjectSetInteger(0, Name + "OBCon" + intrepl, OBJPROP_COLOR, clrWhite);
				//ObjectSetInteger(0, Name + "OBCon" + intrepl, OBJPROP_WIDTH, 2);
   		   //obname = Name + "OBTouch" + intrepl;
   		   ObjectSetString(0, Name + "OBTouch" + intrepl, OBJPROP_TEXT, IntegerToString(counta) + " - " + IntegerToString(count1) + " - " + IntegerToString(count) + " - " + IntegerToString(countb));	
   	      //ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
			}
		}
		if (finn[x] == EMPTY_VALUE)
		{
			ObjectDelete(Name + "OBFirst" + intrepl);
			ObjectDelete(Name + "OBSecond" + intrepl);
			ObjectDelete(Name + "OBTouch" + intrepl);
		}
	}
}
//+------------------------------------------------------------------+

//+SUP/RES FUNCTION--------------------------------------------------+
void objtrend2(string name, double pr1, double pr2, int t1, int t2, int t3, int wi, int st, color col, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TREND, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t1]);
	ObjectSet(name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSet(name, OBJPROP_PRICE1, pr1);
	ObjectSet(name, OBJPROP_PRICE2, pr2);
	ObjectSet(name, OBJPROP_STYLE, st);
	ObjectSet(name, OBJPROP_WIDTH, wi);
	ObjectSet(name, OBJPROP_RAY, false);
	ObjectSet(name, OBJPROP_BACK, true);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett + " Price: " + DoubleToStr(pr1, _Digits) + " Date: " + TimeToStr(Time[t1], TIME_DATE));
}
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name, const double pr1, const double pr2, const int t1, const int t2, const int t3, const color BCol, string tett)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, BCol);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[t1]);
	ObjectSetInteger(0, name, OBJPROP_TIME2, Time[t2] + t3);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, pr1);
	ObjectSetDouble(0, name, OBJPROP_PRICE2, pr2);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 0);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, tett);
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[0] + y);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9);
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+

//+ARROW CREATE------------------------------------------------------+
void burnarr(string name, double p, int arrow, int t, color col)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_ARROW, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSet(name, OBJPROP_TIME1, Time[t]);
	ObjectSet(name, OBJPROP_PRICE1, p);
	ObjectSet(name, OBJPROP_ARROWCODE, arrow);
	ObjectSet(name, OBJPROP_COLOR, col);
	ObjectSet(name, OBJPROP_WIDTH, 1);
}
//+------------------------------------------------------------------+