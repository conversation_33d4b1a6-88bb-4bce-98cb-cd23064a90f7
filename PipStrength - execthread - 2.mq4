#property indicator_chart_window
#property strict

//+INPUTS------------------------------------------------------------+

// presentation
enum fontSelect
{
	Arial = 0,	 // Arial
	TNR = 1,	 // Times New Roman
	Calibri = 2, // Calibri
	SegoeUI = 3	 // Segoe UI
};

input fontSelect fselect = 2; // Choose font

string Font;

enum fontSize
{
	seven = 0, // 7
	eight = 1, // 8
	nine = 2,   // 9
	ten = 3   // 10
};

input fontSize fsize = 3; // Fontsize

int FontSize;

// colors
input color FontColor1 = clrBlue;		 // Positive colours
input color FontColor2 = clrRed;		 // Negative colours
input color FontColor3 = clrYellow;		 // Extreme Positive colours
input color FontColor4 = clrBlack;		 // Extreme Negative colours
input color FontColorBGC = clrBlack; // Background colour for current calcs

//+GLOBAL VARIABLES--------------------------------------------------+

// object naming
#define Name WindowExpertName()

// pairs
#define aa "EURUSD"
#define ab "EURCHF"
#define ac "EURGBP"
#define ad "EURJPY"
#define ae "EURAUD"
#define af "EURNZD"
#define ag "EURCAD"
#define bb "USDCHF"
#define bc "GBPCHF"
#define bd "CHFJPY"
#define be "AUDCHF"
#define bf "NZDCHF"
#define bg "CADCHF"
#define cc "GBPUSD"
#define cd "GBPJPY"
#define ce "GBPAUD"
#define cf "GBPNZD"
#define cg "GBPCAD"
#define dd "USDJPY"
#define de "AUDJPY"
#define df "NZDJPY"
#define dg "CADJPY"
#define ee "AUDUSD"
#define ef "AUDNZD"
#define eg "AUDCAD"
#define ff "NZDUSD"
#define fg "NZDCAD"
#define gg "USDCAD"

// set for loop int
int i;
int h;

//big global buffers

// hour
int ochoureur_dblar[15], ochourchf_dblar[15], ochourgbp_dblar[15], ochourjpy_dblar[15], ochouraud_dblar[15], ochournzd_dblar[15], ochourcad_dblar[15], ochourusd_dblar[15];
// thirty
int ochoureur_dblar4[15], ochourchf_dblar4[15], ochourgbp_dblar4[15], ochourjpy_dblar4[15], ochouraud_dblar4[15], ochournzd_dblar4[15], ochourcad_dblar4[15], ochourusd_dblar4[15];
// four
int ochoureur_dblar5[15], ochourchf_dblar5[15], ochourgbp_dblar5[15], ochourjpy_dblar5[15], ochouraud_dblar5[15], ochournzd_dblar5[15], ochourcad_dblar5[15], ochourusd_dblar5[15];
// fifteen
int ochoureur_dblar1[15], ochourchf_dblar1[15], ochourgbp_dblar1[15], ochourjpy_dblar1[15], ochouraud_dblar1[15], ochournzd_dblar1[15], ochourcad_dblar1[15], ochourusd_dblar1[15];
// five
int ochoureur_dblar2[15], ochourchf_dblar2[15], ochourgbp_dblar2[15], ochourjpy_dblar2[15], ochouraud_dblar2[15], ochournzd_dblar2[15], ochourcad_dblar2[15], ochourusd_dblar2[15];
// one
int ochoureur_dblar3[15], ochourchf_dblar3[15], ochourgbp_dblar3[15], ochourjpy_dblar3[15], ochouraud_dblar3[15], ochournzd_dblar3[15], ochourcad_dblar3[15], ochourusd_dblar3[15];
// fill buffers
int ocdayseur_dblar[205], ocdayschf_dblar[205], ocdaysgbp_dblar[205], ocdaysjpy_dblar[205], ocdaysaud_dblar[205], ocdaysnzd_dblar[205], ocdayscad_dblar[205], ocdaysusd_dblar[205];
int oldayseur_dblar[205], oldayschf_dblar[205], oldaysgbp_dblar[205], oldaysjpy_dblar[205], oldaysaud_dblar[205], oldaysnzd_dblar[205], oldayscad_dblar[205], oldaysusd_dblar[205];
int ohdayseur_dblar[205], ohdayschf_dblar[205], ohdaysgbp_dblar[205], ohdaysjpy_dblar[205], ohdaysaud_dblar[205], ohdaysnzd_dblar[205], ohdayscad_dblar[205], ohdaysusd_dblar[205];
int ocwayseur_dblar[30], ocwayschf_dblar[30], ocwaysgbp_dblar[30], ocwaysjpy_dblar[30], ocwaysaud_dblar[30], ocwaysnzd_dblar[30], ocwayscad_dblar[30], ocwaysusd_dblar[30];
int olwayseur_dblar[30], olwayschf_dblar[30], olwaysgbp_dblar[30], olwaysjpy_dblar[30], olwaysaud_dblar[30], olwaysnzd_dblar[30], olwayscad_dblar[30], olwaysusd_dblar[30];
int ohwayseur_dblar[30], ohwayschf_dblar[30], ohwaysgbp_dblar[30], ohwaysjpy_dblar[30], ohwaysaud_dblar[30], ohwaysnzd_dblar[30], ohwayscad_dblar[30], ohwaysusd_dblar[30];
int ocmayseur_dblar[8], ocmayschf_dblar[8], ocmaysgbp_dblar[8], ocmaysjpy_dblar[8], ocmaysaud_dblar[8], ocmaysnzd_dblar[8], ocmayscad_dblar[8], ocmaysusd_dblar[8];
int olmayseur_dblar[8], olmayschf_dblar[8], olmaysgbp_dblar[8], olmaysjpy_dblar[8], olmaysaud_dblar[8], olmaysnzd_dblar[8], olmayscad_dblar[8], olmaysusd_dblar[8];
int ohmayseur_dblar[8], ohmayschf_dblar[8], ohmaysgbp_dblar[8], ohmaysjpy_dblar[8], ohmaysaud_dblar[8], ohmaysnzd_dblar[8], ohmayscad_dblar[8], ohmaysusd_dblar[8];
// avg buffers (avgfill)
double euravg[15], gbpavg[15], chfavg[15], jpyavg[15], audavg[15], nzdavg[15], cadavg[15], usdavg[15], euravgs[12], chfavgs[12], gbpavgs[12], jpyavgs[12], audavgs[12], nzdavgs[12], cadavgs[12], usdavgs[12];
// avg DWM buffers (avgCalcs)
double euravd[3], euravw[3], euravm[3];
double chfavd[3], chfavw[3], chfavm[3];
double gbpavd[3], gbpavw[3], gbpavm[3];
double jpyavd[3], jpyavw[3], jpyavm[3];
double audavd[3], audavw[3], audavm[3];
double nzdavd[3], nzdavw[3], nzdavm[3];
double cadavd[3], cadavw[3], cadavm[3];
double usdavd[3], usdavw[3], usdavm[3];
double eurcavg[3], chfcavg[3], gbpcavg[3], jpycavg[3], audcavg[3], nzdcavg[3], cadcavg[3], usdcavg[3];

double avgeurl, avgeurh, avgchfl, avgchfh, avggbpl, avggbph, avgjpyl, avgjpyh, avgaudl, avgaudh, avgnzdl, avgnzdh, avgcadl, avgcadh, avgusdl, avgusdh;
//hourly
bool Eualh, Chalh, Gualh, Jpalh, Aualh, Nzalh, Caalh, Usalh;
// enable debugging prints
bool debugpr = false;

bool firstrun; //first run bool
bool timer;
//+------------------------------------------------------------------+
//| Custom indicator initialization function |
//+------------------------------------------------------------------+
int OnInit()
{
	//uint start1 = GetTickCount();
	//1st phase
	firstrun = true;
	DeleteObjects();
	StaticBuilds();
	FontSelect();
	FontSize1();
	//2nd phase
   ThirtyCalcs();
	HourlyCalcs();
	FourCalcs();
	FifteenCalcs();
	FiveCalcs();
	OneCalcs();
	FillTodayBuffer();
	FillPastBuffer();
	avgCalcs();
	avgfill();
	RunnerTab();
	//uint end1 = GetTickCount(); Print(end1 - start1);
	
	/* If going to use timer
	OnTimer();
	EventSetTimer(300);
	*/
	EventSetTimer(1);
	timer = true;
	
	Print("Init done. Values loaded.");
	return (INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	//EventKillTimer();
	Comment(""); // Cleanup
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	EventKillTimer();
	return;
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom indicator iteration function |
//+------------------------------------------------------------------+
//+ON CALCULATE MAIN CALC,BUILDS & FUNCTIONS LOAD--------------------+
int OnCalculate(const int rates_total,
				const int prev_calculated,
				const datetime &time[],
				const double &open[],
				const double &high[],
				const double &low[],
				const double &close[],
				const long &tick_volume[],
				const long &volume[],
				const int &spread[])
{
	datetime expiry = D'2025.12.31 00:00'; // Set expiry date
	bool YesStop = false;

	if (TimeCurrent() > expiry)
	{
		Print("PipStrength expired on " + TimeToStr(expiry, TIME_DATE) + ", contact sakisf for an update/new version.");
		YesStop = true;
	}

	if (YesStop != true)
	{
		//AlertsHour();
		
		bool new_5m_check = false;
		static datetime start_5m_time = 0;
		if (start_5m_time < iTime(_Symbol, 5, 0))
		{
			new_5m_check = true;
			start_5m_time = iTime(_Symbol, 5, 0);
		}
		if (new_5m_check)
		{
			Eualh = true;
			Chalh = true;
			Gualh = true;
			Jpalh = true;
			Aualh = true;
			Nzalh = true;
			Caalh = true;
			Usalh = true;
			new_5m_check = false;
		}
		//Execute on new daily bar
		if (firstrun || New_Hourly_Bar())
		{
		   updatesAvg();
		}
		if (firstrun || New_Daily_Bar())
		{
		   BuildPastDays();
		   DoAvg();
		}
		   
		//Close init:
		firstrun = false;
				
	   if (DayOfWeek() == 5 && TimeHour(TimeCurrent()) == 23 && TimeMinute(TimeCurrent()) >= 52 && timer == true) { Print("Timer dead"); EventKillTimer(); timer = false; }
	   if (DayOfWeek() == 1 && (TimeHour(TimeCurrent()) == 0 && TimeMinute(TimeCurrent()) >= 1) && (TimeHour(TimeCurrent()) == 0 && TimeMinute(TimeCurrent()) <= 5) && timer == false) { Print("Timer up"); EventSetTimer(1); timer = true; }
	   
	}
	//--- return value of prev_calculated for next call
	return (rates_total);
}
//+------------------------------------------------------------------+

void OnTimer()
{
   BuildThirty();
	BuildHourly();
	BuildFour();
	BuildFifteen();
	BuildFive();
	BuildOne();
	BuildDaily();
	BuildWMTable();
	RunnerTab();
   dxya();
	updatesAvg2();
}

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//++++STATIC BUILDS (RECTANGLES, LABELS ETC)-------------------------+
void StaticBuilds()
{
	//uint start=GetTickCount();
   {
      string obname;
      int x = 320;
      int y = 85;
      obname = Name + " " + "recdxy";
		RecMake(obname, x - 5, y - 5, 113, 25, clrGold);    
		obname = Name + " " + "dxy";
		LabelMake(obname, 0, x, y - 2, "DXY : ", 12, clrBlack);
		ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		obname = Name + " " + "dxyval";
		LabelMake(obname, 0, x + 45, y - 2, "", 12, clrBlack);
		ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
   }		
	{ //Daily table
			string obname;
			int x = 10;
			int y = 50;
			int xs = 80;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "dailyrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 3 * ys - 5, FontColorBGC);
         obname = Name + " " + "cad" + "D1";
         LabelMake(obname, 0, x, y, "D:", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "cad" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //30M table
			string obname;
			int x = 10;
			int y = 130;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "thirtyrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "Dca";
         LabelMake(obname, 0, x, y + (-1) * ys, "M30", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "D1ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //Hourly table
			string obname;
			int x = 10;
			int y = 400;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "hourlyrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "ca";
         LabelMake(obname, 0, x, y + (-1) * ys, "H1", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "1ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //Four table
			string obname;
			int x = 10;
			int y = 670;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "fourrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "Fca";
         LabelMake(obname, 0, x, y + (-1) * ys, "H4", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "F1ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //Fifteen table
			string obname;
			int x = 380;
			int y = 130;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "fifteenrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "2ca";
         LabelMake(obname, 0, x, y + (-1) * ys, "M15", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "3ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //Five table
			string obname;
			int x = 380;
			int y = 400;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "fiverec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "4ca";
         LabelMake(obname, 0, x, y + (-1) * ys, "M5", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "5ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //One table
			string obname;
			int x = 380;
			int y = 670;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "onerec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs, 13 * ys + 5, FontColorBGC);
         obname = Name + " " + "6ca ";
         LabelMake(obname, 0, x, y + (-1) * ys, "M1", 12, clrDodgerBlue);
         ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "7ca" + labs1[i];
			   LabelMake(obname, 0, x + (i + 1) * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
   { //RunnerTab
      
   	string obname;
   	int x = 750;
   	int xs = 60;
   	int y = 50;
   	int ys = 20;
   	obname = Name + " " + "TabR";
   	RecMake(obname, x - 5, y - 25, (8 * xs) + 10, (14 * ys) + 10, FontColorBGC);
   	string label[8] = {"EUR:", "CHF:", "GBP:", "JPY:", "AUD:", "NZD:", "CAD:", "USD:"};
   	for (i = 7; i >= 0; i--)
   	{
   		obname = Name + " " + "TabLabs" + label[i];
   		LabelMake(obname, 0, x + 10 + (i * xs), y - 20, label[i], FontSize, clrDodgerBlue);
   	}
   }
	{ //WM table
			string obname;
			int x = 1250;
			int y = 50;
			int xs = 40;
			int ys = 20;
			string labs1[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "WMrec";
			RecMake(obname, x - 5, y - ys - 5, 20 + 15 * xs, 8 * ys + 5, FontColorBGC);
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "9ca" + labs1[i];
			   LabelMake(obname, 0, x + 4 * i * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			   if (i > 3) LabelMake(obname, 0, x + 4 * (i - 4) * xs, y + 3 * ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //OWM table
			string obname;
			int x = 1480;
			int y = 345;
			int xs = 40;
			int ys = 15;
			string labs1[8] = {"EUR", "CHF", "GBP", "AUD", "NZD", "CAD", "JPY", "USD"};
			obname = Name + " " + "OWMrec";
			RecMake(obname, x - 5, y - ys - 5, 9 * xs + 20, 38 * ys + 10, FontColorBGC);
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "10ca" + labs1[i];
			   LabelMake(obname, 0, 40 + x + 3 * i * xs, y - ys, labs1[i], FontSize, clrDodgerBlue);
			   if (i > 2) LabelMake(obname, 0, 40 + x + 3 * (i - 3) * xs, y + 12 * ys, labs1[i], FontSize, clrDodgerBlue);
			   if (i > 5) LabelMake(obname, 0, 100 + x + 3 * (i - 6) * xs, y + 25 * ys, labs1[i], FontSize, clrDodgerBlue);
			}
   }
	{ //Avg table
			string obname;
			int x = 750;
			int y = 350;
			int xs = 90;
			int ys = 20;
			string labs50[8] = {"EUR", "CHF", "GBP", "JPY", "AUD", "NZD", "CAD", "USD"};
			obname = Name + " " + "dailyavg";
			RecMake(obname, x - 5, y - ys - 5, 8 * xs, 29 * ys + 5, FontColorBGC);
			for (i = 7; i >= 0; i--)
			{
			   obname = Name + " " + "avg" + labs50[i];
			   LabelMake(obname, 0, x + 15 + i * xs, y - ys, labs50[i], FontSize, clrDodgerBlue);
			}
   }  
	//uint end=GetTickCount()-start; Print("BuildStaticBuilds function took ms: ",end);
}
//+------------------------------------------------------------------+

//+DXY---------------------------------------------------------------+
void dxya()
{
	double bid = MarketInfo("USDSEK", MODE_BID);
	if (GetLastError() == 4106)
	{ // unknown symbol
		ObjectSetString(0, Name + " " + "dxyval", OBJPROP_TEXT, "No calc");
	}
	else
	{
   	double dxy = NormalizeDouble(50.14348112 * MathPow(MarketInfo(aa, MODE_BID), -0.576) * MathPow(MarketInfo(dd, MODE_BID), 0.136) * MathPow(MarketInfo(cc, MODE_BID), -0.119) * MathPow(MarketInfo(gg, MODE_BID), 0.091) * MathPow(MarketInfo("USDSEK", MODE_BID), 0.042) * MathPow(MarketInfo(bb, MODE_BID), 0.036), 3);
	   ObjectSetString(0, Name + " " + "dxyval", OBJPROP_TEXT, DoubleToString(dxy, 3));
	}
}
//+------------------------------------------------------------------+

void updatesAvg2()
{
   avgfill2();
   
	int x = 750;	 // initial x distance
	int y = 350; // initial y distance
	int xs = 90; // xstep
	int ys = 15; // ystep
	
	double eur10 = ((ocdayseur_dblar[0] + eurcavg[0]) / 10);
	double eur30 = (eurcavg[1] / 20);
	double eur50 = (eurcavg[2] / 20);
	double chf10 = ((ocdayschf_dblar[0] + chfcavg[0]) / 10);
	double chf30 = (chfcavg[1] / 20);
	double chf50 = (chfcavg[2] / 20);
	double gbp10 = ((ocdaysgbp_dblar[0] + gbpcavg[0]) / 10);
	double gbp30 = (gbpcavg[1] / 20);
	double gbp50 = (gbpcavg[2] / 20);
	double jpy10 = ((ocdaysjpy_dblar[0] + jpycavg[0]) / 10);
	double jpy30 = (jpycavg[1] / 20);
	double jpy50 = (jpycavg[2] / 20);
	double aud10 = ((ocdaysaud_dblar[0] + audcavg[0]) / 10);
	double aud30 = (audcavg[1] / 20);
	double aud50 = (audcavg[2] / 20);
	double nzd10 = ((ocdaysnzd_dblar[0] + nzdcavg[0]) / 10);
	double nzd30 = (nzdcavg[1] / 20);
	double nzd50 = (nzdcavg[2] / 20);
	double cad10 = ((ocdayscad_dblar[0] + cadcavg[0]) / 10);
	double cad30 = (cadcavg[1] / 20);
	double cad50 = (cadcavg[2] / 20);
	double usd10 = ((ocdaysusd_dblar[0] + usdcavg[0]) / 10);
	double usd30 = (usdcavg[1] / 20);
	double usd50 = (usdcavg[2] / 20);
	
	string obname;
   obname = Name + " avgEURmt";
   LabelMake(obname, 0, x, y + 37 * ys, DoubleToString((2.3 * eur10 + 1.7 * eur30 + eur50) / 5, 1) + "/" + DoubleToString(eur10, 0) + "/" + DoubleToString(eur30, 0) + "/" + DoubleToString(eur50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgCHFmt";
   LabelMake(obname, 0, x + xs, y + 37 * ys, DoubleToString((2.3 * chf10 + 1.7 * chf30 + chf50) / 5, 1) + "/" + DoubleToString(chf10, 0) + "/" + DoubleToString(chf30, 0) + "/" + DoubleToString(chf50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgGBPmt";
   LabelMake(obname, 0, x + 2 * xs, y + 37 * ys, DoubleToString((2.3 * gbp10 + 1.7 * gbp30 + gbp50) / 5, 1) + "/" + DoubleToString(gbp10, 0) + "/" + DoubleToString(gbp30, 0) + "/" + DoubleToString(gbp50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgJPYmt";
   LabelMake(obname, 0, x + 3 * xs, y + 37 * ys, DoubleToString((2.3 * jpy10 + 1.7 * jpy30 + jpy50) / 5, 1) + "/" + DoubleToString(jpy10, 0) + "/" + DoubleToString(jpy30, 0) + "/" + DoubleToString(jpy50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgAUDmt";
   LabelMake(obname, 0, x + 4 * xs, y + 37 * ys, DoubleToString((2.3 * aud10 + 1.7 * aud30 + aud50) / 5, 1) + "/" + DoubleToString(aud10, 0) + "/" + DoubleToString(aud30, 0) + "/" + DoubleToString(aud50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgNZDmt";
   LabelMake(obname, 0, x + 5 * xs, y + 37 * ys, DoubleToString((2.3 * nzd10 + 1.7 * nzd30 + nzd50) / 5, 1) + "/" + DoubleToString(nzd10, 0) + "/" + DoubleToString(nzd30, 0) + "/" + DoubleToString(nzd50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgCADmt";
   LabelMake(obname, 0, x + 6 * xs, y + 37 * ys, DoubleToString((2.3 * cad10 + 1.7 * cad30 + cad50) / 5, 1) + "/" + DoubleToString(cad10, 0) + "/" + DoubleToString(cad30, 0) + "/" + DoubleToString(cad50, 0), FontSize - 2, clrWhite);
   obname = Name + " avgUSDmt";
   LabelMake(obname, 0, x + 7 * xs, y + 37 * ys, DoubleToString((2.3 * usd10 + 1.7 * usd30 + usd50) / 5, 1) + "/" + DoubleToString(usd10, 0) + "/" + DoubleToString(usd30, 0) + "/" + DoubleToString(usd50, 0), FontSize - 2, clrWhite);
}
   

//+PERC UPDATES------------------------------------------------------+
void updatesAvg()
{
   avgCalcs();
   avgfill();
   
   ObjectSetString(0, Name + " perc EUR1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldayseur_dblar[0]) + MathAbs(ohdayseur_dblar[0])) / (MathAbs(euravg[0]) + MathAbs(euravg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc CHF1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldayschf_dblar[0]) + MathAbs(ohdayschf_dblar[0])) / (MathAbs(chfavg[0]) + MathAbs(chfavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc GBP1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldaysgbp_dblar[0]) + MathAbs(ohdaysgbp_dblar[0])) / (MathAbs(gbpavg[0]) + MathAbs(gbpavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc JPY1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldaysjpy_dblar[0]) + MathAbs(ohdaysjpy_dblar[0])) / (MathAbs(jpyavg[0]) + MathAbs(jpyavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc AUD1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldaysaud_dblar[0]) + MathAbs(ohdaysaud_dblar[0])) / (MathAbs(audavg[0]) + MathAbs(audavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc NZD1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldaysnzd_dblar[0]) + MathAbs(ohdaysnzd_dblar[0])) / (MathAbs(nzdavg[0]) + MathAbs(nzdavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc CAD1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldayscad_dblar[0]) + MathAbs(ohdayscad_dblar[0])) / (MathAbs(cadavg[0]) + MathAbs(cadavg[2]))) * 100, 2) + "%");
   ObjectSetString(0, Name + " perc USD1-10", OBJPROP_TEXT, DoubleToString(((MathAbs(oldaysusd_dblar[0]) + MathAbs(ohdaysusd_dblar[0])) / (MathAbs(usdavg[0]) + MathAbs(usdavg[2]))) * 100, 2) + "%");
   
	ObjectSetString(0, Name + " " + "EURDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]), 2));
	if ((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]) > 75)
	ObjectSetString(0, Name + " " + "EURDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "EURDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "EURDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]), 2));
	if ((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]) > 75)
	ObjectSetString(0, Name + " " + "EURDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "EURDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "EURDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]), 2) + " / " + DoubleToString(100 * ocmayseur_dblar[0] / (MathAbs(euravm[0]) + euravm[2]), 1));
	if ((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]) > 75)
	ObjectSetString(0, Name + " " + "EURDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "EURDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "CHFDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]), 2));
	if ((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]) > 75)
	ObjectSetString(0, Name + " " + "CHFDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CHFDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "CHFDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]), 2));
	if ((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]) > 75)
	ObjectSetString(0, Name + " " + "CHFDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CHFDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "CHFDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]), 2) + " / " + DoubleToString(100 * ocmayschf_dblar[0] / (MathAbs(chfavm[0]) + chfavm[2]), 1));
	if ((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]) > 75)
	ObjectSetString(0, Name + " " + "CHFDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CHFDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "GBPDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]), 2));
	if ((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]) > 75)
	ObjectSetString(0, Name + " " + "GBPDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "GBPDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "GBPDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]), 2));
	if ((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]) > 75)
	ObjectSetString(0, Name + " " + "GBPDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "GBPDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "GBPDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]), 2) + " / " + DoubleToString(100 * ocmaysgbp_dblar[0] / (MathAbs(gbpavm[0]) + gbpavm[2]), 1));
	if ((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]) > 75)
	ObjectSetString(0, Name + " " + "GBPDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "GBPDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "JPYDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]), 2));
	if ((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]) > 75)
	ObjectSetString(0, Name + " " + "JPYDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "JPYDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "JPYDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]), 2));
	if ((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]) > 75)
	ObjectSetString(0, Name + " " + "JPYDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "JPYDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "JPYDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]), 2) + " / " + DoubleToString(100 * ocmaysjpy_dblar[0] / (MathAbs(jpyavm[0]) + jpyavm[2]), 1));
	if ((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]) > 75)
	ObjectSetString(0, Name + " " + "JPYDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "JPYDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "AUDDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]), 2));
	if ((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]) > 75)
	ObjectSetString(0, Name + " " + "AUDDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "AUDDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "AUDDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]), 2));
	if ((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]) > 75)
	ObjectSetString(0, Name + " " + "AUDDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "AUDDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "AUDDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]), 2) + " / " + DoubleToString(100 * ocmaysaud_dblar[0] / (MathAbs(audavm[0]) + audavm[2]), 1));
	if ((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]) > 75)
	ObjectSetString(0, Name + " " + "AUDDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "AUDDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "NZDDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]), 2));
	if ((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]) > 75)
	ObjectSetString(0, Name + " " + "NZDDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "NZDDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "NZDDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]), 2));
	if ((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]) > 75)
	ObjectSetString(0, Name + " " + "NZDDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "NZDDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "NZDDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]), 2) + " / " + DoubleToString(100 * ocmaysnzd_dblar[0] / (MathAbs(nzdavm[0]) + nzdavm[2]), 1));
	if ((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]) > 75)
	ObjectSetString(0, Name + " " + "NZDDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "NZDDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "CADDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]), 2));
	if ((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]) > 75)
	ObjectSetString(0, Name + " " + "CADDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CADDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "CADDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]), 2));
	if ((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]) > 75)
	ObjectSetString(0, Name + " " + "CADDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CADDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "CADDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]), 2) + " / " + DoubleToString(100 * ocmayscad_dblar[0] / (MathAbs(cadavm[0]) + cadavm[2]), 1));
	if ((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]) > 75)
	ObjectSetString(0, Name + " " + "CADDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "CADDWM9", OBJPROP_COLOR, clrAqua);
	
	ObjectSetString(0, Name + " " + "USDDWM3", OBJPROP_TEXT, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]), 2));
	if ((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]) > 75)
	ObjectSetString(0, Name + " " + "USDDWM3", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]) > 90)
	ObjectSetInteger(0, Name + " " + "USDDWM3", OBJPROP_COLOR, clrLime);
	ObjectSetString(0, Name + " " + "USDDWM6", OBJPROP_TEXT, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]), 2));
	if ((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]) > 75)
	ObjectSetString(0, Name + " " + "USDDWM6", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]) > 90)
	ObjectSetInteger(0, Name + " " + "USDDWM6", OBJPROP_COLOR, clrMagenta);
	ObjectSetString(0, Name + " " + "USDDWM9", OBJPROP_TEXT, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]), 2) + " / " + DoubleToString(100 * ocmaysusd_dblar[0] / (MathAbs(usdavm[0]) + usdavm[2]), 1));
	if ((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]) > 75)
	ObjectSetString(0, Name + " " + "USDDWM9", OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]) > 90)
	ObjectSetInteger(0, Name + " " + "USDDWM9", OBJPROP_COLOR, clrAqua);
}
//+------------------------------------------------------------------+

//+FONTSELECT--------------------------------------------------------+
void FontSelect()
{
	if (fselect == 0)
	{
		Font = "Arial";
	}
	if (fselect == 1)
	{
		Font = "Times New Roman";
	}
	if (fselect == 2)
	{
		Font = "Calibri";
	}
	if (fselect == 3)
	{
		Font = "Segoe UI";
	}
}
//+------------------------------------------------------------------+

//+FONTSIZE----------------------------------------------------------+
void FontSize1()
{
	if (fsize == 0)
	{
		FontSize = 7;
	}
	if (fsize == 1)
	{
		FontSize = 8;
	}
	if (fsize == 2)
	{
		FontSize = 9;
	}
	if (fsize == 3)
	{
		FontSize = 10;
	}
}
//+------------------------------------------------------------------+

//+THIRTY MIN BUILD--------------------------------------------------+
void BuildThirty()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M30, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M15, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_M15, 0) + 210)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M30, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		ThirtyCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar4[0] = (choh4(aa, n1, 0) + choh4(ab, n1, 0) + choh4(ac, n1, 0) + choh4(ad, n2, 0) + choh4(ae, n1, 0) + choh4(af, n1, 0) + choh4(ag, n1, 0));
		ochourchf_dblar4[0] = (-choh4(ab, n1, 0) - choh4(bb, n1, 0) - choh4(bc, n1, 0) + choh4(bd, n2, 0) - choh4(be, n1, 0) - choh4(bf, n1, 0) - choh4(bg, n1, 0));
		ochourgbp_dblar4[0] = (-choh4(ac, n1, 0) + choh4(bc, n1, 0) + choh4(cc, n1, 0) + choh4(cd, n2, 0) + choh4(ce, n1, 0) + choh4(cf, n1, 0) + choh4(cg, n1, 0));
		ochourjpy_dblar4[0] = (-choh4(ad, n2, 0) - choh4(bd, n2, 0) - choh4(cd, n2, 0) - choh4(dd, n2, 0) - choh4(de, n2, 0) - choh4(df, n2, 0) - choh4(dg, n2, 0));
		ochouraud_dblar4[0] = (-choh4(ae, n1, 0) + choh4(be, n1, 0) - choh4(ce, n1, 0) + choh4(de, n2, 0) + choh4(ee, n1, 0) + choh4(ef, n1, 0) + choh4(eg, n1, 0));
		ochournzd_dblar4[0] = (-choh4(af, n1, 0) + choh4(bf, n1, 0) - choh4(cf, n1, 0) + choh4(df, n2, 0) - choh4(ef, n1, 0) + choh4(ff, n1, 0) + choh4(fg, n1, 0));
		ochourcad_dblar4[0] = (-choh4(ag, n1, 0) + choh4(bg, n1, 0) - choh4(cg, n1, 0) + choh4(dg, n2, 0) - choh4(eg, n1, 0) - choh4(fg, n1, 0) - choh4(gg, n1, 0));
		ochourusd_dblar4[0] = (-choh4(aa, n1, 0) + choh4(bb, n1, 0) - choh4(cc, n1, 0) + choh4(dd, n2, 0) - choh4(ee, n1, 0) - choh4(ff, n1, 0) + choh4(gg, n1, 0));
	}

	int x = 10;	 // initial x distance
	int y = 130; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
	
   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_M30, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "Dca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(1800 + a - i * 1800, TIME_MINUTES), FontSize, clrDodgerBlue);
		ObjectSetString(0, obname, OBJPROP_TOOLTIP, TimeToString(1800 + a - i * 1800, TIME_DATE|TIME_MINUTES));
		//Print(TimeToString(3600 + a - i * 3600, TIME_MINUTES));
	}

	{ // EUR
		hourbuild4("eurH1", ochoureur_dblar4, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild4("chfH1", ochourchf_dblar4, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild4("gbpH1", ochourgbp_dblar4, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild4("jpyH1", ochourjpy_dblar4, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild4("audH1", ochouraud_dblar4, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild4("nzdH1", ochournzd_dblar4, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild4("cadH1", ochourcad_dblar4, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild4("usdH1", ochourusd_dblar4, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+HOURLY BUILD------------------------------------------------------+
void BuildHourly()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M30, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M30, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_M30, 0) + 210)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M30, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		HourlyCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar[0] = (choh(aa, n1, 0) + choh(ab, n1, 0) + choh(ac, n1, 0) + choh(ad, n2, 0) + choh(ae, n1, 0) + choh(af, n1, 0) + choh(ag, n1, 0));
		ochourchf_dblar[0] = (-choh(ab, n1, 0) - choh(bb, n1, 0) - choh(bc, n1, 0) + choh(bd, n2, 0) - choh(be, n1, 0) - choh(bf, n1, 0) - choh(bg, n1, 0));
		ochourgbp_dblar[0] = (-choh(ac, n1, 0) + choh(bc, n1, 0) + choh(cc, n1, 0) + choh(cd, n2, 0) + choh(ce, n1, 0) + choh(cf, n1, 0) + choh(cg, n1, 0));
		ochourjpy_dblar[0] = (-choh(ad, n2, 0) - choh(bd, n2, 0) - choh(cd, n2, 0) - choh(dd, n2, 0) - choh(de, n2, 0) - choh(df, n2, 0) - choh(dg, n2, 0));
		ochouraud_dblar[0] = (-choh(ae, n1, 0) + choh(be, n1, 0) - choh(ce, n1, 0) + choh(de, n2, 0) + choh(ee, n1, 0) + choh(ef, n1, 0) + choh(eg, n1, 0));
		ochournzd_dblar[0] = (-choh(af, n1, 0) + choh(bf, n1, 0) - choh(cf, n1, 0) + choh(df, n2, 0) - choh(ef, n1, 0) + choh(ff, n1, 0) + choh(fg, n1, 0));
		ochourcad_dblar[0] = (-choh(ag, n1, 0) + choh(bg, n1, 0) - choh(cg, n1, 0) + choh(dg, n2, 0) - choh(eg, n1, 0) - choh(fg, n1, 0) - choh(gg, n1, 0));
		ochourusd_dblar[0] = (-choh(aa, n1, 0) + choh(bb, n1, 0) - choh(cc, n1, 0) + choh(dd, n2, 0) - choh(ee, n1, 0) - choh(ff, n1, 0) + choh(gg, n1, 0));
	}

	int x = 10;	 // initial x distance
	int y = 400; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
	
   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_H1, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "ca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(3600 + a - i * 3600, TIME_MINUTES), FontSize, clrDodgerBlue);
		ObjectSetString(0, obname, OBJPROP_TOOLTIP, TimeToString(3600 + a - i * 3600, TIME_DATE|TIME_MINUTES));
		//Print(TimeToString(3600 + a - i * 3600, TIME_MINUTES));
	}

	{ // EUR
		hourbuild("eurH1", ochoureur_dblar, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild("chfH1", ochourchf_dblar, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild("gbpH1", ochourgbp_dblar, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild("jpyH1", ochourjpy_dblar, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild("audH1", ochouraud_dblar, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild("nzdH1", ochournzd_dblar, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild("cadH1", ochourcad_dblar, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild("usdH1", ochourusd_dblar, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+FOUR HOUR BUILD---------------------------------------------------+
void BuildFour()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M30, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M30, 0) + 35) && TimeCurrent() <= (iTime(NULL, PERIOD_M30, 0) + 210)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M30, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		FourCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar5[0] = (choh5(aa, n1, 0) + choh5(ab, n1, 0) + choh5(ac, n1, 0) + choh5(ad, n2, 0) + choh5(ae, n1, 0) + choh5(af, n1, 0) + choh5(ag, n1, 0));
		ochourchf_dblar5[0] = (-choh5(ab, n1, 0) - choh5(bb, n1, 0) - choh5(bc, n1, 0) + choh5(bd, n2, 0) - choh5(be, n1, 0) - choh5(bf, n1, 0) - choh5(bg, n1, 0));
		ochourgbp_dblar5[0] = (-choh5(ac, n1, 0) + choh5(bc, n1, 0) + choh5(cc, n1, 0) + choh5(cd, n2, 0) + choh5(ce, n1, 0) + choh5(cf, n1, 0) + choh5(cg, n1, 0));
		ochourjpy_dblar5[0] = (-choh5(ad, n2, 0) - choh5(bd, n2, 0) - choh5(cd, n2, 0) - choh5(dd, n2, 0) - choh5(de, n2, 0) - choh5(df, n2, 0) - choh5(dg, n2, 0));
		ochouraud_dblar5[0] = (-choh5(ae, n1, 0) + choh5(be, n1, 0) - choh5(ce, n1, 0) + choh5(de, n2, 0) + choh5(ee, n1, 0) + choh5(ef, n1, 0) + choh5(eg, n1, 0));
		ochournzd_dblar5[0] = (-choh5(af, n1, 0) + choh5(bf, n1, 0) - choh5(cf, n1, 0) + choh5(df, n2, 0) - choh5(ef, n1, 0) + choh5(ff, n1, 0) + choh5(fg, n1, 0));
		ochourcad_dblar5[0] = (-choh5(ag, n1, 0) + choh5(bg, n1, 0) - choh5(cg, n1, 0) + choh5(dg, n2, 0) - choh5(eg, n1, 0) - choh5(fg, n1, 0) - choh5(gg, n1, 0));
		ochourusd_dblar5[0] = (-choh5(aa, n1, 0) + choh5(bb, n1, 0) - choh5(cc, n1, 0) + choh5(dd, n2, 0) - choh5(ee, n1, 0) - choh5(ff, n1, 0) + choh5(gg, n1, 0));
	}

	int x = 10;	 // initial x distance
	int y = 670; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
	
   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_H4, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "Fca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(14400 + a - i * 14400, TIME_MINUTES), FontSize, clrDodgerBlue);
		ObjectSetString(0, obname, OBJPROP_TOOLTIP, TimeToString(14400 + a - i * 14400, TIME_DATE|TIME_MINUTES));
		//Print(TimeToString(3600 + a - i * 3600, TIME_MINUTES));
	}

	{ // EUR
		hourbuild5("eurH1", ochoureur_dblar5, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild5("chfH1", ochourchf_dblar5, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild5("gbpH1", ochourgbp_dblar5, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild5("jpyH1", ochourjpy_dblar5, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild5("audH1", ochouraud_dblar5, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild5("nzdH1", ochournzd_dblar5, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild5("cadH1", ochourcad_dblar5, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild5("usdH1", ochourusd_dblar5, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+FIFTEEN MIN BUILD-------------------------------------------------+
void BuildFifteen()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M5, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M5, 0) + 10) && TimeCurrent() <= (iTime(NULL, PERIOD_M5, 0) + 50)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M5, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		FifteenCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar1[0] = (choh1(aa, n1, 0) + choh1(ab, n1, 0) + choh1(ac, n1, 0) + choh1(ad, n2, 0) + choh1(ae, n1, 0) + choh1(af, n1, 0) + choh1(ag, n1, 0));
		ochourchf_dblar1[0] = (-choh1(ab, n1, 0) - choh1(bb, n1, 0) - choh1(bc, n1, 0) + choh1(bd, n2, 0) - choh1(be, n1, 0) - choh1(bf, n1, 0) - choh1(bg, n1, 0));
		ochourgbp_dblar1[0] = (-choh1(ac, n1, 0) + choh1(bc, n1, 0) + choh1(cc, n1, 0) + choh1(cd, n2, 0) + choh1(ce, n1, 0) + choh1(cf, n1, 0) + choh1(cg, n1, 0));
		ochourjpy_dblar1[0] = (-choh1(ad, n2, 0) - choh1(bd, n2, 0) - choh1(cd, n2, 0) - choh1(dd, n2, 0) - choh1(de, n2, 0) - choh1(df, n2, 0) - choh1(dg, n2, 0));
		ochouraud_dblar1[0] = (-choh1(ae, n1, 0) + choh1(be, n1, 0) - choh1(ce, n1, 0) + choh1(de, n2, 0) + choh1(ee, n1, 0) + choh1(ef, n1, 0) + choh1(eg, n1, 0));
		ochournzd_dblar1[0] = (-choh1(af, n1, 0) + choh1(bf, n1, 0) - choh1(cf, n1, 0) + choh1(df, n2, 0) - choh1(ef, n1, 0) + choh1(ff, n1, 0) + choh1(fg, n1, 0));
		ochourcad_dblar1[0] = (-choh1(ag, n1, 0) + choh1(bg, n1, 0) - choh1(cg, n1, 0) + choh1(dg, n2, 0) - choh1(eg, n1, 0) - choh1(fg, n1, 0) - choh1(gg, n1, 0));
		ochourusd_dblar1[0] = (-choh1(aa, n1, 0) + choh1(bb, n1, 0) - choh1(cc, n1, 0) + choh1(dd, n2, 0) - choh1(ee, n1, 0) - choh1(ff, n1, 0) + choh1(gg, n1, 0));
	}

	int x = 380;	 // initial x distance
	int y = 130; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
	
   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_M15, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "2ca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(900 + a - i * 900, TIME_MINUTES), FontSize, clrDodgerBlue);
		ObjectSetString(0, obname, OBJPROP_TOOLTIP, TimeToString(900 + a - i * 900, TIME_DATE|TIME_MINUTES));
	}

	{ // EUR
		hourbuild1("eurM15", ochoureur_dblar1, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild1("chfM15", ochourchf_dblar1, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild1("gbpM15", ochourgbp_dblar1, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild1("jpyM15", ochourjpy_dblar1, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild1("audM15", ochouraud_dblar1, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild1("nzdM15", ochournzd_dblar1, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild1("cadM15", ochourcad_dblar1, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild1("usdM15", ochourusd_dblar1, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+FIVE MIN BUILD----------------------------------------------------+
void BuildFive()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M1, 0) + 10) && TimeCurrent() <= (iTime(NULL, PERIOD_M1, 0) + 30)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M1, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		FiveCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar2[0] = (choh2(aa, n1, 0) + choh2(ab, n1, 0) + choh2(ac, n1, 0) + choh2(ad, n2, 0) + choh2(ae, n1, 0) + choh2(af, n1, 0) + choh2(ag, n1, 0));
		ochourchf_dblar2[0] = (-choh2(ab, n1, 0) - choh2(bb, n1, 0) - choh2(bc, n1, 0) + choh2(bd, n2, 0) - choh2(be, n1, 0) - choh2(bf, n1, 0) - choh2(bg, n1, 0));
		ochourgbp_dblar2[0] = (-choh2(ac, n1, 0) + choh2(bc, n1, 0) + choh2(cc, n1, 0) + choh2(cd, n2, 0) + choh2(ce, n1, 0) + choh2(cf, n1, 0) + choh2(cg, n1, 0));
		ochourjpy_dblar2[0] = (-choh2(ad, n2, 0) - choh2(bd, n2, 0) - choh2(cd, n2, 0) - choh2(dd, n2, 0) - choh2(de, n2, 0) - choh2(df, n2, 0) - choh2(dg, n2, 0));
		ochouraud_dblar2[0] = (-choh2(ae, n1, 0) + choh2(be, n1, 0) - choh2(ce, n1, 0) + choh2(de, n2, 0) + choh2(ee, n1, 0) + choh2(ef, n1, 0) + choh2(eg, n1, 0));
		ochournzd_dblar2[0] = (-choh2(af, n1, 0) + choh2(bf, n1, 0) - choh2(cf, n1, 0) + choh2(df, n2, 0) - choh2(ef, n1, 0) + choh2(ff, n1, 0) + choh2(fg, n1, 0));
		ochourcad_dblar2[0] = (-choh2(ag, n1, 0) + choh2(bg, n1, 0) - choh2(cg, n1, 0) + choh2(dg, n2, 0) - choh2(eg, n1, 0) - choh2(fg, n1, 0) - choh2(gg, n1, 0));
		ochourusd_dblar2[0] = (-choh2(aa, n1, 0) + choh2(bb, n1, 0) - choh2(cc, n1, 0) + choh2(dd, n2, 0) - choh2(ee, n1, 0) - choh2(ff, n1, 0) + choh2(gg, n1, 0));
	}
	
	int x = 380;	 // initial x distance
	int y = 400; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
   
   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_M5, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "4ca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(300 + a - i * 300, TIME_MINUTES), FontSize, clrDodgerBlue);
	}
      
	{ // EUR
		hourbuild2("eurM5", ochoureur_dblar2, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild2("chfM5", ochourchf_dblar2, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild2("gbpM5", ochourgbp_dblar2, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild2("jpyM5", ochourjpy_dblar2, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild2("audM5", ochouraud_dblar2, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild2("nzdM5", ochournzd_dblar2, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild2("cadM5", ochourcad_dblar2, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild2("usdM5", ochourusd_dblar2, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+ONE MIN BUILD-----------------------------------------------------+
void BuildOne()
{
	//uint hourlystart=GetTickCount();
	bool hourlycheck1 = false;
	static datetime hourcheck1 = 0;
	if (hourcheck1 < iTime(NULL, PERIOD_M1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M1, 0) + 5) && TimeCurrent() <= (iTime(NULL, PERIOD_M1, 0) + 10)))
	{
		//Print("New hourly check started at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS));
		hourlycheck1 = true;
		hourcheck1 = iTime(NULL, PERIOD_M1, 0);
	}
	if (firstrun || hourlycheck1)
	{
		//uint start=GetTickCount();
		OneCalcs();
		//uint time=GetTickCount()-start;
		//Print("New hourly check completed at: ",TimeToStr(TimeCurrent(),TIME_MINUTES|TIME_SECONDS)," and took ms: ",time);
		hourlycheck1 = false;
	}
	int curht1 = 0, curht2 = 0, curht3 = 0, curht4 = 0, curht5 = 0, curht6 = 0, curht7 = 0, curht8 = 0;
	{
		int n1 = 10000, n2 = 100;
		ochoureur_dblar3[0] = (choh3(aa, n1, 0) + choh3(ab, n1, 0) + choh3(ac, n1, 0) + choh3(ad, n2, 0) + choh3(ae, n1, 0) + choh3(af, n1, 0) + choh3(ag, n1, 0));
		ochourchf_dblar3[0] = (-choh3(ab, n1, 0) - choh3(bb, n1, 0) - choh3(bc, n1, 0) + choh3(bd, n2, 0) - choh3(be, n1, 0) - choh3(bf, n1, 0) - choh3(bg, n1, 0));
		ochourgbp_dblar3[0] = (-choh3(ac, n1, 0) + choh3(bc, n1, 0) + choh3(cc, n1, 0) + choh3(cd, n2, 0) + choh3(ce, n1, 0) + choh3(cf, n1, 0) + choh3(cg, n1, 0));
		ochourjpy_dblar3[0] = (-choh3(ad, n2, 0) - choh3(bd, n2, 0) - choh3(cd, n2, 0) - choh3(dd, n2, 0) - choh3(de, n2, 0) - choh3(df, n2, 0) - choh3(dg, n2, 0));
		ochouraud_dblar3[0] = (-choh3(ae, n1, 0) + choh3(be, n1, 0) - choh3(ce, n1, 0) + choh3(de, n2, 0) + choh3(ee, n1, 0) + choh3(ef, n1, 0) + choh3(eg, n1, 0));
		ochournzd_dblar3[0] = (-choh3(af, n1, 0) + choh3(bf, n1, 0) - choh3(cf, n1, 0) + choh3(df, n2, 0) - choh3(ef, n1, 0) + choh3(ff, n1, 0) + choh3(fg, n1, 0));
		ochourcad_dblar3[0] = (-choh3(ag, n1, 0) + choh3(bg, n1, 0) - choh3(cg, n1, 0) + choh3(dg, n2, 0) - choh3(eg, n1, 0) - choh3(fg, n1, 0) - choh3(gg, n1, 0));
		ochourusd_dblar3[0] = (-choh3(aa, n1, 0) + choh3(bb, n1, 0) - choh3(cc, n1, 0) + choh3(dd, n2, 0) - choh3(ee, n1, 0) - choh3(ff, n1, 0) + choh3(gg, n1, 0));
	}
	
	int x = 380;	 // initial x distance
	int y = 670; // initial y distance
	int xs = 40; // xstep
	int ys = 20; // ystep
	
	static datetime alert_time = 0;
	bool alert_check = false;
	if (alert_time < iTime(_Symbol, PERIOD_M1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M1, 0) + 5) && TimeCurrent() <= (iTime(NULL, PERIOD_M1, 0) + 10)))
	{
	   alert_time = iTime(_Symbol, PERIOD_M1, 0);
	   alert_check = false;
	}
	if (alert_check)
	{
	   int count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourusd_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("USD continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourusd_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("USD continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochoureur_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("EUR continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochoureur_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("EUR continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourgbp_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("GBP continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourgbp_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("GBP continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochouraud_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("AUD continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochouraud_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("AUD continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochournzd_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("NZD continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochournzd_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("NZD continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourcad_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("CAD continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourcad_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("CAD continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourchf_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("CHF continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourchf_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("CHF continuous BEAR");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourjpy_dblar3[q] >= 1) count++;
	      if (count == 10) Alert("JPY continuous BULL");
	   }
	   count = 0;
	   for (int q = 1; q <= 10; q++)
	   {
	      if (ochourjpy_dblar3[q] <= -1) count++;
	      if (count == 10) Alert("JPY continuous BEAR");
	   }
	   count = 0;
	   //Print(count);	   
	      
	   if (ochourusd_dblar3[1] >= 30) Alert("USD High Vol 1m +" + IntegerToString(ochourusd_dblar3[1]));
	   if (ochourusd_dblar3[1] <= -30) Alert("USD High Vol 1m " + IntegerToString(ochourusd_dblar3[1]));
	   if (ochoureur_dblar3[1] >= 30) Alert("EUR High Vol 1m +" + IntegerToString(ochoureur_dblar3[1]));
	   if (ochoureur_dblar3[1] <= -30) Alert("EUR High Vol 1m " + IntegerToString(ochoureur_dblar3[1]));
	   if (ochourgbp_dblar3[1] >= 30) Alert("GBP High Vol 1m +" + IntegerToString(ochourgbp_dblar3[1]));
	   if (ochourgbp_dblar3[1] <= -30) Alert("GBP High Vol 1m " + IntegerToString(ochourgbp_dblar3[1]));
	   if (ochouraud_dblar3[1] >= 30) Alert("AUD High Vol 1m +" + IntegerToString(ochouraud_dblar3[1]));
	   if (ochouraud_dblar3[1] <= -30) Alert("AUD High Vol 1m " + IntegerToString(ochouraud_dblar3[1]));
	   if (ochournzd_dblar3[1] >= 30) Alert("NZD High Vol 1m +" + IntegerToString(ochournzd_dblar3[1]));
	   if (ochournzd_dblar3[1] <= -30) Alert("NZD High Vol 1m " + IntegerToString(ochournzd_dblar3[1]));
	   if (ochourcad_dblar3[1] >= 30) Alert("CAD High Vol 1m +" + IntegerToString(ochourcad_dblar3[1]));
	   if (ochourcad_dblar3[1] <= -30) Alert("CAD High Vol 1m " + IntegerToString(ochourcad_dblar3[1]));
	   if (ochourchf_dblar3[1] >= 30) Alert("CHF High Vol 1m +" + IntegerToString(ochourchf_dblar3[1]));
	   if (ochourchf_dblar3[1] <= -30) Alert("CHF High Vol 1m " + IntegerToString(ochourchf_dblar3[1]));
	   if (ochourjpy_dblar3[1] >= 30) Alert("JPY High Vol 1m +" + IntegerToString(ochourjpy_dblar3[1]));
	   if (ochourjpy_dblar3[1] <= -30) Alert("JPY High Vol 1m " + IntegerToString(ochourjpy_dblar3[1]));
	   alert_check = false;
	}	

   string obname;
   
   datetime a = iTime(_Symbol, PERIOD_M1, 0);
   for (i = 12; i >= 1; i--){
		obname = Name + " " + "6ca" + IntegerToString(i);
		LabelMake(obname, 0, x, y + (i - 1) * ys, TimeToString(60 + a - i * 60, TIME_MINUTES), FontSize, clrDodgerBlue);
	}
      
	{ // EUR
		hourbuild3("eurM1", ochoureur_dblar3, curht1, x + xs, y);
	}
	{ // CHF
		hourbuild3("chfM1", ochourchf_dblar3, curht2, x + 2 * xs, y);
	}
	{ // GBP
		hourbuild3("gbpM1", ochourgbp_dblar3, curht3, x + 3 * xs, y);
	}
	{ // JPY
		hourbuild3("jpyM1", ochourjpy_dblar3, curht4, x + 4 * xs, y);
	}
	{ // AUD
		hourbuild3("audM1", ochouraud_dblar3, curht5, x + 5 * xs, y);
	}
	{ // NZD
		hourbuild3("nzdM1", ochournzd_dblar3, curht6, x + 6 * xs, y);
	}
	{ // CAD
		hourbuild3("cadM1", ochourcad_dblar3, curht7, x + 7 * xs, y);
	}
	{ // USD
		hourbuild3("usdM1", ochourusd_dblar3, curht8, x + 8 * xs, y);
	}
	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+


//+DAILY BUILD-------------------------------------------------------+
void BuildDaily()
{
	//uint hourlystart=GetTickCount();
	FillTodayBuffer();

	int x = 90;	 // initial x distance
	int y = 50; // initial y distance
	int xs = 80; // xstep
	int ys = 15;
	
	string obname;
	obname = Name + " D1 EUR";
	LabelMake(obname, 0, x, y, IntegerToString(ocdayseur_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdayseur_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 CHF";
	LabelMake(obname, 0, x + xs, y, IntegerToString(ocdayschf_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdayschf_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 GBP";
	LabelMake(obname, 0, x + 2 * xs, y, IntegerToString(ocdaysgbp_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdaysgbp_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 JPY";
	LabelMake(obname, 0, x + 3 * xs, y, IntegerToString(ocdaysjpy_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdaysjpy_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 AUD";
	LabelMake(obname, 0, x + 4 * xs, y, IntegerToString(ocdaysaud_dblar[0]), FontSize + 1, clrBlack);
	if (ocdaysaud_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 NZD";
	LabelMake(obname, 0, x + 5 * xs, y, IntegerToString(ocdaysnzd_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdaysnzd_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 CAD";
	LabelMake(obname, 0, x + 6 * xs, y, IntegerToString(ocdayscad_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdayscad_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	obname = Name + " D1 USD";
	LabelMake(obname, 0, x + 7 * xs, y, IntegerToString(ocdaysusd_dblar[0]), FontSize + 1, clrBlack);
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if (ocdaysusd_dblar[0] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
	
	obname = Name + " D1 EUR hl";
	LabelMake(obname, 0, x - 10, y + ys, IntegerToString(oldayseur_dblar[0]) + " / " + IntegerToString(ohdayseur_dblar[0]), FontSize, clrGreen);
	if (oldayseur_dblar[0] > avgeurl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdayseur_dblar[0] < avgeurh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 CHF hl";
	LabelMake(obname, 0, x + 1 * xs - 10, y + ys, IntegerToString(oldayschf_dblar[0]) + " / " + IntegerToString(ohdayschf_dblar[0]), FontSize, clrGreen);
	if (oldayschf_dblar[0] > avgchfl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdayschf_dblar[0] < avgchfh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 GBP hl";
	LabelMake(obname, 0, x + 2 * xs - 10, y + ys, IntegerToString(oldaysgbp_dblar[0]) + " / " + IntegerToString(ohdaysgbp_dblar[0]), FontSize, clrGreen);
	if (oldaysgbp_dblar[0] > avggbpl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdaysgbp_dblar[0] < avggbph) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 JPY hl";
	LabelMake(obname, 0, x + 3 * xs - 10, y + ys, IntegerToString(oldaysjpy_dblar[0]) + " / " + IntegerToString(ohdaysjpy_dblar[0]), FontSize, clrGreen);
	if (oldaysjpy_dblar[0] > avgjpyl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdaysjpy_dblar[0] < avgjpyh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 AUD hl";
	LabelMake(obname, 0, x + 4 * xs - 10, y + ys, IntegerToString(oldaysaud_dblar[0]) + " / " + IntegerToString(ohdaysaud_dblar[0]), FontSize, clrGreen);
	if (oldaysaud_dblar[0] > avgaudl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdaysaud_dblar[0] < avgaudh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 NZD hl";
	LabelMake(obname, 0, x + 5 * xs - 10, y + ys, IntegerToString(oldaysnzd_dblar[0]) + " / " + IntegerToString(ohdaysnzd_dblar[0]), FontSize, clrGreen);
	if (oldaysnzd_dblar[0] > avgnzdl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdaysnzd_dblar[0] < avgnzdh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 CAD hl";
	LabelMake(obname, 0, x + 6 * xs - 10, y + ys, IntegerToString(oldayscad_dblar[0]) + " / " + IntegerToString(ohdayscad_dblar[0]), FontSize, clrGreen);
	if (oldayscad_dblar[0] > avgcadl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdayscad_dblar[0] < avgcadh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + " D1 USD hl";
	LabelMake(obname, 0, x + 7 * xs - 10, y + ys, IntegerToString(oldaysusd_dblar[0]) + " / " + IntegerToString(ohdaysusd_dblar[0]), FontSize, clrGreen);
	if (oldaysusd_dblar[0] > avgusdl) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
	else if (ohdaysusd_dblar[0] < avgusdh) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);

	//uint end=GetTickCount()-hourlystart; Print("BuildHourly12Table function took ms: ",end);
}
//+------------------------------------------------------------------+

//+D/W/M AVG TABLE---------------------------------------------------+
void DoAvg(){
   avgCalcs();
   string obname;
	int x = 1480;
	int y = 345;
	int xs = 40;
	int ys = 15;
	
	obname = Name + " " + "EURDWM1";
	LabelMake(obname, 0, 10 + x, y, "D: " + DoubleToString(euravd[0], 0) + " / " + DoubleToString(euravd[1], 0) + " / " + DoubleToString(euravd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM2";
	LabelMake(obname, 0, 10 + x, y + ys, "D L/H: " + DoubleToString(oldayseur_dblar[ArrayMinimum(oldayseur_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdayseur_dblar[ArrayMaximum(ohdayseur_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM3";
	LabelMake(obname, 0, 10 + x, y + 2 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayseur_dblar[0]) + ohdayseur_dblar[0]) / (MathAbs(euravd[0]) + euravd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "EURDWM4";
	LabelMake(obname, 0, 10 + x, y + 4 * ys, "W: " + DoubleToString(euravw[0], 0) + " / " + DoubleToString(euravw[1], 0) + " / " + DoubleToString(euravw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM5";
	LabelMake(obname, 0, 10 + x, y + 5 * ys, "W L/H: " + DoubleToString(olwayseur_dblar[ArrayMinimum(olwayseur_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwayseur_dblar[ArrayMaximum(ohwayseur_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM6";
	LabelMake(obname, 0, 10 + x, y + 6 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayseur_dblar[0]) + ohwayseur_dblar[0]) / (MathAbs(euravw[0]) + euravw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "EURDWM7";
	LabelMake(obname, 0, 10 + x, y + 8 * ys, "M: " + DoubleToString(euravm[0], 0) + " / " + DoubleToString(euravm[1], 0) + " / " + DoubleToString(euravm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM8";
	LabelMake(obname, 0, 10 + x, y + 9 * ys, "M L/H: " + DoubleToString(olmayseur_dblar[ArrayMinimum(olmayseur_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmayseur_dblar[ArrayMaximum(ohmayseur_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "EURDWM9";
	LabelMake(obname, 0, 10 + x, y + 10 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]), 2) + " / " + DoubleToString(100 * ocmayseur_dblar[0] / (MathAbs(euravm[0]) + euravm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayseur_dblar[0]) + ohmayseur_dblar[0]) / (MathAbs(euravm[0]) + euravm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "CHFDWM1";
	LabelMake(obname, 0, 10 + x + 3 * xs, y, "D: " + DoubleToString(chfavd[0], 0) + " / " + DoubleToString(chfavd[1], 0) + " / " + DoubleToString(chfavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM2";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + ys, "D L/H: " + DoubleToString(oldayschf_dblar[ArrayMinimum(oldayschf_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdayschf_dblar[ArrayMaximum(ohdayschf_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM3";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 2 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayschf_dblar[0]) + ohdayschf_dblar[0]) / (MathAbs(chfavd[0]) + chfavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "CHFDWM4";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 4 * ys, "W: " + DoubleToString(chfavw[0], 0) + " / " + DoubleToString(chfavw[1], 0) + " / " + DoubleToString(chfavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM5";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 5 * ys, "W L/H: " + DoubleToString(olwayschf_dblar[ArrayMinimum(olwayschf_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwayschf_dblar[ArrayMaximum(ohwayschf_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM6";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 6 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayschf_dblar[0]) + ohwayschf_dblar[0]) / (MathAbs(chfavw[0]) + chfavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "CHFDWM7";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 8 * ys, "M: " + DoubleToString(chfavm[0], 0) + " / " + DoubleToString(chfavm[1], 0) + " / " + DoubleToString(chfavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM8";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 9 * ys, "M L/H: " + DoubleToString(olmayschf_dblar[ArrayMinimum(olmayschf_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmayschf_dblar[ArrayMaximum(ohmayschf_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CHFDWM9";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 10 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]), 2) + " / " + DoubleToString(100 * ocmayschf_dblar[0] / (MathAbs(chfavm[0]) + chfavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayschf_dblar[0]) + ohmayschf_dblar[0]) / (MathAbs(chfavm[0]) + chfavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "GBPDWM1";
	LabelMake(obname, 0, 10 + x + 6 * xs, y, "D: " + DoubleToString(gbpavd[0], 0) + " / " + DoubleToString(gbpavd[1], 0) + " / " + DoubleToString(gbpavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM2";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + ys, "D L/H: " + DoubleToString(oldaysgbp_dblar[ArrayMinimum(oldaysgbp_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdaysgbp_dblar[ArrayMaximum(ohdaysgbp_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM3";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 2 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysgbp_dblar[0]) + ohdaysgbp_dblar[0]) / (MathAbs(gbpavd[0]) + gbpavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "GBPDWM4";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 4 * ys, "W: " + DoubleToString(gbpavw[0], 0) + " / " + DoubleToString(gbpavw[1], 0) + " / " + DoubleToString(gbpavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM5";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 5 * ys, "W L/H: " + DoubleToString(olwaysgbp_dblar[ArrayMinimum(olwaysgbp_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwaysgbp_dblar[ArrayMaximum(ohwaysgbp_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM6";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 6 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysgbp_dblar[0]) + ohwaysgbp_dblar[0]) / (MathAbs(gbpavw[0]) + gbpavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "GBPDWM7";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 8 * ys, "M: " + DoubleToString(gbpavm[0], 0) + " / " + DoubleToString(gbpavm[1], 0) + " / " + DoubleToString(gbpavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM8";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 9 * ys, "M L/H: " + DoubleToString(olmaysgbp_dblar[ArrayMinimum(olmaysgbp_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmaysgbp_dblar[ArrayMaximum(ohmaysgbp_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "GBPDWM9";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 10 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]), 2) + " / " + DoubleToString(100 * ocmaysgbp_dblar[0] / (MathAbs(gbpavm[0]) + gbpavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysgbp_dblar[0]) + ohmaysgbp_dblar[0]) / (MathAbs(gbpavm[0]) + gbpavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "AUDDWM1";
	LabelMake(obname, 0, 10 + x, y + 13 * ys, "D: " + DoubleToString(audavd[0], 0) + " / " + DoubleToString(audavd[1], 0) + " / " + DoubleToString(audavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM2";
	LabelMake(obname, 0, 10 + x, y + 14 * ys, "D L/H: " + DoubleToString(oldaysaud_dblar[ArrayMinimum(oldaysaud_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdaysaud_dblar[ArrayMaximum(ohdaysaud_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM3";
	LabelMake(obname, 0, 10 + x, y + 15 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysaud_dblar[0]) + ohdaysaud_dblar[0]) / (MathAbs(audavd[0]) + audavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "AUDDWM4";
	LabelMake(obname, 0, 10 + x, y + 17 * ys, "W: " + DoubleToString(audavw[0], 0) + " / " + DoubleToString(audavw[1], 0) + " / " + DoubleToString(audavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM5";
	LabelMake(obname, 0, 10 + x, y + 18 * ys, "W L/H: " + DoubleToString(olwaysaud_dblar[ArrayMinimum(olwaysaud_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwaysaud_dblar[ArrayMaximum(ohwaysaud_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM6";
	LabelMake(obname, 0, 10 + x, y + 19 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysaud_dblar[0]) + ohwaysaud_dblar[0]) / (MathAbs(audavw[0]) + audavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "AUDDWM7";
	LabelMake(obname, 0, 10 + x, y + 21 * ys, "M: " + DoubleToString(audavm[0], 0) + " / " + DoubleToString(audavm[1], 0) + " / " + DoubleToString(audavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM8";
	LabelMake(obname, 0, 10 + x, y + 22 * ys, "M L/H: " + DoubleToString(olmaysaud_dblar[ArrayMinimum(olmaysaud_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmaysaud_dblar[ArrayMaximum(ohmaysaud_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "AUDDWM9";
	LabelMake(obname, 0, 10 + x, y + 23 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]), 2) + " / " + DoubleToString(100 * ocmaysaud_dblar[0] / (MathAbs(audavm[0]) + audavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysaud_dblar[0]) + ohmaysaud_dblar[0]) / (MathAbs(audavm[0]) + audavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "NZDDWM1";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 13 * ys, "D: " + DoubleToString(nzdavd[0], 0) + " / " + DoubleToString(nzdavd[1], 0) + " / " + DoubleToString(nzdavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM2";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 14 * ys, "D L/H: " + DoubleToString(oldaysnzd_dblar[ArrayMinimum(oldaysnzd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdaysnzd_dblar[ArrayMaximum(ohdaysnzd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM3";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 15 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysnzd_dblar[0]) + ohdaysnzd_dblar[0]) / (MathAbs(nzdavd[0]) + nzdavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "NZDDWM4";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 17 * ys, "W: " + DoubleToString(nzdavw[0], 0) + " / " + DoubleToString(nzdavw[1], 0) + " / " + DoubleToString(nzdavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM5";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 18 * ys, "W L/H: " + DoubleToString(olwaysnzd_dblar[ArrayMinimum(olwaysnzd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwaysnzd_dblar[ArrayMaximum(ohwaysnzd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM6";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 19 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysnzd_dblar[0]) + ohwaysnzd_dblar[0]) / (MathAbs(nzdavw[0]) + nzdavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "NZDDWM7";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 21 * ys, "M: " + DoubleToString(nzdavm[0], 0) + " / " + DoubleToString(nzdavm[1], 0) + " / " + DoubleToString(nzdavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM8";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 22 * ys, "M L/H: " + DoubleToString(olmaysnzd_dblar[ArrayMinimum(olmaysnzd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmaysnzd_dblar[ArrayMaximum(ohmaysnzd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "NZDDWM9";
	LabelMake(obname, 0, 10 + x + 3 * xs, y + 23 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]), 2) + " / " + DoubleToString(100 * ocmaysnzd_dblar[0] / (MathAbs(nzdavm[0]) + nzdavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysnzd_dblar[0]) + ohmaysnzd_dblar[0]) / (MathAbs(nzdavm[0]) + nzdavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "CADDWM1";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 13 * ys, "D: " + DoubleToString(cadavd[0], 0) + " / " + DoubleToString(cadavd[1], 0) + " / " + DoubleToString(cadavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM2";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 14 * ys, "D L/H: " + DoubleToString(oldayscad_dblar[ArrayMinimum(oldayscad_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdayscad_dblar[ArrayMaximum(ohdayscad_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM3";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 15 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldayscad_dblar[0]) + ohdayscad_dblar[0]) / (MathAbs(cadavd[0]) + cadavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "CADDWM4";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 17 * ys, "W: " + DoubleToString(cadavw[0], 0) + " / " + DoubleToString(cadavw[1], 0) + " / " + DoubleToString(cadavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM5";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 18 * ys, "W L/H: " + DoubleToString(olwayscad_dblar[ArrayMinimum(olwayscad_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwayscad_dblar[ArrayMaximum(ohwayscad_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM6";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 19 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwayscad_dblar[0]) + ohwayscad_dblar[0]) / (MathAbs(cadavw[0]) + cadavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "CADDWM7";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 21 * ys, "M: " + DoubleToString(cadavm[0], 0) + " / " + DoubleToString(cadavm[1], 0) + " / " + DoubleToString(cadavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM8";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 22 * ys, "M L/H: " + DoubleToString(olmayscad_dblar[ArrayMinimum(olmayscad_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmayscad_dblar[ArrayMaximum(ohmayscad_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "CADDWM9";
	LabelMake(obname, 0, 10 + x + 6 * xs, y + 23 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]), 2) + " / " + DoubleToString(100 * ocmayscad_dblar[0] / (MathAbs(cadavm[0]) + cadavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmayscad_dblar[0]) + ohmayscad_dblar[0]) / (MathAbs(cadavm[0]) + cadavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "JPYDWM1";
	LabelMake(obname, 0, 70 + x, y + 26 * ys, "D: " + DoubleToString(jpyavd[0], 0) + " / " + DoubleToString(jpyavd[1], 0) + " / " + DoubleToString(jpyavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM2";
	LabelMake(obname, 0, 70 + x, y + 27 * ys, "D L/H: " + DoubleToString(oldaysjpy_dblar[ArrayMinimum(oldaysjpy_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdaysjpy_dblar[ArrayMaximum(ohdaysjpy_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM3";
	LabelMake(obname, 0, 70 + x, y + 28 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysjpy_dblar[0]) + ohdaysjpy_dblar[0]) / (MathAbs(jpyavd[0]) + jpyavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "JPYDWM4";
	LabelMake(obname, 0, 70 + x, y + 30 * ys, "W: " + DoubleToString(jpyavw[0], 0) + " / " + DoubleToString(jpyavw[1], 0) + " / " + DoubleToString(jpyavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM5";
	LabelMake(obname, 0, 70 + x, y + 31 * ys, "W L/H: " + DoubleToString(olwaysjpy_dblar[ArrayMinimum(olwaysjpy_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwaysjpy_dblar[ArrayMaximum(ohwaysjpy_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM6";
	LabelMake(obname, 0, 70 + x, y + 32 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysjpy_dblar[0]) + ohwaysjpy_dblar[0]) / (MathAbs(jpyavw[0]) + jpyavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "JPYDWM7";
	LabelMake(obname, 0, 70 + x, y + 34 * ys, "M: " + DoubleToString(jpyavm[0], 0) + " / " + DoubleToString(jpyavm[1], 0) + " / " + DoubleToString(jpyavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM8";
	LabelMake(obname, 0, 70 + x, y + 35 * ys, "M L/H: " + DoubleToString(olmaysjpy_dblar[ArrayMinimum(olmaysjpy_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmaysjpy_dblar[ArrayMaximum(ohmaysjpy_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "JPYDWM9";
	LabelMake(obname, 0, 70 + x, y + 36 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]), 2) + " / " + DoubleToString(100 * ocmaysjpy_dblar[0] / (MathAbs(jpyavm[0]) + jpyavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysjpy_dblar[0]) + ohmaysjpy_dblar[0]) / (MathAbs(jpyavm[0]) + jpyavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
	
	obname = Name + " " + "USDDWM1";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 26 * ys, "D: " + DoubleToString(usdavd[0], 0) + " / " + DoubleToString(usdavd[1], 0) + " / " + DoubleToString(usdavd[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM2";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 27 * ys, "D L/H: " + DoubleToString(oldaysusd_dblar[ArrayMinimum(oldaysusd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohdaysusd_dblar[ArrayMaximum(ohdaysusd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM3";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 28 * ys, "DVol: " + DoubleToString((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]), 2), FontSize, clrGreen);
	////ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(oldaysusd_dblar[0]) + ohdaysusd_dblar[0]) / (MathAbs(usdavd[0]) + usdavd[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrLime);
	obname = Name + " " + "USDDWM4";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 30 * ys, "W: " + DoubleToString(usdavw[0], 0) + " / " + DoubleToString(usdavw[1], 0) + " / " + DoubleToString(usdavw[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM5";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 31 * ys, "W L/H: " + DoubleToString(olwaysusd_dblar[ArrayMinimum(olwaysusd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohwaysusd_dblar[ArrayMaximum(ohwaysusd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM6";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 32 * ys, "WVol: " + DoubleToString((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]), 2), FontSize, clrRed);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olwaysusd_dblar[0]) + ohwaysusd_dblar[0]) / (MathAbs(usdavw[0]) + usdavw[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrMagenta);
	obname = Name + " " + "USDDWM7";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 34 * ys, "M: " + DoubleToString(usdavm[0], 0) + " / " + DoubleToString(usdavm[1], 0) + " / " + DoubleToString(usdavm[2], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM8";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 35 * ys, "M L/H: " + DoubleToString(olmaysusd_dblar[ArrayMinimum(olmaysusd_dblar, 0, 1)], 0) + " / " + DoubleToString(ohmaysusd_dblar[ArrayMaximum(ohmaysusd_dblar, 0, 1)], 0), FontSize - 1, clrDodgerBlue);
	obname = Name + " " + "USDDWM9";
	LabelMake(obname, 0, 70 + x + 3 * xs, y + 36 * ys, "MVol: " + DoubleToString((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]), 2) + " / " + DoubleToString(100 * ocmaysusd_dblar[0] / (MathAbs(usdavm[0]) + usdavm[2]), 1), FontSize, clrBlue);
	//ObjectSetInteger(0, obname, OBJPROP_COLOR, clrGreen);
	if ((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]) > 75)
	ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
	if ((100.0) * (MathAbs(olmaysusd_dblar[0]) + ohmaysusd_dblar[0]) / (MathAbs(usdavm[0]) + usdavm[2]) > 90)
	ObjectSetInteger(0, obname, OBJPROP_COLOR, clrAqua);
}
//+------------------------------------------------------------------+

//+DWM TABLE---------------------------------------------------------+
void BuildWMTable(){
   int x = 1250;
	int y = 50;
	int xs = 40;
	int ys = 20;
	
   string obname;
   
   obname = Name + " " + "EURD";
   LabelMake(obname, 0, x, y, "D: " + IntegerToString(oldayseur_dblar[0]) + " / " + IntegerToString(ocdayseur_dblar[0]) + " / " + IntegerToString(ohdayseur_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdayseur_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "EURW";
   LabelMake(obname, 0, x, y + ys, "W: " + IntegerToString(olwayseur_dblar[0]) + " / " + IntegerToString(ocwayseur_dblar[0]) + " / " + IntegerToString(ohwayseur_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwayseur_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "EURM";
   LabelMake(obname, 0, x, y + 2 * ys, "M: " + IntegerToString(olmayseur_dblar[0]) + " / " + IntegerToString(ocmayseur_dblar[0]) + " / " + IntegerToString(ohmayseur_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmayseur_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "CHFD";
   LabelMake(obname, 0, x + 4 * xs, y, "D: " + IntegerToString(oldayschf_dblar[0]) + " / " + IntegerToString(ocdayschf_dblar[0]) + " / " + IntegerToString(ohdayschf_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdayschf_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "CHFW";
   LabelMake(obname, 0, x + 4 * xs, y + ys, "W: " + IntegerToString(olwayschf_dblar[0]) + " / " + IntegerToString(ocwayschf_dblar[0]) + " / " + IntegerToString(ohwayschf_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwayschf_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "CHFM";
   LabelMake(obname, 0, x + 4 * xs, y + 2 * ys, "M: " + IntegerToString(olmayschf_dblar[0]) + " / " + IntegerToString(ocmayschf_dblar[0]) + " / " + IntegerToString(ohmayschf_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmayschf_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "GBPD";
   LabelMake(obname, 0, x + 8 * xs, y, "D: " + IntegerToString(oldaysgbp_dblar[0]) + " / " + IntegerToString(ocdaysgbp_dblar[0]) + " / " + IntegerToString(ohdaysgbp_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdaysgbp_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "GBPW";
   LabelMake(obname, 0, x + 8 * xs, y + ys, "W: " + IntegerToString(olwaysgbp_dblar[0]) + " / " + IntegerToString(ocwaysgbp_dblar[0]) + " / " + IntegerToString(ohwaysgbp_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwaysgbp_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "GBPM";
   LabelMake(obname, 0, x + 8 * xs, y + 2 * ys, "M: " + IntegerToString(olmaysgbp_dblar[0]) + " / " + IntegerToString(ocmaysgbp_dblar[0]) + " / " + IntegerToString(ohmaysgbp_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmaysgbp_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "JPYD";
   LabelMake(obname, 0, x + 12 * xs, y, "D: " + IntegerToString(oldaysjpy_dblar[0]) + " / " + IntegerToString(ocdaysjpy_dblar[0]) + " / " + IntegerToString(ohdaysjpy_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdaysjpy_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "JPYW";
   LabelMake(obname, 0, x + 12 * xs, y + ys, "W: " + IntegerToString(olwaysjpy_dblar[0]) + " / " + IntegerToString(ocwaysjpy_dblar[0]) + " / " + IntegerToString(ohwaysjpy_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwaysjpy_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "JPYM";
   LabelMake(obname, 0, x + 12 * xs, y + 2 * ys, "M: " + IntegerToString(olmaysjpy_dblar[0]) + " / " + IntegerToString(ocmaysjpy_dblar[0]) + " / " + IntegerToString(ohmaysjpy_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmaysjpy_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "AUDD";
   LabelMake(obname, 0, x, y + 4 * ys, "D: " + IntegerToString(oldaysaud_dblar[0]) + " / " + IntegerToString(ocdaysaud_dblar[0]) + " / " + IntegerToString(ohdaysaud_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdaysaud_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "AUDW";
   LabelMake(obname, 0, x, y + 5 * ys, "W: " + IntegerToString(olwaysaud_dblar[0]) + " / " + IntegerToString(ocwaysaud_dblar[0]) + " / " + IntegerToString(ohwaysaud_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwaysaud_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "AUDM";
   LabelMake(obname, 0, x, y + 6 * ys, "M: " + IntegerToString(olmaysaud_dblar[0]) + " / " + IntegerToString(ocmaysaud_dblar[0]) + " / " + IntegerToString(ohmaysaud_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmaysaud_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "NZDD";
   LabelMake(obname, 0, x + 4 * xs, y + 4 * ys, "D: " + IntegerToString(oldaysnzd_dblar[0]) + " / " + IntegerToString(ocdaysnzd_dblar[0]) + " / " + IntegerToString(ohdaysnzd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdaysnzd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "NZDW";
   LabelMake(obname, 0, x + 4 * xs, y + 5 * ys, "W: " + IntegerToString(olwaysnzd_dblar[0]) + " / " + IntegerToString(ocwaysnzd_dblar[0]) + " / " + IntegerToString(ohwaysnzd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwaysnzd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "NZDM";
   LabelMake(obname, 0, x + 4 * xs, y + 6 * ys, "M: " + IntegerToString(olmaysnzd_dblar[0]) + " / " + IntegerToString(ocmaysnzd_dblar[0]) + " / " + IntegerToString(ohmaysnzd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmaysnzd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "CADD";
   LabelMake(obname, 0, x + 8 * xs, y + 4 * ys, "D: " + IntegerToString(oldayscad_dblar[0]) + " / " + IntegerToString(ocdayscad_dblar[0]) + " / " + IntegerToString(ohdayscad_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdayscad_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "CADW";
   LabelMake(obname, 0, x + 8 * xs, y + 5 * ys, "W: " + IntegerToString(olwayscad_dblar[0]) + " / " + IntegerToString(ocwayscad_dblar[0]) + " / " + IntegerToString(ohwayscad_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwayscad_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "CADM";
   LabelMake(obname, 0, x + 8 * xs, y + 6 * ys, "M: " + IntegerToString(olmayscad_dblar[0]) + " / " + IntegerToString(ocmayscad_dblar[0]) + " / " + IntegerToString(ohmayscad_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmayscad_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   
   obname = Name + " " + "USDD";
   LabelMake(obname, 0, x + 12 * xs, y + 4 * ys, "D: " + IntegerToString(oldaysusd_dblar[0]) + " / " + IntegerToString(ocdaysusd_dblar[0]) + " / " + IntegerToString(ohdaysusd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocdaysusd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "USDW";
   LabelMake(obname, 0, x + 12 * xs, y + 5 * ys, "W: " + IntegerToString(olwaysusd_dblar[0]) + " / " + IntegerToString(ocwaysusd_dblar[0]) + " / " + IntegerToString(ohwaysusd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocwaysusd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   obname = Name + " " + "USDM";
   LabelMake(obname, 0, x + 12 * xs, y + 6 * ys, "M: " + IntegerToString(olmaysusd_dblar[0]) + " / " + IntegerToString(ocmaysusd_dblar[0]) + " / " + IntegerToString(ohmaysusd_dblar[0]), FontSize, clrDodgerBlue);
   if (ocmaysusd_dblar[0] < 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed); else ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
}
//+------------------------------------------------------------------+

//+THIRTY MIN CALCULATIONS-------------------------------------------+
void ThirtyCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_M30, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_M30, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_M30, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_M30, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_M30, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_M30, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_M30, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_M30, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_M30, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_M30, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_M30, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_M30, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_M30, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_M30, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_M30, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_M30, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_M30, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_M30, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_M30, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_M30, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_M30, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_M30, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_M30, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_M30, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_M30, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_M30, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_M30, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_M30, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar4, 0);
	ArrayInitialize(ochourchf_dblar4, 0);
	ArrayInitialize(ochourgbp_dblar4, 0);
	ArrayInitialize(ochourjpy_dblar4, 0);
	ArrayInitialize(ochouraud_dblar4, 0);
	ArrayInitialize(ochournzd_dblar4, 0);
	ArrayInitialize(ochourcad_dblar4, 0);
	ArrayInitialize(ochourusd_dblar4, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar4[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar4[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar4[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar4[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar4[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar4[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar4[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar4[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+HOURLY CALCULATIONS-----------------------------------------------+
void HourlyCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_H1, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_H1, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_H1, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_H1, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_H1, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_H1, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_H1, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_H1, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_H1, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_H1, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_H1, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_H1, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_H1, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_H1, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_H1, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_H1, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_H1, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_H1, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_H1, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_H1, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_H1, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_H1, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_H1, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_H1, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_H1, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_H1, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_H1, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_H1, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar, 0);
	ArrayInitialize(ochourchf_dblar, 0);
	ArrayInitialize(ochourgbp_dblar, 0);
	ArrayInitialize(ochourjpy_dblar, 0);
	ArrayInitialize(ochouraud_dblar, 0);
	ArrayInitialize(ochournzd_dblar, 0);
	ArrayInitialize(ochourcad_dblar, 0);
	ArrayInitialize(ochourusd_dblar, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+FOUR HOUR CALCULATIONS--------------------------------------------+
void FourCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_H4, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_H4, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_H4, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_H4, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_H4, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_H4, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_H4, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_H4, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_H4, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_H4, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_H4, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_H4, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_H4, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_H4, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_H4, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_H4, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_H4, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_H4, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_H4, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_H4, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_H4, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_H4, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_H4, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_H4, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_H4, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_H4, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_H4, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_H4, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar5, 0);
	ArrayInitialize(ochourchf_dblar5, 0);
	ArrayInitialize(ochourgbp_dblar5, 0);
	ArrayInitialize(ochourjpy_dblar5, 0);
	ArrayInitialize(ochouraud_dblar5, 0);
	ArrayInitialize(ochournzd_dblar5, 0);
	ArrayInitialize(ochourcad_dblar5, 0);
	ArrayInitialize(ochourusd_dblar5, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar5[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar5[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar5[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar5[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar5[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar5[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar5[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar5[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+FIFTEEN MIN CALCULATIONS------------------------------------------+
void FifteenCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_M15, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_M15, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_M15, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_M15, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_M15, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_M15, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_M15, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_M15, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_M15, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_M15, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_M15, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_M15, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_M15, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_M15, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_M15, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_M15, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_M15, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_M15, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_M15, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_M15, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_M15, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_M15, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_M15, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_M15, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_M15, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_M15, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_M15, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_M15, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar1, 0);
	ArrayInitialize(ochourchf_dblar1, 0);
	ArrayInitialize(ochourgbp_dblar1, 0);
	ArrayInitialize(ochourjpy_dblar1, 0);
	ArrayInitialize(ochouraud_dblar1, 0);
	ArrayInitialize(ochournzd_dblar1, 0);
	ArrayInitialize(ochourcad_dblar1, 0);
	ArrayInitialize(ochourusd_dblar1, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar1[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar1[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar1[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar1[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar1[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar1[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar1[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar1[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+FIVE MIN CALCULATIONS---------------------------------------------+
void FiveCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_M5, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_M5, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_M5, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_M5, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_M5, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_M5, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_M5, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_M5, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_M5, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_M5, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_M5, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_M5, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_M5, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_M5, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_M5, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_M5, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_M5, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_M5, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_M5, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_M5, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_M5, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_M5, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_M5, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_M5, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_M5, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_M5, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_M5, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_M5, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar2, 0);
	ArrayInitialize(ochourchf_dblar2, 0);
	ArrayInitialize(ochourgbp_dblar2, 0);
	ArrayInitialize(ochourjpy_dblar2, 0);
	ArrayInitialize(ochouraud_dblar2, 0);
	ArrayInitialize(ochournzd_dblar2, 0);
	ArrayInitialize(ochourcad_dblar2, 0);
	ArrayInitialize(ochourusd_dblar2, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar2[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar2[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar2[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar2[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar2[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar2[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar2[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar2[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+ONE MIN CALCULATIONS----------------------------------------------+
void OneCalcs()
{
	int n1 = 10000;
	int n2 = 100;

	//HOUR
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 15);
	ArrayResize(CD1e2, 15);
	ArrayResize(CD1e3, 15);
	ArrayResize(CD1e4, 15);
	ArrayResize(CD1e5, 15);
	ArrayResize(CD1e6, 15);
	ArrayResize(CD1e7, 15);
	if (CopyClose(aa, PERIOD_M1, 0, 15, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_M1, 0, 15, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_M1, 0, 15, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_M1, 0, 15, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_M1, 0, 15, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_M1, 0, 15, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_M1, 0, 15, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 15);
	ArrayResize(CD1f3, 15);
	ArrayResize(CD1f4, 15);
	ArrayResize(CD1f5, 15);
	ArrayResize(CD1f6, 15);
	ArrayResize(CD1f7, 15);
	if (CopyClose(bb, PERIOD_M1, 0, 15, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_M1, 0, 15, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_M1, 0, 15, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_M1, 0, 15, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_M1, 0, 15, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_M1, 0, 15, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 15);
	ArrayResize(CD1g4, 15);
	ArrayResize(CD1g5, 15);
	ArrayResize(CD1g6, 15);
	ArrayResize(CD1g7, 15);
	if (CopyClose(cc, PERIOD_M1, 0, 15, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_M1, 0, 15, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_M1, 0, 15, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_M1, 0, 15, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_M1, 0, 15, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 15);
	ArrayResize(CD1j5, 15);
	ArrayResize(CD1j6, 15);
	ArrayResize(CD1j7, 15);
	if (CopyClose(dd, PERIOD_M1, 0, 15, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_M1, 0, 15, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_M1, 0, 15, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_M1, 0, 15, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 15);
	ArrayResize(CD1a6, 15);
	ArrayResize(CD1a7, 15);
	if (CopyClose(ee, PERIOD_M1, 0, 15, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_M1, 0, 15, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_M1, 0, 15, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 15);
	ArrayResize(CD1n7, 15);
	if (CopyClose(ff, PERIOD_M1, 0, 15, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_M1, 0, 15, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 15);
	if (CopyClose(gg, PERIOD_M1, 0, 15, CD1c7) < 0)
		Print(GetLastError());
	//Print(CD1e1[1501] + " " + CD1e2[1501] + " " + CD1e3[1501] + " " + CD1e4[1501] + " " + CD1e5[1501] + " " + CD1e6[1501] + " " + CD1e7[1501] + " " + CD1f2[1501] + " " + CD1f3[1501] + " " + CD1f4[1501] + " " + CD1f5[1501] + " " + CD1f6[1501] + " " + CD1f7[1501] + " " + CD1g3[1501] + " " + CD1g4[1501] + " " + CD1g5[1501] + " " + CD1g6[1501] + " " + CD1g7[1501] + " " + CD1j4[1501] + " " + CD1j5[1501] + " " + CD1j6[1501] + " " + CD1j7[1501] + " " + CD1a5[1501] + " " + CD1a6[1501] + " " + CD1a7[1501] + " " + CD1n6[1501] + " " + CD1n7[1501] + " " + CD1c7[1501]);

	ArrayInitialize(ochoureur_dblar3, 0);
	ArrayInitialize(ochourchf_dblar3, 0);
	ArrayInitialize(ochourgbp_dblar3, 0);
	ArrayInitialize(ochourjpy_dblar3, 0);
	ArrayInitialize(ochouraud_dblar3, 0);
	ArrayInitialize(ochournzd_dblar3, 0);
	ArrayInitialize(ochourcad_dblar3, 0);
	ArrayInitialize(ochourusd_dblar3, 0);

	for (i = 13; i >= 1; i--)
	{
		ochoureur_dblar3[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
		// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
		ochourchf_dblar3[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
		// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
		ochourgbp_dblar3[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
		// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
		ochourjpy_dblar3[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
		// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
		ochouraud_dblar3[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
		// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
		ochournzd_dblar3[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
		// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
		ochourcad_dblar3[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
		// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
		ochourusd_dblar3[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
	}
}
//+------------------------------------------------------------------+

//+FILL TODAY D/W/M BUFFER-------------------------------------------+
void FillTodayBuffer()
{
	//uint start=GetTickCount();
	int n1 = 10000;
	int n2 = 100;
	{	//BufferFill for current
		//Day
		ocdayseur_dblar[0] = (cdod(aa, n1, 0) + cdod(ab, n1, 0) + cdod(ac, n1, 0) + cdod(ad, n2, 0) + cdod(ae, n1, 0) + cdod(af, n1, 0) + cdod(ag, n1, 0));
		ocdayschf_dblar[0] = (-cdod(ab, n1, 0) - cdod(bb, n1, 0) - cdod(bc, n1, 0) + cdod(bd, n2, 0) - cdod(be, n1, 0) - cdod(bf, n1, 0) - cdod(bg, n1, 0));
		ocdaysgbp_dblar[0] = (-cdod(ac, n1, 0) + cdod(bc, n1, 0) + cdod(cc, n1, 0) + cdod(cd, n2, 0) + cdod(ce, n1, 0) + cdod(cf, n1, 0) + cdod(cg, n1, 0));
		ocdaysjpy_dblar[0] = (-cdod(ad, n2, 0) - cdod(bd, n2, 0) - cdod(cd, n2, 0) - cdod(dd, n2, 0) - cdod(de, n2, 0) - cdod(df, n2, 0) - cdod(dg, n2, 0));
		ocdaysaud_dblar[0] = (-cdod(ae, n1, 0) + cdod(be, n1, 0) - cdod(ce, n1, 0) + cdod(de, n2, 0) + cdod(ee, n1, 0) + cdod(ef, n1, 0) + cdod(eg, n1, 0));
		ocdaysnzd_dblar[0] = (-cdod(af, n1, 0) + cdod(bf, n1, 0) - cdod(cf, n1, 0) + cdod(df, n2, 0) - cdod(ef, n1, 0) + cdod(ff, n1, 0) + cdod(fg, n1, 0));
		ocdayscad_dblar[0] = (-cdod(ag, n1, 0) + cdod(bg, n1, 0) - cdod(cg, n1, 0) + cdod(dg, n2, 0) - cdod(eg, n1, 0) - cdod(fg, n1, 0) - cdod(gg, n1, 0));
		ocdaysusd_dblar[0] = (-cdod(aa, n1, 0) + cdod(bb, n1, 0) - cdod(cc, n1, 0) + cdod(dd, n2, 0) - cdod(ee, n1, 0) - cdod(ff, n1, 0) + cdod(gg, n1, 0));

		ohdayseur_dblar[0] = (hdod(aa, n1, 0) + hdod(ab, n1, 0) + hdod(ac, n1, 0) + hdod(ad, n2, 0) + hdod(ae, n1, 0) + hdod(af, n1, 0) + hdod(ag, n1, 0));
		ohdayschf_dblar[0] = (-ldod(ab, n1, 0) - ldod(bb, n1, 0) - ldod(bc, n1, 0) + hdod(bd, n2, 0) - ldod(be, n1, 0) - ldod(bf, n1, 0) - ldod(bg, n1, 0));
		ohdaysgbp_dblar[0] = (-ldod(ac, n1, 0) + hdod(bc, n1, 0) + hdod(cc, n1, 0) + hdod(cd, n2, 0) + hdod(ce, n1, 0) + hdod(cf, n1, 0) + hdod(cg, n1, 0));
		ohdaysjpy_dblar[0] = (-ldod(ad, n2, 0) - ldod(bd, n2, 0) - ldod(cd, n2, 0) - ldod(dd, n2, 0) - ldod(de, n2, 0) - ldod(df, n2, 0) - ldod(dg, n2, 0));
		ohdaysaud_dblar[0] = (-ldod(ae, n1, 0) + hdod(be, n1, 0) - ldod(ce, n1, 0) + hdod(de, n2, 0) + hdod(ee, n1, 0) + hdod(ef, n1, 0) + hdod(eg, n1, 0));
		ohdaysnzd_dblar[0] = (-ldod(af, n1, 0) + hdod(bf, n1, 0) - ldod(cf, n1, 0) + hdod(df, n2, 0) - ldod(ef, n1, 0) + hdod(ff, n1, 0) + hdod(fg, n1, 0));
		ohdayscad_dblar[0] = (-ldod(ag, n1, 0) + hdod(bg, n1, 0) - ldod(cg, n1, 0) + hdod(dg, n2, 0) - ldod(eg, n1, 0) - ldod(fg, n1, 0) - ldod(gg, n1, 0));
		ohdaysusd_dblar[0] = (-ldod(aa, n1, 0) + hdod(bb, n1, 0) - ldod(cc, n1, 0) + hdod(dd, n2, 0) - ldod(ee, n1, 0) - ldod(ff, n1, 0) + hdod(gg, n1, 0));

		oldayseur_dblar[0] = (ldod(aa, n1, 0) + ldod(ab, n1, 0) + ldod(ac, n1, 0) + ldod(ad, n2, 0) + ldod(ae, n1, 0) + ldod(af, n1, 0) + ldod(ag, n1, 0));
		oldayschf_dblar[0] = (-hdod(ab, n1, 0) - hdod(bb, n1, 0) - hdod(bc, n1, 0) + ldod(bd, n2, 0) - hdod(be, n1, 0) - hdod(bf, n1, 0) - hdod(bg, n1, 0));
		oldaysgbp_dblar[0] = (-hdod(ac, n1, 0) + ldod(bc, n1, 0) + ldod(cc, n1, 0) + ldod(cd, n2, 0) + ldod(ce, n1, 0) + ldod(cf, n1, 0) + ldod(cg, n1, 0));
		oldaysjpy_dblar[0] = (-hdod(ad, n2, 0) - hdod(bd, n2, 0) - hdod(cd, n2, 0) - hdod(dd, n2, 0) - hdod(de, n2, 0) - hdod(df, n2, 0) - hdod(dg, n2, 0));
		oldaysaud_dblar[0] = (-hdod(ae, n1, 0) + ldod(be, n1, 0) - hdod(ce, n1, 0) + ldod(de, n2, 0) + ldod(ee, n1, 0) + ldod(ef, n1, 0) + ldod(eg, n1, 0));
		oldaysnzd_dblar[0] = (-hdod(af, n1, 0) + ldod(bf, n1, 0) - hdod(cf, n1, 0) + ldod(df, n2, 0) - hdod(ef, n1, 0) + ldod(ff, n1, 0) + ldod(fg, n1, 0));
		oldayscad_dblar[0] = (-hdod(ag, n1, 0) + ldod(bg, n1, 0) - hdod(cg, n1, 0) + ldod(dg, n2, 0) - hdod(eg, n1, 0) - hdod(fg, n1, 0) - hdod(gg, n1, 0));
		oldaysusd_dblar[0] = (-hdod(aa, n1, 0) + ldod(bb, n1, 0) - hdod(cc, n1, 0) + ldod(dd, n2, 0) - hdod(ee, n1, 0) - hdod(ff, n1, 0) + ldod(gg, n1, 0));

		//Week
		ocwayseur_dblar[0] = (cwow(aa, n1, 0) + cwow(ab, n1, 0) + cwow(ac, n1, 0) + cwow(ad, n2, 0) + cwow(ae, n1, 0) + cwow(af, n1, 0) + cwow(ag, n1, 0));
		ocwayschf_dblar[0] = (-cwow(ab, n1, 0) - cwow(bb, n1, 0) - cwow(bc, n1, 0) + cwow(bd, n2, 0) - cwow(be, n1, 0) - cwow(bf, n1, 0) - cwow(bg, n1, 0));
		ocwaysgbp_dblar[0] = (-cwow(ac, n1, 0) + cwow(bc, n1, 0) + cwow(cc, n1, 0) + cwow(cd, n2, 0) + cwow(ce, n1, 0) + cwow(cf, n1, 0) + cwow(cg, n1, 0));
		ocwaysjpy_dblar[0] = (-cwow(ad, n2, 0) - cwow(bd, n2, 0) - cwow(cd, n2, 0) - cwow(dd, n2, 0) - cwow(de, n2, 0) - cwow(df, n2, 0) - cwow(dg, n2, 0));
		ocwaysaud_dblar[0] = (-cwow(ae, n1, 0) + cwow(be, n1, 0) - cwow(ce, n1, 0) + cwow(de, n2, 0) + cwow(ee, n1, 0) + cwow(ef, n1, 0) + cwow(eg, n1, 0));
		ocwaysnzd_dblar[0] = (-cwow(af, n1, 0) + cwow(bf, n1, 0) - cwow(cf, n1, 0) + cwow(df, n2, 0) - cwow(ef, n1, 0) + cwow(ff, n1, 0) + cwow(fg, n1, 0));
		ocwayscad_dblar[0] = (-cwow(ag, n1, 0) + cwow(bg, n1, 0) - cwow(cg, n1, 0) + cwow(dg, n2, 0) - cwow(eg, n1, 0) - cwow(fg, n1, 0) - cwow(gg, n1, 0));
		ocwaysusd_dblar[0] = (-cwow(aa, n1, 0) + cwow(bb, n1, 0) - cwow(cc, n1, 0) + cwow(dd, n2, 0) - cwow(ee, n1, 0) - cwow(ff, n1, 0) + cwow(gg, n1, 0));

		ohwayseur_dblar[0] = (hwow(aa, n1, 0) + hwow(ab, n1, 0) + hwow(ac, n1, 0) + hwow(ad, n2, 0) + hwow(ae, n1, 0) + hwow(af, n1, 0) + hwow(ag, n1, 0));
		ohwayschf_dblar[0] = (-lwow(ab, n1, 0) - lwow(bb, n1, 0) - lwow(bc, n1, 0) + hwow(bd, n2, 0) - lwow(be, n1, 0) - lwow(bf, n1, 0) - lwow(bg, n1, 0));
		ohwaysgbp_dblar[0] = (-lwow(ac, n1, 0) + hwow(bc, n1, 0) + hwow(cc, n1, 0) + hwow(cd, n2, 0) + hwow(ce, n1, 0) + hwow(cf, n1, 0) + hwow(cg, n1, 0));
		ohwaysjpy_dblar[0] = (-lwow(ad, n2, 0) - lwow(bd, n2, 0) - lwow(cd, n2, 0) - lwow(dd, n2, 0) - lwow(de, n2, 0) - lwow(df, n2, 0) - lwow(dg, n2, 0));
		ohwaysaud_dblar[0] = (-lwow(ae, n1, 0) + hwow(be, n1, 0) - lwow(ce, n1, 0) + hwow(de, n2, 0) + hwow(ee, n1, 0) + hwow(ef, n1, 0) + hwow(eg, n1, 0));
		ohwaysnzd_dblar[0] = (-lwow(af, n1, 0) + hwow(bf, n1, 0) - lwow(cf, n1, 0) + hwow(df, n2, 0) - lwow(ef, n1, 0) + hwow(ff, n1, 0) + hwow(fg, n1, 0));
		ohwayscad_dblar[0] = (-lwow(ag, n1, 0) + hwow(bg, n1, 0) - lwow(cg, n1, 0) + hwow(dg, n2, 0) - lwow(eg, n1, 0) - lwow(fg, n1, 0) - lwow(gg, n1, 0));
		ohwaysusd_dblar[0] = (-lwow(aa, n1, 0) + hwow(bb, n1, 0) - lwow(cc, n1, 0) + hwow(dd, n2, 0) - lwow(ee, n1, 0) - lwow(ff, n1, 0) + hwow(gg, n1, 0));

		olwayseur_dblar[0] = (lwow(aa, n1, 0) + lwow(ab, n1, 0) + lwow(ac, n1, 0) + lwow(ad, n2, 0) + lwow(ae, n1, 0) + lwow(af, n1, 0) + lwow(ag, n1, 0));
		olwayschf_dblar[0] = (-hwow(ab, n1, 0) - hwow(bb, n1, 0) - hwow(bc, n1, 0) + lwow(bd, n2, 0) - hwow(be, n1, 0) - hwow(bf, n1, 0) - hwow(bg, n1, 0));
		olwaysgbp_dblar[0] = (-hwow(ac, n1, 0) + lwow(bc, n1, 0) + lwow(cc, n1, 0) + lwow(cd, n2, 0) + lwow(ce, n1, 0) + lwow(cf, n1, 0) + lwow(cg, n1, 0));
		olwaysjpy_dblar[0] = (-hwow(ad, n2, 0) - hwow(bd, n2, 0) - hwow(cd, n2, 0) - hwow(dd, n2, 0) - hwow(de, n2, 0) - hwow(df, n2, 0) - hwow(dg, n2, 0));
		olwaysaud_dblar[0] = (-hwow(ae, n1, 0) + lwow(be, n1, 0) - hwow(ce, n1, 0) + lwow(de, n2, 0) + lwow(ee, n1, 0) + lwow(ef, n1, 0) + lwow(eg, n1, 0));
		olwaysnzd_dblar[0] = (-hwow(af, n1, 0) + lwow(bf, n1, 0) - hwow(cf, n1, 0) + lwow(df, n2, 0) - hwow(ef, n1, 0) + lwow(ff, n1, 0) + lwow(fg, n1, 0));
		olwayscad_dblar[0] = (-hwow(ag, n1, 0) + lwow(bg, n1, 0) - hwow(cg, n1, 0) + lwow(dg, n2, 0) - hwow(eg, n1, 0) - hwow(fg, n1, 0) - hwow(gg, n1, 0));
		olwaysusd_dblar[0] = (-hwow(aa, n1, 0) + lwow(bb, n1, 0) - hwow(cc, n1, 0) + lwow(dd, n2, 0) - hwow(ee, n1, 0) - hwow(ff, n1, 0) + lwow(gg, n1, 0));

		//Month
		ocmayseur_dblar[0] = (cmom(aa, n1, 0) + cmom(ab, n1, 0) + cmom(ac, n1, 0) + cmom(ad, n2, 0) + cmom(ae, n1, 0) + cmom(af, n1, 0) + cmom(ag, n1, 0));
		ocmayschf_dblar[0] = (-cmom(ab, n1, 0) - cmom(bb, n1, 0) - cmom(bc, n1, 0) + cmom(bd, n2, 0) - cmom(be, n1, 0) - cmom(bf, n1, 0) - cmom(bg, n1, 0));
		ocmaysgbp_dblar[0] = (-cmom(ac, n1, 0) + cmom(bc, n1, 0) + cmom(cc, n1, 0) + cmom(cd, n2, 0) + cmom(ce, n1, 0) + cmom(cf, n1, 0) + cmom(cg, n1, 0));
		ocmaysjpy_dblar[0] = (-cmom(ad, n2, 0) - cmom(bd, n2, 0) - cmom(cd, n2, 0) - cmom(dd, n2, 0) - cmom(de, n2, 0) - cmom(df, n2, 0) - cmom(dg, n2, 0));
		ocmaysaud_dblar[0] = (-cmom(ae, n1, 0) + cmom(be, n1, 0) - cmom(ce, n1, 0) + cmom(de, n2, 0) + cmom(ee, n1, 0) + cmom(ef, n1, 0) + cmom(eg, n1, 0));
		ocmaysnzd_dblar[0] = (-cmom(af, n1, 0) + cmom(bf, n1, 0) - cmom(cf, n1, 0) + cmom(df, n2, 0) - cmom(ef, n1, 0) + cmom(ff, n1, 0) + cmom(fg, n1, 0));
		ocmayscad_dblar[0] = (-cmom(ag, n1, 0) + cmom(bg, n1, 0) - cmom(cg, n1, 0) + cmom(dg, n2, 0) - cmom(eg, n1, 0) - cmom(fg, n1, 0) - cmom(gg, n1, 0));
		ocmaysusd_dblar[0] = (-cmom(aa, n1, 0) + cmom(bb, n1, 0) - cmom(cc, n1, 0) + cmom(dd, n2, 0) - cmom(ee, n1, 0) - cmom(ff, n1, 0) + cmom(gg, n1, 0));

		ohmayseur_dblar[0] = (hmom(aa, n1, 0) + hmom(ab, n1, 0) + hmom(ac, n1, 0) + hmom(ad, n2, 0) + hmom(ae, n1, 0) + hmom(af, n1, 0) + hmom(ag, n1, 0));
		ohmayschf_dblar[0] = (-lmom(ab, n1, 0) - lmom(bb, n1, 0) - lmom(bc, n1, 0) + hmom(bd, n2, 0) - lmom(be, n1, 0) - lmom(bf, n1, 0) - lmom(bg, n1, 0));
		ohmaysgbp_dblar[0] = (-lmom(ac, n1, 0) + hmom(bc, n1, 0) + hmom(cc, n1, 0) + hmom(cd, n2, 0) + hmom(ce, n1, 0) + hmom(cf, n1, 0) + hmom(cg, n1, 0));
		ohmaysjpy_dblar[0] = (-lmom(ad, n2, 0) - lmom(bd, n2, 0) - lmom(cd, n2, 0) - lmom(dd, n2, 0) - lmom(de, n2, 0) - lmom(df, n2, 0) - lmom(dg, n2, 0));
		ohmaysaud_dblar[0] = (-lmom(ae, n1, 0) + hmom(be, n1, 0) - lmom(ce, n1, 0) + hmom(de, n2, 0) + hmom(ee, n1, 0) + hmom(ef, n1, 0) + hmom(eg, n1, 0));
		ohmaysnzd_dblar[0] = (-lmom(af, n1, 0) + hmom(bf, n1, 0) - lmom(cf, n1, 0) + hmom(df, n2, 0) - lmom(ef, n1, 0) + hmom(ff, n1, 0) + hmom(fg, n1, 0));
		ohmayscad_dblar[0] = (-lmom(ag, n1, 0) + hmom(bg, n1, 0) - lmom(cg, n1, 0) + hmom(dg, n2, 0) - lmom(eg, n1, 0) - lmom(fg, n1, 0) - lmom(gg, n1, 0));
		ohmaysusd_dblar[0] = (-lmom(aa, n1, 0) + hmom(bb, n1, 0) - lmom(cc, n1, 0) + hmom(dd, n2, 0) - lmom(ee, n1, 0) - lmom(ff, n1, 0) + hmom(gg, n1, 0));

		olmayseur_dblar[0] = (lmom(aa, n1, 0) + lmom(ab, n1, 0) + lmom(ac, n1, 0) + lmom(ad, n2, 0) + lmom(ae, n1, 0) + lmom(af, n1, 0) + lmom(ag, n1, 0));
		olmayschf_dblar[0] = (-hmom(ab, n1, 0) - hmom(bb, n1, 0) - hmom(bc, n1, 0) + lmom(bd, n2, 0) - hmom(be, n1, 0) - hmom(bf, n1, 0) - hmom(bg, n1, 0));
		olmaysgbp_dblar[0] = (-hmom(ac, n1, 0) + lmom(bc, n1, 0) + lmom(cc, n1, 0) + lmom(cd, n2, 0) + lmom(ce, n1, 0) + lmom(cf, n1, 0) + lmom(cg, n1, 0));
		olmaysjpy_dblar[0] = (-hmom(ad, n2, 0) - hmom(bd, n2, 0) - hmom(cd, n2, 0) - hmom(dd, n2, 0) - hmom(de, n2, 0) - hmom(df, n2, 0) - hmom(dg, n2, 0));
		olmaysaud_dblar[0] = (-hmom(ae, n1, 0) + lmom(be, n1, 0) - hmom(ce, n1, 0) + lmom(de, n2, 0) + lmom(ee, n1, 0) + lmom(ef, n1, 0) + lmom(eg, n1, 0));
		olmaysnzd_dblar[0] = (-hmom(af, n1, 0) + lmom(bf, n1, 0) - hmom(cf, n1, 0) + lmom(df, n2, 0) - hmom(ef, n1, 0) + lmom(ff, n1, 0) + lmom(fg, n1, 0));
		olmayscad_dblar[0] = (-hmom(ag, n1, 0) + lmom(bg, n1, 0) - hmom(cg, n1, 0) + lmom(dg, n2, 0) - hmom(eg, n1, 0) - hmom(fg, n1, 0) - hmom(gg, n1, 0));
		olmaysusd_dblar[0] = (-hmom(aa, n1, 0) + lmom(bb, n1, 0) - hmom(cc, n1, 0) + lmom(dd, n2, 0) - hmom(ee, n1, 0) - hmom(ff, n1, 0) + lmom(gg, n1, 0));
	}
	//uint end=GetTickCount()-start; Print("BuildTodayBuffers function took ms: ",end);
}
//+------------------------------------------------------------------+

//+FILL PAST D/W/M BUFFERS-------------------------------------------+
void FillPastBuffer()
{
	int n1 = 10000;
	int n2 = 100;

	//DAY
	//CLOSE ARRAYS
	double CD1e1[], CD1e2[], CD1e3[], CD1e4[], CD1e5[], CD1e6[], CD1e7[];
	ArraySetAsSeries(CD1e1, true);
	ArraySetAsSeries(CD1e2, true);
	ArraySetAsSeries(CD1e3, true);
	ArraySetAsSeries(CD1e4, true);
	ArraySetAsSeries(CD1e5, true);
	ArraySetAsSeries(CD1e6, true);
	ArraySetAsSeries(CD1e7, true);
	ArrayResize(CD1e1, 205);
	ArrayResize(CD1e2, 205);
	ArrayResize(CD1e3, 205);
	ArrayResize(CD1e4, 205);
	ArrayResize(CD1e5, 205);
	ArrayResize(CD1e6, 205);
	ArrayResize(CD1e7, 205);
	if (CopyClose(aa, PERIOD_D1, 0, 205, CD1e1) < 0)
		Print(GetLastError());
	if (CopyClose(ab, PERIOD_D1, 0, 205, CD1e2) < 0)
		Print(GetLastError());
	if (CopyClose(ac, PERIOD_D1, 0, 205, CD1e3) < 0)
		Print(GetLastError());
	if (CopyClose(ad, PERIOD_D1, 0, 205, CD1e4) < 0)
		Print(GetLastError());
	if (CopyClose(ae, PERIOD_D1, 0, 205, CD1e5) < 0)
		Print(GetLastError());
	if (CopyClose(af, PERIOD_D1, 0, 205, CD1e6) < 0)
		Print(GetLastError());
	if (CopyClose(ag, PERIOD_D1, 0, 205, CD1e7) < 0)
		Print(GetLastError());

	double CD1f2[], CD1f3[], CD1f4[], CD1f5[], CD1f6[], CD1f7[];
	ArraySetAsSeries(CD1f2, true);
	ArraySetAsSeries(CD1f3, true);
	ArraySetAsSeries(CD1f4, true);
	ArraySetAsSeries(CD1f5, true);
	ArraySetAsSeries(CD1f6, true);
	ArraySetAsSeries(CD1f7, true);
	ArrayResize(CD1f2, 205);
	ArrayResize(CD1f3, 205);
	ArrayResize(CD1f4, 205);
	ArrayResize(CD1f5, 205);
	ArrayResize(CD1f6, 205);
	ArrayResize(CD1f7, 205);
	if (CopyClose(bb, PERIOD_D1, 0, 205, CD1f2) < 0)
		Print(GetLastError());
	if (CopyClose(bc, PERIOD_D1, 0, 205, CD1f3) < 0)
		Print(GetLastError());
	if (CopyClose(bd, PERIOD_D1, 0, 205, CD1f4) < 0)
		Print(GetLastError());
	if (CopyClose(be, PERIOD_D1, 0, 205, CD1f5) < 0)
		Print(GetLastError());
	if (CopyClose(bf, PERIOD_D1, 0, 205, CD1f6) < 0)
		Print(GetLastError());
	if (CopyClose(bg, PERIOD_D1, 0, 205, CD1f7) < 0)
		Print(GetLastError());

	double CD1g3[], CD1g4[], CD1g5[], CD1g6[], CD1g7[];
	ArraySetAsSeries(CD1g3, true);
	ArraySetAsSeries(CD1g4, true);
	ArraySetAsSeries(CD1g5, true);
	ArraySetAsSeries(CD1g6, true);
	ArraySetAsSeries(CD1g7, true);
	ArrayResize(CD1g3, 205);
	ArrayResize(CD1g4, 205);
	ArrayResize(CD1g5, 205);
	ArrayResize(CD1g6, 205);
	ArrayResize(CD1g7, 205);
	if (CopyClose(cc, PERIOD_D1, 0, 205, CD1g3) < 0)
		Print(GetLastError());
	if (CopyClose(cd, PERIOD_D1, 0, 205, CD1g4) < 0)
		Print(GetLastError());
	if (CopyClose(ce, PERIOD_D1, 0, 205, CD1g5) < 0)
		Print(GetLastError());
	if (CopyClose(cf, PERIOD_D1, 0, 205, CD1g6) < 0)
		Print(GetLastError());
	if (CopyClose(cg, PERIOD_D1, 0, 205, CD1g7) < 0)
		Print(GetLastError());

	double CD1j4[], CD1j5[], CD1j6[], CD1j7[];
	ArraySetAsSeries(CD1j4, true);
	ArraySetAsSeries(CD1j5, true);
	ArraySetAsSeries(CD1j6, true);
	ArraySetAsSeries(CD1j7, true);
	ArrayResize(CD1j4, 205);
	ArrayResize(CD1j5, 205);
	ArrayResize(CD1j6, 205);
	ArrayResize(CD1j7, 205);
	if (CopyClose(dd, PERIOD_D1, 0, 205, CD1j4) < 0)
		Print(GetLastError());
	if (CopyClose(de, PERIOD_D1, 0, 205, CD1j5) < 0)
		Print(GetLastError());
	if (CopyClose(df, PERIOD_D1, 0, 205, CD1j6) < 0)
		Print(GetLastError());
	if (CopyClose(dg, PERIOD_D1, 0, 205, CD1j7) < 0)
		Print(GetLastError());

	double CD1a5[], CD1a6[], CD1a7[];
	ArraySetAsSeries(CD1a5, true);
	ArraySetAsSeries(CD1a6, true);
	ArraySetAsSeries(CD1a7, true);
	ArrayResize(CD1a5, 205);
	ArrayResize(CD1a6, 205);
	ArrayResize(CD1a7, 205);
	if (CopyClose(ee, PERIOD_D1, 0, 205, CD1a5) < 0)
		Print(GetLastError());
	if (CopyClose(ef, PERIOD_D1, 0, 205, CD1a6) < 0)
		Print(GetLastError());
	if (CopyClose(eg, PERIOD_D1, 0, 205, CD1a7) < 0)
		Print(GetLastError());

	double CD1n6[], CD1n7[];
	ArraySetAsSeries(CD1n6, true);
	ArraySetAsSeries(CD1n7, true);
	ArrayResize(CD1n6, 205);
	ArrayResize(CD1n7, 205);
	if (CopyClose(ff, PERIOD_D1, 0, 205, CD1n6) < 0)
		Print(GetLastError());
	if (CopyClose(fg, PERIOD_D1, 0, 205, CD1n7) < 0)
		Print(GetLastError());

	double CD1c7[];
	ArraySetAsSeries(CD1c7, true);
	ArrayResize(CD1c7, 205);
	if (CopyClose(gg, PERIOD_D1, 0, 205, CD1c7) < 0)
		Print(GetLastError());

	//HIGH ARRAYS
	double HD1e1[], HD1e2[], HD1e3[], HD1e4[], HD1e5[], HD1e6[], HD1e7[];
	ArraySetAsSeries(HD1e1, true);
	ArraySetAsSeries(HD1e2, true);
	ArraySetAsSeries(HD1e3, true);
	ArraySetAsSeries(HD1e4, true);
	ArraySetAsSeries(HD1e5, true);
	ArraySetAsSeries(HD1e6, true);
	ArraySetAsSeries(HD1e7, true);
	ArrayResize(HD1e1, 205);
	ArrayResize(HD1e2, 205);
	ArrayResize(HD1e3, 205);
	ArrayResize(HD1e4, 205);
	ArrayResize(HD1e5, 205);
	ArrayResize(HD1e6, 205);
	ArrayResize(HD1e7, 205);
	if (CopyHigh(aa, PERIOD_D1, 0, 205, HD1e1) < 0)
		Print(GetLastError());
	if (CopyHigh(ab, PERIOD_D1, 0, 205, HD1e2) < 0)
		Print(GetLastError());
	if (CopyHigh(ac, PERIOD_D1, 0, 205, HD1e3) < 0)
		Print(GetLastError());
	if (CopyHigh(ad, PERIOD_D1, 0, 205, HD1e4) < 0)
		Print(GetLastError());
	if (CopyHigh(ae, PERIOD_D1, 0, 205, HD1e5) < 0)
		Print(GetLastError());
	if (CopyHigh(af, PERIOD_D1, 0, 205, HD1e6) < 0)
		Print(GetLastError());
	if (CopyHigh(ag, PERIOD_D1, 0, 205, HD1e7) < 0)
		Print(GetLastError());

	double HD1f2[], HD1f3[], HD1f4[], HD1f5[], HD1f6[], HD1f7[];
	ArraySetAsSeries(HD1f2, true);
	ArraySetAsSeries(HD1f3, true);
	ArraySetAsSeries(HD1f4, true);
	ArraySetAsSeries(HD1f5, true);
	ArraySetAsSeries(HD1f6, true);
	ArraySetAsSeries(HD1f7, true);
	ArrayResize(HD1f2, 205);
	ArrayResize(HD1f3, 205);
	ArrayResize(HD1f4, 205);
	ArrayResize(HD1f5, 205);
	ArrayResize(HD1f6, 205);
	ArrayResize(HD1f7, 205);
	if (CopyHigh(bb, PERIOD_D1, 0, 205, HD1f2) < 0)
		Print(GetLastError());
	if (CopyHigh(bc, PERIOD_D1, 0, 205, HD1f3) < 0)
		Print(GetLastError());
	if (CopyHigh(bd, PERIOD_D1, 0, 205, HD1f4) < 0)
		Print(GetLastError());
	if (CopyHigh(be, PERIOD_D1, 0, 205, HD1f5) < 0)
		Print(GetLastError());
	if (CopyHigh(bf, PERIOD_D1, 0, 205, HD1f6) < 0)
		Print(GetLastError());
	if (CopyHigh(bg, PERIOD_D1, 0, 205, HD1f7) < 0)
		Print(GetLastError());

	double HD1g3[], HD1g4[], HD1g5[], HD1g6[], HD1g7[];
	ArraySetAsSeries(HD1g3, true);
	ArraySetAsSeries(HD1g4, true);
	ArraySetAsSeries(HD1g5, true);
	ArraySetAsSeries(HD1g6, true);
	ArraySetAsSeries(HD1g7, true);
	ArrayResize(HD1g3, 205);
	ArrayResize(HD1g4, 205);
	ArrayResize(HD1g5, 205);
	ArrayResize(HD1g6, 205);
	ArrayResize(HD1g7, 205);
	if (CopyHigh(cc, PERIOD_D1, 0, 205, HD1g3) < 0)
		Print(GetLastError());
	if (CopyHigh(cd, PERIOD_D1, 0, 205, HD1g4) < 0)
		Print(GetLastError());
	if (CopyHigh(ce, PERIOD_D1, 0, 205, HD1g5) < 0)
		Print(GetLastError());
	if (CopyHigh(cf, PERIOD_D1, 0, 205, HD1g6) < 0)
		Print(GetLastError());
	if (CopyHigh(cg, PERIOD_D1, 0, 205, HD1g7) < 0)
		Print(GetLastError());

	double HD1j4[], HD1j5[], HD1j6[], HD1j7[];
	ArraySetAsSeries(HD1j4, true);
	ArraySetAsSeries(HD1j5, true);
	ArraySetAsSeries(HD1j6, true);
	ArraySetAsSeries(HD1j7, true);
	ArrayResize(HD1j4, 205);
	ArrayResize(HD1j5, 205);
	ArrayResize(HD1j6, 205);
	ArrayResize(HD1j7, 205);
	if (CopyHigh(dd, PERIOD_D1, 0, 205, HD1j4) < 0)
		Print(GetLastError());
	if (CopyHigh(de, PERIOD_D1, 0, 205, HD1j5) < 0)
		Print(GetLastError());
	if (CopyHigh(df, PERIOD_D1, 0, 205, HD1j6) < 0)
		Print(GetLastError());
	if (CopyHigh(dg, PERIOD_D1, 0, 205, HD1j7) < 0)
		Print(GetLastError());

	double HD1a5[], HD1a6[], HD1a7[];
	ArraySetAsSeries(HD1a5, true);
	ArraySetAsSeries(HD1a6, true);
	ArraySetAsSeries(HD1a7, true);
	ArrayResize(HD1a5, 205);
	ArrayResize(HD1a6, 205);
	ArrayResize(HD1a7, 205);
	if (CopyHigh(ee, PERIOD_D1, 0, 205, HD1a5) < 0)
		Print(GetLastError());
	if (CopyHigh(ef, PERIOD_D1, 0, 205, HD1a6) < 0)
		Print(GetLastError());
	if (CopyHigh(eg, PERIOD_D1, 0, 205, HD1a7) < 0)
		Print(GetLastError());

	double HD1n6[], HD1n7[];
	ArraySetAsSeries(HD1n6, true);
	ArraySetAsSeries(HD1n7, true);
	ArrayResize(HD1n6, 205);
	ArrayResize(HD1n7, 205);
	if (CopyHigh(ff, PERIOD_D1, 0, 205, HD1n6) < 0)
		Print(GetLastError());
	if (CopyHigh(fg, PERIOD_D1, 0, 205, HD1n7) < 0)
		Print(GetLastError());

	double HD1c7[];
	ArraySetAsSeries(HD1c7, true);
	ArrayResize(HD1c7, 205);
	if (CopyHigh(gg, PERIOD_D1, 0, 205, HD1c7) < 0)
		Print(GetLastError());

	//LOW ARRAYS
	double LD1e1[], LD1e2[], LD1e3[], LD1e4[], LD1e5[], LD1e6[], LD1e7[];
	ArraySetAsSeries(LD1e1, true);
	ArraySetAsSeries(LD1e2, true);
	ArraySetAsSeries(LD1e3, true);
	ArraySetAsSeries(LD1e4, true);
	ArraySetAsSeries(LD1e5, true);
	ArraySetAsSeries(LD1e6, true);
	ArraySetAsSeries(LD1e7, true);
	ArrayResize(LD1e1, 205);
	ArrayResize(LD1e2, 205);
	ArrayResize(LD1e3, 205);
	ArrayResize(LD1e4, 205);
	ArrayResize(LD1e5, 205);
	ArrayResize(LD1e6, 205);
	ArrayResize(LD1e7, 205);
	if (CopyLow(aa, PERIOD_D1, 0, 205, LD1e1) < 0)
		Print(GetLastError());
	if (CopyLow(ab, PERIOD_D1, 0, 205, LD1e2) < 0)
		Print(GetLastError());
	if (CopyLow(ac, PERIOD_D1, 0, 205, LD1e3) < 0)
		Print(GetLastError());
	if (CopyLow(ad, PERIOD_D1, 0, 205, LD1e4) < 0)
		Print(GetLastError());
	if (CopyLow(ae, PERIOD_D1, 0, 205, LD1e5) < 0)
		Print(GetLastError());
	if (CopyLow(af, PERIOD_D1, 0, 205, LD1e6) < 0)
		Print(GetLastError());
	if (CopyLow(ag, PERIOD_D1, 0, 205, LD1e7) < 0)
		Print(GetLastError());

	double LD1f2[], LD1f3[], LD1f4[], LD1f5[], LD1f6[], LD1f7[];
	ArraySetAsSeries(LD1f2, true);
	ArraySetAsSeries(LD1f3, true);
	ArraySetAsSeries(LD1f4, true);
	ArraySetAsSeries(LD1f5, true);
	ArraySetAsSeries(LD1f6, true);
	ArraySetAsSeries(LD1f7, true);
	ArrayResize(LD1f2, 205);
	ArrayResize(LD1f3, 205);
	ArrayResize(LD1f4, 205);
	ArrayResize(LD1f5, 205);
	ArrayResize(LD1f6, 205);
	ArrayResize(LD1f7, 205);
	if (CopyLow(bb, PERIOD_D1, 0, 205, LD1f2) < 0)
		Print(GetLastError());
	if (CopyLow(bc, PERIOD_D1, 0, 205, LD1f3) < 0)
		Print(GetLastError());
	if (CopyLow(bd, PERIOD_D1, 0, 205, LD1f4) < 0)
		Print(GetLastError());
	if (CopyLow(be, PERIOD_D1, 0, 205, LD1f5) < 0)
		Print(GetLastError());
	if (CopyLow(bf, PERIOD_D1, 0, 205, LD1f6) < 0)
		Print(GetLastError());
	if (CopyLow(bg, PERIOD_D1, 0, 205, LD1f7) < 0)
		Print(GetLastError());

	double LD1g3[], LD1g4[], LD1g5[], LD1g6[], LD1g7[];
	ArraySetAsSeries(LD1g3, true);
	ArraySetAsSeries(LD1g4, true);
	ArraySetAsSeries(LD1g5, true);
	ArraySetAsSeries(LD1g6, true);
	ArraySetAsSeries(LD1g7, true);
	ArrayResize(LD1g3, 205);
	ArrayResize(LD1g4, 205);
	ArrayResize(LD1g5, 205);
	ArrayResize(LD1g6, 205);
	ArrayResize(LD1g7, 205);
	if (CopyLow(cc, PERIOD_D1, 0, 205, LD1g3) < 0)
		Print(GetLastError());
	if (CopyLow(cd, PERIOD_D1, 0, 205, LD1g4) < 0)
		Print(GetLastError());
	if (CopyLow(ce, PERIOD_D1, 0, 205, LD1g5) < 0)
		Print(GetLastError());
	if (CopyLow(cf, PERIOD_D1, 0, 205, LD1g6) < 0)
		Print(GetLastError());
	if (CopyLow(cg, PERIOD_D1, 0, 205, LD1g7) < 0)
		Print(GetLastError());

	double LD1j4[], LD1j5[], LD1j6[], LD1j7[];
	ArraySetAsSeries(LD1j4, true);
	ArraySetAsSeries(LD1j5, true);
	ArraySetAsSeries(LD1j6, true);
	ArraySetAsSeries(LD1j7, true);
	ArrayResize(LD1j4, 205);
	ArrayResize(LD1j5, 205);
	ArrayResize(LD1j6, 205);
	ArrayResize(LD1j7, 205);
	if (CopyLow(dd, PERIOD_D1, 0, 205, LD1j4) < 0)
		Print(GetLastError());
	if (CopyLow(de, PERIOD_D1, 0, 205, LD1j5) < 0)
		Print(GetLastError());
	if (CopyLow(df, PERIOD_D1, 0, 205, LD1j6) < 0)
		Print(GetLastError());
	if (CopyLow(dg, PERIOD_D1, 0, 205, LD1j7) < 0)
		Print(GetLastError());

	double LD1a5[], LD1a6[], LD1a7[];
	ArraySetAsSeries(LD1a5, true);
	ArraySetAsSeries(LD1a6, true);
	ArraySetAsSeries(LD1a7, true);
	ArrayResize(LD1a5, 205);
	ArrayResize(LD1a6, 205);
	ArrayResize(LD1a7, 205);
	if (CopyLow(ee, PERIOD_D1, 0, 205, LD1a5) < 0)
		Print(GetLastError());
	if (CopyLow(ef, PERIOD_D1, 0, 205, LD1a6) < 0)
		Print(GetLastError());
	if (CopyLow(eg, PERIOD_D1, 0, 205, LD1a7) < 0)
		Print(GetLastError());

	double LD1n6[], LD1n7[];
	ArraySetAsSeries(LD1n6, true);
	ArraySetAsSeries(LD1n7, true);
	ArrayResize(LD1n6, 205);
	ArrayResize(LD1n7, 205);
	if (CopyLow(ff, PERIOD_D1, 0, 205, LD1n6) < 0)
		Print(GetLastError());
	if (CopyLow(fg, PERIOD_D1, 0, 205, LD1n7) < 0)
		Print(GetLastError());

	double LD1c7[];
	ArraySetAsSeries(LD1c7, true);
	ArrayResize(LD1c7, 205);
	if (CopyLow(gg, PERIOD_D1, 0, 205, LD1c7) < 0)
		Print(GetLastError());

	{	//BufferFill for past prices
		//Day
		for (i = 1; i <= 200; i++)
		{
			ocdayseur_dblar[i] = cdod1(CD1e1, n1, i) + cdod1(CD1e2, n1, i) + cdod1(CD1e3, n1, i) + cdod1(CD1e4, n2, i) + cdod1(CD1e5, n1, i) + cdod1(CD1e6, n1, i) + cdod1(CD1e7, n1, i);
			// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
			ocdayschf_dblar[i] = -cdod1(CD1e2, n1, i) - cdod1(CD1f2, n1, i) - cdod1(CD1f3, n1, i) + cdod1(CD1f4, n2, i) - cdod1(CD1f5, n1, i) - cdod1(CD1f6, n1, i) - cdod1(CD1f7, n1, i);
			// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
			ocdaysgbp_dblar[i] = -cdod1(CD1e3, n1, i) + cdod1(CD1f3, n1, i) + cdod1(CD1g3, n1, i) + cdod1(CD1g4, n2, i) + cdod1(CD1g5, n1, i) + cdod1(CD1g6, n1, i) + cdod1(CD1g7, n1, i);
			// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
			ocdaysjpy_dblar[i] = -cdod1(CD1e4, n2, i) - cdod1(CD1f4, n2, i) - cdod1(CD1g4, n2, i) - cdod1(CD1j4, n2, i) - cdod1(CD1j5, n2, i) - cdod1(CD1j6, n2, i) - cdod1(CD1j7, n2, i);
			// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
			ocdaysaud_dblar[i] = -cdod1(CD1e5, n1, i) + cdod1(CD1f5, n1, i) - cdod1(CD1g5, n1, i) + cdod1(CD1j5, n2, i) + cdod1(CD1a5, n1, i) + cdod1(CD1a6, n1, i) + cdod1(CD1a7, n1, i);
			// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
			ocdaysnzd_dblar[i] = -cdod1(CD1e6, n1, i) + cdod1(CD1f6, n1, i) - cdod1(CD1g6, n1, i) + cdod1(CD1j6, n2, i) - cdod1(CD1a6, n1, i) + cdod1(CD1n6, n1, i) + cdod1(CD1n7, n1, i);
			// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
			ocdayscad_dblar[i] = -cdod1(CD1e7, n1, i) + cdod1(CD1f7, n1, i) - cdod1(CD1g7, n1, i) + cdod1(CD1j7, n2, i) - cdod1(CD1a7, n1, i) - cdod1(CD1n7, n1, i) - cdod1(CD1c7, n1, i);
			// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
			ocdaysusd_dblar[i] = -cdod1(CD1e1, n1, i) + cdod1(CD1f2, n1, i) - cdod1(CD1g3, n1, i) + cdod1(CD1j4, n2, i) - cdod1(CD1a5, n1, i) - cdod1(CD1n6, n1, i) + cdod1(CD1c7, n1, i);
			// -E/USD USD/C -G/USD USD/J -A/USD -N/USD USD/C

			ohdayseur_dblar[i] = hdod1(HD1e1, CD1e1, n1, i) + hdod1(HD1e2, CD1e2, n1, i) + hdod1(HD1e3, CD1e3, n1, i) + hdod1(HD1e4, CD1e4, n2, i) + hdod1(HD1e5, CD1e5, n1, i) + hdod1(HD1e6, CD1e6, n1, i) + hdod1(HD1e7, CD1e7, n1, i);
			ohdayschf_dblar[i] = -ldod1(LD1e2, CD1e2, n1, i) - ldod1(LD1f2, CD1f2, n1, i) - ldod1(LD1f3, CD1f3, n1, i) + hdod1(HD1f4, CD1f4, n2, i) - ldod1(LD1f5, CD1f5, n1, i) - ldod1(LD1f6, CD1f6, n1, i) - ldod1(LD1f7, CD1f7, n1, i);
			ohdaysgbp_dblar[i] = -ldod1(LD1e3, CD1e3, n1, i) + hdod1(HD1f3, CD1f3, n1, i) + hdod1(HD1g3, CD1g3, n1, i) + hdod1(HD1g4, CD1g4, n2, i) + hdod1(HD1g5, CD1g5, n1, i) + hdod1(HD1g6, CD1g6, n1, i) + hdod1(HD1g7, CD1g7, n1, i);
			ohdaysjpy_dblar[i] = -ldod1(LD1e4, CD1e4, n2, i) - ldod1(LD1f4, CD1f4, n2, i) - ldod1(LD1g4, CD1g4, n2, i) - ldod1(LD1j4, CD1j4, n2, i) - ldod1(LD1j5, CD1j5, n2, i) - ldod1(LD1j6, CD1j6, n2, i) - ldod1(LD1j7, CD1j7, n2, i);
			ohdaysaud_dblar[i] = -ldod1(LD1e5, CD1e5, n1, i) + hdod1(HD1f5, CD1f5, n1, i) - ldod1(LD1g5, CD1g5, n1, i) + hdod1(HD1j5, CD1j5, n2, i) + hdod1(HD1a5, CD1a5, n1, i) + hdod1(HD1a6, CD1a6, n1, i) + hdod1(HD1a7, CD1a7, n1, i);
			ohdaysnzd_dblar[i] = -ldod1(LD1e6, CD1e6, n1, i) + hdod1(HD1f6, CD1f6, n1, i) - ldod1(LD1g6, CD1g6, n1, i) + hdod1(HD1j6, CD1j6, n2, i) - ldod1(LD1a6, CD1a6, n1, i) + hdod1(HD1n6, CD1n6, n1, i) + hdod1(HD1n7, CD1n7, n1, i);
			ohdayscad_dblar[i] = -ldod1(LD1e7, CD1e7, n1, i) + hdod1(HD1f7, CD1f7, n1, i) - ldod1(LD1g7, CD1g7, n1, i) + hdod1(HD1j7, CD1j7, n2, i) - ldod1(LD1a7, CD1a7, n1, i) - ldod1(LD1n7, CD1n7, n1, i) - ldod1(LD1c7, CD1c7, n1, i);
			ohdaysusd_dblar[i] = -ldod1(LD1e1, CD1e1, n1, i) + hdod1(HD1f2, CD1f2, n1, i) - ldod1(LD1g3, CD1g3, n1, i) + hdod1(HD1j4, CD1j4, n2, i) - ldod1(LD1a5, CD1a5, n1, i) - ldod1(LD1n6, CD1n6, n1, i) + hdod1(HD1c7, CD1c7, n1, i);

			oldayseur_dblar[i] = ldod1(LD1e1, CD1e1, n1, i) + ldod1(LD1e2, CD1e2, n1, i) + ldod1(LD1e3, CD1e3, n1, i) + ldod1(LD1e4, CD1e4, n2, i) + ldod1(LD1e5, CD1e5, n1, i) + ldod1(LD1e6, CD1e6, n1, i) + ldod1(LD1e7, CD1e7, n1, i);
			oldayschf_dblar[i] = -hdod1(HD1e2, CD1e2, n1, i) - hdod1(HD1f2, CD1f2, n1, i) - hdod1(HD1f3, CD1f3, n1, i) + ldod1(LD1f4, CD1f4, n2, i) - hdod1(HD1f5, CD1f5, n1, i) - hdod1(HD1f6, CD1f6, n1, i) - hdod1(HD1f7, CD1f7, n1, i);
			oldaysgbp_dblar[i] = -hdod1(HD1e3, CD1e3, n1, i) + ldod1(LD1f3, CD1f3, n1, i) + ldod1(LD1g3, CD1g3, n1, i) + ldod1(LD1g4, CD1g4, n2, i) + ldod1(LD1g5, CD1g5, n1, i) + ldod1(LD1g6, CD1g6, n1, i) + ldod1(LD1g7, CD1g7, n1, i);
			oldaysjpy_dblar[i] = -hdod1(HD1e4, CD1e4, n2, i) - hdod1(HD1f4, CD1f4, n2, i) - hdod1(HD1g4, CD1g4, n2, i) - hdod1(HD1j4, CD1j4, n2, i) - hdod1(HD1j5, CD1j5, n2, i) - hdod1(HD1j6, CD1j6, n2, i) - hdod1(HD1j7, CD1j7, n2, i);
			oldaysaud_dblar[i] = -hdod1(HD1e5, CD1e5, n1, i) + ldod1(LD1f5, CD1f5, n1, i) - hdod1(HD1g5, CD1g5, n1, i) + ldod1(LD1j5, CD1j5, n2, i) + ldod1(LD1a5, CD1a5, n1, i) + ldod1(LD1a6, CD1a6, n1, i) + ldod1(LD1a7, CD1a7, n1, i);
			oldaysnzd_dblar[i] = -hdod1(HD1e6, CD1e6, n1, i) + ldod1(LD1f6, CD1f6, n1, i) - hdod1(HD1g6, CD1g6, n1, i) + ldod1(LD1j6, CD1j6, n2, i) - hdod1(HD1a6, CD1a6, n1, i) + ldod1(LD1n6, CD1n6, n1, i) + ldod1(LD1n7, CD1n7, n1, i);
			oldayscad_dblar[i] = -hdod1(HD1e7, CD1e7, n1, i) + ldod1(LD1f7, CD1f7, n1, i) - hdod1(HD1g7, CD1g7, n1, i) + ldod1(LD1j7, CD1j7, n2, i) - hdod1(HD1a7, CD1a7, n1, i) - hdod1(HD1n7, CD1n7, n1, i) - hdod1(HD1c7, CD1c7, n1, i);
			oldaysusd_dblar[i] = -hdod1(HD1e1, CD1e1, n1, i) + ldod1(LD1f2, CD1f2, n1, i) - hdod1(HD1g3, CD1g3, n1, i) + ldod1(LD1j4, CD1j4, n2, i) - hdod1(HD1a5, CD1a5, n1, i) - hdod1(HD1n6, CD1n6, n1, i) + ldod1(LD1c7, CD1c7, n1, i);
		}

		//ways
		//CLOSE ARRAYS
		double WCD1e1[], WCD1e2[], WCD1e3[], WCD1e4[], WCD1e5[], WCD1e6[], WCD1e7[];
		ArraySetAsSeries(WCD1e1, true);
		ArraySetAsSeries(WCD1e2, true);
		ArraySetAsSeries(WCD1e3, true);
		ArraySetAsSeries(WCD1e4, true);
		ArraySetAsSeries(WCD1e5, true);
		ArraySetAsSeries(WCD1e6, true);
		ArraySetAsSeries(WCD1e7, true);
		ArrayResize(WCD1e1, 30);
		ArrayResize(WCD1e2, 30);
		ArrayResize(WCD1e3, 30);
		ArrayResize(WCD1e4, 30);
		ArrayResize(WCD1e5, 30);
		ArrayResize(WCD1e6, 30);
		ArrayResize(WCD1e7, 30);
		if (CopyClose(aa, PERIOD_W1, 0, 30, WCD1e1) < 0)
			Print(GetLastError());
		if (CopyClose(ab, PERIOD_W1, 0, 30, WCD1e2) < 0)
			Print(GetLastError());
		if (CopyClose(ac, PERIOD_W1, 0, 30, WCD1e3) < 0)
			Print(GetLastError());
		if (CopyClose(ad, PERIOD_W1, 0, 30, WCD1e4) < 0)
			Print(GetLastError());
		if (CopyClose(ae, PERIOD_W1, 0, 30, WCD1e5) < 0)
			Print(GetLastError());
		if (CopyClose(af, PERIOD_W1, 0, 30, WCD1e6) < 0)
			Print(GetLastError());
		if (CopyClose(ag, PERIOD_W1, 0, 30, WCD1e7) < 0)
			Print(GetLastError());

		double WCD1f2[], WCD1f3[], WCD1f4[], WCD1f5[], WCD1f6[], WCD1f7[];
		ArraySetAsSeries(WCD1f2, true);
		ArraySetAsSeries(WCD1f3, true);
		ArraySetAsSeries(WCD1f4, true);
		ArraySetAsSeries(WCD1f5, true);
		ArraySetAsSeries(WCD1f6, true);
		ArraySetAsSeries(WCD1f7, true);
		ArrayResize(WCD1f2, 30);
		ArrayResize(WCD1f3, 30);
		ArrayResize(WCD1f4, 30);
		ArrayResize(WCD1f5, 30);
		ArrayResize(WCD1f6, 30);
		ArrayResize(WCD1f7, 30);
		if (CopyClose(bb, PERIOD_W1, 0, 30, WCD1f2) < 0)
			Print(GetLastError());
		if (CopyClose(bc, PERIOD_W1, 0, 30, WCD1f3) < 0)
			Print(GetLastError());
		if (CopyClose(bd, PERIOD_W1, 0, 30, WCD1f4) < 0)
			Print(GetLastError());
		if (CopyClose(be, PERIOD_W1, 0, 30, WCD1f5) < 0)
			Print(GetLastError());
		if (CopyClose(bf, PERIOD_W1, 0, 30, WCD1f6) < 0)
			Print(GetLastError());
		if (CopyClose(bg, PERIOD_W1, 0, 30, WCD1f7) < 0)
			Print(GetLastError());

		double WCD1g3[], WCD1g4[], WCD1g5[], WCD1g6[], WCD1g7[];
		ArraySetAsSeries(WCD1g3, true);
		ArraySetAsSeries(WCD1g4, true);
		ArraySetAsSeries(WCD1g5, true);
		ArraySetAsSeries(WCD1g6, true);
		ArraySetAsSeries(WCD1g7, true);
		ArrayResize(WCD1g3, 30);
		ArrayResize(WCD1g4, 30);
		ArrayResize(WCD1g5, 30);
		ArrayResize(WCD1g6, 30);
		ArrayResize(WCD1g7, 30);
		if (CopyClose(cc, PERIOD_W1, 0, 30, WCD1g3) < 0)
			Print(GetLastError());
		if (CopyClose(cd, PERIOD_W1, 0, 30, WCD1g4) < 0)
			Print(GetLastError());
		if (CopyClose(ce, PERIOD_W1, 0, 30, WCD1g5) < 0)
			Print(GetLastError());
		if (CopyClose(cf, PERIOD_W1, 0, 30, WCD1g6) < 0)
			Print(GetLastError());
		if (CopyClose(cg, PERIOD_W1, 0, 30, WCD1g7) < 0)
			Print(GetLastError());

		double WCD1j4[], WCD1j5[], WCD1j6[], WCD1j7[];
		ArraySetAsSeries(WCD1j4, true);
		ArraySetAsSeries(WCD1j5, true);
		ArraySetAsSeries(WCD1j6, true);
		ArraySetAsSeries(WCD1j7, true);
		ArrayResize(WCD1j4, 30);
		ArrayResize(WCD1j5, 30);
		ArrayResize(WCD1j6, 30);
		ArrayResize(WCD1j7, 30);
		if (CopyClose(dd, PERIOD_W1, 0, 30, WCD1j4) < 0)
			Print(GetLastError());
		if (CopyClose(de, PERIOD_W1, 0, 30, WCD1j5) < 0)
			Print(GetLastError());
		if (CopyClose(df, PERIOD_W1, 0, 30, WCD1j6) < 0)
			Print(GetLastError());
		if (CopyClose(dg, PERIOD_W1, 0, 30, WCD1j7) < 0)
			Print(GetLastError());

		double WCD1a5[], WCD1a6[], WCD1a7[];
		ArraySetAsSeries(WCD1a5, true);
		ArraySetAsSeries(WCD1a6, true);
		ArraySetAsSeries(WCD1a7, true);
		ArrayResize(WCD1a5, 30);
		ArrayResize(WCD1a6, 30);
		ArrayResize(WCD1a7, 30);
		if (CopyClose(ee, PERIOD_W1, 0, 30, WCD1a5) < 0)
			Print(GetLastError());
		if (CopyClose(ef, PERIOD_W1, 0, 30, WCD1a6) < 0)
			Print(GetLastError());
		if (CopyClose(eg, PERIOD_W1, 0, 30, WCD1a7) < 0)
			Print(GetLastError());

		double WCD1n6[], WCD1n7[];
		ArraySetAsSeries(WCD1n6, true);
		ArraySetAsSeries(WCD1n7, true);
		ArrayResize(WCD1n6, 30);
		ArrayResize(WCD1n7, 30);
		if (CopyClose(ff, PERIOD_W1, 0, 30, WCD1n6) < 0)
			Print(GetLastError());
		if (CopyClose(fg, PERIOD_W1, 0, 30, WCD1n7) < 0)
			Print(GetLastError());

		double WCD1c7[];
		ArraySetAsSeries(WCD1c7, true);
		ArrayResize(WCD1c7, 30);
		if (CopyClose(gg, PERIOD_W1, 0, 30, WCD1c7) < 0)
			Print(GetLastError());

		//HIGH ARRAYS
		double WHD1e1[], WHD1e2[], WHD1e3[], WHD1e4[], WHD1e5[], WHD1e6[], WHD1e7[];
		ArraySetAsSeries(WHD1e1, true);
		ArraySetAsSeries(WHD1e2, true);
		ArraySetAsSeries(WHD1e3, true);
		ArraySetAsSeries(WHD1e4, true);
		ArraySetAsSeries(WHD1e5, true);
		ArraySetAsSeries(WHD1e6, true);
		ArraySetAsSeries(WHD1e7, true);
		ArrayResize(WHD1e1, 30);
		ArrayResize(WHD1e2, 30);
		ArrayResize(WHD1e3, 30);
		ArrayResize(WHD1e4, 30);
		ArrayResize(WHD1e5, 30);
		ArrayResize(WHD1e6, 30);
		ArrayResize(WHD1e7, 30);
		if (CopyHigh(aa, PERIOD_W1, 0, 30, WHD1e1) < 0)
			Print(GetLastError());
		if (CopyHigh(ab, PERIOD_W1, 0, 30, WHD1e2) < 0)
			Print(GetLastError());
		if (CopyHigh(ac, PERIOD_W1, 0, 30, WHD1e3) < 0)
			Print(GetLastError());
		if (CopyHigh(ad, PERIOD_W1, 0, 30, WHD1e4) < 0)
			Print(GetLastError());
		if (CopyHigh(ae, PERIOD_W1, 0, 30, WHD1e5) < 0)
			Print(GetLastError());
		if (CopyHigh(af, PERIOD_W1, 0, 30, WHD1e6) < 0)
			Print(GetLastError());
		if (CopyHigh(ag, PERIOD_W1, 0, 30, WHD1e7) < 0)
			Print(GetLastError());

		double WHD1f2[], WHD1f3[], WHD1f4[], WHD1f5[], WHD1f6[], WHD1f7[];
		ArraySetAsSeries(WHD1f2, true);
		ArraySetAsSeries(WHD1f3, true);
		ArraySetAsSeries(WHD1f4, true);
		ArraySetAsSeries(WHD1f5, true);
		ArraySetAsSeries(WHD1f6, true);
		ArraySetAsSeries(WHD1f7, true);
		ArrayResize(WHD1f2, 30);
		ArrayResize(WHD1f3, 30);
		ArrayResize(WHD1f4, 30);
		ArrayResize(WHD1f5, 30);
		ArrayResize(WHD1f6, 30);
		ArrayResize(WHD1f7, 30);
		if (CopyHigh(bb, PERIOD_W1, 0, 30, WHD1f2) < 0)
			Print(GetLastError());
		if (CopyHigh(bc, PERIOD_W1, 0, 30, WHD1f3) < 0)
			Print(GetLastError());
		if (CopyHigh(bd, PERIOD_W1, 0, 30, WHD1f4) < 0)
			Print(GetLastError());
		if (CopyHigh(be, PERIOD_W1, 0, 30, WHD1f5) < 0)
			Print(GetLastError());
		if (CopyHigh(bf, PERIOD_W1, 0, 30, WHD1f6) < 0)
			Print(GetLastError());
		if (CopyHigh(bg, PERIOD_W1, 0, 30, WHD1f7) < 0)
			Print(GetLastError());

		double WHD1g3[], WHD1g4[], WHD1g5[], WHD1g6[], WHD1g7[];
		ArraySetAsSeries(WHD1g3, true);
		ArraySetAsSeries(WHD1g4, true);
		ArraySetAsSeries(WHD1g5, true);
		ArraySetAsSeries(WHD1g6, true);
		ArraySetAsSeries(WHD1g7, true);
		ArrayResize(WHD1g3, 30);
		ArrayResize(WHD1g4, 30);
		ArrayResize(WHD1g5, 30);
		ArrayResize(WHD1g6, 30);
		ArrayResize(WHD1g7, 30);
		if (CopyHigh(cc, PERIOD_W1, 0, 30, WHD1g3) < 0)
			Print(GetLastError());
		if (CopyHigh(cd, PERIOD_W1, 0, 30, WHD1g4) < 0)
			Print(GetLastError());
		if (CopyHigh(ce, PERIOD_W1, 0, 30, WHD1g5) < 0)
			Print(GetLastError());
		if (CopyHigh(cf, PERIOD_W1, 0, 30, WHD1g6) < 0)
			Print(GetLastError());
		if (CopyHigh(cg, PERIOD_W1, 0, 30, WHD1g7) < 0)
			Print(GetLastError());

		double WHD1j4[], WHD1j5[], WHD1j6[], WHD1j7[];
		ArraySetAsSeries(WHD1j4, true);
		ArraySetAsSeries(WHD1j5, true);
		ArraySetAsSeries(WHD1j6, true);
		ArraySetAsSeries(WHD1j7, true);
		ArrayResize(WHD1j4, 30);
		ArrayResize(WHD1j5, 30);
		ArrayResize(WHD1j6, 30);
		ArrayResize(WHD1j7, 30);
		if (CopyHigh(dd, PERIOD_W1, 0, 30, WHD1j4) < 0)
			Print(GetLastError());
		if (CopyHigh(de, PERIOD_W1, 0, 30, WHD1j5) < 0)
			Print(GetLastError());
		if (CopyHigh(df, PERIOD_W1, 0, 30, WHD1j6) < 0)
			Print(GetLastError());
		if (CopyHigh(dg, PERIOD_W1, 0, 30, WHD1j7) < 0)
			Print(GetLastError());

		double WHD1a5[], WHD1a6[], WHD1a7[];
		ArraySetAsSeries(WHD1a5, true);
		ArraySetAsSeries(WHD1a6, true);
		ArraySetAsSeries(WHD1a7, true);
		ArrayResize(WHD1a5, 30);
		ArrayResize(WHD1a6, 30);
		ArrayResize(WHD1a7, 30);
		if (CopyHigh(ee, PERIOD_W1, 0, 30, WHD1a5) < 0)
			Print(GetLastError());
		if (CopyHigh(ef, PERIOD_W1, 0, 30, WHD1a6) < 0)
			Print(GetLastError());
		if (CopyHigh(eg, PERIOD_W1, 0, 30, WHD1a7) < 0)
			Print(GetLastError());

		double WHD1n6[], WHD1n7[];
		ArraySetAsSeries(WHD1n6, true);
		ArraySetAsSeries(WHD1n7, true);
		ArrayResize(WHD1n6, 30);
		ArrayResize(WHD1n7, 30);
		if (CopyHigh(ff, PERIOD_W1, 0, 30, WHD1n6) < 0)
			Print(GetLastError());
		if (CopyHigh(fg, PERIOD_W1, 0, 30, WHD1n7) < 0)
			Print(GetLastError());

		double WHD1c7[];
		ArraySetAsSeries(WHD1c7, true);
		ArrayResize(WHD1c7, 30);
		if (CopyHigh(gg, PERIOD_W1, 0, 30, WHD1c7) < 0)
			Print(GetLastError());

		//LOW ARRAYS
		double WLD1e1[], WLD1e2[], WLD1e3[], WLD1e4[], WLD1e5[], WLD1e6[], WLD1e7[];
		ArraySetAsSeries(WLD1e1, true);
		ArraySetAsSeries(WLD1e2, true);
		ArraySetAsSeries(WLD1e3, true);
		ArraySetAsSeries(WLD1e4, true);
		ArraySetAsSeries(WLD1e5, true);
		ArraySetAsSeries(WLD1e6, true);
		ArraySetAsSeries(WLD1e7, true);
		ArrayResize(WLD1e1, 30);
		ArrayResize(WLD1e2, 30);
		ArrayResize(WLD1e3, 30);
		ArrayResize(WLD1e4, 30);
		ArrayResize(WLD1e5, 30);
		ArrayResize(WLD1e6, 30);
		ArrayResize(WLD1e7, 30);
		if (CopyLow(aa, PERIOD_W1, 0, 30, WLD1e1) < 0)
			Print(GetLastError());
		if (CopyLow(ab, PERIOD_W1, 0, 30, WLD1e2) < 0)
			Print(GetLastError());
		if (CopyLow(ac, PERIOD_W1, 0, 30, WLD1e3) < 0)
			Print(GetLastError());
		if (CopyLow(ad, PERIOD_W1, 0, 30, WLD1e4) < 0)
			Print(GetLastError());
		if (CopyLow(ae, PERIOD_W1, 0, 30, WLD1e5) < 0)
			Print(GetLastError());
		if (CopyLow(af, PERIOD_W1, 0, 30, WLD1e6) < 0)
			Print(GetLastError());
		if (CopyLow(ag, PERIOD_W1, 0, 30, WLD1e7) < 0)
			Print(GetLastError());

		double WLD1f2[], WLD1f3[], WLD1f4[], WLD1f5[], WLD1f6[], WLD1f7[];
		ArraySetAsSeries(WLD1f2, true);
		ArraySetAsSeries(WLD1f3, true);
		ArraySetAsSeries(WLD1f4, true);
		ArraySetAsSeries(WLD1f5, true);
		ArraySetAsSeries(WLD1f6, true);
		ArraySetAsSeries(WLD1f7, true);
		ArrayResize(WLD1f2, 30);
		ArrayResize(WLD1f3, 30);
		ArrayResize(WLD1f4, 30);
		ArrayResize(WLD1f5, 30);
		ArrayResize(WLD1f6, 30);
		ArrayResize(WLD1f7, 30);
		if (CopyLow(bb, PERIOD_W1, 0, 30, WLD1f2) < 0)
			Print(GetLastError());
		if (CopyLow(bc, PERIOD_W1, 0, 30, WLD1f3) < 0)
			Print(GetLastError());
		if (CopyLow(bd, PERIOD_W1, 0, 30, WLD1f4) < 0)
			Print(GetLastError());
		if (CopyLow(be, PERIOD_W1, 0, 30, WLD1f5) < 0)
			Print(GetLastError());
		if (CopyLow(bf, PERIOD_W1, 0, 30, WLD1f6) < 0)
			Print(GetLastError());
		if (CopyLow(bg, PERIOD_W1, 0, 30, WLD1f7) < 0)
			Print(GetLastError());

		double WLD1g3[], WLD1g4[], WLD1g5[], WLD1g6[], WLD1g7[];
		ArraySetAsSeries(WLD1g3, true);
		ArraySetAsSeries(WLD1g4, true);
		ArraySetAsSeries(WLD1g5, true);
		ArraySetAsSeries(WLD1g6, true);
		ArraySetAsSeries(WLD1g7, true);
		ArrayResize(WLD1g3, 30);
		ArrayResize(WLD1g4, 30);
		ArrayResize(WLD1g5, 30);
		ArrayResize(WLD1g6, 30);
		ArrayResize(WLD1g7, 30);
		if (CopyLow(cc, PERIOD_W1, 0, 30, WLD1g3) < 0)
			Print(GetLastError());
		if (CopyLow(cd, PERIOD_W1, 0, 30, WLD1g4) < 0)
			Print(GetLastError());
		if (CopyLow(ce, PERIOD_W1, 0, 30, WLD1g5) < 0)
			Print(GetLastError());
		if (CopyLow(cf, PERIOD_W1, 0, 30, WLD1g6) < 0)
			Print(GetLastError());
		if (CopyLow(cg, PERIOD_W1, 0, 30, WLD1g7) < 0)
			Print(GetLastError());

		double WLD1j4[], WLD1j5[], WLD1j6[], WLD1j7[];
		ArraySetAsSeries(WLD1j4, true);
		ArraySetAsSeries(WLD1j5, true);
		ArraySetAsSeries(WLD1j6, true);
		ArraySetAsSeries(WLD1j7, true);
		ArrayResize(WLD1j4, 30);
		ArrayResize(WLD1j5, 30);
		ArrayResize(WLD1j6, 30);
		ArrayResize(WLD1j7, 30);
		if (CopyLow(dd, PERIOD_W1, 0, 30, WLD1j4) < 0)
			Print(GetLastError());
		if (CopyLow(de, PERIOD_W1, 0, 30, WLD1j5) < 0)
			Print(GetLastError());
		if (CopyLow(df, PERIOD_W1, 0, 30, WLD1j6) < 0)
			Print(GetLastError());
		if (CopyLow(dg, PERIOD_W1, 0, 30, WLD1j7) < 0)
			Print(GetLastError());

		double WLD1a5[], WLD1a6[], WLD1a7[];
		ArraySetAsSeries(WLD1a5, true);
		ArraySetAsSeries(WLD1a6, true);
		ArraySetAsSeries(WLD1a7, true);
		ArrayResize(WLD1a5, 30);
		ArrayResize(WLD1a6, 30);
		ArrayResize(WLD1a7, 30);
		if (CopyLow(ee, PERIOD_W1, 0, 30, WLD1a5) < 0)
			Print(GetLastError());
		if (CopyLow(ef, PERIOD_W1, 0, 30, WLD1a6) < 0)
			Print(GetLastError());
		if (CopyLow(eg, PERIOD_W1, 0, 30, WLD1a7) < 0)
			Print(GetLastError());

		double WLD1n6[], WLD1n7[];
		ArraySetAsSeries(WLD1n6, true);
		ArraySetAsSeries(WLD1n7, true);
		ArrayResize(WLD1n6, 30);
		ArrayResize(WLD1n7, 30);
		if (CopyLow(ff, PERIOD_W1, 0, 30, WLD1n6) < 0)
			Print(GetLastError());
		if (CopyLow(fg, PERIOD_W1, 0, 30, WLD1n7) < 0)
			Print(GetLastError());

		double WLD1c7[];
		ArraySetAsSeries(WLD1c7, true);
		ArrayResize(WLD1c7, 30);
		if (CopyLow(gg, PERIOD_W1, 0, 30, WLD1c7) < 0)
			Print(GetLastError());

		//ways
		for (i = 1; i <= 26; i++)
		{
			ocwayseur_dblar[i] = cdod1(WCD1e1, n1, i) + cdod1(WCD1e2, n1, i) + cdod1(WCD1e3, n1, i) + cdod1(WCD1e4, n2, i) + cdod1(WCD1e5, n1, i) + cdod1(WCD1e6, n1, i) + cdod1(WCD1e7, n1, i);
			// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
			ocwayschf_dblar[i] = -cdod1(WCD1e2, n1, i) - cdod1(WCD1f2, n1, i) - cdod1(WCD1f3, n1, i) + cdod1(WCD1f4, n2, i) - cdod1(WCD1f5, n1, i) - cdod1(WCD1f6, n1, i) - cdod1(WCD1f7, n1, i);
			// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
			ocwaysgbp_dblar[i] = -cdod1(WCD1e3, n1, i) + cdod1(WCD1f3, n1, i) + cdod1(WCD1g3, n1, i) + cdod1(WCD1g4, n2, i) + cdod1(WCD1g5, n1, i) + cdod1(WCD1g6, n1, i) + cdod1(WCD1g7, n1, i);
			// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
			ocwaysjpy_dblar[i] = -cdod1(WCD1e4, n2, i) - cdod1(WCD1f4, n2, i) - cdod1(WCD1g4, n2, i) - cdod1(WCD1j4, n2, i) - cdod1(WCD1j5, n2, i) - cdod1(WCD1j6, n2, i) - cdod1(WCD1j7, n2, i);
			// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
			ocwaysaud_dblar[i] = -cdod1(WCD1e5, n1, i) + cdod1(WCD1f5, n1, i) - cdod1(WCD1g5, n1, i) + cdod1(WCD1j5, n2, i) + cdod1(WCD1a5, n1, i) + cdod1(WCD1a6, n1, i) + cdod1(WCD1a7, n1, i);
			// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
			ocwaysnzd_dblar[i] = -cdod1(WCD1e6, n1, i) + cdod1(WCD1f6, n1, i) - cdod1(WCD1g6, n1, i) + cdod1(WCD1j6, n2, i) - cdod1(WCD1a6, n1, i) + cdod1(WCD1n6, n1, i) + cdod1(WCD1n7, n1, i);
			// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
			ocwayscad_dblar[i] = -cdod1(WCD1e7, n1, i) + cdod1(WCD1f7, n1, i) - cdod1(WCD1g7, n1, i) + cdod1(WCD1j7, n2, i) - cdod1(WCD1a7, n1, i) - cdod1(WCD1n7, n1, i) - cdod1(WCD1c7, n1, i);
			// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
			ocwaysusd_dblar[i] = -cdod1(WCD1e1, n1, i) + cdod1(WCD1f2, n1, i) - cdod1(WCD1g3, n1, i) + cdod1(WCD1j4, n2, i) - cdod1(WCD1a5, n1, i) - cdod1(WCD1n6, n1, i) + cdod1(WCD1c7, n1, i);
			// -E/USD USD/C -G/USD USD/J -A/USD -N/USD USD/C

			ohwayseur_dblar[i] = hdod1(WHD1e1, WCD1e1, n1, i) + hdod1(WHD1e2, WCD1e2, n1, i) + hdod1(WHD1e3, WCD1e3, n1, i) + hdod1(WHD1e4, WCD1e4, n2, i) + hdod1(WHD1e5, WCD1e5, n1, i) + hdod1(WHD1e6, WCD1e6, n1, i) + hdod1(WHD1e7, WCD1e7, n1, i);
			ohwayschf_dblar[i] = -ldod1(WLD1e2, WCD1e2, n1, i) - ldod1(WLD1f2, WCD1f2, n1, i) - ldod1(WLD1f3, WCD1f3, n1, i) + hdod1(WHD1f4, WCD1f4, n2, i) - ldod1(WLD1f5, WCD1f5, n1, i) - ldod1(WLD1f6, WCD1f6, n1, i) - ldod1(WLD1f7, WCD1f7, n1, i);
			ohwaysgbp_dblar[i] = -ldod1(WLD1e3, WCD1e3, n1, i) + hdod1(WHD1f3, WCD1f3, n1, i) + hdod1(WHD1g3, WCD1g3, n1, i) + hdod1(WHD1g4, WCD1g4, n2, i) + hdod1(WHD1g5, WCD1g5, n1, i) + hdod1(WHD1g6, WCD1g6, n1, i) + hdod1(WHD1g7, WCD1g7, n1, i);
			ohwaysjpy_dblar[i] = -ldod1(WLD1e4, WCD1e4, n2, i) - ldod1(WLD1f4, WCD1f4, n2, i) - ldod1(WLD1g4, WCD1g4, n2, i) - ldod1(WLD1j4, WCD1j4, n2, i) - ldod1(WLD1j5, WCD1j5, n2, i) - ldod1(WLD1j6, WCD1j6, n2, i) - ldod1(WLD1j7, WCD1j7, n2, i);
			ohwaysaud_dblar[i] = -ldod1(WLD1e5, WCD1e5, n1, i) + hdod1(WHD1f5, WCD1f5, n1, i) - ldod1(WLD1g5, WCD1g5, n1, i) + hdod1(WHD1j5, WCD1j5, n2, i) + hdod1(WHD1a5, WCD1a5, n1, i) + hdod1(WHD1a6, WCD1a6, n1, i) + hdod1(WHD1a7, WCD1a7, n1, i);
			ohwaysnzd_dblar[i] = -ldod1(WLD1e6, WCD1e6, n1, i) + hdod1(WHD1f6, WCD1f6, n1, i) - ldod1(WLD1g6, WCD1g6, n1, i) + hdod1(WHD1j6, WCD1j6, n2, i) - ldod1(WLD1a6, WCD1a6, n1, i) + hdod1(WHD1n6, WCD1n6, n1, i) + hdod1(WHD1n7, WCD1n7, n1, i);
			ohwayscad_dblar[i] = -ldod1(WLD1e7, WCD1e7, n1, i) + hdod1(WHD1f7, WCD1f7, n1, i) - ldod1(WLD1g7, WCD1g7, n1, i) + hdod1(WHD1j7, WCD1j7, n2, i) - ldod1(WLD1a7, WCD1a7, n1, i) - ldod1(WLD1n7, WCD1n7, n1, i) - ldod1(WLD1c7, WCD1c7, n1, i);
			ohwaysusd_dblar[i] = -ldod1(WLD1e1, WCD1e1, n1, i) + hdod1(WHD1f2, WCD1f2, n1, i) - ldod1(WLD1g3, WCD1g3, n1, i) + hdod1(WHD1j4, WCD1j4, n2, i) - ldod1(WLD1a5, WCD1a5, n1, i) - ldod1(WLD1n6, WCD1n6, n1, i) + hdod1(WHD1c7, WCD1c7, n1, i);

			olwayseur_dblar[i] = ldod1(WLD1e1, WCD1e1, n1, i) + ldod1(WLD1e2, WCD1e2, n1, i) + ldod1(WLD1e3, WCD1e3, n1, i) + ldod1(WLD1e4, WCD1e4, n2, i) + ldod1(WLD1e5, WCD1e5, n1, i) + ldod1(WLD1e6, WCD1e6, n1, i) + ldod1(WLD1e7, WCD1e7, n1, i);
			olwayschf_dblar[i] = -hdod1(WHD1e2, WCD1e2, n1, i) - hdod1(WHD1f2, WCD1f2, n1, i) - hdod1(WHD1f3, WCD1f3, n1, i) + ldod1(WLD1f4, WCD1f4, n2, i) - hdod1(WHD1f5, WCD1f5, n1, i) - hdod1(WHD1f6, WCD1f6, n1, i) - hdod1(WHD1f7, WCD1f7, n1, i);
			olwaysgbp_dblar[i] = -hdod1(WHD1e3, WCD1e3, n1, i) + ldod1(WLD1f3, WCD1f3, n1, i) + ldod1(WLD1g3, WCD1g3, n1, i) + ldod1(WLD1g4, WCD1g4, n2, i) + ldod1(WLD1g5, WCD1g5, n1, i) + ldod1(WLD1g6, WCD1g6, n1, i) + ldod1(WLD1g7, WCD1g7, n1, i);
			olwaysjpy_dblar[i] = -hdod1(WHD1e4, WCD1e4, n2, i) - hdod1(WHD1f4, WCD1f4, n2, i) - hdod1(WHD1g4, WCD1g4, n2, i) - hdod1(WHD1j4, WCD1j4, n2, i) - hdod1(WHD1j5, WCD1j5, n2, i) - hdod1(WHD1j6, WCD1j6, n2, i) - hdod1(WHD1j7, WCD1j7, n2, i);
			olwaysaud_dblar[i] = -hdod1(WHD1e5, WCD1e5, n1, i) + ldod1(WLD1f5, WCD1f5, n1, i) - hdod1(WHD1g5, WCD1g5, n1, i) + ldod1(WLD1j5, WCD1j5, n2, i) + ldod1(WLD1a5, WCD1a5, n1, i) + ldod1(WLD1a6, WCD1a6, n1, i) + ldod1(WLD1a7, WCD1a7, n1, i);
			olwaysnzd_dblar[i] = -hdod1(WHD1e6, WCD1e6, n1, i) + ldod1(WLD1f6, WCD1f6, n1, i) - hdod1(WHD1g6, WCD1g6, n1, i) + ldod1(WLD1j6, WCD1j6, n2, i) - hdod1(WHD1a6, WCD1a6, n1, i) + ldod1(WLD1n6, WCD1n6, n1, i) + ldod1(WLD1n7, WCD1n7, n1, i);
			olwayscad_dblar[i] = -hdod1(WHD1e7, WCD1e7, n1, i) + ldod1(WLD1f7, WCD1f7, n1, i) - hdod1(WHD1g7, WCD1g7, n1, i) + ldod1(WLD1j7, WCD1j7, n2, i) - hdod1(WHD1a7, WCD1a7, n1, i) - hdod1(WHD1n7, WCD1n7, n1, i) - hdod1(WHD1c7, WCD1c7, n1, i);
			olwaysusd_dblar[i] = -hdod1(WHD1e1, WCD1e1, n1, i) + ldod1(WLD1f2, WCD1f2, n1, i) - hdod1(WHD1g3, WCD1g3, n1, i) + ldod1(WLD1j4, WCD1j4, n2, i) - hdod1(WHD1a5, WCD1a5, n1, i) - hdod1(WHD1n6, WCD1n6, n1, i) + ldod1(WLD1c7, WCD1c7, n1, i);
		}

		//ways
		//CLOSE ARRAYS
		double MCD1e1[], MCD1e2[], MCD1e3[], MCD1e4[], MCD1e5[], MCD1e6[], MCD1e7[];
		ArraySetAsSeries(MCD1e1, true);
		ArraySetAsSeries(MCD1e2, true);
		ArraySetAsSeries(MCD1e3, true);
		ArraySetAsSeries(MCD1e4, true);
		ArraySetAsSeries(MCD1e5, true);
		ArraySetAsSeries(MCD1e6, true);
		ArraySetAsSeries(MCD1e7, true);
		ArrayResize(MCD1e1, 8);
		ArrayResize(MCD1e2, 8);
		ArrayResize(MCD1e3, 8);
		ArrayResize(MCD1e4, 8);
		ArrayResize(MCD1e5, 8);
		ArrayResize(MCD1e6, 8);
		ArrayResize(MCD1e7, 8);
		if (CopyClose(aa, PERIOD_MN1, 0, 8, MCD1e1) < 0)
			Print(GetLastError());
		if (CopyClose(ab, PERIOD_MN1, 0, 8, MCD1e2) < 0)
			Print(GetLastError());
		if (CopyClose(ac, PERIOD_MN1, 0, 8, MCD1e3) < 0)
			Print(GetLastError());
		if (CopyClose(ad, PERIOD_MN1, 0, 8, MCD1e4) < 0)
			Print(GetLastError());
		if (CopyClose(ae, PERIOD_MN1, 0, 8, MCD1e5) < 0)
			Print(GetLastError());
		if (CopyClose(af, PERIOD_MN1, 0, 8, MCD1e6) < 0)
			Print(GetLastError());
		if (CopyClose(ag, PERIOD_MN1, 0, 8, MCD1e7) < 0)
			Print(GetLastError());

		double MCD1f2[], MCD1f3[], MCD1f4[], MCD1f5[], MCD1f6[], MCD1f7[];
		ArraySetAsSeries(MCD1f2, true);
		ArraySetAsSeries(MCD1f3, true);
		ArraySetAsSeries(MCD1f4, true);
		ArraySetAsSeries(MCD1f5, true);
		ArraySetAsSeries(MCD1f6, true);
		ArraySetAsSeries(MCD1f7, true);
		ArrayResize(MCD1f2, 8);
		ArrayResize(MCD1f3, 8);
		ArrayResize(MCD1f4, 8);
		ArrayResize(MCD1f5, 8);
		ArrayResize(MCD1f6, 8);
		ArrayResize(MCD1f7, 8);
		if (CopyClose(bb, PERIOD_MN1, 0, 8, MCD1f2) < 0)
			Print(GetLastError());
		if (CopyClose(bc, PERIOD_MN1, 0, 8, MCD1f3) < 0)
			Print(GetLastError());
		if (CopyClose(bd, PERIOD_MN1, 0, 8, MCD1f4) < 0)
			Print(GetLastError());
		if (CopyClose(be, PERIOD_MN1, 0, 8, MCD1f5) < 0)
			Print(GetLastError());
		if (CopyClose(bf, PERIOD_MN1, 0, 8, MCD1f6) < 0)
			Print(GetLastError());
		if (CopyClose(bg, PERIOD_MN1, 0, 8, MCD1f7) < 0)
			Print(GetLastError());

		double MCD1g3[], MCD1g4[], MCD1g5[], MCD1g6[], MCD1g7[];
		ArraySetAsSeries(MCD1g3, true);
		ArraySetAsSeries(MCD1g4, true);
		ArraySetAsSeries(MCD1g5, true);
		ArraySetAsSeries(MCD1g6, true);
		ArraySetAsSeries(MCD1g7, true);
		ArrayResize(MCD1g3, 8);
		ArrayResize(MCD1g4, 8);
		ArrayResize(MCD1g5, 8);
		ArrayResize(MCD1g6, 8);
		ArrayResize(MCD1g7, 8);
		if (CopyClose(cc, PERIOD_MN1, 0, 8, MCD1g3) < 0)
			Print(GetLastError());
		if (CopyClose(cd, PERIOD_MN1, 0, 8, MCD1g4) < 0)
			Print(GetLastError());
		if (CopyClose(ce, PERIOD_MN1, 0, 8, MCD1g5) < 0)
			Print(GetLastError());
		if (CopyClose(cf, PERIOD_MN1, 0, 8, MCD1g6) < 0)
			Print(GetLastError());
		if (CopyClose(cg, PERIOD_MN1, 0, 8, MCD1g7) < 0)
			Print(GetLastError());

		double MCD1j4[], MCD1j5[], MCD1j6[], MCD1j7[];
		ArraySetAsSeries(MCD1j4, true);
		ArraySetAsSeries(MCD1j5, true);
		ArraySetAsSeries(MCD1j6, true);
		ArraySetAsSeries(MCD1j7, true);
		ArrayResize(MCD1j4, 8);
		ArrayResize(MCD1j5, 8);
		ArrayResize(MCD1j6, 8);
		ArrayResize(MCD1j7, 8);
		if (CopyClose(dd, PERIOD_MN1, 0, 8, MCD1j4) < 0)
			Print(GetLastError());
		if (CopyClose(de, PERIOD_MN1, 0, 8, MCD1j5) < 0)
			Print(GetLastError());
		if (CopyClose(df, PERIOD_MN1, 0, 8, MCD1j6) < 0)
			Print(GetLastError());
		if (CopyClose(dg, PERIOD_MN1, 0, 8, MCD1j7) < 0)
			Print(GetLastError());

		double MCD1a5[], MCD1a6[], MCD1a7[];
		ArraySetAsSeries(MCD1a5, true);
		ArraySetAsSeries(MCD1a6, true);
		ArraySetAsSeries(MCD1a7, true);
		ArrayResize(MCD1a5, 8);
		ArrayResize(MCD1a6, 8);
		ArrayResize(MCD1a7, 8);
		if (CopyClose(ee, PERIOD_MN1, 0, 8, MCD1a5) < 0)
			Print(GetLastError());
		if (CopyClose(ef, PERIOD_MN1, 0, 8, MCD1a6) < 0)
			Print(GetLastError());
		if (CopyClose(eg, PERIOD_MN1, 0, 8, MCD1a7) < 0)
			Print(GetLastError());

		double MCD1n6[], MCD1n7[];
		ArraySetAsSeries(MCD1n6, true);
		ArraySetAsSeries(MCD1n7, true);
		ArrayResize(MCD1n6, 8);
		ArrayResize(MCD1n7, 8);
		if (CopyClose(ff, PERIOD_MN1, 0, 8, MCD1n6) < 0)
			Print(GetLastError());
		if (CopyClose(fg, PERIOD_MN1, 0, 8, MCD1n7) < 0)
			Print(GetLastError());

		double MCD1c7[];
		ArraySetAsSeries(MCD1c7, true);
		ArrayResize(MCD1c7, 8);
		if (CopyClose(gg, PERIOD_MN1, 0, 8, MCD1c7) < 0)
			Print(GetLastError());

		//HIGH ARRAYS
		double MHD1e1[], MHD1e2[], MHD1e3[], MHD1e4[], MHD1e5[], MHD1e6[], MHD1e7[];
		ArraySetAsSeries(MHD1e1, true);
		ArraySetAsSeries(MHD1e2, true);
		ArraySetAsSeries(MHD1e3, true);
		ArraySetAsSeries(MHD1e4, true);
		ArraySetAsSeries(MHD1e5, true);
		ArraySetAsSeries(MHD1e6, true);
		ArraySetAsSeries(MHD1e7, true);
		ArrayResize(MHD1e1, 8);
		ArrayResize(MHD1e2, 8);
		ArrayResize(MHD1e3, 8);
		ArrayResize(MHD1e4, 8);
		ArrayResize(MHD1e5, 8);
		ArrayResize(MHD1e6, 8);
		ArrayResize(MHD1e7, 8);
		if (CopyHigh(aa, PERIOD_MN1, 0, 8, MHD1e1) < 0)
			Print(GetLastError());
		if (CopyHigh(ab, PERIOD_MN1, 0, 8, MHD1e2) < 0)
			Print(GetLastError());
		if (CopyHigh(ac, PERIOD_MN1, 0, 8, MHD1e3) < 0)
			Print(GetLastError());
		if (CopyHigh(ad, PERIOD_MN1, 0, 8, MHD1e4) < 0)
			Print(GetLastError());
		if (CopyHigh(ae, PERIOD_MN1, 0, 8, MHD1e5) < 0)
			Print(GetLastError());
		if (CopyHigh(af, PERIOD_MN1, 0, 8, MHD1e6) < 0)
			Print(GetLastError());
		if (CopyHigh(ag, PERIOD_MN1, 0, 8, MHD1e7) < 0)
			Print(GetLastError());

		double MHD1f2[], MHD1f3[], MHD1f4[], MHD1f5[], MHD1f6[], MHD1f7[];
		ArraySetAsSeries(MHD1f2, true);
		ArraySetAsSeries(MHD1f3, true);
		ArraySetAsSeries(MHD1f4, true);
		ArraySetAsSeries(MHD1f5, true);
		ArraySetAsSeries(MHD1f6, true);
		ArraySetAsSeries(MHD1f7, true);
		ArrayResize(MHD1f2, 8);
		ArrayResize(MHD1f3, 8);
		ArrayResize(MHD1f4, 8);
		ArrayResize(MHD1f5, 8);
		ArrayResize(MHD1f6, 8);
		ArrayResize(MHD1f7, 8);
		if (CopyHigh(bb, PERIOD_MN1, 0, 8, MHD1f2) < 0)
			Print(GetLastError());
		if (CopyHigh(bc, PERIOD_MN1, 0, 8, MHD1f3) < 0)
			Print(GetLastError());
		if (CopyHigh(bd, PERIOD_MN1, 0, 8, MHD1f4) < 0)
			Print(GetLastError());
		if (CopyHigh(be, PERIOD_MN1, 0, 8, MHD1f5) < 0)
			Print(GetLastError());
		if (CopyHigh(bf, PERIOD_MN1, 0, 8, MHD1f6) < 0)
			Print(GetLastError());
		if (CopyHigh(bg, PERIOD_MN1, 0, 8, MHD1f7) < 0)
			Print(GetLastError());

		double MHD1g3[], MHD1g4[], MHD1g5[], MHD1g6[], MHD1g7[];
		ArraySetAsSeries(MHD1g3, true);
		ArraySetAsSeries(MHD1g4, true);
		ArraySetAsSeries(MHD1g5, true);
		ArraySetAsSeries(MHD1g6, true);
		ArraySetAsSeries(MHD1g7, true);
		ArrayResize(MHD1g3, 8);
		ArrayResize(MHD1g4, 8);
		ArrayResize(MHD1g5, 8);
		ArrayResize(MHD1g6, 8);
		ArrayResize(MHD1g7, 8);
		if (CopyHigh(cc, PERIOD_MN1, 0, 8, MHD1g3) < 0)
			Print(GetLastError());
		if (CopyHigh(cd, PERIOD_MN1, 0, 8, MHD1g4) < 0)
			Print(GetLastError());
		if (CopyHigh(ce, PERIOD_MN1, 0, 8, MHD1g5) < 0)
			Print(GetLastError());
		if (CopyHigh(cf, PERIOD_MN1, 0, 8, MHD1g6) < 0)
			Print(GetLastError());
		if (CopyHigh(cg, PERIOD_MN1, 0, 8, MHD1g7) < 0)
			Print(GetLastError());

		double MHD1j4[], MHD1j5[], MHD1j6[], MHD1j7[];
		ArraySetAsSeries(MHD1j4, true);
		ArraySetAsSeries(MHD1j5, true);
		ArraySetAsSeries(MHD1j6, true);
		ArraySetAsSeries(MHD1j7, true);
		ArrayResize(MHD1j4, 8);
		ArrayResize(MHD1j5, 8);
		ArrayResize(MHD1j6, 8);
		ArrayResize(MHD1j7, 8);
		if (CopyHigh(dd, PERIOD_MN1, 0, 8, MHD1j4) < 0)
			Print(GetLastError());
		if (CopyHigh(de, PERIOD_MN1, 0, 8, MHD1j5) < 0)
			Print(GetLastError());
		if (CopyHigh(df, PERIOD_MN1, 0, 8, MHD1j6) < 0)
			Print(GetLastError());
		if (CopyHigh(dg, PERIOD_MN1, 0, 8, MHD1j7) < 0)
			Print(GetLastError());

		double MHD1a5[], MHD1a6[], MHD1a7[];
		ArraySetAsSeries(MHD1a5, true);
		ArraySetAsSeries(MHD1a6, true);
		ArraySetAsSeries(MHD1a7, true);
		ArrayResize(MHD1a5, 8);
		ArrayResize(MHD1a6, 8);
		ArrayResize(MHD1a7, 8);
		if (CopyHigh(ee, PERIOD_MN1, 0, 8, MHD1a5) < 0)
			Print(GetLastError());
		if (CopyHigh(ef, PERIOD_MN1, 0, 8, MHD1a6) < 0)
			Print(GetLastError());
		if (CopyHigh(eg, PERIOD_MN1, 0, 8, MHD1a7) < 0)
			Print(GetLastError());

		double MHD1n6[], MHD1n7[];
		ArraySetAsSeries(MHD1n6, true);
		ArraySetAsSeries(MHD1n7, true);
		ArrayResize(MHD1n6, 8);
		ArrayResize(MHD1n7, 8);
		if (CopyHigh(ff, PERIOD_MN1, 0, 8, MHD1n6) < 0)
			Print(GetLastError());
		if (CopyHigh(fg, PERIOD_MN1, 0, 8, MHD1n7) < 0)
			Print(GetLastError());

		double MHD1c7[];
		ArraySetAsSeries(MHD1c7, true);
		ArrayResize(MHD1c7, 8);
		if (CopyHigh(gg, PERIOD_MN1, 0, 8, MHD1c7) < 0)
			Print(GetLastError());

		//LOW ARRAYS
		double MLD1e1[], MLD1e2[], MLD1e3[], MLD1e4[], MLD1e5[], MLD1e6[], MLD1e7[];
		ArraySetAsSeries(MLD1e1, true);
		ArraySetAsSeries(MLD1e2, true);
		ArraySetAsSeries(MLD1e3, true);
		ArraySetAsSeries(MLD1e4, true);
		ArraySetAsSeries(MLD1e5, true);
		ArraySetAsSeries(MLD1e6, true);
		ArraySetAsSeries(MLD1e7, true);
		ArrayResize(MLD1e1, 8);
		ArrayResize(MLD1e2, 8);
		ArrayResize(MLD1e3, 8);
		ArrayResize(MLD1e4, 8);
		ArrayResize(MLD1e5, 8);
		ArrayResize(MLD1e6, 8);
		ArrayResize(MLD1e7, 8);
		if (CopyLow(aa, PERIOD_MN1, 0, 8, MLD1e1) < 0)
			Print(GetLastError());
		if (CopyLow(ab, PERIOD_MN1, 0, 8, MLD1e2) < 0)
			Print(GetLastError());
		if (CopyLow(ac, PERIOD_MN1, 0, 8, MLD1e3) < 0)
			Print(GetLastError());
		if (CopyLow(ad, PERIOD_MN1, 0, 8, MLD1e4) < 0)
			Print(GetLastError());
		if (CopyLow(ae, PERIOD_MN1, 0, 8, MLD1e5) < 0)
			Print(GetLastError());
		if (CopyLow(af, PERIOD_MN1, 0, 8, MLD1e6) < 0)
			Print(GetLastError());
		if (CopyLow(ag, PERIOD_MN1, 0, 8, MLD1e7) < 0)
			Print(GetLastError());

		double MLD1f2[], MLD1f3[], MLD1f4[], MLD1f5[], MLD1f6[], MLD1f7[];
		ArraySetAsSeries(MLD1f2, true);
		ArraySetAsSeries(MLD1f3, true);
		ArraySetAsSeries(MLD1f4, true);
		ArraySetAsSeries(MLD1f5, true);
		ArraySetAsSeries(MLD1f6, true);
		ArraySetAsSeries(MLD1f7, true);
		ArrayResize(MLD1f2, 8);
		ArrayResize(MLD1f3, 8);
		ArrayResize(MLD1f4, 8);
		ArrayResize(MLD1f5, 8);
		ArrayResize(MLD1f6, 8);
		ArrayResize(MLD1f7, 8);
		if (CopyLow(bb, PERIOD_MN1, 0, 8, MLD1f2) < 0)
			Print(GetLastError());
		if (CopyLow(bc, PERIOD_MN1, 0, 8, MLD1f3) < 0)
			Print(GetLastError());
		if (CopyLow(bd, PERIOD_MN1, 0, 8, MLD1f4) < 0)
			Print(GetLastError());
		if (CopyLow(be, PERIOD_MN1, 0, 8, MLD1f5) < 0)
			Print(GetLastError());
		if (CopyLow(bf, PERIOD_MN1, 0, 8, MLD1f6) < 0)
			Print(GetLastError());
		if (CopyLow(bg, PERIOD_MN1, 0, 8, MLD1f7) < 0)
			Print(GetLastError());

		double MLD1g3[], MLD1g4[], MLD1g5[], MLD1g6[], MLD1g7[];
		ArraySetAsSeries(MLD1g3, true);
		ArraySetAsSeries(MLD1g4, true);
		ArraySetAsSeries(MLD1g5, true);
		ArraySetAsSeries(MLD1g6, true);
		ArraySetAsSeries(MLD1g7, true);
		ArrayResize(MLD1g3, 8);
		ArrayResize(MLD1g4, 8);
		ArrayResize(MLD1g5, 8);
		ArrayResize(MLD1g6, 8);
		ArrayResize(MLD1g7, 8);
		if (CopyLow(cc, PERIOD_MN1, 0, 8, MLD1g3) < 0)
			Print(GetLastError());
		if (CopyLow(cd, PERIOD_MN1, 0, 8, MLD1g4) < 0)
			Print(GetLastError());
		if (CopyLow(ce, PERIOD_MN1, 0, 8, MLD1g5) < 0)
			Print(GetLastError());
		if (CopyLow(cf, PERIOD_MN1, 0, 8, MLD1g6) < 0)
			Print(GetLastError());
		if (CopyLow(cg, PERIOD_MN1, 0, 8, MLD1g7) < 0)
			Print(GetLastError());

		double MLD1j4[], MLD1j5[], MLD1j6[], MLD1j7[];
		ArraySetAsSeries(MLD1j4, true);
		ArraySetAsSeries(MLD1j5, true);
		ArraySetAsSeries(MLD1j6, true);
		ArraySetAsSeries(MLD1j7, true);
		ArrayResize(MLD1j4, 8);
		ArrayResize(MLD1j5, 8);
		ArrayResize(MLD1j6, 8);
		ArrayResize(MLD1j7, 8);
		if (CopyLow(dd, PERIOD_MN1, 0, 8, MLD1j4) < 0)
			Print(GetLastError());
		if (CopyLow(de, PERIOD_MN1, 0, 8, MLD1j5) < 0)
			Print(GetLastError());
		if (CopyLow(df, PERIOD_MN1, 0, 8, MLD1j6) < 0)
			Print(GetLastError());
		if (CopyLow(dg, PERIOD_MN1, 0, 8, MLD1j7) < 0)
			Print(GetLastError());

		double MLD1a5[], MLD1a6[], MLD1a7[];
		ArraySetAsSeries(MLD1a5, true);
		ArraySetAsSeries(MLD1a6, true);
		ArraySetAsSeries(MLD1a7, true);
		ArrayResize(MLD1a5, 8);
		ArrayResize(MLD1a6, 8);
		ArrayResize(MLD1a7, 8);
		if (CopyLow(ee, PERIOD_MN1, 0, 8, MLD1a5) < 0)
			Print(GetLastError());
		if (CopyLow(ef, PERIOD_MN1, 0, 8, MLD1a6) < 0)
			Print(GetLastError());
		if (CopyLow(eg, PERIOD_MN1, 0, 8, MLD1a7) < 0)
			Print(GetLastError());

		double MLD1n6[], MLD1n7[];
		ArraySetAsSeries(MLD1n6, true);
		ArraySetAsSeries(MLD1n7, true);
		ArrayResize(MLD1n6, 8);
		ArrayResize(MLD1n7, 8);
		if (CopyLow(ff, PERIOD_MN1, 0, 8, MLD1n6) < 0)
			Print(GetLastError());
		if (CopyLow(fg, PERIOD_MN1, 0, 8, MLD1n7) < 0)
			Print(GetLastError());

		double MLD1c7[];
		ArraySetAsSeries(MLD1c7, true);
		ArrayResize(MLD1c7, 8);
		if (CopyLow(gg, PERIOD_MN1, 0, 8, MLD1c7) < 0)
			Print(GetLastError());

		//maysh
		for (i = 1; i <= 6; i++)
		{
			ocmayseur_dblar[i] = cdod1(MCD1e1, n1, i) + cdod1(MCD1e2, n1, i) + cdod1(MCD1e3, n1, i) + cdod1(MCD1e4, n2, i) + cdod1(MCD1e5, n1, i) + cdod1(MCD1e6, n1, i) + cdod1(MCD1e7, n1, i);
			// E/USD E/CHF E/GBP E/JPY E/AUD E/NZD E/CAD
			ocmayschf_dblar[i] = -cdod1(MCD1e2, n1, i) - cdod1(MCD1f2, n1, i) - cdod1(MCD1f3, n1, i) + cdod1(MCD1f4, n2, i) - cdod1(MCD1f5, n1, i) - cdod1(MCD1f6, n1, i) - cdod1(MCD1f7, n1, i);
			// -E/CHF -U/CHF -G/CHF CHF/J -A/CHF -N/CHF -C/CHF
			ocmaysgbp_dblar[i] = -cdod1(MCD1e3, n1, i) + cdod1(MCD1f3, n1, i) + cdod1(MCD1g3, n1, i) + cdod1(MCD1g4, n2, i) + cdod1(MCD1g5, n1, i) + cdod1(MCD1g6, n1, i) + cdod1(MCD1g7, n1, i);
			// -E/GBP GBP/C GBP/U GBP/J GBP/A GBP/N GBP/C
			ocmaysjpy_dblar[i] = -cdod1(MCD1e4, n2, i) - cdod1(MCD1f4, n2, i) - cdod1(MCD1g4, n2, i) - cdod1(MCD1j4, n2, i) - cdod1(MCD1j5, n2, i) - cdod1(MCD1j6, n2, i) - cdod1(MCD1j7, n2, i);
			// -E/JPY -C/JPY -G/JPY -U/JPY -A/JPY -N/JPY -C/JPY
			ocmaysaud_dblar[i] = -cdod1(MCD1e5, n1, i) + cdod1(MCD1f5, n1, i) - cdod1(MCD1g5, n1, i) + cdod1(MCD1j5, n2, i) + cdod1(MCD1a5, n1, i) + cdod1(MCD1a6, n1, i) + cdod1(MCD1a7, n1, i);
			// -E/AUD AUD/C -G/AUD AUD/J AUD/U AUD/N AUD/C
			ocmaysnzd_dblar[i] = -cdod1(MCD1e6, n1, i) + cdod1(MCD1f6, n1, i) - cdod1(MCD1g6, n1, i) + cdod1(MCD1j6, n2, i) - cdod1(MCD1a6, n1, i) + cdod1(MCD1n6, n1, i) + cdod1(MCD1n7, n1, i);
			// -E/NZD NZD/C -G/NZD NZD/J -A/NZD NZD/U NZD/C
			ocmayscad_dblar[i] = -cdod1(MCD1e7, n1, i) + cdod1(MCD1f7, n1, i) - cdod1(MCD1g7, n1, i) + cdod1(MCD1j7, n2, i) - cdod1(MCD1a7, n1, i) - cdod1(MCD1n7, n1, i) - cdod1(MCD1c7, n1, i);
			// -E/CAD CAD/C -G/CAD CAD/J -A/CAD -N/CAD -U/CAD
			ocmaysusd_dblar[i] = -cdod1(MCD1e1, n1, i) + cdod1(MCD1f2, n1, i) - cdod1(MCD1g3, n1, i) + cdod1(MCD1j4, n2, i) - cdod1(MCD1a5, n1, i) - cdod1(MCD1n6, n1, i) + cdod1(MCD1c7, n1, i);
			// -E/USD USD/C -G/USD USD/J -A/USD -N/USD USD/C

			ohmayseur_dblar[i] = hdod1(MHD1e1, MCD1e1, n1, i) + hdod1(MHD1e2, MCD1e2, n1, i) + hdod1(MHD1e3, MCD1e3, n1, i) + hdod1(MHD1e4, MCD1e4, n2, i) + hdod1(MHD1e5, MCD1e5, n1, i) + hdod1(MHD1e6, MCD1e6, n1, i) + hdod1(MHD1e7, MCD1e7, n1, i);
			ohmayschf_dblar[i] = -ldod1(MLD1e2, MCD1e2, n1, i) - ldod1(MLD1f2, MCD1f2, n1, i) - ldod1(MLD1f3, MCD1f3, n1, i) + hdod1(MHD1f4, MCD1f4, n2, i) - ldod1(MLD1f5, MCD1f5, n1, i) - ldod1(MLD1f6, MCD1f6, n1, i) - ldod1(MLD1f7, MCD1f7, n1, i);
			ohmaysgbp_dblar[i] = -ldod1(MLD1e3, MCD1e3, n1, i) + hdod1(MHD1f3, MCD1f3, n1, i) + hdod1(MHD1g3, MCD1g3, n1, i) + hdod1(MHD1g4, MCD1g4, n2, i) + hdod1(MHD1g5, MCD1g5, n1, i) + hdod1(MHD1g6, MCD1g6, n1, i) + hdod1(MHD1g7, MCD1g7, n1, i);
			ohmaysjpy_dblar[i] = -ldod1(MLD1e4, MCD1e4, n2, i) - ldod1(MLD1f4, MCD1f4, n2, i) - ldod1(MLD1g4, MCD1g4, n2, i) - ldod1(MLD1j4, MCD1j4, n2, i) - ldod1(MLD1j5, MCD1j5, n2, i) - ldod1(MLD1j6, MCD1j6, n2, i) - ldod1(MLD1j7, MCD1j7, n2, i);
			ohmaysaud_dblar[i] = -ldod1(MLD1e5, MCD1e5, n1, i) + hdod1(MHD1f5, MCD1f5, n1, i) - ldod1(MLD1g5, MCD1g5, n1, i) + hdod1(MHD1j5, MCD1j5, n2, i) + hdod1(MHD1a5, MCD1a5, n1, i) + hdod1(MHD1a6, MCD1a6, n1, i) + hdod1(MHD1a7, MCD1a7, n1, i);
			ohmaysnzd_dblar[i] = -ldod1(MLD1e6, MCD1e6, n1, i) + hdod1(MHD1f6, MCD1f6, n1, i) - ldod1(MLD1g6, MCD1g6, n1, i) + hdod1(MHD1j6, MCD1j6, n2, i) - ldod1(MLD1a6, MCD1a6, n1, i) + hdod1(MHD1n6, MCD1n6, n1, i) + hdod1(MHD1n7, MCD1n7, n1, i);
			ohmayscad_dblar[i] = -ldod1(MLD1e7, MCD1e7, n1, i) + hdod1(MHD1f7, MCD1f7, n1, i) - ldod1(MLD1g7, MCD1g7, n1, i) + hdod1(MHD1j7, MCD1j7, n2, i) - ldod1(MLD1a7, MCD1a7, n1, i) - ldod1(MLD1n7, MCD1n7, n1, i) - ldod1(MLD1c7, MCD1c7, n1, i);
			ohmaysusd_dblar[i] = -ldod1(MLD1e1, MCD1e1, n1, i) + hdod1(MHD1f2, MCD1f2, n1, i) - ldod1(MLD1g3, MCD1g3, n1, i) + hdod1(MHD1j4, MCD1j4, n2, i) - ldod1(MLD1a5, MCD1a5, n1, i) - ldod1(MLD1n6, MCD1n6, n1, i) + hdod1(MHD1c7, MCD1c7, n1, i);

			olmayseur_dblar[i] = ldod1(MLD1e1, MCD1e1, n1, i) + ldod1(MLD1e2, MCD1e2, n1, i) + ldod1(MLD1e3, MCD1e3, n1, i) + ldod1(MLD1e4, MCD1e4, n2, i) + ldod1(MLD1e5, MCD1e5, n1, i) + ldod1(MLD1e6, MCD1e6, n1, i) + ldod1(MLD1e7, MCD1e7, n1, i);
			olmayschf_dblar[i] = -hdod1(MHD1e2, MCD1e2, n1, i) - hdod1(MHD1f2, MCD1f2, n1, i) - hdod1(MHD1f3, MCD1f3, n1, i) + ldod1(MLD1f4, MCD1f4, n2, i) - hdod1(MHD1f5, MCD1f5, n1, i) - hdod1(MHD1f6, MCD1f6, n1, i) - hdod1(MHD1f7, MCD1f7, n1, i);
			olmaysgbp_dblar[i] = -hdod1(MHD1e3, MCD1e3, n1, i) + ldod1(MLD1f3, MCD1f3, n1, i) + ldod1(MLD1g3, MCD1g3, n1, i) + ldod1(MLD1g4, MCD1g4, n2, i) + ldod1(MLD1g5, MCD1g5, n1, i) + ldod1(MLD1g6, MCD1g6, n1, i) + ldod1(MLD1g7, MCD1g7, n1, i);
			olmaysjpy_dblar[i] = -hdod1(MHD1e4, MCD1e4, n2, i) - hdod1(MHD1f4, MCD1f4, n2, i) - hdod1(MHD1g4, MCD1g4, n2, i) - hdod1(MHD1j4, MCD1j4, n2, i) - hdod1(MHD1j5, MCD1j5, n2, i) - hdod1(MHD1j6, MCD1j6, n2, i) - hdod1(MHD1j7, MCD1j7, n2, i);
			olmaysaud_dblar[i] = -hdod1(MHD1e5, MCD1e5, n1, i) + ldod1(MLD1f5, MCD1f5, n1, i) - hdod1(MHD1g5, MCD1g5, n1, i) + ldod1(MLD1j5, MCD1j5, n2, i) + ldod1(MLD1a5, MCD1a5, n1, i) + ldod1(MLD1a6, MCD1a6, n1, i) + ldod1(MLD1a7, MCD1a7, n1, i);
			olmaysnzd_dblar[i] = -hdod1(MHD1e6, MCD1e6, n1, i) + ldod1(MLD1f6, MCD1f6, n1, i) - hdod1(MHD1g6, MCD1g6, n1, i) + ldod1(MLD1j6, MCD1j6, n2, i) - hdod1(MHD1a6, MCD1a6, n1, i) + ldod1(MLD1n6, MCD1n6, n1, i) + ldod1(MLD1n7, MCD1n7, n1, i);
			olmayscad_dblar[i] = -hdod1(MHD1e7, MCD1e7, n1, i) + ldod1(MLD1f7, MCD1f7, n1, i) - hdod1(MHD1g7, MCD1g7, n1, i) + ldod1(MLD1j7, MCD1j7, n2, i) - hdod1(MHD1a7, MCD1a7, n1, i) - hdod1(MHD1n7, MCD1n7, n1, i) - hdod1(MHD1c7, MCD1c7, n1, i);
			olmaysusd_dblar[i] = -hdod1(MHD1e1, MCD1e1, n1, i) + ldod1(MLD1f2, MCD1f2, n1, i) - hdod1(MHD1g3, MCD1g3, n1, i) + ldod1(MLD1j4, MCD1j4, n2, i) - hdod1(MHD1a5, MCD1a5, n1, i) - hdod1(MHD1n6, MCD1n6, n1, i) + ldod1(MLD1c7, MCD1c7, n1, i);
		}
	}
}
//+------------------------------------------------------------------+
void avgfill2()
{

		double euravg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			euravg9d += (1.0) * ocdayseur_dblar[i];
		}
		double euravg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			euravg29d += (1.0) * ocdayseur_dblar[i];
		}
		double euravg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			euravg49d += (1.0) * ocdayseur_dblar[i];
		}
		eurcavg[0] = euravg9d; eurcavg[1] = euravg29d; eurcavg[2] = euravg49d;
		
		double gbpavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			gbpavg9d += (1.0) * ocdaysgbp_dblar[i];
		}
		double gbpavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			gbpavg29d += (1.0) * ocdaysgbp_dblar[i];
		}
		double gbpavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			gbpavg49d += (1.0) * ocdaysgbp_dblar[i];
		}
		gbpcavg[0] = gbpavg9d; gbpcavg[1] = gbpavg29d; gbpcavg[2] = gbpavg49d;
		
		double chfavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			chfavg9d += (1.0) * ocdayschf_dblar[i];
		}
		double chfavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			chfavg29d += (1.0) * ocdayschf_dblar[i];
		}
		double chfavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			chfavg49d += (1.0) * ocdayschf_dblar[i];
		}
		chfcavg[0] = chfavg9d; chfcavg[1] = chfavg29d; chfcavg[2] = chfavg49d;
		
		double jpyavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			jpyavg9d += (1.0) * ocdaysjpy_dblar[i];
		}
		double jpyavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			jpyavg29d += (1.0) * ocdaysjpy_dblar[i];
		}
		double jpyavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			jpyavg49d += (1.0) * ocdaysjpy_dblar[i];
		}
		jpycavg[0] = jpyavg9d; jpycavg[1] = jpyavg29d; jpycavg[2] = jpyavg49d;
		
		double audavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			audavg9d += (1.0) * ocdaysaud_dblar[i];
		}
		double audavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			audavg29d += (1.0) * ocdaysaud_dblar[i];
		}
		double audavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			audavg49d += (1.0) * ocdaysaud_dblar[i];
		}
		audcavg[0] = audavg9d; audcavg[1] = audavg29d; audcavg[2] = audavg49d;
		
		double nzdavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			nzdavg9d += (1.0) * ocdaysnzd_dblar[i];
		}
		double nzdavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			nzdavg29d += (1.0) * ocdaysnzd_dblar[i];
		}
		double nzdavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			nzdavg49d += (1.0) * ocdaysnzd_dblar[i];
		}
		nzdcavg[0] = nzdavg9d; nzdcavg[1] = nzdavg29d; nzdcavg[2] = nzdavg49d;
		
		double cadavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			cadavg9d += (1.0) * ocdayscad_dblar[i];
		}
		double cadavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			cadavg29d += (1.0) * ocdayscad_dblar[i];
		}
		double cadavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			cadavg49d += (1.0) * ocdayscad_dblar[i];
		}
		cadcavg[0] = cadavg9d; cadcavg[1] = cadavg29d; cadcavg[2] = cadavg49d;
		
		double usdavg9d = 0;
		for (i = 9; i >= 1; i--)
		{
			usdavg9d += (1.0) * ocdaysusd_dblar[i];
		}
		double usdavg29d = 0;
		for (i = 29; i >= 10; i--)
		{
			usdavg29d += (1.0) * ocdaysusd_dblar[i];
		}
		double usdavg49d = 0;
		for (i = 49; i >= 30; i--)
		{
			usdavg49d += (1.0) * ocdaysusd_dblar[i];
		}
		usdcavg[0] = usdavg9d; usdcavg[1] = usdavg29d; usdcavg[2] = usdavg49d;
}
		
//+Averages Fill-----------------------------------------------------+
void avgfill(){   
      //EUR
		double euravgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			euravgl10d += (1.0) * oldayseur_dblar[i] / 10;
		}
		double euravgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			euravgm10d += (1.0) * ocdayseur_dblar[i] / 10;
		}
		double euravgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			euravgh10d += (1.0) * ohdayseur_dblar[i] / 10;
		}
		double euravgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			euravgl20d += (1.0) * oldayseur_dblar[i] / 20;
		}
		double euravgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			euravgm20d += (1.0) * ocdayseur_dblar[i] / 20;
		}
		double euravgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			euravgh20d += (1.0) * ohdayseur_dblar[i] / 20;
		}
		double euravgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			euravgl20s += (1.0) * oldayseur_dblar[i] / 10;
		}
		double euravgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			euravgh20s += (1.0) * ohdayseur_dblar[i] / 10;
		}
		double euravgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			euravgm20s += (1.0) * ocdayseur_dblar[i] / 10;
		}
		double euravgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			euravgl30d += (1.0) * oldayseur_dblar[i] / 30;
		}
		double euravgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			euravgm30d += (1.0) * ocdayseur_dblar[i] / 30;
		}
		double euravgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			euravgh30d += (1.0) * ohdayseur_dblar[i] / 30;
		}
		double euravgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			euravgl30s += (1.0) * oldayseur_dblar[i] / 20;
		}
		double euravgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			euravgh30s += (1.0) * ohdayseur_dblar[i] / 20;
		}
		double euravgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			euravgm30s += (1.0) * ocdayseur_dblar[i] / 20;
		}
		double euravgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			euravgl40d += (1.0) * oldayseur_dblar[i] / 40;
		}
		double euravgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			euravgm40d += (1.0) * ocdayseur_dblar[i] / 40;
		}
		double euravgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			euravgh40d += (1.0) * ohdayseur_dblar[i] / 40;
		}
		double euravgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			euravgl40s += (1.0) * oldayseur_dblar[i] / 10;
		}
		double euravgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			euravgh40s += (1.0) * ohdayseur_dblar[i] / 10;
		}
		double euravgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			euravgm40s += (1.0) * ocdayseur_dblar[i] / 10;
		}
		double euravgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			euravgl50d += (1.0) * oldayseur_dblar[i] / 50;
		}
		double euravgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			euravgm50d += (1.0) * ocdayseur_dblar[i] / 50;
		}
		double euravgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			euravgh50d += (1.0) * ohdayseur_dblar[i] / 50;
		}
		double euravgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			euravgl50s += (1.0) * oldayseur_dblar[i] / 20;
		}
		double euravgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			euravgh50s += (1.0) * ohdayseur_dblar[i] / 20;
		}
		double euravgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			euravgm50s += (1.0) * ocdayseur_dblar[i] / 20;
		}
		euravg[0] = euravgl10d; euravg[1] = euravgm10d; euravg[2] = euravgh10d; euravg[3] = euravgl20d; euravg[4] = euravgm20d; euravg[5] = euravgh20d; euravg[6] = euravgl30d; euravg[7] = euravgm30d; euravg[8] = euravgh30d; euravg[9] = euravgl40d; euravg[10] = euravgm40d; euravg[11] = euravgh40d; euravg[12] = euravgl50d; euravg[13] = euravgm50d; euravg[14] = euravgh50d;
		euravgs[0] = euravgl20s; euravgs[1] = euravgm20s; euravgs[2] = euravgh20s; euravgs[3] = euravgl30s; euravgs[4] = euravgm30s; euravgs[5] = euravgh30s; euravgs[6] = euravgl40s; euravgs[7] = euravgm40s; euravgs[8] = euravgh40s; euravgs[9] = euravgl50s; euravgs[10] = euravgm50s; euravgs[11] = euravgh50s;
      //gbp
		double gbpavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			gbpavgl10d += (1.0) * oldaysgbp_dblar[i] / 10;
		}
		double gbpavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			gbpavgm10d += (1.0) * ocdaysgbp_dblar[i] / 10;
		}
		double gbpavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			gbpavgh10d += (1.0) * ohdaysgbp_dblar[i] / 10;
		}
		double gbpavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			gbpavgl20d += (1.0) * oldaysgbp_dblar[i] / 20;
		}
		double gbpavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			gbpavgm20d += (1.0) * ocdaysgbp_dblar[i] / 20;
		}
		double gbpavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			gbpavgh20d += (1.0) * ohdaysgbp_dblar[i] / 20;
		}
		double gbpavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			gbpavgl20s += (1.0) * oldaysgbp_dblar[i] / 10;
		}
		double gbpavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			gbpavgh20s += (1.0) * ohdaysgbp_dblar[i] / 10;
		}
		double gbpavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			gbpavgm20s += (1.0) * ocdaysgbp_dblar[i] / 10;
		}
		double gbpavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			gbpavgl30d += (1.0) * oldaysgbp_dblar[i] / 30;
		}
		double gbpavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			gbpavgm30d += (1.0) * ocdaysgbp_dblar[i] / 30;
		}
		double gbpavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			gbpavgh30d += (1.0) * ohdaysgbp_dblar[i] / 30;
		}
		double gbpavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			gbpavgl30s += (1.0) * oldaysgbp_dblar[i] / 20;
		}
		double gbpavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			gbpavgh30s += (1.0) * ohdaysgbp_dblar[i] / 20;
		}
		double gbpavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			gbpavgm30s += (1.0) * ocdaysgbp_dblar[i] / 20;
		}
		double gbpavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			gbpavgl40d += (1.0) * oldaysgbp_dblar[i] / 40;
		}
		double gbpavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			gbpavgm40d += (1.0) * ocdaysgbp_dblar[i] / 40;
		}
		double gbpavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			gbpavgh40d += (1.0) * ohdaysgbp_dblar[i] / 40;
		}
		double gbpavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			gbpavgl40s += (1.0) * oldaysgbp_dblar[i] / 10;
		}
		double gbpavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			gbpavgh40s += (1.0) * ohdaysgbp_dblar[i] / 10;
		}
		double gbpavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			gbpavgm40s += (1.0) * ocdaysgbp_dblar[i] / 10;
		}
		double gbpavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			gbpavgl50d += (1.0) * oldaysgbp_dblar[i] / 50;
		}
		double gbpavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			gbpavgm50d += (1.0) * ocdaysgbp_dblar[i] / 50;
		}
		double gbpavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			gbpavgh50d += (1.0) * ohdaysgbp_dblar[i] / 50;
		}
		double gbpavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			gbpavgl50s += (1.0) * oldaysgbp_dblar[i] / 20;
		}
		double gbpavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			gbpavgh50s += (1.0) * ohdaysgbp_dblar[i] / 20;
		}
		double gbpavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			gbpavgm50s += (1.0) * ocdaysgbp_dblar[i] / 20;
		}
		gbpavg[0] = gbpavgl10d; gbpavg[1] = gbpavgm10d; gbpavg[2] = gbpavgh10d; gbpavg[3] = gbpavgl20d; gbpavg[4] = gbpavgm20d; gbpavg[5] = gbpavgh20d; gbpavg[6] = gbpavgl30d; gbpavg[7] = gbpavgm30d; gbpavg[8] = gbpavgh30d; gbpavg[9] = gbpavgl40d; gbpavg[10] = gbpavgm40d; gbpavg[11] = gbpavgh40d; gbpavg[12] = gbpavgl50d; gbpavg[13] = gbpavgm50d; gbpavg[14] = gbpavgh50d;
		gbpavgs[0] = gbpavgl20s; gbpavgs[1] = gbpavgm20s; gbpavgs[2] = gbpavgh20s; gbpavgs[3] = gbpavgl30s; gbpavgs[4] = gbpavgm30s; gbpavgs[5] = gbpavgh30s; gbpavgs[6] = gbpavgl40s; gbpavgs[7] = gbpavgm40s; gbpavgs[8] = gbpavgh40s; gbpavgs[9] = gbpavgl50s; gbpavgs[10] = gbpavgm50s; gbpavgs[11] = gbpavgh50s;
      //chf		
		double chfavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			chfavgl10d += (1.0) * oldayschf_dblar[i] / 10;
		}
		double chfavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			chfavgm10d += (1.0) * ocdayschf_dblar[i] / 10;
		}
		double chfavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			chfavgh10d += (1.0) * ohdayschf_dblar[i] / 10;
		}
		double chfavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			chfavgl20d += (1.0) * oldayschf_dblar[i] / 20;
		}
		double chfavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			chfavgm20d += (1.0) * ocdayschf_dblar[i] / 20;
		}
		double chfavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			chfavgh20d += (1.0) * ohdayschf_dblar[i] / 20;
		}
		double chfavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			chfavgl20s += (1.0) * oldayschf_dblar[i] / 10;
		}
		double chfavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			chfavgh20s += (1.0) * ohdayschf_dblar[i] / 10;
		}
		double chfavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			chfavgm20s += (1.0) * ocdayschf_dblar[i] / 10;
		}
		double chfavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			chfavgl30d += (1.0) * oldayschf_dblar[i] / 30;
		}
		double chfavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			chfavgm30d += (1.0) * ocdayschf_dblar[i] / 30;
		}
		double chfavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			chfavgh30d += (1.0) * ohdayschf_dblar[i] / 30;
		}
		double chfavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			chfavgl30s += (1.0) * oldayschf_dblar[i] / 20;
		}
		double chfavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			chfavgh30s += (1.0) * ohdayschf_dblar[i] / 20;
		}
		double chfavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			chfavgm30s += (1.0) * ocdayschf_dblar[i] / 20;
		}
		double chfavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			chfavgl40d += (1.0) * oldayschf_dblar[i] / 40;
		}
		double chfavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			chfavgm40d += (1.0) * ocdayschf_dblar[i] / 40;
		}
		double chfavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			chfavgh40d += (1.0) * ohdayschf_dblar[i] / 40;
		}
		double chfavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			chfavgl40s += (1.0) * oldayschf_dblar[i] / 10;
		}
		double chfavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			chfavgh40s += (1.0) * ohdayschf_dblar[i] / 10;
		}
		double chfavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			chfavgm40s += (1.0) * ocdayschf_dblar[i] / 10;
		}
		double chfavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			chfavgl50d += (1.0) * oldayschf_dblar[i] / 50;
		}
		double chfavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			chfavgm50d += (1.0) * ocdayschf_dblar[i] / 50;
		}
		double chfavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			chfavgh50d += (1.0) * ohdayschf_dblar[i] / 50;
		}
		double chfavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			chfavgl50s += (1.0) * oldayschf_dblar[i] / 20;
		}
		double chfavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			chfavgh50s += (1.0) * ohdayschf_dblar[i] / 20;
		}
		double chfavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			chfavgm50s += (1.0) * ocdayschf_dblar[i] / 20;
		}
		chfavg[0] = chfavgl10d; chfavg[1] = chfavgm10d; chfavg[2] = chfavgh10d; chfavg[3] = chfavgl20d; chfavg[4] = chfavgm20d; chfavg[5] = chfavgh20d; chfavg[6] = chfavgl30d; chfavg[7] = chfavgm30d; chfavg[8] = chfavgh30d; chfavg[9] = chfavgl40d; chfavg[10] = chfavgm40d; chfavg[11] = chfavgh40d; chfavg[12] = chfavgl50d; chfavg[13] = chfavgm50d; chfavg[14] = chfavgh50d;
		chfavgs[0] = chfavgl20s; chfavgs[1] = chfavgm20s; chfavgs[2] = chfavgh20s; chfavgs[3] = chfavgl30s; chfavgs[4] = chfavgm30s; chfavgs[5] = chfavgh30s; chfavgs[6] = chfavgl40s; chfavgs[7] = chfavgm40s; chfavgs[8] = chfavgh40s; chfavgs[9] = chfavgl50s; chfavgs[10] = chfavgm50s; chfavgs[11] = chfavgh50s;
      //jpy
		double jpyavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			jpyavgl10d += (1.0) * oldaysjpy_dblar[i] / 10;
		}
		double jpyavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			jpyavgm10d += (1.0) * ocdaysjpy_dblar[i] / 10;
		}
		double jpyavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			jpyavgh10d += (1.0) * ohdaysjpy_dblar[i] / 10;
		}
		double jpyavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			jpyavgl20d += (1.0) * oldaysjpy_dblar[i] / 20;
		}
		double jpyavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			jpyavgm20d += (1.0) * ocdaysjpy_dblar[i] / 20;
		}
		double jpyavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			jpyavgh20d += (1.0) * ohdaysjpy_dblar[i] / 20;
		}
		double jpyavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			jpyavgl20s += (1.0) * oldaysjpy_dblar[i] / 10;
		}
		double jpyavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			jpyavgh20s += (1.0) * ohdaysjpy_dblar[i] / 10;
		}
		double jpyavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			jpyavgm20s += (1.0) * ocdaysjpy_dblar[i] / 10;
		}
		double jpyavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			jpyavgl30d += (1.0) * oldaysjpy_dblar[i] / 30;
		}
		double jpyavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			jpyavgm30d += (1.0) * ocdaysjpy_dblar[i] / 30;
		}
		double jpyavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			jpyavgh30d += (1.0) * ohdaysjpy_dblar[i] / 20;
		}
		double jpyavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			jpyavgl30s += (1.0) * oldaysjpy_dblar[i] / 20;
		}
		double jpyavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			jpyavgh30s += (1.0) * ohdaysjpy_dblar[i] / 20;
		}
		double jpyavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			jpyavgm30s += (1.0) * ocdaysjpy_dblar[i] / 10;
		}
		double jpyavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			jpyavgl40d += (1.0) * oldaysjpy_dblar[i] / 40;
		}
		double jpyavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			jpyavgm40d += (1.0) * ocdaysjpy_dblar[i] / 40;
		}
		double jpyavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			jpyavgh40d += (1.0) * ohdaysjpy_dblar[i] / 40;
		}
		double jpyavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			jpyavgl40s += (1.0) * oldaysjpy_dblar[i] / 10;
		}
		double jpyavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			jpyavgh40s += (1.0) * ohdaysjpy_dblar[i] / 10;
		}
		double jpyavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			jpyavgm40s += (1.0) * ocdaysjpy_dblar[i] / 10;
		}
		double jpyavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			jpyavgl50d += (1.0) * oldaysjpy_dblar[i] / 50;
		}
		double jpyavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			jpyavgm50d += (1.0) * ocdaysjpy_dblar[i] / 50;
		}
		double jpyavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			jpyavgh50d += (1.0) * ohdaysjpy_dblar[i] / 50;
		}
		double jpyavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			jpyavgl50s += (1.0) * oldaysjpy_dblar[i] / 20;
		}
		double jpyavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			jpyavgh50s += (1.0) * ohdaysjpy_dblar[i] / 20;
		}
		double jpyavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			jpyavgm50s += (1.0) * ocdaysjpy_dblar[i] / 20;
		}
		jpyavg[0] = jpyavgl10d; jpyavg[1] = jpyavgm10d; jpyavg[2] = jpyavgh10d; jpyavg[3] = jpyavgl20d; jpyavg[4] = jpyavgm20d; jpyavg[5] = jpyavgh20d; jpyavg[6] = jpyavgl30d; jpyavg[7] = jpyavgm30d; jpyavg[8] = jpyavgh30d; jpyavg[9] = jpyavgl40d; jpyavg[10] = jpyavgm40d; jpyavg[11] = jpyavgh40d; jpyavg[12] = jpyavgl50d; jpyavg[13] = jpyavgm50d; jpyavg[14] = jpyavgh50d;
		jpyavgs[0] = jpyavgl20s; jpyavgs[1] = jpyavgm20s; jpyavgs[2] = jpyavgh20s; jpyavgs[3] = jpyavgl30s; jpyavgs[4] = jpyavgm30s; jpyavgs[5] = jpyavgh30s; jpyavgs[6] = jpyavgl40s; jpyavgs[7] = jpyavgm40s; jpyavgs[8] = jpyavgh40s; jpyavgs[9] = jpyavgl50s; jpyavgs[10] = jpyavgm50s; jpyavgs[11] = jpyavgh50s;
      //aud
		double audavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			audavgl10d += (1.0) * oldaysaud_dblar[i] / 10;
		}
		double audavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			audavgm10d += (1.0) * ocdaysaud_dblar[i] / 10;
		}
		double audavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			audavgh10d += (1.0) * ohdaysaud_dblar[i] / 10;
		}
		double audavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			audavgl20d += (1.0) * oldaysaud_dblar[i] / 20;
		}
		double audavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			audavgm20d += (1.0) * ocdaysaud_dblar[i] / 20;
		}
		double audavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			audavgh20d += (1.0) * ohdaysaud_dblar[i] / 20;
		}
		double audavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			audavgl20s += (1.0) * oldaysaud_dblar[i] / 10;
		}
		double audavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			audavgh20s += (1.0) * ohdaysaud_dblar[i] / 10;
		}
		double audavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			audavgm20s += (1.0) * ocdaysaud_dblar[i] / 10;
		}
		double audavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			audavgl30d += (1.0) * oldaysaud_dblar[i] / 30;
		}
		double audavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			audavgm30d += (1.0) * ocdaysaud_dblar[i] / 30;
		}
		double audavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			audavgh30d += (1.0) * ohdaysaud_dblar[i] / 30;
		}
		double audavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			audavgl30s += (1.0) * oldaysaud_dblar[i] / 20;
		}
		double audavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			audavgh30s += (1.0) * ohdaysaud_dblar[i] / 20;
		}
		double audavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			audavgm30s += (1.0) * ocdaysaud_dblar[i] / 20;
		}
		double audavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			audavgl40d += (1.0) * oldaysaud_dblar[i] / 40;
		}
		double audavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			audavgm40d += (1.0) * ocdaysaud_dblar[i] / 40;
		}
		double audavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			audavgh40d += (1.0) * ohdaysaud_dblar[i] / 40;
		}
		double audavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			audavgl40s += (1.0) * oldaysaud_dblar[i] / 10;
		}
		double audavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			audavgh40s += (1.0) * ohdaysaud_dblar[i] / 10;
		}
		double audavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			audavgm40s += (1.0) * ocdaysaud_dblar[i] / 10;
		}
		double audavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			audavgl50d += (1.0) * oldaysaud_dblar[i] / 50;
		}
		double audavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			audavgm50d += (1.0) * ocdaysaud_dblar[i] / 50;
		}
		double audavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			audavgh50d += (1.0) * ohdaysaud_dblar[i] / 50;
		}
		double audavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			audavgl50s += (1.0) * oldaysaud_dblar[i] / 20;
		}
		double audavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			audavgh50s += (1.0) * ohdaysaud_dblar[i] / 20;
		}
		double audavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			audavgm50s += (1.0) * ocdaysaud_dblar[i] / 20;
		}
		audavg[0] = audavgl10d; audavg[1] = audavgm10d; audavg[2] = audavgh10d; audavg[3] = audavgl20d; audavg[4] = audavgm20d; audavg[5] = audavgh20d; audavg[6] = audavgl30d; audavg[7] = audavgm30d; audavg[8] = audavgh30d; audavg[9] = audavgl40d; audavg[10] = audavgm40d; audavg[11] = audavgh40d; audavg[12] = audavgl50d; audavg[13] = audavgm50d; audavg[14] = audavgh50d;
		audavgs[0] = audavgl20s; audavgs[1] = audavgm20s; audavgs[2] = audavgh20s; audavgs[3] = audavgl30s; audavgs[4] = audavgm30s; audavgs[5] = audavgh30s; audavgs[6] = audavgl40s; audavgs[7] = audavgm40s; audavgs[8] = audavgh40s; audavgs[9] = audavgl50s; audavgs[10] = audavgm50s; audavgs[11] = audavgh50s;
      //nzd
		double nzdavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			nzdavgl10d += (1.0) * oldaysnzd_dblar[i] / 10;
		}
		double nzdavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			nzdavgm10d += (1.0) * ocdaysnzd_dblar[i] / 10;
		}
		double nzdavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			nzdavgh10d += (1.0) * ohdaysnzd_dblar[i] / 10;
		}
		double nzdavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			nzdavgl20d += (1.0) * oldaysnzd_dblar[i] / 20;
		}
		double nzdavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			nzdavgm20d += (1.0) * ocdaysnzd_dblar[i] / 20;
		}
		double nzdavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			nzdavgh20d += (1.0) * ohdaysnzd_dblar[i] / 20;
		}
		double nzdavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			nzdavgl20s += (1.0) * oldaysnzd_dblar[i] / 10;
		}
		double nzdavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			nzdavgh20s += (1.0) * ohdaysnzd_dblar[i] / 10;
		}
		double nzdavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			nzdavgm20s += (1.0) * ocdaysnzd_dblar[i] / 10;
		}
		double nzdavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			nzdavgl30d += (1.0) * oldaysnzd_dblar[i] / 30;
		}
		double nzdavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			nzdavgm30d += (1.0) * ocdaysnzd_dblar[i] / 30;
		}
		double nzdavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			nzdavgh30d += (1.0) * ohdaysnzd_dblar[i] / 30;
		}
		double nzdavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			nzdavgl30s += (1.0) * oldaysnzd_dblar[i] / 20;
		}
		double nzdavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			nzdavgh30s += (1.0) * ohdaysnzd_dblar[i] / 20;
		}
		double nzdavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			nzdavgm30s += (1.0) * ocdaysnzd_dblar[i] / 20;
		}
		double nzdavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			nzdavgl40d += (1.0) * oldaysnzd_dblar[i] / 40;
		}
		double nzdavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			nzdavgm40d += (1.0) * ocdaysnzd_dblar[i] / 40;
		}
		double nzdavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			nzdavgh40d += (1.0) * ohdaysnzd_dblar[i] / 40;
		}
		double nzdavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			nzdavgl40s += (1.0) * oldaysnzd_dblar[i] / 10;
		}
		double nzdavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			nzdavgh40s += (1.0) * ohdaysnzd_dblar[i] / 10;
		}
		double nzdavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			nzdavgm40s += (1.0) * ocdaysnzd_dblar[i] / 10;
		}
		double nzdavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			nzdavgl50d += (1.0) * oldaysnzd_dblar[i] / 50;
		}
		double nzdavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			nzdavgm50d += (1.0) * ocdaysnzd_dblar[i] / 50;
		}
		double nzdavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			nzdavgh50d += (1.0) * ohdaysnzd_dblar[i] / 50;
		}
		double nzdavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			nzdavgl50s += (1.0) * oldaysnzd_dblar[i] / 20;
		}
		double nzdavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			nzdavgh50s += (1.0) * ohdaysnzd_dblar[i] / 20;
		}
		double nzdavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			nzdavgm50s += (1.0) * ocdaysnzd_dblar[i] / 20;
		}
		nzdavg[0] = nzdavgl10d; nzdavg[1] = nzdavgm10d; nzdavg[2] = nzdavgh10d; nzdavg[3] = nzdavgl20d; nzdavg[4] = nzdavgm20d; nzdavg[5] = nzdavgh20d; nzdavg[6] = nzdavgl30d; nzdavg[7] = nzdavgm30d; nzdavg[8] = nzdavgh30d; nzdavg[9] = nzdavgl40d; nzdavg[10] = nzdavgm40d; nzdavg[11] = nzdavgh40d; nzdavg[12] = nzdavgl50d; nzdavg[13] = nzdavgm50d; nzdavg[14] = nzdavgh50d;
		nzdavgs[0] = nzdavgl20s; nzdavgs[1] = nzdavgm20s; nzdavgs[2] = nzdavgh20s; nzdavgs[3] = nzdavgl30s; nzdavgs[4] = nzdavgm30s; nzdavgs[5] = nzdavgh30s; nzdavgs[6] = nzdavgl40s; nzdavgs[7] = nzdavgm40s; nzdavgs[8] = nzdavgh40s; nzdavgs[9] = nzdavgl50s; nzdavgs[10] = nzdavgm50s; nzdavgs[11] = nzdavgh50s;
      //cad
		double cadavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			cadavgl10d += (1.0) * oldayscad_dblar[i] / 10;
		}
		double cadavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			cadavgm10d += (1.0) * ocdayscad_dblar[i] / 10;
		}
		double cadavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			cadavgh10d += (1.0) * ohdayscad_dblar[i] / 10;
		}
		double cadavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			cadavgl20d += (1.0) * oldayscad_dblar[i] / 20;
		}
		double cadavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			cadavgm20d += (1.0) * ocdayscad_dblar[i] / 20;
		}
		double cadavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			cadavgh20d += (1.0) * ohdayscad_dblar[i] / 20;
		}
		double cadavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			cadavgl20s += (1.0) * oldayscad_dblar[i] / 10;
		}
		double cadavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			cadavgh20s += (1.0) * ohdayscad_dblar[i] / 10;
		}
		double cadavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			cadavgm20s += (1.0) * ocdayscad_dblar[i] / 10;
		}
		double cadavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			cadavgl30d += (1.0) * oldayscad_dblar[i] / 30;
		}
		double cadavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			cadavgm30d += (1.0) * ocdayscad_dblar[i] / 30;
		}
		double cadavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			cadavgh30d += (1.0) * ohdayscad_dblar[i] / 30;
		}
		double cadavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			cadavgl30s += (1.0) * oldayscad_dblar[i] / 20;
		}
		double cadavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			cadavgh30s += (1.0) * ohdayscad_dblar[i] / 20;
		}
		double cadavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			cadavgm30s += (1.0) * ocdayscad_dblar[i] / 20;
		}
		double cadavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			cadavgl40d += (1.0) * oldayscad_dblar[i] / 40;
		}
		double cadavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			cadavgm40d += (1.0) * ocdayscad_dblar[i] / 40;
		}
		double cadavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			cadavgh40d += (1.0) * ohdayscad_dblar[i] / 40;
		}
		double cadavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			cadavgl40s += (1.0) * oldayscad_dblar[i] / 10;
		}
		double cadavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			cadavgh40s += (1.0) * ohdayscad_dblar[i] / 10;
		}
		double cadavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			cadavgm40s += (1.0) * ocdayscad_dblar[i] / 10;
		}
		double cadavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			cadavgl50d += (1.0) * oldayscad_dblar[i] / 50;
		}
		double cadavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			cadavgm50d += (1.0) * ocdayscad_dblar[i] / 50;
		}
		double cadavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			cadavgh50d += (1.0) * ohdayscad_dblar[i] / 50;
		}
		double cadavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			cadavgl50s += (1.0) * oldayscad_dblar[i] / 20;
		}
		double cadavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			cadavgh50s += (1.0) * ohdayscad_dblar[i] / 20;
		}
		double cadavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			cadavgm50s += (1.0) * ocdayscad_dblar[i] / 20;
		}
		cadavg[0] = cadavgl10d; cadavg[1] = cadavgm10d; cadavg[2] = cadavgh10d; cadavg[3] = cadavgl20d; cadavg[4] = cadavgm20d; cadavg[5] = cadavgh20d; cadavg[6] = cadavgl30d; cadavg[7] = cadavgm30d; cadavg[8] = cadavgh30d; cadavg[9] = cadavgl40d; cadavg[10] = cadavgm40d; cadavg[11] = cadavgh40d; cadavg[12] = cadavgl50d; cadavg[13] = cadavgm50d; cadavg[14] = cadavgh50d;
		cadavgs[0] = cadavgl20s; cadavgs[1] = cadavgm20s; cadavgs[2] = cadavgh20s; cadavgs[3] = cadavgl30s; cadavgs[4] = cadavgm30s; cadavgs[5] = cadavgh30s; cadavgs[6] = cadavgl40s; cadavgs[7] = cadavgm40s; cadavgs[8] = cadavgh40s; cadavgs[9] = cadavgl50s; cadavgs[10] = cadavgm50s; cadavgs[11] = cadavgh50s;
      //usd
		double usdavgl10d = 0;
		for (i = 10; i >= 1; i--)
		{
			usdavgl10d += (1.0) * oldaysusd_dblar[i] / 10;
		}
		double usdavgm10d = 0;
		for (i = 10; i >= 1; i--)
		{
			usdavgm10d += (1.0) * ocdaysusd_dblar[i] / 10;
		}
		double usdavgh10d = 0;
		for (i = 10; i >= 1; i--)
		{
			usdavgh10d += (1.0) * ohdaysusd_dblar[i] / 10;
		}
		double usdavgl20d = 0;
		for (i = 20; i >= 1; i--)
		{
			usdavgl20d += (1.0) * oldaysusd_dblar[i] / 20;
		}
		double usdavgm20d = 0;
		for (i = 20; i >= 1; i--)
		{
			usdavgm20d += (1.0) * ocdaysusd_dblar[i] / 20;
		}
		double usdavgh20d = 0;
		for (i = 20; i >= 1; i--)
		{
			usdavgh20d += (1.0) * ohdaysusd_dblar[i] / 20;
		}
		double usdavgl20s = 0;
		for (i = 20; i >= 11; i--)
		{
			usdavgl20s += (1.0) * oldaysusd_dblar[i] / 10;
		}
		double usdavgh20s = 0;
		for (i = 20; i >= 11; i--)
		{
			usdavgh20s += (1.0) * ohdaysusd_dblar[i] / 10;
		}
		double usdavgm20s = 0;
		for (i = 20; i >= 11; i--)
		{
			usdavgm20s += (1.0) * ocdaysusd_dblar[i] / 10;
		}
		double usdavgl30d = 0;
		for (i = 30; i >= 1; i--)
		{
			usdavgl30d += (1.0) * oldaysusd_dblar[i] / 30;
		}
		double usdavgm30d = 0;
		for (i = 30; i >= 1; i--)
		{
			usdavgm30d += (1.0) * ocdaysusd_dblar[i] / 30;
		}
		double usdavgh30d = 0;
		for (i = 30; i >= 1; i--)
		{
			usdavgh30d += (1.0) * ohdaysusd_dblar[i] / 30;
		}
		double usdavgl30s = 0;
		for (i = 30; i >= 11; i--)
		{
			usdavgl30s += (1.0) * oldaysusd_dblar[i] / 20;
		}
		double usdavgh30s = 0;
		for (i = 30; i >= 11; i--)
		{
			usdavgh30s += (1.0) * ohdaysusd_dblar[i] / 20;
		}
		double usdavgm30s = 0;
		for (i = 30; i >= 11; i--)
		{
			usdavgm30s += (1.0) * ocdaysusd_dblar[i] / 20;
		}
		double usdavgl40d = 0;
		for (i = 40; i >= 1; i--)
		{
			usdavgl40d += (1.0) * oldaysusd_dblar[i] / 40;
		}
		double usdavgm40d = 0;
		for (i = 40; i >= 1; i--)
		{
			usdavgm40d += (1.0) * ocdaysusd_dblar[i] / 40;
		}
		double usdavgh40d = 0;
		for (i = 40; i >= 1; i--)
		{
			usdavgh40d += (1.0) * ohdaysusd_dblar[i] / 40;
		}
		double usdavgl40s = 0;
		for (i = 40; i >= 31; i--)
		{
			usdavgl40s += (1.0) * oldaysusd_dblar[i] / 10;
		}
		double usdavgh40s = 0;
		for (i = 40; i >= 31; i--)
		{
			usdavgh40s += (1.0) * ohdaysusd_dblar[i] / 10;
		}
		double usdavgm40s = 0;
		for (i = 40; i >= 31; i--)
		{
			usdavgm40s += (1.0) * ocdaysusd_dblar[i] / 10;
		}
		double usdavgl50d = 0;
		for (i = 50; i >= 1; i--)
		{
			usdavgl50d += (1.0) * oldaysusd_dblar[i] / 50;
		}
		double usdavgm50d = 0;
		for (i = 50; i >= 1; i--)
		{
			usdavgm50d += (1.0) * ocdaysusd_dblar[i] / 50;
		}
		double usdavgh50d = 0;
		for (i = 50; i >= 1; i--)
		{
			usdavgh50d += (1.0) * ohdaysusd_dblar[i] / 50;
		}
		double usdavgl50s = 0;
		for (i = 50; i >= 31; i--)
		{
			usdavgl50s += (1.0) * oldaysusd_dblar[i] / 20;
		}
		double usdavgh50s = 0;
		for (i = 50; i >= 31; i--)
		{
			usdavgh50s += (1.0) * ohdaysusd_dblar[i] / 20;
		}
		double usdavgm50s = 0;
		for (i = 50; i >= 31; i--)
		{
			usdavgm50s += (1.0) * ocdaysusd_dblar[i] / 20;
		}
		usdavg[0] = usdavgl10d; usdavg[1] = usdavgm10d; usdavg[2] = usdavgh10d; usdavg[3] = usdavgl20d; usdavg[4] = usdavgm20d; usdavg[5] = usdavgh20d; usdavg[6] = usdavgl30d; usdavg[7] = usdavgm30d; usdavg[8] = usdavgh30d; usdavg[9] = usdavgl40d; usdavg[10] = usdavgm40d; usdavg[11] = usdavgh40d; usdavg[12] = usdavgl50d; usdavg[13] = usdavgm50d; usdavg[14] = usdavgh50d;
		usdavgs[0] = usdavgl20s; usdavgs[1] = usdavgm20s; usdavgs[2] = usdavgh20s; usdavgs[3] = usdavgl30s; usdavgs[4] = usdavgm30s; usdavgs[5] = usdavgh30s; usdavgs[6] = usdavgl40s; usdavgs[7] = usdavgm40s; usdavgs[8] = usdavgh40s; usdavgs[9] = usdavgl50s; usdavgs[10] = usdavgm50s; usdavgs[11] = usdavgh50s;
}
//+------------------------------------------------------------------+

//+BUILD HRRUNNERALL TAB---------------------------------------------+
void RunnerTab()
{
	string obname;
	int x = 750;
	int xs = 60;
	int y = 50;
	int ys = 20;
	int eurhour[13], chfhour[13], gbphour[13], jpyhour[13], audhour[13], nzdhour[13], cadhour[13], usdhour[13];
	{ //EUR
		eurhour[0] = ochoureur_dblar[0] + ochoureur_dblar[1] + ochoureur_dblar[2] + ochoureur_dblar[3] + ochoureur_dblar[4] + ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[1] = ochoureur_dblar[1] + ochoureur_dblar[2] + ochoureur_dblar[3] + ochoureur_dblar[4] + ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[2] = ochoureur_dblar[2] + ochoureur_dblar[3] + ochoureur_dblar[4] + ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[3] = ochoureur_dblar[3] + ochoureur_dblar[4] + ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[4] = ochoureur_dblar[4] + ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[5] = ochoureur_dblar[5] + ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[6] = ochoureur_dblar[6] + ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[7] = ochoureur_dblar[7] + ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[8] = ochoureur_dblar[8] + ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[9] = ochoureur_dblar[9] + ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[10] = ochoureur_dblar[10] + ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[11] = ochoureur_dblar[11] + ochoureur_dblar[12];
		eurhour[12] = ochoureur_dblar[12];
	}
	{ //CHF
		chfhour[0] = ochourchf_dblar[0] + ochourchf_dblar[1] + ochourchf_dblar[2] + ochourchf_dblar[3] + ochourchf_dblar[4] + ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[1] = ochourchf_dblar[1] + ochourchf_dblar[2] + ochourchf_dblar[3] + ochourchf_dblar[4] + ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[2] = ochourchf_dblar[2] + ochourchf_dblar[3] + ochourchf_dblar[4] + ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[3] = ochourchf_dblar[3] + ochourchf_dblar[4] + ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[4] = ochourchf_dblar[4] + ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[5] = ochourchf_dblar[5] + ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[6] = ochourchf_dblar[6] + ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[7] = ochourchf_dblar[7] + ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[8] = ochourchf_dblar[8] + ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[9] = ochourchf_dblar[9] + ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[10] = ochourchf_dblar[10] + ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[11] = ochourchf_dblar[11] + ochourchf_dblar[12];
		chfhour[12] = ochourchf_dblar[12];
	}
	{ //GBP
		gbphour[0] = ochourgbp_dblar[0] + ochourgbp_dblar[1] + ochourgbp_dblar[2] + ochourgbp_dblar[3] + ochourgbp_dblar[4] + ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[1] = ochourgbp_dblar[1] + ochourgbp_dblar[2] + ochourgbp_dblar[3] + ochourgbp_dblar[4] + ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[2] = ochourgbp_dblar[2] + ochourgbp_dblar[3] + ochourgbp_dblar[4] + ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[3] = ochourgbp_dblar[3] + ochourgbp_dblar[4] + ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[4] = ochourgbp_dblar[4] + ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[5] = ochourgbp_dblar[5] + ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[6] = ochourgbp_dblar[6] + ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[7] = ochourgbp_dblar[7] + ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[8] = ochourgbp_dblar[8] + ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[9] = ochourgbp_dblar[9] + ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[10] = ochourgbp_dblar[10] + ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[11] = ochourgbp_dblar[11] + ochourgbp_dblar[12];
		gbphour[12] = ochourgbp_dblar[12];
	}
	{ //JPY
		jpyhour[0] = ochourjpy_dblar[0] + ochourjpy_dblar[1] + ochourjpy_dblar[2] + ochourjpy_dblar[3] + ochourjpy_dblar[4] + ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[1] = ochourjpy_dblar[1] + ochourjpy_dblar[2] + ochourjpy_dblar[3] + ochourjpy_dblar[4] + ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[2] = ochourjpy_dblar[2] + ochourjpy_dblar[3] + ochourjpy_dblar[4] + ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[3] = ochourjpy_dblar[3] + ochourjpy_dblar[4] + ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[4] = ochourjpy_dblar[4] + ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[5] = ochourjpy_dblar[5] + ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[6] = ochourjpy_dblar[6] + ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[7] = ochourjpy_dblar[7] + ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[8] = ochourjpy_dblar[8] + ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[9] = ochourjpy_dblar[9] + ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[10] = ochourjpy_dblar[10] + ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[11] = ochourjpy_dblar[11] + ochourjpy_dblar[12];
		jpyhour[12] = ochourjpy_dblar[12];
	}
	{ //AUD
		audhour[0] = ochouraud_dblar[0] + ochouraud_dblar[1] + ochouraud_dblar[2] + ochouraud_dblar[3] + ochouraud_dblar[4] + ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[1] = ochouraud_dblar[1] + ochouraud_dblar[2] + ochouraud_dblar[3] + ochouraud_dblar[4] + ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[2] = ochouraud_dblar[2] + ochouraud_dblar[3] + ochouraud_dblar[4] + ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[3] = ochouraud_dblar[3] + ochouraud_dblar[4] + ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[4] = ochouraud_dblar[4] + ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[5] = ochouraud_dblar[5] + ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[6] = ochouraud_dblar[6] + ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[7] = ochouraud_dblar[7] + ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[8] = ochouraud_dblar[8] + ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[9] = ochouraud_dblar[9] + ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[10] = ochouraud_dblar[10] + ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[11] = ochouraud_dblar[11] + ochouraud_dblar[12];
		audhour[12] = ochouraud_dblar[12];
	}
	{ //NZD
		nzdhour[0] = ochournzd_dblar[0] + ochournzd_dblar[1] + ochournzd_dblar[2] + ochournzd_dblar[3] + ochournzd_dblar[4] + ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[1] = ochournzd_dblar[1] + ochournzd_dblar[2] + ochournzd_dblar[3] + ochournzd_dblar[4] + ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[2] = ochournzd_dblar[2] + ochournzd_dblar[3] + ochournzd_dblar[4] + ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[3] = ochournzd_dblar[3] + ochournzd_dblar[4] + ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[4] = ochournzd_dblar[4] + ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[5] = ochournzd_dblar[5] + ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[6] = ochournzd_dblar[6] + ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[7] = ochournzd_dblar[7] + ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[8] = ochournzd_dblar[8] + ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[9] = ochournzd_dblar[9] + ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[10] = ochournzd_dblar[10] + ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[11] = ochournzd_dblar[11] + ochournzd_dblar[12];
		nzdhour[12] = ochournzd_dblar[12];
	}
	{ //CAD
		cadhour[0] = ochourcad_dblar[0] + ochourcad_dblar[1] + ochourcad_dblar[2] + ochourcad_dblar[3] + ochourcad_dblar[4] + ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[1] = ochourcad_dblar[1] + ochourcad_dblar[2] + ochourcad_dblar[3] + ochourcad_dblar[4] + ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[2] = ochourcad_dblar[2] + ochourcad_dblar[3] + ochourcad_dblar[4] + ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[3] = ochourcad_dblar[3] + ochourcad_dblar[4] + ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[4] = ochourcad_dblar[4] + ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[5] = ochourcad_dblar[5] + ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[6] = ochourcad_dblar[6] + ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[7] = ochourcad_dblar[7] + ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[8] = ochourcad_dblar[8] + ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[9] = ochourcad_dblar[9] + ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[10] = ochourcad_dblar[10] + ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[11] = ochourcad_dblar[11] + ochourcad_dblar[12];
		cadhour[12] = ochourcad_dblar[12];
	}
	{ //USD
		usdhour[0] = ochourusd_dblar[0] + ochourusd_dblar[1] + ochourusd_dblar[2] + ochourusd_dblar[3] + ochourusd_dblar[4] + ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[1] = ochourusd_dblar[1] + ochourusd_dblar[2] + ochourusd_dblar[3] + ochourusd_dblar[4] + ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[2] = ochourusd_dblar[2] + ochourusd_dblar[3] + ochourusd_dblar[4] + ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[3] = ochourusd_dblar[3] + ochourusd_dblar[4] + ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[4] = ochourusd_dblar[4] + ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[5] = ochourusd_dblar[5] + ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[6] = ochourusd_dblar[6] + ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[7] = ochourusd_dblar[7] + ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[8] = ochourusd_dblar[8] + ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[9] = ochourusd_dblar[9] + ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[10] = ochourusd_dblar[10] + ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[11] = ochourusd_dblar[11] + ochourusd_dblar[12];
		usdhour[12] = ochourusd_dblar[12];
	}

   int z = Hour() - 10 + 1;
   int tt = 0;
   
   if (z < 0) tt = 0;
   else tt = z;
   
   int f = Hour() - 15 + 1;
   int yy = 0;
   
   if (f < 0) yy = 0;
   else yy = f;
   
	for (i = 12; i >= 0; i--)
	{
	   if (tt > 0)
	   {
         if (i == tt)
         {
            obname = Name + " " + "SignE";
      		LabelMake(obname, 0, x + 49, y + ((tt - 1) * ys), IntegerToString(eurhour[0] - eurhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignC";
      		LabelMake(obname, 0, x + 49 + xs, y + ((tt - 1) * ys), IntegerToString(chfhour[0] - chfhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignG";
      		LabelMake(obname, 0, x + 49 + 2 * xs, y + ((tt - 1) * ys), IntegerToString(gbphour[0] - gbphour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignJ";
      		LabelMake(obname, 0, x + 49 + 3 * xs, y + ((tt - 1) * ys), IntegerToString(jpyhour[0] - jpyhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignA";
      		LabelMake(obname, 0, x + 49 + 4 * xs, y + ((tt - 1) * ys), IntegerToString(audhour[0] - audhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignN";
      		LabelMake(obname, 0, x + 49 + 5 * xs, y + ((tt - 1) * ys), IntegerToString(nzdhour[0] - nzdhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignCa";
      		LabelMake(obname, 0, x + 49 + 6 * xs, y + ((tt - 1) * ys), IntegerToString(cadhour[0] - cadhour[tt]), 10, clrDodgerBlue);
            obname = Name + " " + "SignU";
      		LabelMake(obname, 0, x + 49 + 7 * xs, y + ((tt - 1) * ys), IntegerToString(usdhour[0] - usdhour[tt]), 10, clrDodgerBlue);
      		//ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
      		//ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
         }
         if (tt == 13)
         {
            obname = Name + " " + "SignE";
      		LabelMake(obname, 0, x + 49, y + ((tt - 1) * ys), IntegerToString(eurhour[0] - eurhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignC";
      		LabelMake(obname, 0, x + 49 + xs, y + ((tt - 1) * ys), IntegerToString(chfhour[0] - chfhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignG";
      		LabelMake(obname, 0, x + 49 + 2 * xs, y + ((tt - 1) * ys), IntegerToString(gbphour[0] - gbphour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignJ";
      		LabelMake(obname, 0, x + 49 + 3 * xs, y + ((tt - 1) * ys), IntegerToString(jpyhour[0] - jpyhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignA";
      		LabelMake(obname, 0, x + 49 + 4 * xs, y + ((tt - 1) * ys), IntegerToString(audhour[0] - audhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignN";
      		LabelMake(obname, 0, x + 49 + 5 * xs, y + ((tt - 1) * ys), IntegerToString(nzdhour[0] - nzdhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignCa";
      		LabelMake(obname, 0, x + 49 + 6 * xs, y + ((tt - 1) * ys), IntegerToString(cadhour[0] - cadhour[tt - 1]), 10, clrDodgerBlue);
            obname = Name + " " + "SignU";
      		LabelMake(obname, 0, x + 49 + 7 * xs, y + ((tt - 1) * ys), IntegerToString(usdhour[0] - usdhour[tt - 1]), 10, clrDodgerBlue);
      	}
      	if (tt > 13)
      	{
      	   ObjectsDeleteAll(0, Name + " " + "Sign");
      	}    
      }
      
	   if (yy > 0)
	   {
         if (i == yy)
         {
            obname = Name + " " + "StignE";
      		LabelMake(obname, 0, x + 49, y + ((yy - 1) * ys), IntegerToString(eurhour[0] - eurhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignC";
      		LabelMake(obname, 0, x + 49 + xs, y + ((yy - 1) * ys), IntegerToString(chfhour[0] - chfhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignG";
      		LabelMake(obname, 0, x + 49 + 2 * xs, y + ((yy - 1) * ys), IntegerToString(gbphour[0] - gbphour[yy]), 10, clrYellow);
            obname = Name + " " + "StignJ";
      		LabelMake(obname, 0, x + 49 + 3 * xs, y + ((yy - 1) * ys), IntegerToString(jpyhour[0] - jpyhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignA";
      		LabelMake(obname, 0, x + 49 + 4 * xs, y + ((yy - 1) * ys), IntegerToString(audhour[0] - audhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignN";
      		LabelMake(obname, 0, x + 49 + 5 * xs, y + ((yy - 1) * ys), IntegerToString(nzdhour[0] - nzdhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignCa";
      		LabelMake(obname, 0, x + 49 + 6 * xs, y + ((yy - 1) * ys), IntegerToString(cadhour[0] - cadhour[yy]), 10, clrYellow);
            obname = Name + " " + "StignU";
      		LabelMake(obname, 0, x + 49 + 7 * xs, y + ((yy - 1) * ys), IntegerToString(usdhour[0] - usdhour[yy]), 10, clrYellow);
      		//ObjectSetString(0, obname, OBJPROP_FONT, "Arial");
      		//ObjectSetInteger(0, obname, OBJPROP_ANCHOR, ANCHOR_LEFT_LOWER);
         }
         if (yy > 10)
         {
            ObjectsDeleteAll(0, Name + " " + "Sting");
         }
      }
	   
	   obname = Name + " " + "Tab1" + IntegerToString(i);
		LabelMake(obname, 0, x + 10, y + (i * ys), IntegerToString(eurhour[i]), FontSize, clrDodgerBlue);
		if (eurhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (eurhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && eurhour[i] > eurhour[i + 1])
		{
			obname = Name + " " + "Tab1arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35, y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && eurhour[i] < eurhour[i + 1])
		{
			obname = Name + " " + "Tab1arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35, y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (eurhour[i] == eurhour[ArrayMaximum(eurhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab1arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (eurhour[i] == eurhour[ArrayMinimum(eurhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab1arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab2" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + xs, y + (i * ys), IntegerToString(chfhour[i]), FontSize, clrDodgerBlue);
		if (chfhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (chfhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && chfhour[i] > chfhour[i + 1])
		{
			obname = Name + " " + "Tab2arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + xs, y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && chfhour[i] < chfhour[i + 1])
		{
			obname = Name + " " + "Tab2arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + xs, y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (chfhour[i] == chfhour[ArrayMaximum(chfhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab2arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (chfhour[i] == chfhour[ArrayMinimum(chfhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab2arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab3" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 2 * xs, y + (i * ys), IntegerToString(gbphour[i]), FontSize, clrDodgerBlue);
		if (gbphour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (gbphour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && gbphour[i] > gbphour[i + 1])
		{
			obname = Name + " " + "Tab3arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (2 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && gbphour[i] < gbphour[i + 1])
		{
			obname = Name + " " + "Tab3arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (2 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (gbphour[i] == gbphour[ArrayMaximum(gbphour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab3arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (gbphour[i] == gbphour[ArrayMinimum(gbphour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab3arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab4" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 3 * xs, y + (i * ys), IntegerToString(jpyhour[i]), FontSize, clrDodgerBlue);
		if (jpyhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (jpyhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && jpyhour[i] > jpyhour[i + 1])
		{
			obname = Name + " " + "Tab4arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (3 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && jpyhour[i] < jpyhour[i + 1])
		{
			obname = Name + " " + "Tab4arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (3 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (jpyhour[i] == jpyhour[ArrayMaximum(jpyhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab4arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (jpyhour[i] == jpyhour[ArrayMinimum(jpyhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab4arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab5" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 4 * xs, y + (i * ys), IntegerToString(audhour[i]), FontSize, clrDodgerBlue);
		if (audhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (audhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && audhour[i] > audhour[i + 1])
		{
			obname = Name + " " + "Tab5arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (4 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && audhour[i] < audhour[i + 1])
		{
			obname = Name + " " + "Tab5arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (4 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (audhour[i] == audhour[ArrayMaximum(audhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab5arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (audhour[i] == audhour[ArrayMinimum(audhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab5arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab6" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 5 * xs, y + (i * ys), IntegerToString(nzdhour[i]), FontSize, clrDodgerBlue);
		if (nzdhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (nzdhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && nzdhour[i] > nzdhour[i + 1])
		{
			obname = Name + " " + "Tab6arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (5 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && nzdhour[i] < nzdhour[i + 1])
		{
			obname = Name + " " + "Tab6arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (5 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (nzdhour[i] == nzdhour[ArrayMaximum(nzdhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab6arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (nzdhour[i] == nzdhour[ArrayMinimum(nzdhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab6arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab7" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 6 * xs, y + (i * ys), IntegerToString(cadhour[i]), FontSize, clrDodgerBlue);
		if (cadhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (cadhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && cadhour[i] > cadhour[i + 1])
		{
			obname = Name + " " + "Tab7arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (6 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && cadhour[i] < cadhour[i + 1])
		{
			obname = Name + " " + "Tab7arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (6 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (cadhour[i] == cadhour[ArrayMaximum(cadhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab7arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (cadhour[i] == cadhour[ArrayMinimum(cadhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab7arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}

		obname = Name + " " + "Tab8" + IntegerToString(i);
		LabelMake(obname, 0, x + 10 + 7 * xs, y + (i * ys), IntegerToString(usdhour[i]), FontSize, clrDodgerBlue);
		if (usdhour[i] < 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
		else if (usdhour[i] > 0)
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
		if (i < 12 && usdhour[i] > usdhour[i + 1])
		{
			obname = Name + " " + "Tab8arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (7 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(233), FontSize, "WingDings", FontColor1);
		}
		else if (i < 12 && usdhour[i] < usdhour[i + 1])
		{
			obname = Name + " " + "Tab8arr" + IntegerToString(i);
			LabelMake(obname, 0, x + 35 + (7 * xs), y + (i * ys), "", FontSize, clrDodgerBlue);
			ObjectSetText(obname, CharToStr(234), FontSize, "WingDings", FontColor2);
		}
		if (usdhour[i] == usdhour[ArrayMaximum(usdhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
			ObjectSetInteger(0, Name + " " + "Tab8arr" + IntegerToString(i), OBJPROP_COLOR, FontColor3);
		}
		if (usdhour[i] == usdhour[ArrayMinimum(usdhour, 0, 0)])
		{
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, FontSize + 2);
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, Name + " " + "Tab8arr" + IntegerToString(i), OBJPROP_COLOR, FontColor4);
		}
	}
}
//+------------------------------------------------------------------+

//+BUILD PAST DAYS STATS TABLE---------------------------------------+
void BuildPastDays(){
	//uint hourlystart=GetTickCount();
	FillPastBuffer();
	avgfill();

	int x = 750;	 // initial x distance
	int y = 350; // initial y distance
	int xs = 90; // xstep
	int ys = 15; // ystep
	
	string obname;
	
	for (i = 20; i >= 1; i--)
	{
   	obname = Name + " avg EUR" + IntegerToString(i);
   	LabelMake(obname, 0, x, y + ((i - 1) * ys), IntegerToString(oldayseur_dblar[i]) + " / " + IntegerToString(ocdayseur_dblar[i]) + " / " + IntegerToString(ohdayseur_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdayseur_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg CHF" + IntegerToString(i);
   	LabelMake(obname, 0, x + 1 * 90, y + ((i - 1) * ys), IntegerToString(oldayschf_dblar[i]) + " / " + IntegerToString(ocdayschf_dblar[i]) + " / " + IntegerToString(ohdayschf_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdayschf_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg GBP" + IntegerToString(i);
   	LabelMake(obname, 0, x + 2 * 90, y + ((i - 1) * ys), IntegerToString(oldaysgbp_dblar[i]) + " / " + IntegerToString(ocdaysgbp_dblar[i]) + " / " + IntegerToString(ohdaysgbp_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdaysgbp_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg JPY" + IntegerToString(i);
   	LabelMake(obname, 0, x + 3 * 90, y + ((i - 1) * ys), IntegerToString(oldaysjpy_dblar[i]) + " / " + IntegerToString(ocdaysjpy_dblar[i]) + " / " + IntegerToString(ohdaysjpy_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdaysjpy_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg AUD" + IntegerToString(i);
   	LabelMake(obname, 0, x + 4 * 90, y + ((i - 1) * ys), IntegerToString(oldaysaud_dblar[i]) + " / " + IntegerToString(ocdaysaud_dblar[i]) + " / " + IntegerToString(ohdaysaud_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdaysaud_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg NZD" + IntegerToString(i);
   	LabelMake(obname, 0, x + 5 * 90, y + ((i - 1) * ys), IntegerToString(oldaysnzd_dblar[i]) + " / " + IntegerToString(ocdaysnzd_dblar[i]) + " / " + IntegerToString(ohdaysnzd_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdaysnzd_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg CAD" + IntegerToString(i);
   	LabelMake(obname, 0, x + 6 * 90, y + ((i - 1) * ys), IntegerToString(oldayscad_dblar[i]) + " / " + IntegerToString(ocdayscad_dblar[i]) + " / " + IntegerToString(ohdayscad_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdayscad_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   	obname = Name + " avg USD" + IntegerToString(i);
   	LabelMake(obname, 0, x + 7 * 90, y + ((i - 1) * ys), IntegerToString(oldaysusd_dblar[i]) + " / " + IntegerToString(ocdaysusd_dblar[i]) + " / " + IntegerToString(ohdaysusd_dblar[i]), FontSize - 3, clrBlack);
   	if (ocdaysusd_dblar[i] >= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor1);
   	else ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor2);
   }
   
   int r = 6;
   double totalobs = 15;
   double euriborl = 0;
   double euriborh = 0;
   double gbpiborl = 0;
   double gbpiborh = 0;
   double audiborl = 0;
   double audiborh = 0;
   double nzdiborl = 0;
   double nzdiborh = 0;
   double cadiborl = 0;
   double cadiborh = 0;
   double usdiborl = 0;
   double usdiborh = 0;
   double chfiborl = 0;
   double chfiborh = 0;
   double jpyiborl = 0;
   double jpyiborh = 0;     
   
   for (int s = 5; s >= 1; s--)
   {
      euriborl += oldayseur_dblar[r - s] * (s / totalobs);
      euriborh += ohdayseur_dblar[r - s] * (s / totalobs);
      gbpiborl += oldaysgbp_dblar[r - s] * (s / totalobs);
      gbpiborh += ohdaysgbp_dblar[r - s] * (s / totalobs);
      audiborl += oldaysaud_dblar[r - s] * (s / totalobs);
      audiborh += ohdaysaud_dblar[r - s] * (s / totalobs);
      nzdiborl += oldaysnzd_dblar[r - s] * (s / totalobs);
      nzdiborh += ohdaysnzd_dblar[r - s] * (s / totalobs);
      cadiborl += oldayscad_dblar[r - s] * (s / totalobs);
      cadiborh += ohdayscad_dblar[r - s] * (s / totalobs);
      usdiborl += oldaysusd_dblar[r - s] * (s / totalobs);
      usdiborh += ohdaysusd_dblar[r - s] * (s / totalobs);
      chfiborl += oldayschf_dblar[r - s] * (s / totalobs);
      chfiborh += ohdayschf_dblar[r - s] * (s / totalobs);
      jpyiborl += oldaysjpy_dblar[r - s] * (s / totalobs);
      jpyiborh += ohdaysjpy_dblar[r - s] * (s / totalobs);
   }
   
   r = 11;
   totalobs = 55;
   double euri10borl = 0;
   double euri10borh = 0;
   double gbpi10borl = 0;
   double gbpi10borh = 0;
   double audi10borl = 0;
   double audi10borh = 0;
   double nzdi10borl = 0;
   double nzdi10borh = 0;
   double cadi10borl = 0;
   double cadi10borh = 0;
   double usdi10borl = 0;
   double usdi10borh = 0;
   double chfi10borl = 0;
   double chfi10borh = 0;
   double jpyi10borl = 0;
   double jpyi10borh = 0;     
   
   for (int s = 10; s >= 1; s--)
   {
      euri10borl += oldayseur_dblar[r - s] * (s / totalobs);
      euri10borh += ohdayseur_dblar[r - s] * (s / totalobs);
      gbpi10borl += oldaysgbp_dblar[r - s] * (s / totalobs);
      gbpi10borh += ohdaysgbp_dblar[r - s] * (s / totalobs);
      audi10borl += oldaysaud_dblar[r - s] * (s / totalobs);
      audi10borh += ohdaysaud_dblar[r - s] * (s / totalobs);
      nzdi10borl += oldaysnzd_dblar[r - s] * (s / totalobs);
      nzdi10borh += ohdaysnzd_dblar[r - s] * (s / totalobs);
      cadi10borl += oldayscad_dblar[r - s] * (s / totalobs);
      cadi10borh += ohdayscad_dblar[r - s] * (s / totalobs);
      usdi10borl += oldaysusd_dblar[r - s] * (s / totalobs);
      usdi10borh += ohdaysusd_dblar[r - s] * (s / totalobs);
      chfi10borl += oldayschf_dblar[r - s] * (s / totalobs);
      chfi10borh += ohdayschf_dblar[r - s] * (s / totalobs);
      jpyi10borl += oldaysjpy_dblar[r - s] * (s / totalobs);
      jpyi10borh += ohdaysjpy_dblar[r - s] * (s / totalobs);
   }
   /*
   Print("eur5: " + euriborl + " " + euriborh + " eur10: " + euri10borl + " " + euri10borh);
   Print("gbp5: " + gbpiborl + " " + gbpiborh + " gbp10: " + gbpi10borl + " " + gbpi10borh);
   Print("aud5: " + audiborl + " " + audiborh + " aud10: " + audi10borl + " " + audi10borh);
   Print("nzd5: " + nzdiborl + " " + nzdiborh + " nzd10: " + nzdi10borl + " " + nzdi10borh);
   Print("cad5: " + cadiborl + " " + cadiborh + " cad10: " + cadi10borl + " " + cadi10borh);
   Print("usd5: " + usdiborl + " " + usdiborh + " usd10: " + usdi10borl + " " + usdi10borh);
   Print("chf5: " + chfiborl + " " + chfiborh + " chf10: " + chfi10borl + " " + chfi10borh);
   Print("jpy5: " + jpyiborl + " " + jpyiborh + " jpy10: " + jpyi10borl + " " + jpyi10borh);
   */
   
   obname = Name + " rol5eur";
   LabelMake(obname, 0, x, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(euriborl, 0) + " / " + DoubleToString(euriborh, 0), FontSize - 1, clrWhite);
   if ((euriborl + euriborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((euriborl + euriborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10eur";
   LabelMake(obname, 0, x, y + 10 + 21 * ys, "10Dr: " + DoubleToString(euri10borl, 0) + " / " + DoubleToString(euri10borh, 0), FontSize - 1, clrWhite);
   if ((euri10borl + euri10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((euri10borl + euri10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg EURl10l";
   LabelMake(obname, 0, x, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg EURl10n";
   LabelMake(obname, 0, x, y + 50 + 21 * ys, DoubleToString(euravg[0], 0) + " / " + DoubleToString(euravg[1], 0) + " / "  + DoubleToString(euravg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg EURl20l";
   LabelMake(obname, 0, x, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg EURl20n";
   LabelMake(obname, 0, x, y + 50 + 23 * ys, DoubleToString(euravg[3], 0) + " / " + DoubleToString(euravg[4], 0) + " / "  + DoubleToString(euravg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg EURl20s";
   LabelMake(obname, 0, x, y + 50 + 24 * ys, DoubleToString(euravgs[0], 0) + " / " + DoubleToString(euravgs[1], 0) + " / " + DoubleToString(euravgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg EURl30l";
   LabelMake(obname, 0, x, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg EURl30n";
   LabelMake(obname, 0, x, y + 50 + 26 * ys, DoubleToString(euravg[6], 0) + " / " + DoubleToString(euravg[7], 0) + " / " + DoubleToString(euravg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg EURl30s";
   LabelMake(obname, 0, x, y + 50 + 27 * ys, DoubleToString(euravgs[3], 0) + " / " + DoubleToString(euravgs[4], 0) + " / " + DoubleToString(euravgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg EURl40l";
   LabelMake(obname, 0, x, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg EURl40n";
   LabelMake(obname, 0, x, y + 50 + 29 * ys, DoubleToString(euravg[9], 0) + " / " + DoubleToString(euravg[10], 0) + " / "  + DoubleToString(euravg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg EURl40s";
   LabelMake(obname, 0, x, y + 50 + 30 * ys, DoubleToString(euravgs[6], 0) + " / " + DoubleToString(euravgs[7], 0) + " / " + DoubleToString(euravgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg EURl50l";
   LabelMake(obname, 0, x, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg EURl50n";
   LabelMake(obname, 0, x, y + 50 + 32 * ys, DoubleToString(euravg[12], 0) + " / " + DoubleToString(euravg[13], 0) + " / "  + DoubleToString(euravg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg EURl50s";
   LabelMake(obname, 0, x, y + 50 + 33 * ys, DoubleToString(euravgs[9], 0) + " / " + DoubleToString(euravgs[10], 0) + " / " + DoubleToString(euravgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc EUR10-20";
   LabelMake(obname, 0, x, y + 50 + 34 * ys, DoubleToString(((MathAbs(euravg[0]) + MathAbs(euravg[2])) / (MathAbs(euravgs[0]) + MathAbs(euravgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc EUR1-10";
   LabelMake(obname, 0, x, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldayseur_dblar[0]) + MathAbs(ohdayseur_dblar[0])) / (MathAbs(euravg[0]) + MathAbs(euravg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5chf";
   LabelMake(obname, 0, x + 1 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(chfiborl, 0) + " / " + DoubleToString(chfiborh, 0), FontSize - 1, clrWhite);
   if ((chfiborl + chfiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((chfiborl + chfiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10chf";
   LabelMake(obname, 0, x + 1 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(chfi10borl, 0) + " / " + DoubleToString(chfi10borh, 0), FontSize - 1, clrWhite);
   if ((chfi10borl + chfi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((chfi10borl + chfi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg CHFl10l";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CHFl10n";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 21 * ys, DoubleToString(chfavg[0], 0) + " / " + DoubleToString(chfavg[1], 0) + " / "  + DoubleToString(chfavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CHFl20l";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CHFl20n";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 23 * ys, DoubleToString(chfavg[3], 0) + " / " + DoubleToString(chfavg[4], 0) + " / "  + DoubleToString(chfavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CHFl20s";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 24 * ys, DoubleToString(chfavgs[0], 0) + " / " + DoubleToString(chfavgs[1], 0) + " / " + DoubleToString(chfavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CHFl30l";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CHFl30n";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 26 * ys, DoubleToString(chfavg[6], 0) + " / " + DoubleToString(chfavg[7], 0) + " / " + DoubleToString(chfavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CHFl30s";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 27 * ys, DoubleToString(chfavgs[3], 0) + " / " + DoubleToString(chfavgs[4], 0) + " / " + DoubleToString(chfavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CHFl40l";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CHFl40n";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 29 * ys, DoubleToString(chfavg[9], 0) + " / " + DoubleToString(chfavg[10], 0) + " / "  + DoubleToString(chfavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CHFl40s";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 30 * ys, DoubleToString(chfavgs[6], 0) + " / " + DoubleToString(chfavgs[7], 0) + " / " + DoubleToString(chfavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CHFl50l";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CHFl50n";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 32 * ys, DoubleToString(chfavg[12], 0) + " / " + DoubleToString(chfavg[13], 0) + " / "  + DoubleToString(chfavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CHFl50s";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 33 * ys, DoubleToString(chfavgs[9], 0) + " / " + DoubleToString(chfavgs[10], 0) + " / " + DoubleToString(chfavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc CHF10-20";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(chfavg[0]) + MathAbs(chfavg[2])) / (MathAbs(chfavgs[0]) + MathAbs(chfavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc CHF1-10";
   LabelMake(obname, 0, x + 1 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldayschf_dblar[0]) + MathAbs(ohdayschf_dblar[0])) / (MathAbs(chfavg[0]) + MathAbs(chfavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5gbp";
   LabelMake(obname, 0, x + 2 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(gbpiborl, 0) + " / " + DoubleToString(gbpiborh, 0), FontSize - 1, clrWhite);
   if ((gbpiborl + gbpiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((gbpiborl + gbpiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10gbp";
   LabelMake(obname, 0, x + 2 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(gbpi10borl, 0) + " / " + DoubleToString(gbpi10borh, 0), FontSize - 1, clrWhite);
   if ((gbpi10borl + gbpi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((gbpi10borl + gbpi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg GBPl10l";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg GBPl10n";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 21 * ys, DoubleToString(gbpavg[0], 0) + " / " + DoubleToString(gbpavg[1], 0) + " / "  + DoubleToString(gbpavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg GBPl20l";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg GBPl20n";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 23 * ys, DoubleToString(gbpavg[3], 0) + " / " + DoubleToString(gbpavg[4], 0) + " / "  + DoubleToString(gbpavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg GBPl20s";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 24 * ys, DoubleToString(gbpavgs[0], 0) + " / " + DoubleToString(gbpavgs[1], 0) + " / " + DoubleToString(gbpavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg GBPl30l";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg GBPl30n";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 26 * ys, DoubleToString(gbpavg[6], 0) + " / " + DoubleToString(gbpavg[7], 0) + " / " + DoubleToString(gbpavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg GBPl30s";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 27 * ys, DoubleToString(gbpavgs[3], 0) + " / " + DoubleToString(gbpavgs[4], 0) + " / " + DoubleToString(gbpavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg GBPl40l";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg GBPl40n";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 29 * ys, DoubleToString(gbpavg[9], 0) + " / " + DoubleToString(gbpavg[10], 0) + " / "  + DoubleToString(gbpavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg GBPl40s";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 30 * ys, DoubleToString(gbpavgs[6], 0) + " / " + DoubleToString(gbpavgs[7], 0) + " / " + DoubleToString(gbpavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg GBPl50l";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg GBPl50n";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 32 * ys, DoubleToString(gbpavg[12], 0) + " / " + DoubleToString(gbpavg[13], 0) + " / "  + DoubleToString(gbpavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg GBPl50s";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 33 * ys, DoubleToString(gbpavgs[9], 0) + " / " + DoubleToString(gbpavgs[10], 0) + " / " + DoubleToString(gbpavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc GBP10-20";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(gbpavg[0]) + MathAbs(gbpavg[2])) / (MathAbs(gbpavgs[0]) + MathAbs(gbpavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc GBP1-10";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldaysgbp_dblar[0]) + MathAbs(ohdaysgbp_dblar[0])) / (MathAbs(gbpavg[0]) + MathAbs(gbpavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);

   obname = Name + " rol5jpy";
   LabelMake(obname, 0, x + 3 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(jpyiborl, 0) + " / " + DoubleToString(jpyiborh, 0), FontSize - 1, clrWhite);
   if ((jpyiborl + jpyiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((jpyiborl + jpyiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10jpy";
   LabelMake(obname, 0, x + 3 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(jpyi10borl, 0) + " / " + DoubleToString(jpyi10borh, 0), FontSize - 1, clrWhite);
   if ((jpyi10borl + jpyi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((jpyi10borl + jpyi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg JPYl10l";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg JPYl10n";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 21 * ys, DoubleToString(jpyavg[0], 0) + " / " + DoubleToString(jpyavg[1], 0) + " / "  + DoubleToString(jpyavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg JPYl20l";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg JPYl20n";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 23 * ys, DoubleToString(jpyavg[3], 0) + " / " + DoubleToString(jpyavg[4], 0) + " / "  + DoubleToString(jpyavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg JPYl20s";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 24 * ys, DoubleToString(jpyavgs[0], 0) + " / " + DoubleToString(jpyavgs[1], 0) + " / " + DoubleToString(jpyavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg JPYl30l";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg JPYl30n";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 26 * ys, DoubleToString(jpyavg[6], 0) + " / " + DoubleToString(jpyavg[7], 0) + " / " + DoubleToString(jpyavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg JPYl30s";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 27 * ys, DoubleToString(jpyavgs[3], 0) + " / " + DoubleToString(jpyavgs[4], 0) + " / " + DoubleToString(jpyavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg JPYl40l";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg JPYl40n";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 29 * ys, DoubleToString(jpyavg[9], 0) + " / " + DoubleToString(jpyavg[10], 0) + " / "  + DoubleToString(jpyavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg JPYl40s";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 30 * ys, DoubleToString(jpyavgs[6], 0) + " / " + DoubleToString(jpyavgs[7], 0) + " / " + DoubleToString(jpyavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg JPYl50l";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg JPYl50n";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 32 * ys, DoubleToString(jpyavg[12], 0) + " / " + DoubleToString(jpyavg[13], 0) + " / "  + DoubleToString(jpyavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg JPYl50s";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 33 * ys, DoubleToString(jpyavgs[9], 0) + " / " + DoubleToString(jpyavgs[10], 0) + " / " + DoubleToString(jpyavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc JPY10-20";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(jpyavg[0]) + MathAbs(jpyavg[2])) / (MathAbs(jpyavgs[0]) + MathAbs(jpyavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc JPY1-10";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldaysjpy_dblar[0]) + MathAbs(ohdaysjpy_dblar[0])) / (MathAbs(jpyavg[0]) + MathAbs(jpyavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5aud";
   LabelMake(obname, 0, x + 4 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(audiborl, 0) + " / " + DoubleToString(audiborh, 0), FontSize - 1, clrWhite);
   if ((audiborl + audiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((audiborl + audiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10aud";
   LabelMake(obname, 0, x + 4 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(audi10borl, 0) + " / " + DoubleToString(audi10borh, 0), FontSize - 1, clrWhite);
   if ((audi10borl + audi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((audi10borl + audi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg AUDl10l";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg AUDl10n";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 21 * ys, DoubleToString(audavg[0], 0) + " / " + DoubleToString(audavg[1], 0) + " / "  + DoubleToString(audavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg AUDl20l";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg AUDl20n";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 23 * ys, DoubleToString(audavg[3], 0) + " / " + DoubleToString(audavg[4], 0) + " / "  + DoubleToString(audavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg AUDl20s";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 24 * ys, DoubleToString(audavgs[0], 0) + " / " + DoubleToString(audavgs[1], 0) + " / " + DoubleToString(audavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg AUDl30l";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg AUDl30n";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 26 * ys, DoubleToString(audavg[6], 0) + " / " + DoubleToString(audavg[7], 0) + " / " + DoubleToString(audavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg AUDl30s";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 27 * ys, DoubleToString(audavgs[3], 0) + " / " + DoubleToString(audavgs[4], 0) + " / " + DoubleToString(audavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg AUDl40l";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg AUDl40n";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 29 * ys, DoubleToString(audavg[9], 0) + " / " + DoubleToString(audavg[10], 0) + " / "  + DoubleToString(audavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg AUDl40s";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 30 * ys, DoubleToString(audavgs[6], 0) + " / " + DoubleToString(audavgs[7], 0) + " / " + DoubleToString(audavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg AUDl50l";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg AUDl50n";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 32 * ys, DoubleToString(audavg[12], 0) + " / " + DoubleToString(audavg[13], 0) + " / "  + DoubleToString(audavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg AUDl50s";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 33 * ys, DoubleToString(audavgs[9], 0) + " / " + DoubleToString(audavgs[10], 0) + " / " + DoubleToString(audavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc AUD10-20";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(audavg[0]) + MathAbs(audavg[2])) / (MathAbs(audavgs[0]) + MathAbs(audavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc AUD1-10";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldaysaud_dblar[0]) + MathAbs(ohdaysaud_dblar[0])) / (MathAbs(audavg[0]) + MathAbs(audavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5nzd";
   LabelMake(obname, 0, x + 5 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(nzdiborl, 0) + " / " + DoubleToString(nzdiborh, 0), FontSize - 1, clrWhite);
   if ((nzdiborl + nzdiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((nzdiborl + nzdiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10nzd";
   LabelMake(obname, 0, x + 5 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(nzdi10borl, 0) + " / " + DoubleToString(nzdi10borh, 0), FontSize - 1, clrWhite);
   if ((nzdi10borl + nzdi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((nzdi10borl + nzdi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg NZDl10l";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg NZDl10n";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 21 * ys, DoubleToString(nzdavg[0], 0) + " / " + DoubleToString(nzdavg[1], 0) + " / "  + DoubleToString(nzdavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg NZDl20l";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg NZDl20n";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 23 * ys, DoubleToString(nzdavg[3], 0) + " / " + DoubleToString(nzdavg[4], 0) + " / "  + DoubleToString(nzdavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg NZDl20s";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 24 * ys, DoubleToString(nzdavgs[0], 0) + " / " + DoubleToString(nzdavgs[1], 0) + " / " + DoubleToString(nzdavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg NZDl30l";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg NZDl30n";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 26 * ys, DoubleToString(nzdavg[6], 0) + " / " + DoubleToString(nzdavg[7], 0) + " / " + DoubleToString(nzdavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg NZDl30s";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 27 * ys, DoubleToString(nzdavgs[3], 0) + " / " + DoubleToString(nzdavgs[4], 0) + " / " + DoubleToString(nzdavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg NZDl40l";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg NZDl40n";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 29 * ys, DoubleToString(nzdavg[9], 0) + " / " + DoubleToString(nzdavg[10], 0) + " / "  + DoubleToString(nzdavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg NZDl40s";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 30 * ys, DoubleToString(nzdavgs[6], 0) + " / " + DoubleToString(nzdavgs[7], 0) + " / " + DoubleToString(nzdavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg NZDl50l";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg NZDl50n";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 32 * ys, DoubleToString(nzdavg[12], 0) + " / " + DoubleToString(nzdavg[13], 0) + " / "  + DoubleToString(nzdavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg NZDl50s";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 33 * ys, DoubleToString(nzdavgs[9], 0) + " / " + DoubleToString(nzdavgs[10], 0) + " / " + DoubleToString(nzdavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc NZD10-20";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(nzdavg[0]) + MathAbs(nzdavg[2])) / (MathAbs(nzdavgs[0]) + MathAbs(nzdavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc NZD1-10";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldaysnzd_dblar[0]) + MathAbs(ohdaysnzd_dblar[0])) / (MathAbs(nzdavg[0]) + MathAbs(nzdavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5cad";
   LabelMake(obname, 0, x + 6 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(cadiborl, 0) + " / " + DoubleToString(cadiborh, 0), FontSize - 1, clrWhite);
   if ((cadiborl + cadiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((cadiborl + cadiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10cad";
   LabelMake(obname, 0, x + 6 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(cadi10borl, 0) + " / " + DoubleToString(cadi10borh, 0), FontSize - 1, clrWhite);
   if ((cadi10borl + cadi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((cadi10borl + cadi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg CADl10l";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CADl10n";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 21 * ys, DoubleToString(cadavg[0], 0) + " / " + DoubleToString(cadavg[1], 0) + " / "  + DoubleToString(cadavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CADl20l";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CADl20n";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 23 * ys, DoubleToString(cadavg[3], 0) + " / " + DoubleToString(cadavg[4], 0) + " / "  + DoubleToString(cadavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CADl20s";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 24 * ys, DoubleToString(cadavgs[0], 0) + " / " + DoubleToString(cadavgs[1], 0) + " / " + DoubleToString(cadavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CADl30l";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CADl30n";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 26 * ys, DoubleToString(cadavg[6], 0) + " / " + DoubleToString(cadavg[7], 0) + " / " + DoubleToString(cadavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CADl30s";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 27 * ys, DoubleToString(cadavgs[3], 0) + " / " + DoubleToString(cadavgs[4], 0) + " / " + DoubleToString(cadavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CADl40l";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CADl40n";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 29 * ys, DoubleToString(cadavg[9], 0) + " / " + DoubleToString(cadavg[10], 0) + " / "  + DoubleToString(cadavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CADl40s";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 30 * ys, DoubleToString(cadavgs[6], 0) + " / " + DoubleToString(cadavgs[7], 0) + " / " + DoubleToString(cadavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg CADl50l";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg CADl50n";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 32 * ys, DoubleToString(cadavg[12], 0) + " / " + DoubleToString(cadavg[13], 0) + " / "  + DoubleToString(cadavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg CADl50s";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 33 * ys, DoubleToString(cadavgs[9], 0) + " / " + DoubleToString(cadavgs[10], 0) + " / " + DoubleToString(cadavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc CAD10-20";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(cadavg[0]) + MathAbs(cadavg[2])) / (MathAbs(cadavgs[0]) + MathAbs(cadavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc CAD1-10";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldayscad_dblar[0]) + MathAbs(ohdayscad_dblar[0])) / (MathAbs(cadavg[0]) + MathAbs(cadavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);
   
   obname = Name + " rol5usd";
   LabelMake(obname, 0, x + 7 * xs, y + 10 + 20 * ys, "5Dr:  " + DoubleToString(usdiborl, 0) + " / " + DoubleToString(usdiborh, 0), FontSize - 1, clrWhite);
   if ((usdiborl + usdiborh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((usdiborl + usdiborh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " rol10usd";
   LabelMake(obname, 0, x + 7 * xs, y + 10 + 21 * ys, "10Dr: " + DoubleToString(usdi10borl, 0) + " / " + DoubleToString(usdi10borh, 0), FontSize - 1, clrWhite);
   if ((usdi10borl + usdi10borh) > 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrBlue);
   if ((usdi10borl + usdi10borh) <= 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
   obname = Name + " avg USDl10l";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 20 * ys, "10d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg USDl10n";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 21 * ys, DoubleToString(usdavg[0], 0) + " / " + DoubleToString(usdavg[1], 0) + " / "  + DoubleToString(usdavg[2], 0), FontSize - 1, clrBlack);
   obname = Name + " avg USDl20l";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 22 * ys, "20d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg USDl20n";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 23 * ys, DoubleToString(usdavg[3], 0) + " / " + DoubleToString(usdavg[4], 0) + " / "  + DoubleToString(usdavg[5], 0), FontSize - 1, clrBlack);
   obname = Name + " avg USDl20s";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 24 * ys, DoubleToString(usdavgs[0], 0) + " / " + DoubleToString(usdavgs[1], 0) + " / " + DoubleToString(usdavgs[2], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg USDl30l";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 25 * ys, "30d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg USDl30n";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 26 * ys, DoubleToString(usdavg[6], 0) + " / " + DoubleToString(usdavg[7], 0) + " / " + DoubleToString(usdavg[8], 0), FontSize - 1, clrBlack);
   obname = Name + " avg USDl30s";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 27 * ys, DoubleToString(usdavgs[3], 0) + " / " + DoubleToString(usdavgs[4], 0) + " / " + DoubleToString(usdavgs[5], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg USDl40l";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 28 * ys, "40d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg USDl40n";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 29 * ys, DoubleToString(usdavg[9], 0) + " / " + DoubleToString(usdavg[10], 0) + " / "  + DoubleToString(usdavg[11], 0), FontSize - 1, clrBlack);
   obname = Name + " avg USDl40s";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 30 * ys, DoubleToString(usdavgs[6], 0) + " / " + DoubleToString(usdavgs[7], 0) + " / " + DoubleToString(usdavgs[8], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " avg USDl50l";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 31 * ys, "50d L/H avg:", FontSize - 1, clrBlack);
   obname = Name + " avg USDl50n";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 32 * ys, DoubleToString(usdavg[12], 0) + " / " + DoubleToString(usdavg[13], 0) + " / "  + DoubleToString(usdavg[14], 0), FontSize - 1, clrBlack);
   obname = Name + " avg USDl50s";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 33 * ys, DoubleToString(usdavgs[9], 0) + " / " + DoubleToString(usdavgs[10], 0) + " / " + DoubleToString(usdavgs[11], 0), FontSize - 1, clrDodgerBlue);
   obname = Name + " perc USD10-20";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 34 * ys, DoubleToString(((MathAbs(usdavg[0]) + MathAbs(usdavg[2])) / (MathAbs(usdavgs[0]) + MathAbs(usdavgs[2]))) * 100, 2) + "%", FontSize - 1, clrBlue);
   obname = Name + " perc USD1-10";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 35 * ys, DoubleToString(((MathAbs(oldaysusd_dblar[0]) + MathAbs(ohdaysusd_dblar[0])) / (MathAbs(usdavg[0]) + MathAbs(usdavg[2]))) * 100, 2) + "%", FontSize - 1, clrGreen);

   obname = Name + " avgEURm";
   LabelMake(obname, 0, x, y + 50 + 36 * ys, DoubleToString((2.3 * euravg[1] + 1.7 * euravgs[4] + euravgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgCHFm";
   LabelMake(obname, 0, x + xs, y + 50 + 36 * ys, DoubleToString((2.3 * chfavg[1] + 1.7 * chfavgs[4] + chfavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgGBPm";
   LabelMake(obname, 0, x + 2 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * gbpavg[1] + 1.7 * gbpavgs[4] + gbpavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgJPYm";
   LabelMake(obname, 0, x + 3 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * jpyavg[1] + 1.7 * jpyavgs[4] + jpyavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgAUDm";
   LabelMake(obname, 0, x + 4 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * audavg[1] + 1.7 * audavgs[4] + audavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgNZDm";
   LabelMake(obname, 0, x + 5 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * nzdavg[1] + 1.7 * nzdavgs[4] + nzdavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgCADm";
   LabelMake(obname, 0, x + 6 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * cadavg[1] + 1.7 * cadavgs[4] + cadavgs[10]) / 5, 1), FontSize - 1, clrWhite);
   obname = Name + " avgUSDm";
   LabelMake(obname, 0, x + 7 * xs, y + 50 + 36 * ys, DoubleToString((2.3 * usdavg[1] + 1.7 * usdavgs[4] + usdavgs[10]) / 5, 1), FontSize - 1, clrWhite);
}
//+------------------------------------------------------------------+

//+AVG CALCS---------------------------------------------------------+
void avgCalcs(){
	FillPastBuffer();
	
	avgeurl = 0; avgeurh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgeurl += (0.382 * oldayseur_dblar[i]) / 72;
	   avgeurh += (0.382 * ohdayseur_dblar[i]) / 72;
	}
	//Print("eur: " + avgeurl + " " + avgeurh);
	avgchfl = 0; avgchfh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgchfl += (0.382 * oldayschf_dblar[i]) / 72;
	   avgchfh += (0.382 * ohdayschf_dblar[i]) / 72;
	}
	//Print("chf: " + avgchfl + " " + avgchfh);
	avggbpl = 0; avggbph = 0;
	for (i = 72; i >= 1; i--)
	{
	   avggbpl += (0.382 * oldaysgbp_dblar[i]) / 72;
	   avggbph += (0.382 * ohdaysgbp_dblar[i]) / 72;
	}
	//Print("gbp: " + avggbpl + " " + avggbph);
	avgjpyl = 0; avgjpyh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgjpyl += (0.382 * oldaysjpy_dblar[i]) / 72;
	   avgjpyh += (0.382 * ohdaysjpy_dblar[i]) / 72;
	}
	//Print("jpy: " + avgjpyl + " " + avgjpyh);
	avgaudl = 0; avgaudh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgaudl += (0.382 * oldaysaud_dblar[i]) / 72;
	   avgaudh += (0.382 * ohdaysaud_dblar[i]) / 72;
	}
	//Print("aud: " + avgaudl + " " + avgaudh);
	avgnzdl = 0; avgnzdh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgnzdl += (0.382 * oldaysnzd_dblar[i]) / 72;
	   avgnzdh += (0.382 * ohdaysnzd_dblar[i]) / 72;
	}
	//Print("nzd: " + avgnzdl + " " + avgnzdh);
	avgcadl = 0; avgcadh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgcadl += (0.382 * oldayscad_dblar[i]) / 72;
	   avgcadh += (0.382 * ohdayscad_dblar[i]) / 72;
	}
	//Print("cad: " + avgcadl + " " + avgcadh);
	avgusdl = 0; avgusdh = 0;
	for (i = 72; i >= 1; i--)
	{
	   avgusdl += (0.382 * oldaysusd_dblar[i]) / 72;
	   avgusdh += (0.382 * ohdaysusd_dblar[i]) / 72;
	}
	//Print("usd: " + avgusdl + " " + avgusdh);
	
   double euravdl = 0, euravdm = 0, euravdh = 0;
   for (i = 72; i >= 1; i--)
   {
      euravdl += (1.0) * oldayseur_dblar[i] / 72;
      euravdm += (1.0) * ocdayseur_dblar[i] / 72;
      euravdh += (1.0) * ohdayseur_dblar[i] / 72;
   }
   euravd[0] = euravdl; euravd[1] = euravdm; euravd[2] = euravdh;
   double euravwl = 0, euravwm = 0, euravwh = 0;
   for (i = 26; i >= 1; i--)
   {
      euravwl += (1.0) * olwayseur_dblar[i] / 26;
      euravwm += (1.0) * ocwayseur_dblar[i] / 26;
      euravwh += (1.0) * ohwayseur_dblar[i] / 26;
   }
   euravw[0] = euravwl; euravw[1] = euravwm; euravw[2] = euravwh;
   double euravml = 0, euravmm = 0, euravmh = 0;
   for (i = 6; i >= 1; i--)
   {
      euravml += (1.0) * olmayseur_dblar[i] / 6;
      euravmm += (1.0) * ocmayseur_dblar[i] / 6;
      euravmh += (1.0) * ohmayseur_dblar[i] / 6;
   }
   euravm[0] = euravml; euravm[1] = euravmm; euravm[2] = euravmh;
   
   double chfavdl = 0, chfavdm = 0, chfavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      chfavdl += (1.0) * oldayschf_dblar[i] / 72;
      chfavdm += (1.0) * ocdayschf_dblar[i] / 72;
      chfavdh += (1.0) * ohdayschf_dblar[i] / 72;
   }
   chfavd[0] = chfavdl; chfavd[1] = chfavdm; chfavd[2] = chfavdh;
   double chfavwl = 0, chfavwm = 0, chfavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      chfavwl += (1.0) * olwayschf_dblar[i] / 26;
      chfavwm += (1.0) * ocwayschf_dblar[i] / 26;
      chfavwh += (1.0) * ohwayschf_dblar[i] / 26;
   }
   chfavw[0] = chfavwl; chfavw[1] = chfavwm; chfavw[2] = chfavwh;
   double chfavml = 0, chfavmm = 0, chfavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      chfavml += (1.0) * olmayschf_dblar[i] / 6;
      chfavmm += (1.0) * ocmayschf_dblar[i] / 6;
      chfavmh += (1.0) * ohmayschf_dblar[i] / 6;
   }
   chfavm[0] = chfavml; chfavm[1] = chfavmm; chfavm[2] = chfavmh;
   
   double gbpavdl = 0, gbpavdm = 0, gbpavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      gbpavdl += (1.0) * oldaysgbp_dblar[i] / 72;
      gbpavdm += (1.0) * ocdaysgbp_dblar[i] / 72;
      gbpavdh += (1.0) * ohdaysgbp_dblar[i] / 72;
   }
   gbpavd[0] = gbpavdl; gbpavd[1] = gbpavdm; gbpavd[2] = gbpavdh;
   double gbpavwl = 0, gbpavwm = 0, gbpavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      gbpavwl += (1.0) * olwaysgbp_dblar[i] / 26;
      gbpavwm += (1.0) * ocwaysgbp_dblar[i] / 26;
      gbpavwh += (1.0) * ohwaysgbp_dblar[i] / 26;
   }
   gbpavw[0] = gbpavwl; gbpavw[1] = gbpavwm; gbpavw[2] = gbpavwh;
   double gbpavml = 0, gbpavmm = 0, gbpavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      gbpavml += (1.0) * olmaysgbp_dblar[i] / 6;
      gbpavmm += (1.0) * ocmaysgbp_dblar[i] / 6;
      gbpavmh += (1.0) * ohmaysgbp_dblar[i] / 6;
   }
   gbpavm[0] = gbpavml; gbpavm[1] = gbpavmm; gbpavm[2] = gbpavmh;
   
   double jpyavdl = 0, jpyavdm = 0, jpyavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      jpyavdl += (1.0) * oldaysjpy_dblar[i] / 72;
      jpyavdm += (1.0) * ocdaysjpy_dblar[i] / 72;
      jpyavdh += (1.0) * ohdaysjpy_dblar[i] / 72;
   }
   jpyavd[0] = jpyavdl; jpyavd[1] = jpyavdm; jpyavd[2] = jpyavdh;
   double jpyavwl = 0, jpyavwm = 0, jpyavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      jpyavwl += (1.0) * olwaysjpy_dblar[i] / 26;
      jpyavwm += (1.0) * ocwaysjpy_dblar[i] / 26;
      jpyavwh += (1.0) * ohwaysjpy_dblar[i] / 26;
   }
   jpyavw[0] = jpyavwl; jpyavw[1] = jpyavwm; jpyavw[2] = jpyavwh;
   double jpyavml = 0, jpyavmm = 0, jpyavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      jpyavml += (1.0) * olmaysjpy_dblar[i] / 6;
      jpyavmm += (1.0) * ocmaysjpy_dblar[i] / 6;
      jpyavmh += (1.0) * ohmaysjpy_dblar[i] / 6;
   }
   jpyavm[0] = jpyavml; jpyavm[1] = jpyavmm; jpyavm[2] = jpyavmh;
   
   double audavdl = 0, audavdm = 0, audavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      audavdl += (1.0) * oldaysaud_dblar[i] / 72;
      audavdm += (1.0) * ocdaysaud_dblar[i] / 72;
      audavdh += (1.0) * ohdaysaud_dblar[i] / 72;
   }
   audavd[0] = audavdl; audavd[1] = audavdm; audavd[2] = audavdh;
   double audavwl = 0, audavwm = 0, audavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      audavwl += (1.0) * olwaysaud_dblar[i] / 26;
      audavwm += (1.0) * ocwaysaud_dblar[i] / 26;
      audavwh += (1.0) * ohwaysaud_dblar[i] / 26;
   }
   audavw[0] = audavwl; audavw[1] = audavwm; audavw[2] = audavwh;
   double audavml = 0, audavmm = 0, audavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      audavml += (1.0) * olmaysaud_dblar[i] / 6;
      audavmm += (1.0) * ocmaysaud_dblar[i] / 6;
      audavmh += (1.0) * ohmaysaud_dblar[i] / 6;
   }
   audavm[0] = audavml; audavm[1] = audavmm; audavm[2] = audavmh;
   
   double nzdavdl = 0, nzdavdm = 0, nzdavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      nzdavdl += (1.0) * oldaysnzd_dblar[i] / 72;
      nzdavdm += (1.0) * ocdaysnzd_dblar[i] / 72;
      nzdavdh += (1.0) * ohdaysnzd_dblar[i] / 72;
   }
   nzdavd[0] = nzdavdl; nzdavd[1] = nzdavdm; nzdavd[2] = nzdavdh;
   double nzdavwl = 0, nzdavwm = 0, nzdavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      nzdavwl += (1.0) * olwaysnzd_dblar[i] / 26;
      nzdavwm += (1.0) * ocwaysnzd_dblar[i] / 26;
      nzdavwh += (1.0) * ohwaysnzd_dblar[i] / 26;
   }
   nzdavw[0] = nzdavwl; nzdavw[1] = nzdavwm; nzdavw[2] = nzdavwh;
   double nzdavml = 0, nzdavmm = 0, nzdavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      nzdavml += (1.0) * olmaysnzd_dblar[i] / 6;
      nzdavmm += (1.0) * ocmaysnzd_dblar[i] / 6;
      nzdavmh += (1.0) * ohmaysnzd_dblar[i] / 6;
   }
   nzdavm[0] = nzdavml; nzdavm[1] = nzdavmm; nzdavm[2] = nzdavmh;
   
   double cadavdl = 0, cadavdm = 0, cadavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      cadavdl += (1.0) * oldayscad_dblar[i] / 72;
      cadavdm += (1.0) * ocdayscad_dblar[i] / 72;
      cadavdh += (1.0) * ohdayscad_dblar[i] / 72;
   }
   cadavd[0] = cadavdl; cadavd[1] = cadavdm; cadavd[2] = cadavdh;
   double cadavwl = 0, cadavwm = 0, cadavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      cadavwl += (1.0) * olwayscad_dblar[i] / 26;
      cadavwm += (1.0) * ocwayscad_dblar[i] / 26;
      cadavwh += (1.0) * ohwayscad_dblar[i] / 26;
   }
   cadavw[0] = cadavwl; cadavw[1] = cadavwm; cadavw[2] = cadavwh;
   double cadavml = 0, cadavmm = 0, cadavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      cadavml += (1.0) * olmayscad_dblar[i] / 6;
      cadavmm += (1.0) * ocmayscad_dblar[i] / 6;
      cadavmh += (1.0) * ohmayscad_dblar[i] / 6;
   }
   cadavm[0] = cadavml; cadavm[1] = cadavmm; cadavm[2] = cadavmh;
   
   double usdavdl = 0, usdavdm = 0, usdavdh = 0;
   for (i = 72; i >= 1; i--)
   {
      usdavdl += (1.0) * oldaysusd_dblar[i] / 72;
      usdavdm += (1.0) * ocdaysusd_dblar[i] / 72;
      usdavdh += (1.0) * ohdaysusd_dblar[i] / 72;
   }
   usdavd[0] = usdavdl; usdavd[1] = usdavdm; usdavd[2] = usdavdh;
   double usdavwl = 0, usdavwm = 0, usdavwh = 0;
   for (i = 26; i >= 1; i--)
   {
      usdavwl += (1.0) * olwaysusd_dblar[i] / 26;
      usdavwm += (1.0) * ocwaysusd_dblar[i] / 26;
      usdavwh += (1.0) * ohwaysusd_dblar[i] / 26;
   }
   usdavw[0] = usdavwl; usdavw[1] = usdavwm; usdavw[2] = usdavwh;
   double usdavml = 0, usdavmm = 0, usdavmh = 0;
   for (i = 6; i >= 1; i--)
   {
      usdavml += (1.0) * olmaysusd_dblar[i] / 6;
      usdavmm += (1.0) * ocmaysusd_dblar[i] / 6;
      usdavmh += (1.0) * ohmaysusd_dblar[i] / 6;
   }
   usdavm[0] = usdavml; usdavm[1] = usdavmm; usdavm[2] = usdavmh;
}
//+------------------------------------------------------------------+

//+NEW BAR TIMERS (1M,5M,1H,1D)--------------------------------------+
bool New_Daily_Bar()
{
	static datetime iTime_D = 0;
	if (iTime_D < iTime(NULL, PERIOD_D1, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_D1, 0) + 300) && TimeCurrent() <= (iTime(NULL, PERIOD_D1, 0) + 900)))
	{
		//if(iTime_D<iTime(NULL,PERIOD_D1,0)){
		iTime_D = iTime(NULL, PERIOD_D1, 0);
		return (true);
	}
	else
	{
		return (false);
	}
}
//+------------------------------------------------------------------+

//+NEW BAR TIMERS (1M,5M,1H,1D)--------------------------------------+
bool New_Hourly_Bar()
{
	static datetime iTime_D = 0;
	if (iTime_D < iTime(NULL, PERIOD_M5, 0) && (TimeCurrent() >= (iTime(NULL, PERIOD_M5, 0) + 30) && TimeCurrent() <= (iTime(NULL, PERIOD_M5, 0) + 100)))
	{
		//if(iTime_D<iTime(NULL,PERIOD_D1,0)){
		iTime_D = iTime(NULL, PERIOD_M5, 0);
		return (true);
	}
	else
	{
		return (false);
	}
}
//+------------------------------------------------------------------+

//+HOURLY ALERTS-----------------------------------------------------+
void AlertsHour()
{
	/*	int eut = 0, cht = 0, gut = 0, jpt = 0, aut = 0, nzt = 0, cat = 0, ust = 0;

		for (i = 6; i >= 1; i--) {
			eut += ochoureur_dblar[i];
			cht += ochourchf_dblar[i];
			gut += ochourgbp_dblar[i];
			jpt += ochourjpy_dblar[i];
			aut += ochouraud_dblar[i];
			nzt += ochournzd_dblar[i];
			cat += ochourcad_dblar[i];
			ust += ochourusd_dblar[i];
		}
	*/
	if (Eualh && (MathAbs(ochoureur_dblar[0]) >= 60))
	{
		Alert("EUR increased hourly volatility.");
		Eualh = false;
	}
	if (Chalh && (MathAbs(ochourchf_dblar[0]) >= 60))
	{
		Alert("CHF increased hourly volatility.");
		Chalh = false;
	}
	if (Gualh && (MathAbs(ochourgbp_dblar[0]) >= 60))
	{
		Alert("GBP increased hourly volatility.");
		Gualh = false;
	}
	if (Jpalh && (MathAbs(ochourjpy_dblar[0]) >= 60))
	{
		Alert("JPY increased hourly volatility.");
		Jpalh = false;
	}
	if (Aualh && (MathAbs(ochouraud_dblar[0]) >= 60))
	{
		Alert("AUD increased hourly volatility.");
		Aualh = false;
	}
	if (Nzalh && (MathAbs(ochournzd_dblar[0]) >= 60))
	{
		Alert("NZD increased hourly volatility.");
		Nzalh = false;
	}
	if (Caalh && (MathAbs(ochourcad_dblar[0]) >= 60))
	{
		Alert("CAD increased hourly volatility.");
		Caalh = false;
	}
	if (Usalh && (MathAbs(ochourusd_dblar[0]) >= 60))
	{
		Alert("USD increased hourly volatility.");
		Usalh = false;
	}
}
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Custom functions |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| get text description |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode)
{
	string text = "";
	//---
	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}
	//---
	return text;
}
//Returns reason for re-initializind / de-initializing the indicator
//+------------------------------------------------------------------+

//*DRAWING FUNCTIONS-------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetText(name, label, FSize, Font, FCol);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+RECMAKE FUNCTION--------------------------------------------------+
void RecMake(const string name,
			 const int x,
			 const int y,
			 const int xs,
			 const int ys,
			 const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0, 0))
		{
			Print("error: can't create rectangle_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, xs);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, ys);
	ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
	ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
}
//Create rectangle labels/backgrounds for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild4(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;

	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "Dcb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+

//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;

	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "cb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+

//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild5(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;

	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "Fcb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+

//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild1(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;
	
	int t = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 0), false);
	int w = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 1), false);
	int z = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 2), false);
	int p = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 3), false);
	int o = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 4), false);
	int n = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 5), false);
	int m = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 6), false);
	int l = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 7), false);
	int k = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 8), false);
	int j = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 9), false);
	int g = iBarShift(_Symbol, PERIOD_M15, iTime(_Symbol, PERIOD_H1, 10), false);
	
	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "cb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
		if (i == t)
      {
   		obname = Name + " " + label + "cb CurrentSign15";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == w)
      {
   		obname = Name + " " + label + "cb CurrentSign15a";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == z)
      {
   		obname = Name + " " + label + "cb CurrentSign15b";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == p)
      {
   		obname = Name + " " + label + "cb CurrentSign15c";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == o)
      {
   		obname = Name + " " + label + "cb CurrentSign15d";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == n)
      {
   		obname = Name + " " + label + "cb CurrentSign15e";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == m)
      {
   		obname = Name + " " + label + "cb CurrentSign15f";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == l)
      {
   		obname = Name + " " + label + "cb CurrentSign15g";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == k)
      {
   		obname = Name + " " + label + "cb CurrentSign15h";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == j)
      {
   		obname = Name + " " + label + "cb CurrentSign15i";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
		if (i == g)
      {
   		obname = Name + " " + label + "cb CurrentSign15j";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+
//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild2(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;
	
	int t = iBarShift(_Symbol, PERIOD_M5, iTime(_Symbol, PERIOD_H1, 0), false);	
	
	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "cb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
		if (i == t)
      {
   		obname = Name + " " + label + "cb CurrentSign5";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+
//+HOURLY BUILD FUNC-------------------------------------------------+
void hourbuild3(const string label,
			   const int &curhour[],
			   const int curht,
			   const int x,
			   const int y)
{
	string obname;
	int ys = 20;
	
	int t = iBarShift(_Symbol, PERIOD_M1, iTime(_Symbol, PERIOD_H1, 0), false);	
	
	for (i = 11; i >= 0; i--)
	{
		obname = Name + " " + label + "cb" + IntegerToString(i);
		if (curhour[i] >= 0)
		{
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor1);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
			if (curhour[i] >= 60)
			{
				ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor3);
				ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
			}
		}
		else
			LabelMake(obname, 0, x, y + i * ys, IntegerToString(curhour[i]), FontSize + 1, FontColor2);
			if (i == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Calibri Bold");
		if (curhour[i] <= -60)
		{
			ObjectSetInteger(0, obname, OBJPROP_COLOR, FontColor4);
			ObjectSetInteger(0, obname, OBJPROP_FONTSIZE, 11);
		}
		if (i == t)
      {
   		obname = Name + " " + label + "cb CurrentSign1";
   		LabelMake(obname, 0, x + 23, y + i * ys, CharToStr(60), 11, clrDodgerBlue);
		}
	}
}
//Build hourly table objects
//+------------------------------------------------------------------+

//*CALCULATION FUNCTIONS---------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh4(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_M30, t1) - iClose(a1, PERIOD_M30, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_H1, t1) - iClose(a1, PERIOD_H1, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh5(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_H4, t1) - iClose(a1, PERIOD_H4, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh1(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_M15, t1) - iClose(a1, PERIOD_M15, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh2(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_M5, t1) - iClose(a1, PERIOD_M5, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE H - iOPEN H------------------------------------------------+
int choh3(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_M1, t1) - iClose(a1, PERIOD_M1, t1 + 1)) * r1, 0));
}
//Return range for hour, r1 is multiplier for 5/3 digits / used by hourly buffers for current hour & last 12 hours
//+------------------------------------------------------------------+

//+iCLOSE D - iOPEN D------------------------------------------------+
int cdod1(const double &aday[],
		  int r1,
		  int t1)
{
	int a = (int)NormalizeDouble((aday[t1] - aday[t1 + 1]) * r1, 0);

	return (a);
}
//Return current/past range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iHigh D - iOPEN D-------------------------------------------------+
int hdod1(const double &aday[],
		  const double &bday[],
		  const int r1,
		  const int t1)
{
	int a = (int)NormalizeDouble((aday[t1] - bday[t1 + 1]) * r1, 0);

	return (a);
}
//Return current/past high range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iLow D - iOPEN D--------------------------------------------------+
int ldod1(const double &aday[],
		  const double &bday[],
		  const int r1,
		  const int t1)
{
	int a = (int)NormalizeDouble((aday[t1] - bday[t1 + 1]) * r1, 0);

	return (a);
}
//Return current/past low range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iCLOSE D - iOPEN D------------------------------------------------+
int cdod(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_D1, t1) - iClose(a1, PERIOD_D1, t1 + 1)) * r1, 0));
}
//Return current/past range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iCLOSE W - iOPEN W------------------------------------------------+
int cwow(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_W1, t1) - iClose(a1, PERIOD_W1, t1 + 1)) * r1, 0));
}
//Return current/past range for week, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iCLOSE MN - iOPEN MN----------------------------------------------+
int cmom(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iClose(a1, PERIOD_MN1, t1) - iClose(a1, PERIOD_MN1, t1 + 1)) * r1, 0));
}
//Return current/past range for month, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iHigh D - iOPEN D-------------------------------------------------+
int hdod(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iHigh(a1, PERIOD_D1, t1) - iClose(a1, PERIOD_D1, t1 + 1)) * r1, 0));
}
//Return current/past high range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iHigh W - iOPEN W-------------------------------------------------+
int hwow(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iHigh(a1, PERIOD_W1, t1) - iClose(a1, PERIOD_W1, t1 + 1)) * r1, 0));
}
//Return current/past high range for week, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iHigh MN - iOPEN MN-----------------------------------------------+
int hmom(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iHigh(a1, PERIOD_MN1, t1) - iClose(a1, PERIOD_MN1, t1 + 1)) * r1, 0));
}
//Return current/past high range for month, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iLow D - iOPEN D--------------------------------------------------+
int ldod(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iLow(a1, PERIOD_D1, t1) - iClose(a1, PERIOD_D1, t1 + 1)) * r1, 0));
}
//Return current/past low range for day, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iLow W - iOPEN W--------------------------------------------------+
int lwow(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iLow(a1, PERIOD_W1, t1) - iClose(a1, PERIOD_W1, t1 + 1)) * r1, 0));
}
//Return current/past low range for week, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+

//+iLow MN - iOPEN MN------------------------------------------------+
int lmom(const string a1,
		 const int r1,
		 const int t1)
{

	return ((int)NormalizeDouble((iLow(a1, PERIOD_MN1, t1) - iClose(a1, PERIOD_MN1, t1 + 1)) * r1, 0));
}
//Return current/past low range for month, r1 is multiplier for 5/3 digits / used by buffers
//+------------------------------------------------------------------+