//+------------------------------------------------------------------+
//|                                                   newbaskets.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 48
#property indicator_plots 48
input ENUM_TIMEFRAMES first = PERIOD_H1;
input ENUM_TIMEFRAMES second = PERIOD_MN1;
input int days = 1; //For how many periods back (0 = current)
input int lrlength = 120; //LR lines length
input string prefix = ""; //Broker symbol prefix (before)
input string suffix = ""; //Broker symbol suffix (after)
const string Name = MQLInfoString(MQL_PROGRAM_NAME);

//#include <Math\Alglib\alglib.mqh>
#include <JAson.mqh>
#include <StructSort.mqh>

double eub[], egb[], eab[], enb[], ecb[], efb[], ejb[];
double gub[], gab[], gnb[], gcb[], gfb[], gjb[];
double aub[], anb[], acb[], afb[], ajb[];
double nub[], ncb[], nfb[], njb[];
double ucb[], cfb[], cjb[];
double ufb[], fjb[];
double ujb[];
double euro[], gbpo[], audo[], nzdo[], cado[], chfo[], jpyo[], usdo[];

double eacb[], eaub[], eafb[], eajb[];
double encb[], enub[], enfb[], enjb[];
double ecfb[], ecjb[], eufb[], eujb[];

bool firstrun = false;

bool doublecheck = false;

double lotsize = 1.00;

bool USD = true, EUR = true, GBP = true, AUD = true, NZD = true, CAD = true, CHF = true, JPY = true;

CJAVal thefive;
//double file[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   IndicatorDigits(1);
   IndicatorBuffers(48);
   SetIndexBuffer(0, eub);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 1, clrSteelBlue);
   SetIndexLabel(0, "E+U");
   
   SetIndexBuffer(1, egb);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 1, C'0x7b,0x1f,0xa2');
   SetIndexLabel(1, "E+G");
   
   SetIndexBuffer(2, eab);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(2, "E+A");
   
   SetIndexBuffer(3, enb);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(3, "E+N");
   
   SetIndexBuffer(4, ecb);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(4, "E+C");
   
   SetIndexBuffer(5, efb);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(5, "E+F");
   
   SetIndexBuffer(6, ejb);
   SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, 1, clrDodgerBlue);
   SetIndexLabel(6, "E+J");
   
   SetIndexBuffer(7, gub);
   SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(7, "G+U");
   
   SetIndexBuffer(8, gab);
   SetIndexStyle(8, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(8, "G+A");
   
   SetIndexBuffer(9, gnb);
   SetIndexStyle(9, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(9, "G+N");
   
   SetIndexBuffer(10, gcb);
   SetIndexStyle(10, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(10, "G+C");
   
   SetIndexBuffer(11, gfb);
   SetIndexStyle(11, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(11, "G+F");
   
   SetIndexBuffer(12, gjb);
   SetIndexStyle(12, DRAW_LINE, STYLE_SOLID, 1, clrRed);
   SetIndexLabel(12, "G+J");
   
   SetIndexBuffer(13, aub);
   SetIndexStyle(13, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(13, "A+U");
   
   SetIndexBuffer(14, anb);
   SetIndexStyle(14, DRAW_LINE, STYLE_SOLID, 1, C'0xff,0xeb,0x3b');
   SetIndexLabel(14, "A+N");
   
   SetIndexBuffer(15, acb);
   SetIndexStyle(15, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(15, "A+C");
   
   SetIndexBuffer(16, afb);
   SetIndexStyle(16, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(16, "A+F");
   
   SetIndexBuffer(17, ajb);
   SetIndexStyle(17, DRAW_LINE, STYLE_SOLID, 1, clrOrange);
   SetIndexLabel(17, "A+J");
   
   SetIndexBuffer(18, nub);
   SetIndexStyle(18, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(18, "N+U");
   
   SetIndexBuffer(19, ncb);
   SetIndexStyle(19, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(19, "N+C");
   
   SetIndexBuffer(20, nfb);
   SetIndexStyle(20, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(20, "N+F");
   
   SetIndexBuffer(21, njb);
   SetIndexStyle(21, DRAW_LINE, STYLE_SOLID, 1, clrAqua);
   SetIndexLabel(21, "N+J");
   
   SetIndexBuffer(22, ucb);
   SetIndexStyle(22, DRAW_LINE, STYLE_SOLID, 1, C'0xf8,0xbb,0xd0');
   SetIndexLabel(22, "U+C");
   
   SetIndexBuffer(23, cfb);
   SetIndexStyle(23, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexLabel(23, "C+F");
   
   SetIndexBuffer(24, cjb);
   SetIndexStyle(24, DRAW_LINE, STYLE_SOLID, 1, clrPink);
   SetIndexLabel(24, "C+J");
   
   SetIndexBuffer(25, ufb);
   SetIndexStyle(25, DRAW_LINE, STYLE_SOLID, 1, clrGray);
   SetIndexLabel(25, "F+U");
   
   SetIndexBuffer(26, fjb);
   SetIndexStyle(26, DRAW_LINE, STYLE_SOLID, 1, C'0xf2,0x36,0x45');
   SetIndexLabel(26, "F+J");
   
   SetIndexBuffer(27, ujb);
   SetIndexStyle(27, DRAW_LINE, STYLE_SOLID, 1, clrYellow);
   SetIndexLabel(27, "J+U");
   
   SetIndexBuffer(28, euro);
   SetIndexStyle(28, DRAW_NONE);
   SetIndexBuffer(29, gbpo);
   SetIndexStyle(29, DRAW_NONE);
   SetIndexBuffer(30, audo);
   SetIndexStyle(30, DRAW_NONE);
   SetIndexBuffer(31, nzdo);
   SetIndexStyle(31, DRAW_NONE);
   SetIndexBuffer(32, cado);
   SetIndexStyle(32, DRAW_NONE);
   SetIndexBuffer(33, chfo);
   SetIndexStyle(33, DRAW_NONE);
   SetIndexBuffer(34, jpyo);
   SetIndexStyle(34, DRAW_NONE);
   SetIndexBuffer(35, usdo);
   SetIndexStyle(35, DRAW_NONE);
   /*
   SetIndexBuffer(36, eacb);
   SetIndexStyle(36, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(36, "E+A+C");
   
   SetIndexBuffer(37, eaub);
   SetIndexStyle(37, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(37, "E+A+U");
   
   SetIndexBuffer(38, eafb);
   SetIndexStyle(38, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(38, "E+A+F");
   
   SetIndexBuffer(39, eajb);
   SetIndexStyle(39, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(39, "E+A+J");
   
   SetIndexBuffer(40, encb);
   SetIndexStyle(40, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(40, "E+N+C");
   
   SetIndexBuffer(41, enub);
   SetIndexStyle(41, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(41, "E+N+U");
   
   SetIndexBuffer(42, enfb);
   SetIndexStyle(42, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(42, "E+N+F");
   
   SetIndexBuffer(43, enjb);
   SetIndexStyle(43, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(43, "E+N+J");
   
   SetIndexBuffer(44, ecfb);
   SetIndexStyle(44, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(44, "E+C+F");
   
   SetIndexBuffer(45, ecjb);
   SetIndexStyle(45, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(45, "E+C+J");
   
   SetIndexBuffer(46, eufb);
   SetIndexStyle(46, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(46, "E+U+F");
   
   SetIndexBuffer(47, eujb);
   SetIndexStyle(47, DRAW_LINE, STYLE_SOLID, 2, clrWhite);
   SetIndexLabel(47, "E+U+J");
   */
	if (!EditBox(Name + "LotSize", 3, 40, 20, 100, 700, 8, C'0xb6,0x6f,0x0f', DoubleToString(lotsize, 0)))
		//return;
		
   //ENABLE CURRENCIES
   if (GlobalVariableCheck("fUSD") == false)
   {
      GlobalVariableSet("fUSD", true);
      USD = GlobalVariableGet("fUSD");
   }
   else USD = GlobalVariableGet("fUSD");
   
   if (GlobalVariableCheck("fEUR") == false)
   {
      Print("b");
      GlobalVariableSet("fEUR", true);
      EUR = GlobalVariableGet("fEUR");
   }
   else EUR = GlobalVariableGet("fEUR");
   
   if (GlobalVariableCheck("fGBP") == false)
   {
      GlobalVariableSet("fGBP", true);
      GBP = GlobalVariableGet("fGBP");
   }
   else GBP = GlobalVariableGet("fGBP");
   
   if (GlobalVariableCheck("fAUD") == false)
   {
      GlobalVariableSet("fAUD", true);
      AUD = GlobalVariableGet("fAUD");
   }
   else AUD = GlobalVariableGet("fAUD");
   
   if (GlobalVariableCheck("fNZD") == false)
   {
      GlobalVariableSet("fNZD", true);
      NZD = GlobalVariableGet("fNZD");
   }
   else NZD = GlobalVariableGet("fNZD");
   
   if (GlobalVariableCheck("fCAD") == false)
   {
      GlobalVariableSet("fCAD", true);
      CAD = GlobalVariableGet("fCAD");
   }
   else CAD = GlobalVariableGet("fCAD");
   
   if (GlobalVariableCheck("fCHF") == false)
   {
      GlobalVariableSet("fCHF", true);
      CHF = GlobalVariableGet("fCHF");
   }
   else CHF = GlobalVariableGet("fCHF");
   
   if (GlobalVariableCheck("fJPY") == false)
   {
      GlobalVariableSet("fJPY", true);
      JPY = GlobalVariableGet("fJPY");
   }
   else JPY = GlobalVariableGet("fJPY");
   
   firstrun = true;
      
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
//---
   build();
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+

void build()
{
   int limit = iBarShift(_Symbol, first, iTime(_Symbol, second, days), false);
      
   ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
   ChartSetInteger(0, CHART_FOREGROUND, 0);
   ChartSetInteger(0, CHART_SCALEFIX, 0, true);
   
   const string eu = prefix + "EURUSD" + suffix;
   const string eg = prefix + "EURGBP" + suffix;
   const string ea = prefix + "EURAUD" + suffix;
   const string en = prefix + "EURNZD" + suffix;
   const string ec = prefix + "EURCAD" + suffix;
   const string ef = prefix + "EURCHF" + suffix;
   const string ej = prefix + "EURJPY" + suffix;
   
   const string gu = prefix + "GBPUSD" + suffix;
   const string ga = prefix + "GBPAUD" + suffix;
   const string gn = prefix + "GBPNZD" + suffix;
   const string gc = prefix + "GBPCAD" + suffix;
   const string gf = prefix + "GBPCHF" + suffix;
   const string gj = prefix + "GBPJPY" + suffix;
   
   const string au = prefix + "AUDUSD" + suffix;
   const string an = prefix + "AUDNZD" + suffix;
   const string ac = prefix + "AUDCAD" + suffix;
   const string af = prefix + "AUDCHF" + suffix;
   const string aj = prefix + "AUDJPY" + suffix;
   
   const string nu = prefix + "NZDUSD" + suffix;
   const string nc = prefix + "NZDCAD" + suffix;
   const string nf = prefix + "NZDCHF" + suffix;
   const string nj = prefix + "NZDJPY" + suffix;
   
   const string uc = prefix + "USDCAD" + suffix;
   const string cf = prefix + "CADCHF" + suffix;
   const string cj = prefix + "CADJPY" + suffix;
   
   const string uf = prefix + "USDCHF" + suffix;
   const string fj = prefix + "CHFJPY" + suffix;
   
   const string uj = prefix + "USDJPY" + suffix;
   
   //PIP values
   double eupl = dblPipValue(eu) * lotsize;
   double egpl = dblPipValue(eg) * lotsize;
   double eapl = dblPipValue(ea) * lotsize;
   double enpl = dblPipValue(en) * lotsize;
   double ecpl = dblPipValue(ec) * lotsize;
   double efpl = dblPipValue(ef) * lotsize;
   double ejpl = dblPipValue(ej) * lotsize;
   
   double gupl = dblPipValue(gu) * lotsize;
   double gapl = dblPipValue(ga) * lotsize;
   double gnpl = dblPipValue(gn) * lotsize;
   double gcpl = dblPipValue(gc) * lotsize;
   double gfpl = dblPipValue(gf) * lotsize;
   double gjpl = dblPipValue(gj) * lotsize;
   
   double aupl = dblPipValue(au) * lotsize;
   double anpl = dblPipValue(an) * lotsize;
   double acpl = dblPipValue(ac) * lotsize;
   double afpl = dblPipValue(af) * lotsize;
   double ajpl = dblPipValue(aj) * lotsize;
   
   double nupl = dblPipValue(nu) * lotsize;
   double ncpl = dblPipValue(nc) * lotsize;
   double nfpl = dblPipValue(nf) * lotsize;
   double njpl = dblPipValue(nj) * lotsize;
   
   double ucpl = dblPipValue(uc) * lotsize;
   double cfpl = dblPipValue(cf) * lotsize;
   double cjpl = dblPipValue(cj) * lotsize;
   
   double ufpl = dblPipValue(uf) * lotsize;
   double fjpl = dblPipValue(fj) * lotsize;
   
   double ujpl = dblPipValue(uj) * lotsize;
   
   double eufopen = 0, egfopen = 0, eafopen = 0, enfopen = 0, ecfopen = 0, effopen = 0, ejfopen = 0;
   double gufopen = 0, gafopen = 0, gnfopen = 0, gcfopen = 0, gffopen = 0, gjfopen = 0;
   double aufopen = 0, anfopen = 0, acfopen = 0, affopen = 0, ajfopen = 0;
   double nufopen = 0, ncfopen = 0, nffopen = 0, njfopen = 0;
   double ucfopen = 0, cffopen = 0, cjfopen = 0;
   double uffopen = 0, fjfopen = 0;
   double ujfopen = 0;
   
   static datetime checktime = 0;
   bool check = false;
   if (checktime < iTime(_Symbol, PERIOD_M5, 0))
   {
      checktime = iTime(_Symbol, PERIOD_M5, 0);
      check = true;
   }
   if (check || doublecheck)
   {
      for(int i = limit; i >= 1; i--)
      {
         eufopen = (iClose(eu, first, i) - iClose(eu, second, days + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
         egfopen = (iClose(eg, first, i) - iClose(eg, second, days + 1)) / MarketInfo(eg, MODE_POINT) / 10 * egpl;
         eafopen = (iClose(ea, first, i) - iClose(ea, second, days + 1)) / MarketInfo(ea, MODE_POINT) / 10 * eapl;
         enfopen = (iClose(en, first, i) - iClose(en, second, days + 1)) / MarketInfo(en, MODE_POINT) / 10 * enpl;
         ecfopen = (iClose(ec, first, i) - iClose(ec, second, days + 1)) / MarketInfo(ec, MODE_POINT) / 10 * ecpl;
         effopen = (iClose(ef, first, i) - iClose(ef, second, days + 1)) / MarketInfo(ef, MODE_POINT) / 10 * efpl;
         ejfopen = (iClose(ej, first, i) - iClose(ej, second, days + 1)) / MarketInfo(ej, MODE_POINT) / 10 * ejpl;
         gufopen = (iClose(gu, first, i) - iClose(gu, second, days + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
         gafopen = (iClose(ga, first, i) - iClose(ga, second, days + 1)) / MarketInfo(ga, MODE_POINT) / 10 * gapl;
         gnfopen = (iClose(gn, first, i) - iClose(gn, second, days + 1)) / MarketInfo(gn, MODE_POINT) / 10 * gnpl;
         gcfopen = (iClose(gc, first, i) - iClose(gc, second, days + 1)) / MarketInfo(gc, MODE_POINT) / 10 * gcpl;
         gffopen = (iClose(gf, first, i) - iClose(gf, second, days + 1)) / MarketInfo(gf, MODE_POINT) / 10 * gfpl;
         gjfopen = (iClose(gj, first, i) - iClose(gj, second, days + 1)) / MarketInfo(gj, MODE_POINT) / 10 * gjpl;
         aufopen = (iClose(au, first, i) - iClose(au, second, days + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
         anfopen = (iClose(an, first, i) - iClose(an, second, days + 1)) / MarketInfo(an, MODE_POINT) / 10 * anpl;
         acfopen = (iClose(ac, first, i) - iClose(ac, second, days + 1)) / MarketInfo(ac, MODE_POINT) / 10 * acpl;
         affopen = (iClose(af, first, i) - iClose(af, second, days + 1)) / MarketInfo(af, MODE_POINT) / 10 * afpl;
         ajfopen = (iClose(aj, first, i) - iClose(aj, second, days + 1)) / MarketInfo(aj, MODE_POINT) / 10 * ajpl;
         nufopen = (iClose(nu, first, i) - iClose(nu, second, days + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
         ncfopen = (iClose(nc, first, i) - iClose(nc, second, days + 1)) / MarketInfo(nc, MODE_POINT) / 10 * ncpl;
         nffopen = (iClose(nf, first, i) - iClose(nf, second, days + 1)) / MarketInfo(nf, MODE_POINT) / 10 * nfpl;
         njfopen = (iClose(nj, first, i) - iClose(nj, second, days + 1)) / MarketInfo(nj, MODE_POINT) / 10 * njpl;
         ucfopen = (iClose(uc, first, i) - iClose(uc, second, days + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
         cffopen = (iClose(cf, first, i) - iClose(cf, second, days + 1)) / MarketInfo(cf, MODE_POINT) / 10 * cfpl;
         cjfopen = (iClose(cj, first, i) - iClose(cj, second, days + 1)) / MarketInfo(cj, MODE_POINT) / 10 * cjpl;
         uffopen = (iClose(uf, first, i) - iClose(uf, second, days + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
         fjfopen = (iClose(fj, first, i) - iClose(fj, second, days + 1)) / MarketInfo(fj, MODE_POINT) / 10 * fjpl;
         ujfopen = (iClose(uj, first, i) - iClose(uj, second, days + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
         
         euro[i] = eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen;
         gbpo[i] = gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen;
         audo[i] = aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen;
         nzdo[i] = nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen;
         cado[i] = -ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
         chfo[i] = -uffopen - effopen - gffopen - affopen - nffopen - cffopen + fjfopen;
         jpyo[i] = -ujfopen - ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen;
         usdo[i] = ujfopen - eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen;
         
         eub[i] = euro[i] + usdo[i];//egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen;         
         egb[i] = euro[i] + gbpo[i];//eufopen + eafopen + enfopen + ecfopen + effopen + ejfopen + gufopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen;
         eab[i] = euro[i] + audo[i];//eufopen + egfopen + enfopen + ecfopen + effopen + ejfopen + aufopen - gafopen + anfopen + acfopen + affopen + ajfopen;
         enb[i] = euro[i] + nzdo[i];//eufopen + egfopen + eafopen + ecfopen + effopen + ejfopen + nufopen - gnfopen - anfopen + ncfopen + nffopen + njfopen;
         ecb[i] = euro[i] + cado[i];//eufopen + egfopen + eafopen + enfopen + effopen + ejfopen - ucfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
         efb[i] = euro[i] + chfo[i];//eufopen + egfopen + eafopen + enfopen + ecfopen + ejfopen - uffopen - gffopen - affopen - nffopen - cffopen + fjfopen;
         ejb[i] = euro[i] + jpyo[i];//eufopen + egfopen + eafopen + enfopen + ecfopen + effopen - ujfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen;
         
         gub[i] = gbpo[i] + usdo[i];//-egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen - eufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen;
         gab[i] = gbpo[i] + audo[i];//gufopen - egfopen + gnfopen + gcfopen + gffopen + gjfopen + aufopen - eafopen + anfopen + acfopen + affopen + ajfopen;
         gnb[i] = gbpo[i] + nzdo[i];//gufopen - egfopen + gafopen + gcfopen + gffopen + gjfopen + nufopen - enfopen - anfopen + ncfopen + nffopen + njfopen;
         gcb[i] = gbpo[i] + cado[i];//gufopen - egfopen + gafopen + gnfopen + gffopen + gjfopen - ucfopen - ecfopen - acfopen - ncfopen + cffopen + cjfopen;
         gfb[i] = gbpo[i] + chfo[i];//gufopen - egfopen + gafopen + gnfopen + gcfopen + gjfopen - uffopen - effopen - affopen - nffopen - cffopen + fjfopen;
         gjb[i] = gbpo[i] + jpyo[i];//gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen - ujfopen - ejfopen - ajfopen - njfopen - cjfopen - fjfopen;
         
         aub[i] = audo[i] + usdo[i];//-eafopen - gafopen + anfopen + acfopen + affopen + ajfopen - eufopen - gufopen - nufopen + ucfopen + uffopen + ujfopen;
         anb[i] = audo[i] + nzdo[i];//-eafopen - gafopen + aufopen + acfopen + affopen + ajfopen - nufopen - enfopen - gnfopen + ncfopen + nffopen + njfopen;
         acb[i] = audo[i] + cado[i];//-eafopen - gafopen + aufopen + anfopen + affopen + ajfopen - ucfopen - ecfopen - gcfopen - ncfopen + cffopen + cjfopen;
         afb[i] = audo[i] + chfo[i];//-eafopen - gafopen + aufopen + anfopen + acfopen + ajfopen - uffopen - effopen - gffopen - nffopen - cffopen + fjfopen;
         ajb[i] = audo[i] + jpyo[i];//-eafopen - gafopen + aufopen + anfopen + acfopen + affopen - ujfopen - ejfopen - gjfopen - njfopen - cjfopen - fjfopen;
         
         nub[i] = nzdo[i] + usdo[i];//-enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen - eufopen - gufopen - aufopen + ucfopen + uffopen + ujfopen;
         ncb[i] = nzdo[i] + cado[i];//-enfopen - gnfopen + nufopen - anfopen + nffopen + njfopen - ucfopen - ecfopen - gcfopen - acfopen + cffopen + cjfopen;
         nfb[i] = nzdo[i] + chfo[i];//-enfopen - gnfopen + nufopen - anfopen + ncfopen + njfopen - uffopen - effopen - gffopen - affopen - cffopen + fjfopen;
         njb[i] = nzdo[i] + jpyo[i];//-enfopen - gnfopen + nufopen - anfopen + ncfopen + nffopen - ujfopen - ejfopen - gjfopen - ajfopen - cjfopen - fjfopen;
      
         ucb[i] = usdo[i] + cado[i];//-eufopen - gufopen - aufopen - nufopen + uffopen + ujfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
         cfb[i] = cado[i] + chfo[i];//-ecfopen - gcfopen - acfopen - ncfopen - ucfopen + cjfopen - uffopen - effopen - gffopen - affopen - nffopen + fjfopen;
         cjb[i] = cado[i] + jpyo[i];//-ecfopen - gcfopen - acfopen - ncfopen - ucfopen + cffopen - ujfopen - ejfopen - gjfopen - ajfopen - njfopen - fjfopen;
         
         ufb[i] = usdo[i] + chfo[i];//-eufopen - gufopen - aufopen - nufopen + ucfopen + ujfopen - effopen - gffopen - affopen - nffopen - cffopen + fjfopen;
         fjb[i] = chfo[i] + jpyo[i];//-effopen - gffopen - affopen - nffopen - cffopen - uffopen - ujfopen - ejfopen - gjfopen - ajfopen - njfopen - ujfopen;
         
         ujb[i] = usdo[i] + jpyo[i];//-eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen - ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen; 
         /*
         eacb[i] = euro[i] + audo[i] + cado[i];
         eaub[i] = euro[i] + audo[i] + usdo[i];
         eafb[i] = euro[i] + audo[i] + chfo[i];
         eajb[i] = euro[i] + audo[i] + jpyo[i];
         encb[i] = euro[i] + nzdo[i] + cado[i];
         enub[i] = euro[i] + nzdo[i] + usdo[i];
         enfb[i] = euro[i] + nzdo[i] + chfo[i];
         enjb[i] = euro[i] + nzdo[i] + jpyo[i];
         ecfb[i] = euro[i] + cado[i] + chfo[i];
         ecjb[i] = euro[i] + cado[i] + jpyo[i];
         eufb[i] = euro[i] + usdo[i] + chfo[i];
         eujb[i] = euro[i] + usdo[i] + jpyo[i];
         */
      }
      doublecheck = false;
      check = false;
   }
   
   {
      eufopen = (iClose(eu, first, 0) - iClose(eu, second, days + 1)) / MarketInfo(eu, MODE_POINT) / 10 * eupl;
      egfopen = (iClose(eg, first, 0) - iClose(eg, second, days + 1)) / MarketInfo(eg, MODE_POINT) / 10 * egpl;
      eafopen = (iClose(ea, first, 0) - iClose(ea, second, days + 1)) / MarketInfo(ea, MODE_POINT) / 10 * eapl;
      enfopen = (iClose(en, first, 0) - iClose(en, second, days + 1)) / MarketInfo(en, MODE_POINT) / 10 * enpl;
      ecfopen = (iClose(ec, first, 0) - iClose(ec, second, days + 1)) / MarketInfo(ec, MODE_POINT) / 10 * ecpl;
      effopen = (iClose(ef, first, 0) - iClose(ef, second, days + 1)) / MarketInfo(ef, MODE_POINT) / 10 * efpl;
      ejfopen = (iClose(ej, first, 0) - iClose(ej, second, days + 1)) / MarketInfo(ej, MODE_POINT) / 10 * ejpl;
      gufopen = (iClose(gu, first, 0) - iClose(gu, second, days + 1)) / MarketInfo(gu, MODE_POINT) / 10 * gupl;
      gafopen = (iClose(ga, first, 0) - iClose(ga, second, days + 1)) / MarketInfo(ga, MODE_POINT) / 10 * gapl;
      gnfopen = (iClose(gn, first, 0) - iClose(gn, second, days + 1)) / MarketInfo(gn, MODE_POINT) / 10 * gnpl;
      gcfopen = (iClose(gc, first, 0) - iClose(gc, second, days + 1)) / MarketInfo(gc, MODE_POINT) / 10 * gcpl;
      gffopen = (iClose(gf, first, 0) - iClose(gf, second, days + 1)) / MarketInfo(gf, MODE_POINT) / 10 * gfpl;
      gjfopen = (iClose(gj, first, 0) - iClose(gj, second, days + 1)) / MarketInfo(gj, MODE_POINT) / 10 * gjpl;
      aufopen = (iClose(au, first, 0) - iClose(au, second, days + 1)) / MarketInfo(au, MODE_POINT) / 10 * aupl;
      anfopen = (iClose(an, first, 0) - iClose(an, second, days + 1)) / MarketInfo(an, MODE_POINT) / 10 * anpl;
      acfopen = (iClose(ac, first, 0) - iClose(ac, second, days + 1)) / MarketInfo(ac, MODE_POINT) / 10 * acpl;
      affopen = (iClose(af, first, 0) - iClose(af, second, days + 1)) / MarketInfo(af, MODE_POINT) / 10 * afpl;
      ajfopen = (iClose(aj, first, 0) - iClose(aj, second, days + 1)) / MarketInfo(aj, MODE_POINT) / 10 * ajpl;
      nufopen = (iClose(nu, first, 0) - iClose(nu, second, days + 1)) / MarketInfo(nu, MODE_POINT) / 10 * nupl;
      ncfopen = (iClose(nc, first, 0) - iClose(nc, second, days + 1)) / MarketInfo(nc, MODE_POINT) / 10 * ncpl;
      nffopen = (iClose(nf, first, 0) - iClose(nf, second, days + 1)) / MarketInfo(nf, MODE_POINT) / 10 * nfpl;
      njfopen = (iClose(nj, first, 0) - iClose(nj, second, days + 1)) / MarketInfo(nj, MODE_POINT) / 10 * njpl;
      ucfopen = (iClose(uc, first, 0) - iClose(uc, second, days + 1)) / MarketInfo(uc, MODE_POINT) / 10 * ucpl;
      cffopen = (iClose(cf, first, 0) - iClose(cf, second, days + 1)) / MarketInfo(cf, MODE_POINT) / 10 * cfpl;
      cjfopen = (iClose(cj, first, 0) - iClose(cj, second, days + 1)) / MarketInfo(cj, MODE_POINT) / 10 * cjpl;
      uffopen = (iClose(uf, first, 0) - iClose(uf, second, days + 1)) / MarketInfo(uf, MODE_POINT) / 10 * ufpl;
      fjfopen = (iClose(fj, first, 0) - iClose(fj, second, days + 1)) / MarketInfo(fj, MODE_POINT) / 10 * fjpl;
      ujfopen = (iClose(uj, first, 0) - iClose(uj, second, days + 1)) / MarketInfo(uj, MODE_POINT) / 10 * ujpl;
      
      euro[0] = eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen;
      gbpo[0] = gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen;
      audo[0] = aufopen - eafopen - gafopen + anfopen + acfopen + affopen + ajfopen;
      nzdo[0] = nufopen - enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen;
      cado[0] = -ucfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
      chfo[0] = -uffopen - effopen - gffopen - affopen - nffopen - cffopen + fjfopen;
      jpyo[0] = -ujfopen - ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen;
      usdo[0] = ujfopen - eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen;
      
      eub[0] = euro[0] + usdo[0];//egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen;         
      egb[0] = euro[0] + gbpo[0];//eufopen + eafopen + enfopen + ecfopen + effopen + ejfopen + gufopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen;
      eab[0] = euro[0] + audo[0];//eufopen + egfopen + enfopen + ecfopen + effopen + ejfopen + aufopen - gafopen + anfopen + acfopen + affopen + ajfopen;
      enb[0] = euro[0] + nzdo[0];//eufopen + egfopen + eafopen + ecfopen + effopen + ejfopen + nufopen - gnfopen - anfopen + ncfopen + nffopen + njfopen;
      ecb[0] = euro[0] + cado[0];//eufopen + egfopen + eafopen + enfopen + effopen + ejfopen - ucfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
      efb[0] = euro[0] + chfo[0];//eufopen + egfopen + eafopen + enfopen + ecfopen + ejfopen - uffopen - gffopen - affopen - nffopen - cffopen + fjfopen;
      ejb[0] = euro[0] + jpyo[0];//eufopen + egfopen + eafopen + enfopen + ecfopen + effopen - ujfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen;
      
      gub[0] = gbpo[0] + usdo[0];//-egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen - eufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen;
      gab[0] = gbpo[0] + audo[0];//gufopen - egfopen + gnfopen + gcfopen + gffopen + gjfopen + aufopen - eafopen + anfopen + acfopen + affopen + ajfopen;
      gnb[0] = gbpo[0] + nzdo[0];//gufopen - egfopen + gafopen + gcfopen + gffopen + gjfopen + nufopen - enfopen - anfopen + ncfopen + nffopen + njfopen;
      gcb[0] = gbpo[0] + cado[0];//gufopen - egfopen + gafopen + gnfopen + gffopen + gjfopen - ucfopen - ecfopen - acfopen - ncfopen + cffopen + cjfopen;
      gfb[0] = gbpo[0] + chfo[0];//gufopen - egfopen + gafopen + gnfopen + gcfopen + gjfopen - uffopen - effopen - affopen - nffopen - cffopen + fjfopen;
      gjb[0] = gbpo[0] + jpyo[0];//gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen - ujfopen - ejfopen - ajfopen - njfopen - cjfopen - fjfopen;
      
      aub[0] = audo[0] + usdo[0];//-eafopen - gafopen + anfopen + acfopen + affopen + ajfopen - eufopen - gufopen - nufopen + ucfopen + uffopen + ujfopen;
      anb[0] = audo[0] + nzdo[0];//-eafopen - gafopen + aufopen + acfopen + affopen + ajfopen - nufopen - enfopen - gnfopen + ncfopen + nffopen + njfopen;
      acb[0] = audo[0] + cado[0];//-eafopen - gafopen + aufopen + anfopen + affopen + ajfopen - ucfopen - ecfopen - gcfopen - ncfopen + cffopen + cjfopen;
      afb[0] = audo[0] + chfo[0];//-eafopen - gafopen + aufopen + anfopen + acfopen + ajfopen - uffopen - effopen - gffopen - nffopen - cffopen + fjfopen;
      ajb[0] = audo[0] + jpyo[0];//-eafopen - gafopen + aufopen + anfopen + acfopen + affopen - ujfopen - ejfopen - gjfopen - njfopen - cjfopen - fjfopen;
      
      nub[0] = nzdo[0] + usdo[0];//-enfopen - gnfopen - anfopen + ncfopen + nffopen + njfopen - eufopen - gufopen - aufopen + ucfopen + uffopen + ujfopen;
      ncb[0] = nzdo[0] + cado[0];//-enfopen - gnfopen + nufopen - anfopen + nffopen + njfopen - ucfopen - ecfopen - gcfopen - acfopen + cffopen + cjfopen;
      nfb[0] = nzdo[0] + chfo[0];//-enfopen - gnfopen + nufopen - anfopen + ncfopen + njfopen - uffopen - effopen - gffopen - affopen - cffopen + fjfopen;
      njb[0] = nzdo[0] + jpyo[0];//-enfopen - gnfopen + nufopen - anfopen + ncfopen + nffopen - ujfopen - ejfopen - gjfopen - ajfopen - cjfopen - fjfopen;
   
      ucb[0] = usdo[0] + cado[0];//-eufopen - gufopen - aufopen - nufopen + uffopen + ujfopen - ecfopen - gcfopen - acfopen - ncfopen + cffopen + cjfopen;
      cfb[0] = cado[0] + chfo[0];//-ecfopen - gcfopen - acfopen - ncfopen - ucfopen + cjfopen - uffopen - effopen - gffopen - affopen - nffopen + fjfopen;
      cjb[0] = cado[0] + jpyo[0];//-ecfopen - gcfopen - acfopen - ncfopen - ucfopen + cffopen - ujfopen - ejfopen - gjfopen - ajfopen - njfopen - fjfopen;
      
      ufb[0] = usdo[0] + chfo[0];//-eufopen - gufopen - aufopen - nufopen + ucfopen + ujfopen - effopen - gffopen - affopen - nffopen - cffopen + fjfopen;
      fjb[0] = chfo[0] + jpyo[0];//-effopen - gffopen - affopen - nffopen - cffopen - uffopen - ujfopen - ejfopen - gjfopen - ajfopen - njfopen - ujfopen;
      
      ujb[0] = usdo[0] + jpyo[0];//-eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen - ejfopen - gjfopen - ajfopen - njfopen - cjfopen - fjfopen; 
      /*
      eacb[0] = euro[0] + audo[0] + cado[0];
      eaub[0] = euro[0] + audo[0] + usdo[0];
      eafb[0] = euro[0] + audo[0] + chfo[0];
      eajb[0] = euro[0] + audo[0] + jpyo[0];
      encb[0] = euro[0] + nzdo[0] + cado[0];
      enub[0] = euro[0] + nzdo[0] + usdo[0];
      enfb[0] = euro[0] + nzdo[0] + chfo[0];
      enjb[0] = euro[0] + nzdo[0] + jpyo[0];
      ecfb[0] = euro[0] + cado[0] + chfo[0];
      ecjb[0] = euro[0] + cado[0] + jpyo[0];
      eufb[0] = euro[0] + usdo[0] + chfo[0];
      eujb[0] = euro[0] + usdo[0] + jpyo[0];
      */
   }
   
   Java javac[];
   
   AddToJava(javac, eub[0], "E+U");
   AddToJava(javac, egb[0], "E+G");
   AddToJava(javac, eab[0], "E+A");
   AddToJava(javac, enb[0], "E+N");
   AddToJava(javac, ecb[0], "E+C");
   AddToJava(javac, efb[0], "E+F");
   AddToJava(javac, ejb[0], "E+J");
   AddToJava(javac, gub[0], "G+U");
   AddToJava(javac, gab[0], "G+A");
   AddToJava(javac, gnb[0], "G+N");
   AddToJava(javac, gcb[0], "G+C");
   AddToJava(javac, gfb[0], "G+F");
   AddToJava(javac, gjb[0], "G+J");
   AddToJava(javac, aub[0], "A+U");
   AddToJava(javac, anb[0], "A+N");
   AddToJava(javac, acb[0], "A+C");
   AddToJava(javac, afb[0], "A+F");
   AddToJava(javac, ajb[0], "A+J");
   AddToJava(javac, nub[0], "N+U");
   AddToJava(javac, ncb[0], "N+C");
   AddToJava(javac, nfb[0], "N+F");
   AddToJava(javac, njb[0], "N+J");
   AddToJava(javac, ucb[0], "C+U");
   AddToJava(javac, cfb[0], "C+F");
   AddToJava(javac, cjb[0], "C+J");
   AddToJava(javac, ufb[0], "F+U");
   AddToJava(javac, fjb[0], "F+J");
   AddToJava(javac, ujb[0], "J+U");
   
   ArraySortStruct(javac, coin);
   
   thefive["c"][0].Add(javac[27].coin, 1);
   thefive["c"][0].Add(javac[27].boin);
   thefive["c"][1].Add(javac[26].coin, 1);
   thefive["c"][1].Add(javac[26].boin);
   thefive["c"][2].Add(javac[25].coin, 1);
   thefive["c"][2].Add(javac[25].boin);
   thefive["c"][3].Add(javac[24].coin, 1);
   thefive["c"][3].Add(javac[24].boin);
   thefive["c"][4].Add(javac[23].coin, 1);
   thefive["c"][4].Add(javac[23].boin);
   thefive["c"][5].Add(javac[22].coin, 1);
   thefive["c"][5].Add(javac[22].boin);
   thefive["c"][6].Add(javac[21].coin, 1);
   thefive["c"][6].Add(javac[21].boin);
   thefive["c"][7].Add(javac[20].coin, 1);
   thefive["c"][7].Add(javac[20].boin);
   thefive["c"][8].Add(javac[19].coin, 1);
   thefive["c"][8].Add(javac[19].boin);
   thefive["c"][9].Add(javac[18].coin, 1);
   thefive["c"][9].Add(javac[18].boin);
   thefive["c"][10].Add(javac[17].coin, 1);
   thefive["c"][10].Add(javac[17].boin);
   thefive["c"][11].Add(javac[16].coin, 1);
   thefive["c"][11].Add(javac[16].boin);
   thefive["c"][12].Add(javac[15].coin, 1);
   thefive["c"][12].Add(javac[15].boin);
   thefive["c"][13].Add(javac[14].coin, 1);
   thefive["c"][13].Add(javac[14].boin);
   thefive["c"][14].Add(javac[13].coin, 1);
   thefive["c"][14].Add(javac[13].boin);
   thefive["c"][15].Add(javac[12].coin, 1);
   thefive["c"][15].Add(javac[12].boin);
   thefive["c"][16].Add(javac[11].coin, 1);
   thefive["c"][16].Add(javac[11].boin);
   thefive["c"][17].Add(javac[10].coin, 1);
   thefive["c"][17].Add(javac[10].boin);
   thefive["c"][18].Add(javac[9].coin, 1);
   thefive["c"][18].Add(javac[9].boin);
   thefive["c"][19].Add(javac[8].coin, 1);
   thefive["c"][19].Add(javac[8].boin);
   thefive["c"][20].Add(javac[7].coin, 1);
   thefive["c"][20].Add(javac[7].boin);
   thefive["c"][21].Add(javac[6].coin, 1);
   thefive["c"][21].Add(javac[6].boin);
   thefive["c"][22].Add(javac[5].coin, 1);
   thefive["c"][22].Add(javac[5].boin);
   thefive["c"][23].Add(javac[4].coin, 1);
   thefive["c"][23].Add(javac[4].boin);
   thefive["c"][24].Add(javac[3].coin, 1);
   thefive["c"][24].Add(javac[3].boin);
   thefive["c"][25].Add(javac[2].coin, 1);
   thefive["c"][25].Add(javac[2].boin);
   thefive["c"][26].Add(javac[1].coin, 1);
   thefive["c"][26].Add(javac[1].boin);
   thefive["c"][27].Add(javac[0].coin, 1);
   thefive["c"][27].Add(javac[0].boin);
   
   ArrayFree(javac);
   
   string obname;
   int boldness = 0;
   
   for (int i = 27; i >= 0; i--)
   {
      obname = Name + "N" + IntegerToString(i);
      LabelMake(obname, 3, 140, 600 - i * 20, thefive["c"][i][1].ToStr(), 10, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (EUR == false && StringFind(thefive["c"][i][1].ToStr(), "E", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (GBP == false && (StringFind(thefive["c"][i][1].ToStr(), "G", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+G", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (AUD == false && (StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+A", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+A", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (NZD == false && (StringFind(thefive["c"][i][1].ToStr(), "N", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+N", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+N", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "A+N", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (CAD == false && (StringFind(thefive["c"][i][1].ToStr(), "C", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+C", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+C", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "A+C", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "N+C", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (CHF == false && (StringFind(thefive["c"][i][1].ToStr(), "F", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+F", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+F", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "A+F", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "N+F", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "C+F", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (JPY == false && (StringFind(thefive["c"][i][1].ToStr(), "J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "A+J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "N+J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "C+J", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "F+J", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (USD == false && (StringFind(thefive["c"][i][1].ToStr(), "J+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "E+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "G+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "A+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "N+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "C+U", 0) == 0 || StringFind(thefive["c"][i][1].ToStr(), "F+U", 0) == 0)) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      
      if (i <= 3 || i >= 24) boldness = 2; else boldness = 0;
      
      if ((i <= 3 || i >= 24) && StringFind(thefive["c"][i][1].ToStr(), "E+U", 0) == 0) SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, boldness);
      if ((i <= 3 || i >= 24) && StringFind(thefive["c"][i][1].ToStr(), "E+G", 0) == 0) SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, boldness);
      if ((i <= 3 || i >= 24) && StringFind(thefive["c"][i][1].ToStr(), "A+N", 0) == 0) SetIndexStyle(14, DRAW_LINE, STYLE_SOLID, boldness);
      if ((i <= 3 || i >= 24) && StringFind(thefive["c"][i][1].ToStr(), "C+U", 0) == 0) SetIndexStyle(22, DRAW_LINE, STYLE_SOLID, boldness);
      if ((i <= 3 || i >= 24) && StringFind(thefive["c"][i][1].ToStr(), "F+J", 0) == 0) SetIndexStyle(26, DRAW_LINE, STYLE_SOLID, boldness);
      
      /*
      if (AUD == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (NZD == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (CAD == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (CHF == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (JPY == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      if (USD == false && StringFind(thefive["c"][i][1].ToStr(), "A", 0) == 0) ObjectSetString(0, obname, OBJPROP_FONT, "Arial Black");
      */
      
      if (StringFind(thefive["c"][i][1].ToStr(), "E+A", 0) == 0) SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+N", 0) == 0) SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+C", 0) == 0) SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+F", 0) == 0) SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+J", 0) == 0) SetIndexStyle(6, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "G+U", 0) == 0) SetIndexStyle(7, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "G+A", 0) == 0) SetIndexStyle(8, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "G+N", 0) == 0) SetIndexStyle(9, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "G+C", 0) == 0) SetIndexStyle(10, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "G+F", 0) == 0) SetIndexStyle(11, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "G+J", 0) == 0) SetIndexStyle(12, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "A+U", 0) == 0) SetIndexStyle(13, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "A+C", 0) == 0) SetIndexStyle(15, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "A+F", 0) == 0) SetIndexStyle(16, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "A+J", 0) == 0) SetIndexStyle(17, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "N+U", 0) == 0) SetIndexStyle(18, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "N+C", 0) == 0) SetIndexStyle(19, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "N+F", 0) == 0) SetIndexStyle(20, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "N+J", 0) == 0) SetIndexStyle(21, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "C+F", 0) == 0) SetIndexStyle(23, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "C+J", 0) == 0) SetIndexStyle(24, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "F+U", 0) == 0) SetIndexStyle(25, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "J+U", 0) == 0) SetIndexStyle(27, DRAW_LINE, STYLE_SOLID, boldness, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      
      if (StringFind(thefive["c"][i][1].ToStr(), "E+U", 0) == 0) { ObjectSetInteger(0, obname, OBJPROP_COLOR, clrSteelBlue); ObjectSetString(0, obname, OBJPROP_TEXT, "*" + thefive["c"][i][1].ToStr()); }
      if (StringFind(thefive["c"][i][1].ToStr(), "C+U", 0) == 0) { ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xf8,0xbb,0xd0'); ObjectSetString(0, obname, OBJPROP_TEXT, "*" + thefive["c"][i][1].ToStr()); }
      if (StringFind(thefive["c"][i][1].ToStr(), "F+J", 0) == 0) { ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xf2,0x36,0x45'); ObjectSetString(0, obname, OBJPROP_TEXT, "*" + thefive["c"][i][1].ToStr()); }
      if (StringFind(thefive["c"][i][1].ToStr(), "A+N", 0) == 0) { ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xff,0xeb,0x3b'); ObjectSetString(0, obname, OBJPROP_TEXT, "*" + thefive["c"][i][1].ToStr()); }
      if (StringFind(thefive["c"][i][1].ToStr(), "E+G", 0) == 0) { ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0x7b,0x1f,0xa2'); ObjectSetString(0, obname, OBJPROP_TEXT, "*" + thefive["c"][i][1].ToStr()); }
      
      obname = Name + "C" + IntegerToString(i);
      LabelMake(obname, 3, 95, 600 - i * 20, DoubleToString(thefive["c"][i][0].ToDbl(), 1), 10, C'150,255,0' - (i + 1) * C'0,9,0' + (i + 1) * C'2,0,9');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrSteelBlue);
      if (StringFind(thefive["c"][i][1].ToStr(), "C+U", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xf8,0xbb,0xd0');
      if (StringFind(thefive["c"][i][1].ToStr(), "F+J", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xf2,0x36,0x45');
      if (StringFind(thefive["c"][i][1].ToStr(), "A+N", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0xff,0xeb,0x3b');
      if (StringFind(thefive["c"][i][1].ToStr(), "E+G", 0) == 0) ObjectSetInteger(0, obname, OBJPROP_COLOR, C'0x7b,0x1f,0xa2');
   }
   
   thefive.Clear();
   
   double eur = eufopen + egfopen + eafopen + enfopen + ecfopen + effopen + ejfopen;
   double gbp = gufopen - egfopen + gafopen + gnfopen + gcfopen + gffopen + gjfopen;
   double aud = aufopen - gafopen - eafopen + anfopen + acfopen + affopen + ajfopen;
   double nzd = nufopen - gnfopen - enfopen - anfopen + ncfopen + nffopen + njfopen;
   double cad = -ucfopen - gcfopen - ecfopen - acfopen - ncfopen + cffopen + cjfopen;
   double chf = -uffopen - gffopen - effopen - affopen - nffopen - cffopen + fjfopen;
   double jpy = -ujfopen - gjfopen - ejfopen - ajfopen - njfopen - cjfopen - fjfopen;
   double usd = -eufopen - gufopen - aufopen - nufopen + ucfopen + uffopen + ujfopen;
   
   obname = Name + "euro";
   Texter(obname, eur, 0, DoubleToString(eur, 0), clrDodgerBlue);
   obname = Name + "gbpo";
   Texter(obname, gbp, 0, DoubleToString(gbp, 0), clrRed);
   obname = Name + "audo";
   Texter(obname, aud, 0, DoubleToString(aud, 0), clrOrange);
   obname = Name + "nzdo";
   Texter(obname, nzd, 0, DoubleToString(nzd, 0), clrAqua);
   obname = Name + "cado";
   Texter(obname, cad, 0, DoubleToString(cad, 0), clrPink);
   obname = Name + "usdo";
   Texter(obname, usd, 0, DoubleToString(usd, 0), clrGreen);
   obname = Name + "chfo";
   Texter(obname, chf, 0, DoubleToString(chf, 0), clrGray);
   obname = Name + "jpyo";
   Texter(obname, jpy, 0, DoubleToString(jpy, 0), clrYellow);
     
   double Min1a[28];
   Min1a[0] = eub[ArrayMinimum(eub, limit - 1, 0)];
   Min1a[1] = egb[ArrayMinimum(egb, limit - 1, 0)];
   Min1a[2] = eab[ArrayMinimum(eab, limit - 1, 0)];
   Min1a[3] = enb[ArrayMinimum(enb, limit - 1, 0)];
   Min1a[4] = ecb[ArrayMinimum(ecb, limit - 1, 0)];
   Min1a[5] = efb[ArrayMinimum(efb, limit - 1, 0)];
   Min1a[6] = ejb[ArrayMinimum(ejb, limit - 1, 0)];
   Min1a[7] = gub[ArrayMinimum(gub, limit - 1, 0)];
   Min1a[8] = gab[ArrayMinimum(gab, limit - 1, 0)];
   Min1a[9] = gnb[ArrayMinimum(gnb, limit - 1, 0)];
   Min1a[10] = gcb[ArrayMinimum(gcb, limit - 1, 0)];
   Min1a[11] = gfb[ArrayMinimum(gfb, limit - 1, 0)];
   Min1a[12] = gjb[ArrayMinimum(gjb, limit - 1, 0)];
   Min1a[13] = aub[ArrayMinimum(aub, limit - 1, 0)];
   Min1a[14] = anb[ArrayMinimum(anb, limit - 1, 0)];
   Min1a[15] = acb[ArrayMinimum(acb, limit - 1, 0)];
   Min1a[16] = afb[ArrayMinimum(afb, limit - 1, 0)];
   Min1a[17] = ajb[ArrayMinimum(ajb, limit - 1, 0)];
   Min1a[18] = nub[ArrayMinimum(nub, limit - 1, 0)];
   Min1a[19] = ncb[ArrayMinimum(ncb, limit - 1, 0)];
   Min1a[20] = nfb[ArrayMinimum(nfb, limit - 1, 0)];
   Min1a[21] = njb[ArrayMinimum(njb, limit - 1, 0)];
   Min1a[22] = ucb[ArrayMinimum(ucb, limit - 1, 0)];
   Min1a[23] = cfb[ArrayMinimum(cfb, limit - 1, 0)];
   Min1a[24] = cjb[ArrayMinimum(cjb, limit - 1, 0)];
   Min1a[25] = ufb[ArrayMinimum(ufb, limit - 1, 0)];
   Min1a[26] = fjb[ArrayMinimum(fjb, limit - 1, 0)];
   Min1a[27] = ujb[ArrayMinimum(ujb, limit - 1, 0)];
   
   double Min1 = Min1a[ArrayMinimum(Min1a, 0, 0)];
   
   double Max1a[28];
   Max1a[0] = eub[ArrayMaximum(eub, limit - 1, 0)];
   Max1a[1] = egb[ArrayMaximum(egb, limit - 1, 0)];
   Max1a[2] = eab[ArrayMaximum(eab, limit - 1, 0)];
   Max1a[3] = enb[ArrayMaximum(enb, limit - 1, 0)];
   Max1a[4] = ecb[ArrayMaximum(ecb, limit - 1, 0)];
   Max1a[5] = efb[ArrayMaximum(efb, limit - 1, 0)];
   Max1a[6] = ejb[ArrayMaximum(ejb, limit - 1, 0)];
   Max1a[7] = gub[ArrayMaximum(gub, limit - 1, 0)];
   Max1a[8] = gab[ArrayMaximum(gab, limit - 1, 0)];
   Max1a[9] = gnb[ArrayMaximum(gnb, limit - 1, 0)];
   Max1a[10] = gcb[ArrayMaximum(gcb, limit - 1, 0)];
   Max1a[11] = gfb[ArrayMaximum(gfb, limit - 1, 0)];
   Max1a[12] = gjb[ArrayMaximum(gjb, limit - 1, 0)];
   Max1a[13] = aub[ArrayMaximum(aub, limit - 1, 0)];
   Max1a[14] = anb[ArrayMaximum(anb, limit - 1, 0)];
   Max1a[15] = acb[ArrayMaximum(acb, limit - 1, 0)];
   Max1a[16] = afb[ArrayMaximum(afb, limit - 1, 0)];
   Max1a[17] = ajb[ArrayMaximum(ajb, limit - 1, 0)];
   Max1a[18] = nub[ArrayMaximum(nub, limit - 1, 0)];
   Max1a[19] = ncb[ArrayMaximum(ncb, limit - 1, 0)];
   Max1a[20] = nfb[ArrayMaximum(nfb, limit - 1, 0)];
   Max1a[21] = njb[ArrayMaximum(njb, limit - 1, 0)];
   Max1a[22] = ucb[ArrayMaximum(ucb, limit - 1, 0)];
   Max1a[23] = cfb[ArrayMaximum(cfb, limit - 1, 0)];
   Max1a[24] = cjb[ArrayMaximum(cjb, limit - 1, 0)];
   Max1a[25] = ufb[ArrayMaximum(ufb, limit - 1, 0)];
   Max1a[26] = fjb[ArrayMaximum(fjb, limit - 1, 0)];
   Max1a[27] = ujb[ArrayMaximum(ujb, limit - 1, 0)];
   
   double Max1 = Max1a[ArrayMaximum(Max1a, 0, 0)];
   
   ChartSetDouble(0, CHART_FIXED_MIN, Min1 - 100);
   ChartSetDouble(0, CHART_FIXED_MAX, Max1 + 100);
   ChartSetDouble(0, CHART_PRICE_MIN, Min1 - 100);
   ChartSetDouble(0, CHART_PRICE_MAX, Max1 + 100);
   
	obname = Name + "EurB"; LabelMake(obname, 0, 1670, 30, "EUR", 12, clrWhite);
	if (EUR == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "GbpB"; LabelMake(obname, 0, 1710, 30, "GBP", 12, clrWhite);
	if (GBP == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "ChfB"; LabelMake(obname, 0, 1750, 30, "CHF", 12, clrWhite);
	if (CHF == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "JpyB"; LabelMake(obname, 0, 1790, 30, "JPY", 12, clrWhite);
	if (JPY == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "AudB"; LabelMake(obname, 0, 1670, 50, "AUD", 12, clrWhite);
	if (AUD == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "NzdB"; LabelMake(obname, 0, 1710, 50, "NZD", 12, clrWhite);
	if (NZD == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "CadB"; LabelMake(obname, 0, 1750, 50, "CAD", 12, clrWhite);
	if (CAD == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	obname = Name + "UsdB"; LabelMake(obname, 0, 1790, 50, "USD", 12, clrWhite);
	if (USD == false) ObjectSetInteger(0, obname, OBJPROP_COLOR, clrRed);
	
	if (EUR == false)
	{
	   //SetIndexStyle(0, DRAW_NONE);//eu
	   //SetIndexStyle(1, DRAW_NONE);//eg
	   //SetIndexStyle(2, DRAW_NONE);//ea
	   //SetIndexStyle(3, DRAW_NONE);//en
	   //SetIndexStyle(4, DRAW_NONE);//ec
	   //SetIndexStyle(5, DRAW_NONE);//ef
	   //SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (GBP == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   //SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   //SetIndexStyle(7, DRAW_NONE);//gu
	   //SetIndexStyle(8, DRAW_NONE);//ga
	   //SetIndexStyle(9, DRAW_NONE);//gn
	   //SetIndexStyle(10, DRAW_NONE);//gc
	   //SetIndexStyle(11, DRAW_NONE);//gf
	   //SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (AUD == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   //SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   //SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   //SetIndexStyle(13, DRAW_NONE);//au
	   //SetIndexStyle(14, DRAW_NONE);//an
	   //SetIndexStyle(15, DRAW_NONE);//ac
	   //SetIndexStyle(16, DRAW_NONE);//af
	   //SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (NZD == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   //SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   //SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   //SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   //SetIndexStyle(18, DRAW_NONE);//nu
	   //SetIndexStyle(19, DRAW_NONE);//nc
	   //SetIndexStyle(20, DRAW_NONE);//nf
	   //SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (CAD == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   //SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   //SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   //SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   //SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   //SetIndexStyle(22, DRAW_NONE);//uc
	   //SetIndexStyle(23, DRAW_NONE);//cf
	   //SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (CHF == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   //SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   //SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   //SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   //SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   //SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   //SetIndexStyle(25, DRAW_NONE);//uf
	   //SetIndexStyle(26, DRAW_NONE);//fj
	   SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (JPY == false)
	{
	   SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   //SetIndexStyle(6, DRAW_NONE);//ej
	   SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   //SetIndexStyle(12, DRAW_NONE);//gj
	   SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   //SetIndexStyle(17, DRAW_NONE);//aj
	   SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   //SetIndexStyle(21, DRAW_NONE);//nj
	   SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   //SetIndexStyle(24, DRAW_NONE);//cj
	   SetIndexStyle(25, DRAW_NONE);//uf
	   //SetIndexStyle(26, DRAW_NONE);//fj
	   //SetIndexStyle(27, DRAW_NONE);//uj
	}
	if (USD == false)
	{
	   //SetIndexStyle(0, DRAW_NONE);//eu
	   SetIndexStyle(1, DRAW_NONE);//eg
	   SetIndexStyle(2, DRAW_NONE);//ea
	   SetIndexStyle(3, DRAW_NONE);//en
	   SetIndexStyle(4, DRAW_NONE);//ec
	   SetIndexStyle(5, DRAW_NONE);//ef
	   SetIndexStyle(6, DRAW_NONE);//ej
	   //SetIndexStyle(7, DRAW_NONE);//gu
	   SetIndexStyle(8, DRAW_NONE);//ga
	   SetIndexStyle(9, DRAW_NONE);//gn
	   SetIndexStyle(10, DRAW_NONE);//gc
	   SetIndexStyle(11, DRAW_NONE);//gf
	   SetIndexStyle(12, DRAW_NONE);//gj
	   //SetIndexStyle(13, DRAW_NONE);//au
	   SetIndexStyle(14, DRAW_NONE);//an
	   SetIndexStyle(15, DRAW_NONE);//ac
	   SetIndexStyle(16, DRAW_NONE);//af
	   SetIndexStyle(17, DRAW_NONE);//aj
	   //SetIndexStyle(18, DRAW_NONE);//nu
	   SetIndexStyle(19, DRAW_NONE);//nc
	   SetIndexStyle(20, DRAW_NONE);//nf
	   SetIndexStyle(21, DRAW_NONE);//nj
	   //SetIndexStyle(22, DRAW_NONE);//uc
	   SetIndexStyle(23, DRAW_NONE);//cf
	   SetIndexStyle(24, DRAW_NONE);//cj
	   //SetIndexStyle(25, DRAW_NONE);//uf
	   SetIndexStyle(26, DRAW_NONE);//fj
	   //SetIndexStyle(27, DRAW_NONE);//uj
	}
   
   if (firstrun) 
   {
      //files();
      firstrun = false;
   }
}

//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
	{
		//Move rectangle
		if (id == CHARTEVENT_OBJECT_ENDEDIT)
		{
			if (sparam == StringConcatenate(Name + "LotSize"))
			{
				lotsize = StringToDouble(ObjectGetString(0, Name + "LotSize", OBJPROP_TEXT));
				ArrayInitialize(eub, 0);
				ArrayInitialize(egb, 0);
				ArrayInitialize(eab, 0);
				ArrayInitialize(enb, 0);
				ArrayInitialize(ecb, 0);
				ArrayInitialize(efb, 0);
				ArrayInitialize(ejb, 0);
				doublecheck = true;
				ChartRedraw();
			}
		}
	}
	//BUTTONS CUT SYMBOLS FROM BASKETS
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "EurB") && EUR == true)
			{
				GlobalVariableSet("fEUR", false);
				EUR = GlobalVariableGet("fEUR");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "EurB") && EUR == false)
			{
				GlobalVariableSet("fEUR", true);
				EUR = GlobalVariableGet("fEUR");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "GbpB") && GBP == true)
			{
				GlobalVariableSet("fGBP", false);
				GBP = GlobalVariableGet("fGBP");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "GbpB") && GBP == false)
			{
				GlobalVariableSet("fGBP", true);
				GBP = GlobalVariableGet("fGBP");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "ChfB") && CHF == true)
			{
				GlobalVariableSet("fCHF", false);
				CHF = GlobalVariableGet("fCHF");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "ChfB") && CHF == false)
			{
				GlobalVariableSet("fCHF", true);
				CHF = GlobalVariableGet("fCHF");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "JpyB") && JPY == true)
			{
				GlobalVariableSet("fJPY", false);
				JPY = GlobalVariableGet("fJPY");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "JpyB") && JPY == false)
			{
				GlobalVariableSet("fJPY", true);
				JPY = GlobalVariableGet("fJPY");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "AudB") && AUD == true)
			{
				GlobalVariableSet("fAUD", false);
				AUD = GlobalVariableGet("fAUD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "AudB") && AUD == false)
			{
				GlobalVariableSet("fAUD", true);
				AUD = GlobalVariableGet("fAUD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "NzdB") && NZD == true)
			{
				GlobalVariableSet("fNZD", false);
				NZD = GlobalVariableGet("fNZD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "NzdB") && NZD == false)
			{
				GlobalVariableSet("fNZD", true);
				NZD = GlobalVariableGet("fNZD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "CadB") && CAD == true)
			{
				GlobalVariableSet("fCAD", false);
				CAD = GlobalVariableGet("fCAD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "CadB") && CAD == false)
			{
				GlobalVariableSet("fCAD", true);
				CAD = GlobalVariableGet("fCAD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
	{
		if (id == CHARTEVENT_OBJECT_CLICK)
		{
			if (sparam == StringConcatenate(Name + "UsdB") && USD == true)
			{
				GlobalVariableSet("fUSD", false);
				USD = GlobalVariableGet("fUSD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
			else if (sparam == StringConcatenate(Name + "UsdB") && USD == false)
			{
				GlobalVariableSet("fUSD", true);
				USD = GlobalVariableGet("fUSD");
				GlobalVariablesFlush();
				ChartRedraw();
			}
		}
	}
}
//+------------------------------------------------------------------+

struct Java
{
   double coin;
   string boin;
};

void AddToJava(Java &arr[], double coin, string boin)
{
   int currentSize = ArraySize(arr);
   ArrayResize(arr, currentSize + 1);
   arr[currentSize].coin = coin;
   arr[currentSize].boin = boin;
}

/*
void files()
{
   int now = iBarShift(_Symbol, PERIOD_CURRENT, iTime(_Symbol, PERIOD_D1, 0), false) + 1;
   
   double file[], filea[], file2[], file3[];
   ArrayResize(file, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) + 1);
   ArrayResize(filea, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) + 1);
   ArrayResize(file2, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) + 1);
   ArrayResize(file3, iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) + 1);
   
   //Print(iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) + 1 + " " + now);
   
   for (int i = iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false); i >= 0; i--)
   {
   //Print(i);
      file[iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) - i] = buysell[i * 24 + now];
      file2[iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) - i] = buysell2[i * 24 + now];
      file3[iBarShift(_Symbol, PERIOD_D1, iTime(_Symbol, second, days), false) - i] = buysell3[i * 24 + now];
   }
   
   {
      file(file, "f1");
      file(file2, "f2");
      file(file3, "f3");
   }
}
*/

double dblTickValue( string strSymbol )
{
   return( MarketInfo( strSymbol, MODE_TICKVALUE ) );
}

double dblPipValue( string strSymbol )
{
   double dblCalcPipValue = dblTickValue( strSymbol );
   if (MarketInfo(strSymbol, MODE_DIGITS) == 3) dblCalcPipValue *= 10;
   if (MarketInfo(strSymbol, MODE_DIGITS) == 5) dblCalcPipValue *= 10;
   return( dblCalcPipValue );
}

//+------------------------------------------------------------------+

//+LABELMAKE FUNCTION------------------------------------------------+
void LabelMake(const string name,
			   const int corner,
			   const int x,
			   const int y,
			   const string label,
			   const int FSize,
			   const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_CORNER, corner);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
	//ObjectSetText(name, label, FSize, "Arial", FCol);
	ObjectSetString(0, name, OBJPROP_TEXT, label);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetString(0, name, OBJPROP_TOOLTIP, "\n");
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
//Create live labels for each call - refresh is automatic depending on oninit / oncalculate / timer
//+------------------------------------------------------------------+

//DE-INIT
//+------------------------------------------------------------------+
//| Custom indicator de-init function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
	if (reason != 3 || IsTesting())
		if (!IsTesting())
		{
			DeleteObjects();
		}
	Print(__FUNCTION__, "_Uninitalization reason code = ", getUninitReasonText(_UninitReason));
	return;
}
//+------------------------------------------------------------------+

//+DELETE OBJECTS----------------------------------------------------+
void DeleteObjects()
{
	for (int i = ObjectsTotal() - 1; i >= 0; i--)
	{
		string ObName = ObjectName(i);
		if (StringFind(ObName, Name, 0) != -1)
		{
			ObjectDelete(ObName);
		}
	}
}
//+------------------------------------------------------------------+

//CUSTOM
//+------------------------------------------------------------------+
//| Custom functions                                                 |
//+------------------------------------------------------------------+
string getUninitReasonText(int reasonCode) // Return reason for De-init function
{
	string text = "";

	switch (reasonCode)
	{
	case REASON_ACCOUNT:
		text = "Account was changed";
		break;
	case REASON_CHARTCHANGE:
		text = "Symbol or timeframe was changed";
		break;
	case REASON_CHARTCLOSE:
		text = "Chart was closed";
		break;
	case REASON_PARAMETERS:
		text = "Input-parameter was changed";
		break;
	case REASON_RECOMPILE:
		text = "Program " + __FILE__ + " was recompiled";
		break;
	case REASON_REMOVE:
		text = "Program " + __FILE__ + " was removed from chart";
		break;
	case REASON_TEMPLATE:
		text = "New template was applied to chart";
		break;
	default:
		text = "Another reason";
	}

	return text;
}
//+------------------------------------------------------------------+


void file(double &file[], string fn)
{
   //double arrayToWrite[] = {1.23, 4.56, 7.89, 10.11, 12.13}; // Example array

   string fileName = TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE) + " " + fn + " output.csv";
   int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_CSV);
   
   FileWrite(fileHandle, TimeToString(iTime(_Symbol, PERIOD_D1, 0), TIME_DATE));
   
   if(fileHandle != INVALID_HANDLE)
   {
      for(int i=0; i<=ArraySize(file) - 1; i++)
      {
         FileWrite(fileHandle, NormalizeDouble(file[i], 0));
         //Print(file[i]);
      }
      
      FileClose(fileHandle);
      Print("Data written to ", fileName);
   }
   else
   {
      Print("Failed to open the file!");
   }
}
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
bool EditBox(const string name, const int Corner, const int x, const int y, const int xdist, const int ydist, const int FSize, const color FCol,
								string teter)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_EDIT, ChartWindowFind(), 0, 0))
		{
			Print("error: can't create label_object! code #", GetLastError());
		}

	ObjectSetInteger(0, name, OBJPROP_CORNER, Corner);
	ObjectSetInteger(0, name, OBJPROP_XSIZE, x);
	ObjectSetInteger(0, name, OBJPROP_YSIZE, y);
	ObjectSetInteger(0, name, OBJPROP_XDISTANCE, xdist);
	ObjectSetInteger(0, name, OBJPROP_YDISTANCE, ydist);
	ObjectSetInteger(0, name, OBJPROP_COLOR, clrWhite);
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FSize);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial");
	ObjectSetString(0, name, OBJPROP_TEXT, teter);
	//ObjectSetText(name, teter, FSize, Font_Type, clrWhite);
	ObjectSetInteger(0, name, OBJPROP_ALIGN, ALIGN_CENTER);
	ObjectSetInteger(0, name, OBJPROP_BGCOLOR, FCol);

	return (true);
}

//+TEXT FUNCTION-----------------------------------------------------+
void Texter(const string name, const double x, const int y, const string text, const color FCol)
{
	if (ObjectFind(0, name) < 0)
		if (!ObjectCreate(0, name, OBJ_TEXT, 0, 0, 0))
		{
			Print("error: can't create text_object! code #", GetLastError());
		}
	ObjectSetInteger(0, name, OBJPROP_COLOR, FCol);
	ObjectSetDouble(0, name, OBJPROP_PRICE1, x);
	ObjectSetInteger(0, name, OBJPROP_TIME1, Time[y] + 600);
	ObjectSetInteger(0, name, OBJPROP_BACK, true);
	ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
	ObjectSetInteger(0, name, OBJPROP_READONLY, true);
	ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
	ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_CENTER);
	ObjectSetString(0, name, OBJPROP_FONT, "Arial Black");
	ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
	if(x > 0)
	ObjectSetString(0, name, OBJPROP_TEXT, "+" + text);
	if(x < 0)
	ObjectSetString(0, name, OBJPROP_TEXT, text);
	//ObjectSetString(0, name, OBJPROP_TOOLTIP, "Price: " + DoubleToStr(x, _Digits));
	ObjectSetString(0, name, OBJPROP_TOOLTIP, ObjectGetString(0, name, OBJPROP_TEXT) + " " + DoubleToStr(x, _Digits));
}
//+------------------------------------------------------------------+