//+------------------------------------------------------------------+
//|                                                       fs2-ea.mq4 |
//|                        Copyright 2017, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2017, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

double upOB, dnOB;
double upFR, dnFR;

#include <tmsrv.mqh>
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
//---
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
//---
   static datetime check = 0;
   bool check2 = false;
   if (check < iTime(_Symbol, PERIOD_CURRENT, 0))
   {
      check2 = true;
      check = iTime(_Symbol, PERIOD_CURRENT, 0);
   }
   if (check2)
   {
      runchecker();
      check2 = false;
   }
  }
//+------------------------------------------------------------------+

void runchecker()
{
   upOB = iCustom(_Symbol, PERIOD_CURRENT, "FS2-ea", 8000, 8000, PERIOD_H4, 5, false, true, "", "", 12, 0);
   dnOB = iCustom(_Symbol, PERIOD_CURRENT, "FS2-ea", 8000, 8000, PERIOD_H4, 5, false, true, "", "", 13, 0);
   upFR = iCustom(_Symbol, PERIOD_CURRENT, "FS2-ea", 8000, 8000, PERIOD_H4, 5, false, true, "", "", 14, 0);
   dnFR = iCustom(_Symbol, PERIOD_CURRENT, "FS2-ea", 8000, 8000, PERIOD_H4, 5, false, true, "", "", 15, 0);
   
   if (upOB != EMPTY_VALUE && upOB - iHigh(_Symbol, PERIOD_CURRENT, 1) < 10 * _Point) tms_send(_Symbol + " near OB resistance " + DoubleToString(upOB, _Digits), "-1001895633916:519d91ea");
   if (dnOB != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, 1) - dnOB < 10 * _Point) tms_send(_Symbol + " near OB support " + DoubleToString(dnOB, _Digits), "-1001895633916:519d91ea");
   if (upFR != EMPTY_VALUE && upFR - iHigh(_Symbol, PERIOD_CURRENT, 1) < 10 * _Point) tms_send(_Symbol + " near FR resistance " + DoubleToString(upFR, _Digits), "-1001895633916:519d91ea");
   if (dnFR != EMPTY_VALUE && iLow(_Symbol, PERIOD_CURRENT, 1) - dnFR < 10 * _Point) tms_send(_Symbol + " near FR support " + DoubleToString(dnFR, _Digits), "-1001895633916:519d91ea");
}